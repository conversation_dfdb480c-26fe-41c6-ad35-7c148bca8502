using System;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations.CampaignsGroup;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Repositories;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Newtonsoft.Json;

namespace Motivai.WebCatalog.Middlewares
{
    public class LoginMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IHelperWeb _helperWeb;
        private readonly CampaignRepository _campaignRepository;

        public LoginMiddleware(RequestDelegate next, IHelperWeb helperWeb, CampaignRepository campaignRepository)
        {
            _next = next;
            _helperWeb = helperWeb;
            _campaignRepository = campaignRepository;
        }

        public async Task Invoke(HttpContext context)
        {
            var _httpContext = RequestContextManager.Instance.CurrentContext;
            var path = _httpContext.Request.Path;

            if (path.HasValue && path.Value.ToLower().StartsWith("/login"))
            {
                var hasParticipantSession = _helperWeb.HasParticipantSession();
                if (!hasParticipantSession)
                {
                    var groupCoalitionParametrization = await GetCampaignGroupCoalitionParametrization(await _helperWeb.GetCampaignIdForCurrentDomain());
                    if (groupCoalitionParametrization != null
                        && groupCoalitionParametrization.CampaignRole == CampaignGroupRole.CHILD_CAMPAIGN
                        && groupCoalitionParametrization.Features.EnableRedirectToRootWhenNotLogged
                        && groupCoalitionParametrization.RootCampaignId != Guid.Empty)
                    {
                        await RedirectToRootUrl(context, groupCoalitionParametrization.RootCampaignId);
                        return;
                    }
                }
            }

            if (!path.HasValue || !IsPublicAccess(path)) {
                var userLogged = _httpContext.Session.GetString(HelperWeb.USERPRINCIPAL_KEY);
                if (string.IsNullOrEmpty(userLogged)) {
                    _httpContext.Response.Redirect("/Login");
                    return;
                }
                if (_helperWeb.IsFirstAccessSession() && !IsFirstAccess(path)) {
                    _httpContext.Response.Redirect("/PrimeiroAcesso/Regulamento");
                    return;
                }

                if (!_helperWeb.IsFirstAccessSession()) {
                    if (_helperWeb.IsNewPrivacyPolicySession()) {
                        if (!IsNewPrivacyPolicy(path)) {
                            _httpContext.Response.Redirect("/PoliticaPrivacidade/Privacidade");
                            return;
                        }
                    } else if (_helperWeb.IsNeedSetupAuthenticationMfaSession()) {
                        if (!IsNeedSetupAuthenticationMfa(path)) {
                            _httpContext.Response.Redirect("/Autenticacao/ConfiguracaoMfa");
                            return;
                        }
                    } else if (_helperWeb.IsNeedAuthenticationMfaSession()) {
                        if (!IsNeedAuthenticationMfa(path)) {
                            _httpContext.Response.Redirect("/Autenticacao/AutenticacaoMfa");
                            return;
                        }
                    }
                }
            }
            await _next.Invoke(context);
        }

        private static bool IsPublicAccess(PathString path)
        {
            return IsLoginPath(path) || IsRodape(path) || IsApi(path) || IsErrorPage(path) || IsFactoryOrder(path);
        }

        private static bool IsFactoryOrder(PathString path)
        {
            return path.Value.StartsWith("/PedidoFabricante/", StringComparison.OrdinalIgnoreCase);
        }

        private static bool IsLoginPath(PathString path)
        {
            var lowerPath = path.Value.ToLower();
            return lowerPath.StartsWith("/login") || lowerPath.StartsWith("/integration/authenticate") || lowerPath.StartsWith("/integration/sso")
                    || lowerPath.StartsWith("/marketplace");
        }

        private static bool IsRodape(PathString path)
        {
            return path.Value == "/Catalog/footer";
        }

        public static bool IsApi(PathString path)
        {
            return path.Value == "/env/props" || path.Value == "/healthy/ping" || path.Value == "/loaderio-ae5bdfd5cc7dff1d724ed9d6cf9e4e73.txt";
        }

        private static bool IsErrorPage(PathString path)
        {
            return path.Value.ToLower().StartsWith("/erro");
        }

        private static bool IsFirstAccess(PathString path)
        {
            var pathInLowercase = path.Value.ToLower();
            return pathInLowercase.StartsWith("/primeiroacesso/") || IsLogoutPath(pathInLowercase) ||
                pathInLowercase == "/minhaconta/operadoras" || pathInLowercase == "/minhaconta/estadoscivil" ||
                pathInLowercase.StartsWith("/addresses/query/");
        }
        private bool IsNeedSetupAuthenticationMfa(PathString path)
        {
            var pathInLowercase = path.Value.ToLower();
            return pathInLowercase.StartsWith("/autenticacao") || IsLogoutPath(pathInLowercase);
        }

        private static bool IsNeedAuthenticationMfa(PathString path)
        {
            var pathInLowercase = path.Value.ToLower();
            return pathInLowercase.StartsWith("/autenticacao") || IsLogoutPath(pathInLowercase);
        }

        private static bool IsNewPrivacyPolicy(PathString path)
        {
            var pathInLowercase = path.Value.ToLower();
            return pathInLowercase.StartsWith("/politicaprivacidade") || IsLogoutPath(pathInLowercase);
        }

        private static bool IsLogoutPath(string pathInLowercase)
        {
            return pathInLowercase.StartsWith("/minhaconta/logout");
        }

        private async Task RedirectToRootUrl(HttpContext context, Guid rootCampaignId)
        {
            try
            {
                var campaignRootUrl =  await this._campaignRepository.GetCampaignSiteUrl(rootCampaignId);
                if (string.IsNullOrEmpty(campaignRootUrl)) {
                    throw MotivaiException.ofValidation("Url da campanha raiz inválida.");
                }

                context.Response.Redirect(campaignRootUrl + "/selecao-conta");
            }
            catch (Exception)
            {
                throw;
            }
        }

        private async Task<CampaignGroupCoalitionParametrization> GetCampaignGroupCoalitionParametrization(Guid campaignId) {
            if (campaignId == Guid.Empty)
                return null;
            try {
                var settings = await _campaignRepository.GetCampaignParametrizations(campaignId);
                if (settings?.Parametrizations == null || !settings.Parametrizations.IsEnableGroupCoalition()) {
                    return null;
                }
                return settings.Parametrizations.GroupColiationParametrization;
             } catch (Exception ex) {
                throw MotivaiException.ofException("Erro ao carregar a configuração da campanha.", ex);
            }
        }
    }
}
