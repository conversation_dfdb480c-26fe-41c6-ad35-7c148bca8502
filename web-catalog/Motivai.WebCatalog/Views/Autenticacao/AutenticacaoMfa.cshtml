@model Motivai.WebCatalog.Models.Pages.AuthenticationMfaPageModel
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Dupla Autenticação";
    ViewBag.ClassBody = "home";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li>Dupla Autenticação</li>
        </ul>
    </div>
</div>
<section id="content">
    <div class="container">
        <div loader-container is-loading="loadingMfaSettings" div-style=""></div>
        <article class="register" ng-controller="AutenticacaoMfaCtrl" ng-init="init()">
            <header>
                <h1>Olá, seja bem-vindo(a)</h1>

                <h2>Valide que está conta é sua</h2>
            </header>
            <fieldset class="password">
                @if (@Model.FromValidate)
                {
                    @if (@Model.IsEmailOnly())
                    {
                        <div class="field">
                            O código será enviado para o e-mail: <strong>@Model.Email</strong>
                        </div>
                    }
                    @if (!@Model.IsEmailOnly())
                    {
                        <div class="field">
                            O código será enviado para o número de celular: <strong>
                                @Model.MobilePhone
                            </strong>
                        </div>
                    }
                    <div class="field" ng-if="!tokenSended || counter == 0">
                        <button type="submit" class="button pull-left" ng-click="sendTokenFromValidate(@Model)" ng-class="{'processing': sendingToken}">{{
                            tokenSended ?
                            'Reenviar': 'Enviar' }}</button>
                    </div>
                }
            </fieldset>
            <form id="validateTokenSubmitted" name="validateTokenSubmitted" ng-if="tokenSended">
                <fieldset class="password">
                    @if (!@Model.IsEmailOnly())
                    {
                        <div class="field">
                            <label for="mobilePhoneSender">Informe o código de segurança recebido</label>
                            <input type="text" autocomplete="off" class="form-control" id="token" name="token"
                            ng-model="userToken.token" placeholder="Digite o Código de segurança recebido por SMS">
                        </div>
                    }
                    @if (@Model.IsEmailOnly())
                    {
                        <div class="field">
                            <label for="emailSender">Informe o código de segurança recebido</label>
                            <input type="text" autocomplete="off" class="form-control" id="token" name="token"
                            ng-model="userToken.token" placeholder="Digite o Código de segurança recebido por E-mail">
                        </div>
                    }
                    <div class="field">
                        <button type="submit" class="button" ng-click="validateTokenAndRefresh()"
                            ng-class="{'processing': validatingToken}">{{
                            validatingToken ? 'Validando' : 'Validar' }}</button>
                    </div>
                    <div class="field" ng-if="counter !== 0">
                        <span>Você pode solicitar um novo envio em: <strong> {{ counter * 1000 | date:'mm:ss'}}
                            </strong>
                        </span>
                    </div>
                </fieldset>
            </form>
        </article>
    </div>
</section>

@section ScriptsCustomizados {
<script src="@Url.Content("~/js/lib/angularjs/angular-resource.min.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript"
    asp-append-version="true"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment-with-locales.min.js"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/authentication/authentication-mfa.js")" type="text/javascript"
    asp-append-version="true"></script>
}