@model Motivai.WebCatalog.Models.Pages.AuthenticationMfaPageModel
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Dupla Autenticação";
    ViewBag.ClassBody = "home";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li>Configuração da Dupla Autenticação</li>
        </ul>
    </div>
</div>
<section id="content">
    <div class="container">
        <article class="register" ng-controller="ConfiguracaoMfaCtrl"
            ng-init="init('@(Model.CampaignSettings.Parametrizations.AuthenticationMfaFormat)')">
            <form id="mfaSettings" name="mfaSettings" autocomplete="off">
                <fieldset class="password">
                    <h1>Dados de Dupla Autenticação</h1>
                    <div class="field">
                        <label for="authFormat">Formato de autenticação<span class="required">*</span></label>
                        <select class="autostart" id="authFormat" name="auth_Format"
                            ng-model="participantMfaSettings.authenticationMfaFormat"
                            ng-disabled="!candEditAuthenticationFormat">
                            <option value="">Selecione</option>
                            <option value="SMS_ONLY">Celular</option>
                            <option value="EMAIL_ONLY">E-mail</option>
                        </select>
                    </div>
                    <div class="field" ng-if="participantMfaSettings.authenticationMfaFormat == 'EMAIL_ONLY'">
                        <label for="email">Email</label>
                        <input id="email" name="email" type="text" ng-model="participantMfaSettings.email">
                    </div>
                    <div class="field" ng-if="participantMfaSettings.authenticationMfaFormat == 'SMS_ONLY'">
                        <label for="mobilePhoneMfa">Celular</label>
                        <input id="mobilePhoneMfa" name="mobilePhoneMfa" type="text"
                            ng-model="participantMfaSettings.MobilePhone">
                    </div>
                    <div class="field top-p29" ng-if="!tokenSended || counter == 0">
                        <button type="submit" class="button pull-left" ng-click="sendToken()" ng-disabled="sendingToken"
                            ng-class="{'processing': sendingToken}">{{ sendingToken ?
                            'Enviando':
                            'Enviar'
                            }}</button>
                    </div>

                    <div ng-if="tokenSended">
                        <div class="field" ng-if="participantMfaSettings.authenticationMfaFormat == 'SMS_ONLY'">
                            <label for="mobilePhoneSender">Informe o código de segurança recebido</label>
                            <input type="text" autocomplete="off" class="form-control" id="token" name="token"
                                ng-model="userToken.token" placeholder="Digite o Código de segurança recebido por SMS">
                        </div>
                        <div class="field" ng-if="participantMfaSettings.authenticationMfaFormat == 'EMAIL_ONLY'">
                            <label for="emailSender">Informe o código de segurança recebido</label>
                            <input type="text" autocomplete="off" class="form-control" id="token" name="token"
                                ng-model="userToken.token"
                                placeholder="Digite o Código de segurança recebido por E-mail">
                        </div>

                        <div class="field">
                            <button type="submit" class="button" ng-click="validateToken() "
                                ng-disabled="validatingToken" ng-class="{'processing': validatingToken}">{{
                                validatingToken ? 'Validando' : 'Validar' }}</button>
                        </div>

                        <div class="field" ng-if="counter !== 0">
                            <span>Você pode solicitar um novo envio em: <strong> {{ counter * 1000 | date:'mm:ss'}}
                                </strong>
                            </span>
                        </div>

                    </div>
                </fieldset>
            </form>
        </article>
    </div>
</section>

@section ScriptsCustomizados {
<script src="@Url.Content("~/js/lib/angularjs/angular-resource.min.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript"
    asp-append-version="true"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment-with-locales.min.js"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/authentication/authentication-mfa.js")" type="text/javascript"
    asp-append-version="true"></script>
}