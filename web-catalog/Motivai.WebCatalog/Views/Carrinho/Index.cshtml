@using Motivai.WebCatalog.Helpers;
@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
@{
	ViewBag.Title = "Carrinho";
	Layout = "~/Views/Shared/_Layout.cshtml";

    var httpContext = RequestContextManager.Instance.CurrentContext;
    var coinPrefix = httpContext.Items["coinPrefix"].ToString();
    var coinSufix = httpContext.Items["coinSufix"].ToString();
}
@section StylesCustomizados {
	<style>
		.divLoadCep { text-align: left; }
		.shippingCalc img { margin-left: 0px !important; height: 200px; }
		.bundle-sell h1 { font-size: 1.1em; display: inline; }
	</style>
}
<div class="breadcrumbs">
	<div class="container">
		<ul>
			<li class="home"><a href="/">Home</a></li>
			<li><PERSON><PERSON></li>
		</ul>
	</div>
</div>

<section id="content" ng-controller="cartCtrl" ng-init="init(@(Model.CampaignSettings.Parametrizations.RequireRedeemToken ? "true" : "false"))">
	<div class="container" ng-if="loadingCart || isEmpty(shoppingCart)">
		<h1>Meu Carrinho</h1>
		<div loader-container is-loading="loadingCart"></div>
		<article class="cart-empty" ng-if="!loadingCart && isEmpty(shoppingCart)">
			<h2>Você ainda não tem produtos em seu carrinho.</h2>
			<div ng-class="'validation-summary-errors'" ng-show="ocorreuErroCarrinho">
				<p><span ng-bind="errorMessage"></span></p>
			</div>
			<a href="/" class="back button">Voltar ao Catálogo</a>
		</article>
	</div>

	<div id="divCart" class="container" ng-if="!loadingCart && !isEmpty(shoppingCart)" style="visibility:none;">
		<article class="cart-checkout">
			<h1>Meu Carrinho</h1>

			@if(Model.CampaignSettings.Pages.ShowCartMessage && !string.IsNullOrEmpty(Model.CampaignSettings.Pages.CartMessage))
			{
			<div class="info" style="margin-right:2.5%">
				<p>@Model.CampaignSettings.Pages.CartMessage</p>
			</div>
			}
			<div ng-show="shoppingCart.Notifications && shoppingCart.Notifications.length > 0">
				<h1>Atenção</h1>
				<div class="info">
					<ul class="listing">
						<li ng-repeat="notif in shoppingCart.Notifications">{{ notif }}</li>
					</ul>
				</div>
			</div>

			@if(Model.IsRewards && Model.CampaignSettings.Parametrizations.AllowBuyPoints)
			{
				<div class="notEnoughPoints"  ng-if="shoppingCart && shoppingCart.AmountToPurchase > 0">
					@if(Model.CampaignSettings.UsePoints()) {
						<h2>Você não tem pontos suficientes para esse @Model.ViewStrings.BuyActionTypeDescriptionLowercase</h2>
					} else {
						<h2>Você não tem saldo suficiente, complemente com cartão de crédito</h2>
					}
					<div class="pointsDetails">
						<ul>
						<li>
							<h3>Total do seu pedido</h3>
							<p>@coinPrefix <span ng-bind="::(shoppingCart.Total | currency:'')">0</span> @coinSufix</p>
						</li>
						<li>
							<h3>Seu saldo atual</h3>
							<p>@coinPrefix <span ng-bind="::(shoppingCart.CurrentBalance | currency:'')">0</span> @coinSufix</p>
						</li>
						<li>
							@if(Model.CampaignSettings.UsePoints()) {
								<h3>Pontos necessários para o @Model.ViewStrings.BuyActionTypeDescriptionLowercase</h3>
							} else {
								<h3>Valor necessário para a compra</h3>
							}
							<p>@coinPrefix <span ng-bind="::(shoppingCart.AmountToPurchase | currency:'')">0</span> @coinSufix</p>
						</li>
						</ul>
					</div>
					<h2>Deseja @Model.ViewStrings.BalancePurchaseActionDescriptionLowerCase @coinPrefix <span ng-bind="::(shoppingCart.AmountToPurchase | currency:'')">0</span> @coinSufix?</h2>
					<a href="#" class="button" ng-click="showPointsPurchasePopup()">Sim, quero @Model.ViewStrings.BalancePurchaseActionDescriptionLowerCase</a>
				</div>
			}

			<div class="validation-summary-errors" ng-show="showErrorMessage">
			  <p><span ng-bind="errorMessage">Ocorreu um erro durante o processamento do seu carrinho, por favor, tente novamente.</span></p>
			</div>

			<form method="post" id="frmCart" onsubmit="onFormSubmit()">
				<input type="hidden" id="hidShippingAddress" name="shippingZipcode" value="{{shippingZipcode}}" />

				<fieldset class="cart-products" ng-if="hasProducts(shoppingCart)">
					<h2>Produtos selecionados para @Model.ViewStrings.BuyActionTypeDescriptionLowercase</h2>

					@if(!Model.CampaignSettings.HasMarketplace())
					{
					<fieldset class="bundle-sell">
						<div class="field">
							<input type="checkbox" id="requiresAllChildren" name="requiresAllChildren" value="1" />
							<label for="requiresAllChildren"><h1>Venda casada: </h1>o pedido somente será fechado se todos os fabricantes aprovarem seus pedidos.</label>
						</div>
					</fieldset>
					}

					<table>
						<thead>
							<tr>
								<th colspan="2">Produto</th>
								<th>Qtde</th>
								<th>Valor Unit.</th>
								<th>Total</th>
							</tr>
						</thead>
						<tbody>
							<tr ng-repeat="item in shoppingCart.ProductsItems">
								<td colspan="2">
									<div class="photo">
										<img src="/assets/img/img-loader.gif" blazy-src="{{::item.Image}}">
									</div>
									<div class="product">
										<h2>
											<span ng-bind="::item.Description"></span>
										</h2>
										<p>
											<span ng-show="::item.PartnerName">Parceiro: <span ng-bind="item.PartnerName"></span></span>
											<span ng-show="::item.SkuCode"><br> SKU: <span ng-bind="item.SkuCode"></span></span>
											<span ng-show="::item.Model"><br> <span ng-bind="item.LabelModel || 'Modelo'"></span>: <span ng-bind="::item.Model"></span></span>
											<span ng-show="::item.Color"><br> <span ng-bind="item.LabelColor"></span>: <span ng-bind="::item.Color"></span></span>
											<span ng-show="::item.Size"><br> <span ng-bind="item.LabelSize"></span>: <span ng-bind="::item.Size"></span></span>
											<span ng-show="::item.Voltage"><br> <span ng-bind="item.LabelVoltage"></span>: <span ng-bind="::item.Voltage"></span></span>
										</p>
										<p ng-show="::item.Attributes">
											<span ng-repeat="at in item.Attributes">
												<span ng-bind="::at.Name"></span>:
													<span ng-if="::(at.Tipo != 'File')" ng-bind="::at.Value"></span>
													<span ng-if="::(at.Tipo == 'File')"><a href="{{at.Value}}" target="_blank">Baixar</a></span>
												<br/>
											</span>
										</p>
										<p class="shippingDate" ng-show="::item.EstimatedDeliveryDays">Previsão de entrega: <strong><span ng-bind="::item.EstimatedDeliveryDays"></span></strong></p>
										<p class="partnerError" ng-show="::item.OccurredError"><span ng-bind="::item.ErrorMessage"></span></p>
										<p ng-show="::item.HasNotifications" ng-repeat="not in ::item.Notifications track by $index">
											<span class="partnerError" ng-bind="::not"></span><br/>
										</p>
									</div>
								</td>
								<td>
									<div class="variable quantity" ng-if="!item.CartItemLimitEnabled || item.CartItemLimitEnabled && item.CartItemLimit > 1">
										<div class="field">
											<input type="text" id="{{::item.Id}}" value="0" required ng-model="item.Quantity" ng-change="updateQuantity(item.Id, item.Quantity)"
												ui-number-mask="0" ng-model-options="{updateOn:'default',debounce:{default:500}}" />
										</div>
									</div>
									<div class="variable"  ng-if="item.CartItemLimitEnabled && item.CartItemLimit == 1">
										<div class="field">
											<p><span ng-bind="item.Quantity"> </span></p>
										</div>
									</div>
									<a href="#" class="remove" ng-click="removeItem(item)">Remover</a>
								</td>
								<td>
									<div ng-if="::!item.DynamicPrice">
										<span ng-if="item.DiscountAmount > 0">
											<strike>de <span ng-bind="::((item.UnitPrice + item.DiscountAmount) | currency:'')"></span><br></strike>por
										</span>
										<span ng-bind="::(item.UnitPrice | currency:'')">0,00</span>
									</div>
									<span ng-if="::item.DynamicPrice" ng-bind="::item.DynamicPriceDescription"></span>
								</td>
								<td>
									<p class="price">
										<strong>@coinPrefix <span ng-bind="::(item.Subtotal | currency:'')">0</span> @coinSufix</strong>
									</p>
								</td>
							</tr>
						</tbody>
					</table>
					@if(Model.CampaignSettings.Parametrizations.EnableShippingCalculation) {
					<p><strong>Tipo de entrega: </strong>de acordo com cada parceiro.</p>
					}
				</fieldset>


				<fieldset class="cart-products" ng-if="hasVouchers(shoppingCart)">
					<h2>Vales selecionados para @Model.ViewStrings.BuyActionTypeDescriptionLowercase</h2>
					<table>
						<thead>
							<tr>
								<th colspan="2">Vale</th>
								<th>Qtde</th>
								<th>Valor Unit.</th>
								<th>Total</th>
							</tr>
						</thead>
						<tbody>
							<tr ng-repeat="item in shoppingCart.VouchersItems">
								<td colspan="2">
									<div class="photo">
										<img src="/assets/img/img-loader.gif" blazy-src="{{::item.Image}}">
									</div>
									<div class="product">
										<h2><span ng-bind="::item.Description"></span></h2>
										<p>
											<span ng-show="::item.PartnerName">Parceiro: <span ng-bind="item.PartnerName"></span></span>
											<span ng-show="::item.SkuCode"><br> SKU: <span ng-bind="::item.SkuCode"></span></span>
											<span ng-show="::item.Model"><br>Modelo: <span ng-bind="::item.Model"></span></span>
											<span ng-show="::item.Color"><br>Cor: <span ng-bind="::item.Color"></span></span>
											<span ng-show="::item.Size"><br>Tamanho: <span ng-bind="::item.Size"></span></span>
											<span ng-show="::item.Voltage"><br>Voltagem: <span ng-bind="::item.Voltage"></span></span>
										</p>
										<p ng-show="::item.Attributes">
											<span ng-repeat="at in item.Attributes">
												<span ng-bind="::at.Name"></span>:
													<span ng-if="::(at.Type != 'File')" ng-bind="::at.Value"></span>
													<span ng-if="::(at.Type == 'File')"><a href="{{at.Value}}" target="_blank">Baixar</a></span>
												<br/>
											</span>
										</p>
										<p class="shippingDate" ng-show="::item.EstimatedDeliveryDays">Previsão de entrega: <strong><span ng-bind="::item.EstimatedDeliveryDays"></span></strong></p>
										<p class="partnerError" ng-show="::item.OccurredError"><span ng-bind="::item.ErrorMessage"></span></p>
										<p ng-show="::item.HasNotifications" ng-repeat="not in ::item.Notifications track by $index">
											<span class="partnerError" ng-bind="::not"></span><br/>
										</p>
									</div>
								</td>
								<td>
									<div class="variable quantity"  ng-if="!item.CartItemLimitEnabled || item.CartItemLimitEnabled && item.CartItemLimit > 1">
										<div class="field">
											<input type="text" id="{{::item.Id}}" value="0" required ng-model="item.Quantity" ng-change="updateQuantity(item.Id, item.Quantity)"
												ui-number-mask="0" ng-model-options="{updateOn:'default',debounce:{default:500}}" />
										</div>
									</div>

									<div class="variable"  ng-if="item.CartItemLimitEnabled && item.CartItemLimit == 1">
										<div class="field">
											<p><span ng-bind="item.Quantity"> </span></p>
										</div>
									</div>
									<a href="#" class="remove" ng-click="removeItem(item)">Remover</a>
								</td>
								<td>
									<div ng-if="!item.DynamicPrice">
										<span ng-if="item.DiscountAmount > 0">
											<strike>de <span ng-bind="::((item.UnitPrice + item.DiscountAmount) | currency:'')"></span><br></strike>por
										</span>
										<span ng-bind="::(item.UnitPrice | currency:'')">0,00</span>
									</div>
								</td>
								<td>
									<p class="price"><strong>@coinPrefix <span ng-bind="::(item.Subtotal | currency:'')"></span> @coinSufix</strong></p>
								</td>
							</tr>
						</tbody>
					</table>
					@if(Model.CampaignSettings.Parametrizations.EnableShippingCalculation) {
					<p><strong>Tipo de entrega: </strong>de acordo com cada parceiro.</p>
					}
				</fieldset>



				@if(Model.CampaignSettings.Parametrizations.EnableDiscountCoupon) {
				<fieldset class="cart-discount">
					<h2>Cupom de desconto</h2>
					<table>
						<tbody>
							<tr>
								<td>
									<div class="field">
										<input type="text" id="discountCoupon" name="discountCoupon" class="discount-coupon"
											placeholder="Digite aqui o código" ng-model="discountCoupon.coupon" />
										<button type="button" class="button" ng-click="applyDiscountCoupon()"></button>
									</div>
								</td>
								<td>
									<table class="subtotal">
										<tr>
											<td>Desconto</td>
											<td>
												<span class="discount-coupon" ng-show="shoppingCart.DiscountCouponCode">{{ shoppingCart.DiscountCouponCode }} - </span>
												@coinPrefix <span ng-bind="::(shoppingCart.DiscountAmount | currency:'')">0</span> @coinSufix
											</td>
										</tr>
									</table>
								</td>
							</tr>
						</tbody>
					</table>
				</fieldset>
				}
			</form>
		</article>

		<aside class="sidebar cart-brief">
			<nav>
				<header>
					<h1>Resumo</h1>
				</header>

				@if(Model.CampaignSettings.Parametrizations.EnableSelectShippingAddress)
				{
					<div class="list-address">
						<h2>Endereço de Entrega</h2>
						<div class="address">
							<form>
								<fieldset class="shippingCalc">
									<div class="divLoadCep" loader-container is-loading="loadingShippingAddress">
										<p>
											<span ng-show="!shoppingCart.ShippingHint">Selecione o endereço de entrega</span>
											<span ng-show="shoppingCart.ShippingHint" ng-bind="shoppingCart.ShippingHint"></span>
										</p>
										<div class="field">
											<select id="shippingZipcode" placeholder="Escolha" data-placeholder="Escolha"
												ng-model="cepSelecionado" ng-change="onCepChange()" style="width:100%" required>
												<option value=""></option>
												<option ng-repeat="end in enderecosEntrega track by end.Id" value="{{::end.Id}}">{{::end.AddressName}} ({{::end.Cep}})</option>
											</select>
											<a class="link-edit-address" href="#" ng-click="editSelectedShippingAddress()" ng-show="shoppingCart.ShippingAddress && shoppingCart.ShippingAddress.Id">Editar o endereço selecionado.</a>
										</div>
									</div>
								</fieldset>
							</form>
						</div>
					</div>

					@if(Model.CampaignSettings.Parametrizations.AllowChangeShippingAddress)
					{
					<div class="list-address cart-new-address">
						<a id="btn-novo-endereco" class="button" data-href="/Carrinho/NovoEndereco" ng-click="showAddressPopup()">Cadastrar endereço</a>
					</div>
					}
				}

				<div class="list-cart">
					<h2>Produtos</h2>
					<p>@coinPrefix <span ng-bind="::(shoppingCart.ProductsTotal | currency:'')">0</span> @coinSufix
					@if(Model.CampaignSettings.Parametrizations.EnableShippingCalculation)
					{
						<span><br/>+ @coinPrefix <span ng-bind="::(shoppingCart.ProductsShippingCost | currency:'')">0</span> @coinSufix (Frete)</span>
					}
					</p>
					<h2 ng-if="hasVouchers(shoppingCart)">Vales</h2>
					<p ng-if="hasVouchers(shoppingCart)">@coinPrefix <span ng-bind="::(shoppingCart.VouchersTotal | currency:'')">0</span> @coinSufix
					@if(Model.CampaignSettings.Parametrizations.EnableShippingCalculation)
					{
						<span><br/>+ @coinPrefix <span ng-bind="::(shoppingCart.VouchersShippingCost | currency:'')">0</span>@coinSufix (Frete)</span>
					}
					</p>
				</div>

				<div class="list-cart-total">
					@if(Model.CampaignSettings.Parametrizations.EnableDiscountCoupon)
					{
					<h2>Desconto</h2>
					<p>
						@coinPrefix <span ng-bind="::(shoppingCart. | currency:'')">0</span> @coinSufix
					</p>
					}
					<h2>Frete</h2>
					<p>
						@coinPrefix <span ng-bind="::(shoppingCart.ShippingCost | currency:'')">0</span> @coinSufix
					</p>
					<h2>Total</h2>
					<p>
						<strong>
							@coinPrefix <span ng-bind="::(shoppingCart.Total | currency:'')">0</span> @coinSufix
						</strong>
					</p>

					@if(Model.IsMarketplace)
					{
						<a id="btn-buy" class="button main-action" ng-click="startPurchase()">Seguir para o pagamento</a>
					} else {
						<a id="btn-buy" class="button main-action" ng-click="startPurchase()">Finalizar @Model.ViewStrings.BuyActionTypeDescriptionLowercase</a>
					}
				</div>
			</nav>
			<a href="/" class="back">Voltar ao Catálogo</a>
		</aside>
	</div>
</section>

@section ScriptsCustomizados {
	<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/shopping-cart/shopping-cart.js")" asp-append-version="true"></script>
	@* @{ Html.RenderPartial("_Scripts_Clearsale.cshtml"); } *@
}
