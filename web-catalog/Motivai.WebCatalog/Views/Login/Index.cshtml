﻿@using Motivai.WebCatalog.Helpers;
@{
    ViewBag.Title = "Login";
    var httpContext = Motivai.WebCatalog.Helpers.RequestContextManager.Instance.CurrentContext;
    var themeUrl = "~/themes";
    var themeItem = httpContext.Items["themeUrl"];
    var isLoginRpa = (httpContext.Items["loginRpa"] as bool?) == true;
    if (themeItem != null)
    {
        themeUrl = themeItem.ToString();
    }
    themeUrl = Url.Content(themeUrl);
}
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <!-- <meta name="google-site-verification" content="" /> -->
    @{ Html.RenderPartial("_Head"); }
</head>

<body class="login" ng-app="platformLogin">
    <div id="page">
        <div class="cover-login"></div>
        <div class="box-login">
            <section id="content">
                <div class="container">
                    <h1 class="logo"></h1>
                    <article class="box">
                        <div class="panel" ng-controller="loginCtrl">
                            <form  method="POST" name="loginForm" autocomplete="off">
                                @if(isLoginRpa)
                                {
                                    <input type="hidden" name="loginRpa" id="loginRpa" value="1">
                                }

                                <header>
                                    <h1>Entrar no catálogo</h1>
                                </header>
                                <div ng-class="{'validation-summary-errors': error}" ng-show="mensagemLogin">
                                    <p><span ng-bind="mensagemLogin"></span></p>
                                </div>
                                <div ng-show="loginForm.$invalid && loginForm.login.$touched && loginForm.password.$touched"
                                    ng-class="{'validation-summary-errors':loginForm.$invalid && loginForm.login.$touched && loginForm.password.$touched}">
                                    <p><span ng-bind="requiredFieldMessage"></span></p>
                                </div>
                                <div class="field">
                                    <label for="login">Seu login</label>
                                    <input id="login" type="text" name="login" placeholder="Seu login"
                                        ng-model="usuario.login" ng-required="true">
                                </div>
                                <div class="field">
                                    <label for="password">Sua senha</label>
                                    <input id="password" type="password" name="password" placeholder="Sua senha"
                                        ng-model="usuario.password" ng-required="true">
                                </div>


                                @if(!isLoginRpa)
                                {
                                    <div class="field">
                                        <div id="loginRecaptcha"></div>
                                    </div>
                                }

                                <div class="field">
                                    <button id="btn-login" type="submit" class="button" ng-click="authenticate(usuario)">Entrar</button>
                                </div>
                                <a href="#" class="rememberPassword"><strong>Esqueci minha senha</strong></a>
                            </form>
                        </div>

                        <div class="panel" ng-controller="passwordRecoverCtrl">
                            <form name="rememberForm" ng-show="primeiroPasso" autocomplete="off">
                                <a href="#" class="rememberPassword">Lembrou a senha? <strong>Volte ao login</strong></a>
                                <header>
                                    <h1>Recuperar senha</h1>
                                    <div class="validation-summary-errors" ng-show="rememberPasswordErrorMessage">
                                        <p><span ng-bind="rememberPasswordErrorMessage"></span></p>
                                    </div>
                                    <div class="validation-summary-errors"
                                        ng-show="rememberForm.$invalid && rememberForm.rememberLogin.$touched && rememberForm.rememberEmail.$touched">
                                        <p>Usuário e e-mail são obrigatórios.</p>
                                    </div>
                                    <div class="field">
                                        <label for="rememberLogin">Seu login</label>
                                        <input id="rememberLogin" type="text" name="rememberLogin" placeholder="Seu login" ng-model="usuario.login" ng-required="true">
                                    </div>
                                    <div class="field">
                                        <label for="rememberEmail">Seu e-mail</label>
                                        <input id="rememberEmail" type="email" name="rememberEmail" placeholder="Seu e-mail" ng-model="usuario.email" ng-required="true">
                                    </div>
                                    <div class="field">
                                        <div id="rememberPasswordRecaptcha"></div>
                                    </div>
                                    <div class="field">
                                        <button id="btn-continue" type="submit" class="button" ng-click="startRecover(usuario)">Continuar</button>
                                    </div>
                                </header>
                            </form>

                            <form name="sendFormToken" ng-show="segundoPasso" autocomplete="off">
                                <header>
                                    <h1>Forma de envio</h1>
                                    <p>Selecione a forma que deseja receber o código de segurança.</p>
                                    <div class="validation-summary-errors" ng-show="mensagemEnvio">
                                        <p><span ng-bind="mensagemEnvio"></span></p>
                                    </div>
                                    <div class="field" ng-show="canUseEmail">
                                        <input id="email" name="formaEnvio" type="radio" value="EMAIL" ng-model="formaEnvio" />
                                        <label for="email">Por e-mail: {{email}}</label>
                                    </div>
                                    <div class="field" ng-show="canUseSms">
                                        <input id="telefone" name="formaEnvio" type="radio" value="SMS" ng-model="formaEnvio" />
                                        <label for="telefone">Por SMS: {{telefone}}</label>
                                    </div>
                                    <div class="field">
                                        <button id="btn-send" type="submit" class="button" ng-click="sendToken(formaEnvio)">Continuar</button>
                                        <a href="/Login" type="submit" class="button back" style="width:100%">Voltar ao Login</a>
                                    </div>
                                </header>
                            </form>

                            <form name="sendFormToken" ng-show="terceiroPasso" autocomplete="off">
                                <header>
                                    <h1>Confirmação do código de segurança</h1>
                                    <div class="validation-summary-errors" ng-show="mensagemValidacaoToken">
                                        <p><span ng-bind="mensagemValidacaoToken"></span></p>
                                    </div>
                                    <div class="field">
                                        <label for="token">Código de segurança recebido</label>
                                        <input id="token" type="text" name="token" ng-model="token" ng-required="true">
                                    </div>
                                    <div class="field">
                                        <button id="btnValidar" type="submit" class="button"
                                            ng-click="validateToken(token)">Continuar</button>
                                        <a href="/Login" type="submit" class="button back" style="width:100%">Voltar ao Login</a>
                                    </div>
                                </header>
                            </form>

                            <form name="formSenha" ng-show="quartoPasso" autocomplete="off">
                                <header>
                                    <h1>Alteração de senha</h1>
                                    <div class="validation-summary-errors" ng-show="mensagemAlteracaoSenha">
                                        <p><span ng-bind="mensagemAlteracaoSenha"></span></p>
                                    </div>
                                    <div class="field">
                                        <label for="newPassword">Nova senha</label>
                                        <input id="newPassword" name="newPassword" type="password" ng-model="senha.novaSenha" ng-keyup="validatePassword()" required>
                                        <span ng-class="{'CustomValidationError validationFieldRequired': newPasswordInvalid}" ng-bind="passwordChangeErrorMessage"></span>
                                        <p>
                                            <strong>A senha deve conter ao menos:</strong>
                                            <ul>
                                                <li>Um número</li>
                                                <li>Uma letra maiúscula</li>
                                                <li>Uma letra minúscula</li>
                                                <li>No mínimo 6 caracteres</li>
                                            </ul>
                                        </p>
                                    </div>
                                    <div class="field">
                                        <label for="passwordConfirm">Confirmar senha</label>
                                        <input id="passwordConfirm" name="passwordConfirm" type="password"
                                            data-rule-equalto="#newPassword" ng-model="senha.confirmacaoSenha"
                                            ng-keyup="validatePassword()" required>
                                        <span ng-class="{'CustomValidationError validationFieldRequired': passwordConfirmInvalid}" ng-bind="confirmPasswordErrorMessage"></span>
                                    </div>
                                    <div class="field">
                                        <button id="btn-change" type="submit" class="button" ng-click="changePassword(senha)">Alterar</button>
                                        <a href="/Login" type="submit" class="button back" style="width:100%">Voltar ao Login</a>
                                    </div>
                                </header>
                            </form>

                            <div ng-show="passosFinalizados">
                                <header>
                                    <h1>Recuperação de senha</h1>
                                </header>
                                <p>Sua senha foi alterada com sucesso, por favor, efetue login para prosseguir.</p>
                                <a href="/Login" type="submit" class="button back">Voltar ao Login</a>
                            </div>
                        </div>
                    </article>
                </div>
            </section>
        </div>
        <div class="footer-fix"></div>
    </div>

    @{ Html.RenderPartial("_Footer"); }
    @{ Html.RenderPartial("_Scripts"); }
    <script src="https://www.google.com/recaptcha/api.js?onload=initReCaptcha" async defer></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment-with-locales.min.js"></script>
    <script type="text/javascript" src="~/js/lib/moment/moment-timezone-with-data-2012-2022.min.js" asp-append-version="true"></script>
    <script type="text/javascript" src="~/js/app/app.js" asp-append-version="true"></script>
    <script type="text/javascript" src="~/js/login/controllers.js" asp-append-version="true"></script>
</body>
</html>
