﻿@{
    ViewBag.Title = "Política de Privacidade";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"> <a href="/">Home</a></li>
            <li>Política de Privacidade</li>
        </ul>
    </div>
</div>
<section id="content">
    <div class="container" ng-controller="institutionalCtrl" ng-init="loadPrivacyPolicy()">
        <article class="terms" loader-container is-loading="loadingPolicy">
            <h1>Política de Privacidade</h1>
            <div ng-bind-html="privacyPolicy"></div>
        </article>
    </div>
</section>
@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/institutional/institutional-app.js")"></script>
}
