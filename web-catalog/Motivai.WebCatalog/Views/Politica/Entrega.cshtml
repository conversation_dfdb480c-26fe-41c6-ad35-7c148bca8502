﻿@{
    ViewBag.Title = "Política de Entrega e Troca";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"> <a href="/">Home</a></li>
            <li>Política de Entrega e Troca</li>
        </ul>
    </div>
</div>
<section id="content">
    <div class="container" ng-controller="institutionalCtrl" ng-init="loadShippingPolicy()">
        <article class="terms" loader-container is-loading="loadingPolicy">
            <h1>Política de Entrega e Troca</h1>
            <div ng-bind-html="shippingPolicy"></div>
        </article>
    </div>
</section>
@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/institutional/institutional-app.js")"></script>
}