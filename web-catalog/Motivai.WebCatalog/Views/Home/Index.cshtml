﻿@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Página Principal";
    ViewBag.ClassBody = "home";
}

<div ng-controller="homeCtrl">
    <section id="content" class="mediabox-m" ng-if="mediaBoxHome.length > 0">
        <div class="container">
            <div id="mediabox">
                <article class="mediabox page-banner">
                    <a ng-repeat="item in mediaBoxHome | orderBy:'Position'"
                        target="{{::item.OpenInNewTab ? '_blank' : '' }}" id="{{::item.ElemId}}"
                        href="{{ ::item.Link }}">
                        <img ng-if="item.ContentType == 'BANNER'"
                            data-srcset="{{item.ImageUrl}}/1920 2600w 3x {{item.ImageUrl}}/1680 1920w 3x, {{item.ImageUrl}}/1680 1680w 3x, {{item.MediumImageUrl}}/1280 1400w 3x, {{item.MediumImageUrl}}/1280 1280w 3x, {{item.MediumImageUrl}}/1280 1024w 3x, {{item.SmallImageUrl}}/599 599w 3x, {{item.SmallImageUrl}}/599 425w 3x, {{item.SmallImageUrl}}/599 360w 3x" />

                        <img ng-if="item.ContentType == 'PRODUCT'" title="#{{ ::item.FeaturedProduct.Id }}"
                            data-srcset-base="/assets/img/mediabox/" data-srcset-ext=".jpg"
                            data-srcset="bg-product-wd 1920w 3x, bg-product-wd 1680w 3x, bg-product-d 1280w 3x, bg-product-s 599w 3x" />
                    </a>
                </article>

                <div class="nivo-html-caption mediabox-products" ng-repeat="prod in mediaboxProducts" id="{{ ::prod.Id }}">
                    <div class="container">
                        <product-showcase hide-button="false" product="prod"></product-showcase>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div style="visibility:hidden;position:absolute;">
        <div id="modal-communication" style="min-width:800px;">
            <input type="hidden" id="cname" value="" />
            <h1 id="modal-title"></h1>
            <div id="modal-text"></div>
        </div>
    </div>
</div>

<div ng-controller="specialStoreCtrl">
    <section id="content" class="special-shop-feature-grid" ng-show="showSpecialStoresGrid && showSpecialShopAsFeaturedInHomePage">
        <div class="lojas-destacadas container" is-loading="loadingSpecialStores">
            <div class="vitrine-mktplace">
                <div class="row mktp-center-content">
                    <div ng-repeat="specialShop in specialShopsInGrid" ng-class="specialShop.styleClass">
                        <div class="block-content">
                            <a href="{{::specialShop.PageLinkUrl}}" title="{{::specialShop.Name}}" target="{{::specialShop.ViewParametrization.OpenInNewTab == true ? '_blank' : '' }}" ng-show="specialShop.PageLinkUrl">
                                <img class="mktp-blc-banner full-md" title="{{::specialShop.Name}}" src="/assets/img/img-loader.gif" blazy-src="{{::specialShop.LinkBannerImageUrl}}">
                            </a>
                            <a href="/Loja/Index/{{::specialShop.Url}}" title="{{::specialShop.Name}}" target="{{::specialShop.ViewParametrization.OpenInNewTab == true ? '_blank' : '' }}" ng-hide="specialShop.PageLinkUrl">
                                <img class="mktp-blc-banner full-md" title="{{::specialShop.Name}}" src="/assets/img/img-loader.gif" blazy-src="{{::specialShop.LinkBannerImageUrl}}">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="content" class="mktp-group-card special-shop-feature-card"
        ng-show="showSpecialStoresCards && showSpecialShopAsFeaturedInHomePage">
        <div class="container">
            <div class="row mktp-group-card-m">
                <div class="col-md-12 col-xs-12">
                    <div class="row card-row">
                        <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 card-item mktp-blc-banner"
                            ng-repeat="specialShop in specialShopInCards">
                            <div class="block-content">
                                <a href="{{::specialShop.PageLinkUrl}}" title="{{::specialShop.Name}}" target="{{::specialShop.ViewParametrization.OpenInNewTab == true ? '_blank' : '' }}" ng-show="specialShop.PageLinkUrl">
                                    <img title="{{::specialShop.Name}}" src="/assets/img/img-loader.gif" blazy-src="{{::specialShop.LinkBannerImageUrl}}/450/450/crop">
                                </a>
                                <a href="/Loja/Index/{{::specialShop.Url}}" title="{{::specialShop.Name}}" target="{{::specialShop.ViewParametrization.OpenInNewTab == true ? '_blank' : '' }}" ng-hide="specialShop.PageLinkUrl">
                                    <img title="{{::specialShop.Name}}" src="/assets/img/img-loader.gif" blazy-src="{{::specialShop.LinkBannerImageUrl}}/450/450/crop">
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<section id="content">
    <div class="container">
        <article class="list-products mais-desejados" ng-controller="homeProductsCtrl">
            <header>
                <h1>Produtos em destaque</h1>
            </header>
            <div loader-Container is-loading="loadingProds">
                <ul class="products mais-desejados">
                    <product-showcase ng-repeat="prod in products" product="prod"></product-showcase>
                </ul>
                <div ng-show="hasProducts">
                    <h2>Nenhum produto encontrado...</h2>
                </div>
            </div>
        </article>

        <article class="list-banners lojas" ng-controller="specialStoreCtrl" ng-show="hasAnyDefaultSpecialStore">
            <header><h1></h1></header>
            <div class="slider" loader-container is-loading="loadingSpecialStores">
                <ul class="slides banners">
                    <li ng-repeat="specialShop in specialShop">
                        <a href="/Loja/Index/{{::specialShop.Url}}?origin={{::specialShop.Origin}}" target="{{::specialShop.ViewParametrization.OpenInNewTab == true ? '_blank' : '' }}">
                            <img class="special-shop-link" src="/assets/img/img-loader.gif" title="{{::specialShop.Name}}" blazy-src="{{::specialShop.LinkBannerImageUrl}}/373/375/crop" />
                        </a>
                    </li>
                </ul>
            </div>
        </article>

        <article class="list-products vouchers" ng-controller="vouchersCtrl" ng-show="showVouchers">
            <header>
                <h1>Os vales mais @Model.ViewStrings.MostRedeemedProductsDescriptionLowerCase</h1>
            </header>
            <div loader-container is-loading="loadingVouchers">
                <div class="slider">
                    <ul class="slides products mais-resgatados">
                        <product-showcase ng-repeat="prod in products" product="prod"></product-showcase>
                    </ul>
                </div>
            </div>
        </article>
    </div>
</section>

@section ScriptsCustomizados {
<script src="~/js/app/directives/loader-container.js" type="text/javascript"></script>
<script src="~/js/lib/input-masks/masks.js" type="text/javascript"></script>
<script src="~/js/catalog/catalog-services.js"></script>
<script src="~/js/catalog/catalog-app.js"></script>
}
