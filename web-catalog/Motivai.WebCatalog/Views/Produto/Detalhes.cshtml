﻿@model Motivai.WebCatalog.Models.Pages.Produto.ProductViewModel;
@using Motivai.SharedKernel.Domain.Enums;
@using Motivai.SharedKernel.Helpers;
@{
    ViewBag.Title = "Detalhes - " + Model.Product.Product.Name;
    ViewBag.ClassBody = "produto-detalhes";
    Layout = "~/Views/Shared/_Layout.cshtml";

    var product = Model.Product.Product;

    var canSelectShippingAddress = Model.Product.CanSelectShippingAddress ? "true" : "false";
    var isDynamicPriceDefinedByAdmin = product.Prices.DynamicPrice && !product.Prices.IsDynamicPriceDefinedByParticipant() ? "true" : "false";
    var isDynamicPriceDefinedByParticipant = product.Prices.DynamicPrice && product.Prices.IsDynamicPriceDefinedByParticipant() ? "true" : "false";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"><a href="/">Home</a></li>
            <li><a id="department" href="/Departamento/Index/@product.DepartmentId">@product.Department</a></li>
            @if(product.HasSubcategory()) {
                <li><a id="category" href="/Departamento/Categoria/@product.CategoryId">@product.Category</a></li>
                <li class="active"><a id="subcategory" href="/Departamento/Categoria/@product.CategoryId?sc=@product.Subcategory">@product.Subcategory</a></li>
            } else {
                <li class="active"><a id="category" href="/Departamento/Categoria/@product.CategoryId">@product.Category</a></li>
            }
        </ul>
    </div>
</div>

<section id="content" ng-controller="productCtrl" ng-init="initSearch('@product.ElasticsearchId', '@(Model.Product.EnableMatchingByEan ? product.SkuEan : "")', '@product.CategoryId', @canSelectShippingAddress, @isDynamicPriceDefinedByAdmin, @isDynamicPriceDefinedByParticipant)">
    <div class="container">
        <div class="product-brief">
            @if(product.IsPromotional())
            {
            <div class="tag new">Promoção</div>
            }
            <h1>@product.Name</h1>
            <p class="brand">Cód: <span id="spn-sku">@product.SkuCode</span> - Marca: <span id="spn-brand">@product.Manufacturer</span></p>
            <p class="seller">Vendido por <strong>@product.Partner</strong></p>
        </div>


        <article class="product-details">
            <div id="gallery" class="gallery">
                <ul data-cycle-fx="carousel" data-cycle-timeout="0" data-cycle-next=".controlNav a.next" data-cycle-prev=".controlNav a.prev"
                    data-cycle-carousel-visible="@(product.Images != null && product.Images.Count < 8 ? product.Images.Count : 8)"
                    data-allow-wrap="false" data-cycle-slides="li" data-cycle-carousel-vertical="true" data-cycle-auto-height="false" data-cycle-log="false"
                    class="cycle-slideshow">
                    @if(product.Images != null) {
                        foreach(var img in product.Images) {
                        <li>
                            <a href="#" data-image="@img.Large" data-zoom-image="@img.Zoom" title="@product.Name"
                                class="@(product.FirstImage == img.Large ? "active": "")">
                                <img src="@img.Small">
                            </a>
                        </li>
                        }
                    }
                </ul>
                <div class="controlNav"><a href="#" class="prev">Anterior</a><a href="#" class="next">Próximo</a></div>
            </div>
            <div class="photo">
                <img src="@product.FirstImage" data-zoom-image="@product.FirstZoomImage">
            </div>

            <div class="product-info">
                @if(product.OccurredError && !String.IsNullOrEmpty(product.ErrorDescription))
                {
                    <div class="validation-summary-errors"><span>@product.ErrorDescription</span></div>
                }

                @if(product.IsAvailable())
                {
                <form id="frm-prod" method="post" action="/Carrinho/Add" enctype="multipart/form-data" onsubmit="onAddToCart()">
                    <div class="side-a">
                        <fieldset>
                            <input type="hidden" id="p" value="@product.Id" />
                            <input type="hidden" id="id" name="id" value="@product.ElasticsearchId" />
                            <input type="hidden" id="md" name="md" value="@product.SkuCode" />
                            <input type="hidden" id="model" name="model" value="@product.SkuModel" />

                            @if(product.Offline && product.Attributes.HasColors()) {
                                <div class="variable colors">
                                    <h2>@product.ProductColorLabel</h2>
                                    <div class="field">
                                        <select class="autostart required" id="cor" name="cor" placeholder="Escolha" data-placeholder="Escolha" required
                                            ng-model="::cr" @Html.Raw(product.OnlyOneSku ? "" : "ng-change=\"corOnChange()\"")>
                                            @if(!product.OnlyOneSku) { <option value=""></option> }
                                            @foreach (var color in product.Attributes.Colors) {
                                                <option value="@color">@color</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                            }
                            @if(product.Attributes.HasSizes()) {
                                <div class="variable size">
                                    <h2>@product.ProductSizeLabel</h2>
                                    <div class="field">
                                        <select class="autostart required" id="tamanho" name="tamanho" placeholder="Escolha" data-placeholder="Escolha" required
                                            ng-model="::tam" @Html.Raw(product.OnlyOneSku ? "" : "ng-change=\"tamanhoOnChange()\"")>
                                            @if(!product.OnlyOneSku) { <option value=""></option> }
                                            @foreach (var size in product.Attributes.Sizes) {
                                                <option value="@size">@size</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                            }
                            @if(product.Attributes.HasVoltages()) {
                                <div class="variable voltages">
                                    <h2>@product.ProductVoltageLabel</h2>
                                    <div class="field">
                                        <select class="autostart required" id="voltagem" name="voltagem" placeholder="Escolha" data-placeholder="Escolha" required
                                            ng-model="::volt" @Html.Raw(product.OnlyOneSku ? "" : "ng-change=\"voltagemOnChange()\"")>
                                            @if(!product.OnlyOneSku) { <option value=""></option> }
                                            @foreach (var vol in product.Attributes.Voltages) {
                                                <option value="@vol">@vol</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                            }
                            @if(product.Attributes.HasModels()) {
                                <div class="variable models">
                                    <h2>@product.ProductModelLabel</h2>
                                    <div class="field">
                                        <select class="autostart required" id="modelo" name="modelo" placeholder="Escolha" data-placeholder="Escolha" required
                                            ng-model="::mod" @Html.Raw(product.OnlyOneSku ? "" : "ng-change=\"modelOnChange()\"")>
                                            @if(!product.OnlyOneSku) { <option value=""></option> }
                                            @foreach (var mod in product.Attributes.Models) {
                                                <option value="@mod">@mod</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                            }
                        </fieldset>
                    </div>

                    <div class="side-b">
                        <fieldset id="fieldsetPrices">
                            <div loader-container is-loading="loadingDetails" img-style="height:100px;margin:1em auto;">
                                <span style="display:block" ng-if="showDynamicPriceRange">
                                    Preencha um valor entre <strong ng-bind="(dynamicPriceProductParametrization.dynamicPriceMinimumValue | currency:'')"></strong> e <strong ng-bind="(dynamicPriceProductParametrization.dynamicPriceMaximumValue | currency:'')"></strong>
                                </span>

                                <div class="price">
                                    <p>
                                        <span id="dPreco" ng-if="dynamicPriceProductParametrization.isDynamicPriceDefinedByAdmin">
                                            <strong><span id="dDesc">@(product.CatalogSettings != null && !string.IsNullOrEmpty(product.CatalogSettings.DynamicPriceDescription) ? product.CatalogSettings.DynamicPriceDescription : "Produto sob encomenda")</span></strong>
                                        </span>

                                        <span id="pPreco" ng-if="showProductPriceDetails">
                                            @if(product.IsPromotional()) {
                                            <span class="promo-d" id="pPrecoDe" ng-if="showProductPriceFrom">
                                                De @product.Prices.CoinName.Prefix <span id="precoDe">@product.Prices.PriceFrom.ToCurrency()</span> @product.Prices.CoinName.Sufix por
                                            </span>
                                            }
                                            <span class="break"><strong class="inline">@product.Prices.CoinName.Prefix <span id="preco">@product.GetSalePrice().ToCurrency()</span> @product.Prices.CoinName.Sufix</strong></span>
                                            @if(product.ShowPriceWithPurchaseCost) {
                                                <span class="points-purchase">+ R$ <span>@product.PointsNeededPurchaseCost.Value.ToCurrency()</span></span>
                                            }
                                        </span>

                                        <input type="text" id="priceDefinedByParticipant" name="priceDefinedByParticipant" placeholder="R$ 0,00" minlength="1" maxlength="6"
                                            ui-number-mask ng-model="priceDefinedByParticipant" ng-blur="consultaDisponibilidadePrecoParticipante()"
                                            ng-if="dynamicPriceProductParametrization.isDynamicPriceDefinedByParticipant">
                                    </p>
                                </div>

                                <div class="sendToCart">
                                    @if(product.OnlyOneSku && product.HasCustomAttributes()) {
                                        @foreach(var attr in product.CustomAttributes) {
                                            if (attr.Type == CustomAttributeType.File) {
                                                <input type="file" id="<EMAIL>" name="<EMAIL>" style="visibility:hidden;position:absolute;" />
                                            } else {
                                                <input type="hidden" id="<EMAIL>" name="<EMAIL>" value="" />
                                            }
                                        }

                                        <button type="submit" class="button btn-buy" id="btn-show-modal"  href="#modal-custom-attrs">@Model.ViewStrings.BuyActionDescription</button>

                                        <div style="visibility:hidden;position:absolute;">
                                            <div id="modal-custom-attrs" class="fromMarketplace custom-attrs">
                                                <h1>Preencha as informações abaixo antes de adicionar no carrinho</h1>
                                                <p>Campos com * são obrigatórios.</p>
                                                <div>
                                                    <table>
                                                        <tbody>
                                                        @foreach(var attr in product.CustomAttributes) {
                                                            <tr class="@(attr.Type == CustomAttributeType.Textarea ? "no-border" : "")">
                                                                <td><strong>@attr.Name @(attr.Required ? "*" : "")</strong></td>
                                                                <td><span>@attr.Description</span></td>
                                                                <td>
                                                                @if(attr.Type == CustomAttributeType.Text) {
                                                                    <input type="text" class="@(attr.Required ? "required-attr" : "")" id="@attr.Identifier"
                                                                        name="@attr.Identifier" required="@attr.Required" placeholder="@attr.Example"
                                                                        onchange="$('#<EMAIL>').val(this.value)" />
                                                                } else if(attr.Type == CustomAttributeType.Number) {
                                                                    <input type="number" class="@(attr.Required ? "required-attr" : "")" id="@attr.Identifier"
                                                                        name="@attr.Identifier" required="@attr.Required" placeholder="@attr.Example"
                                                                        onchange="$('#<EMAIL>').val(this.value)" />
                                                                } else if (attr.Type == CustomAttributeType.Check) {
                                                                    <input type="checkbox" class="@(attr.Required ? "required-attr" : "")" id="@attr.Identifier"
                                                                        name="@attr.Identifier" required="@attr.Required" value="true"
                                                                        onchange="$('#<EMAIL>').val(this.value)" />
                                                                    <label for="@attr.Identifier">Sim</label>
                                                                } else if (attr.Type == CustomAttributeType.File) {
                                                                    <input type="file" class="" id="@attr.Identifier" name="@attr.Identifier" required="@attr.Required"
                                                                        onchange="$('#p_'+this.name)[0].files=this.files" />
                                                                }
                                                                </td>
                                                            </tr>
                                                            @if(attr.Type == CustomAttributeType.Textarea) {
                                                            <tr><td colspan="4">
                                                                <textarea class="@(attr.Required ? "required-attr" : "")" id="@attr.Identifier"
                                                                    name="@attr.Identifier" required="@attr.Required" placeholder="@attr.Example"
                                                                    onchange="$('#<EMAIL>').val(this.value)"></textarea>
                                                            </td></tr>
                                                            }
                                                        }
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <button type="submit" id="btn-attr-confirm" class="button btn-buy btn-attr-confirm" onclick="sendWithCustomAttributes()">Confirmar e Adicionar no Carrinho</button>
                                                <div class="validation-summary-errors" id="prod-attrs-msg"></div>
                                            </div>
                                        </div>
                                    } else {
                                        <button type="submit" class="button button-primary btn-buy" ng-if="!hasCustomAttributes" onclick="return verifyRequired()">@Model.ViewStrings.BuyActionDescription</button>
                                        <button type="submit" class="button button-primary btn-buy" style="visibility:hidden" href="#modal-custom-attrs" id="btn-show-modal" ng-if="hasCustomAttributes">@Model.ViewStrings.BuyActionDescription</button>

                                        <span ng-repeat="attr in customAttributes">
                                            <input type="hidden" id="p_{{attr.Identifier}}" name="p_{{attr.Identifier}}" ng-if="attr.Type!='File'" />
                                            <input type="file" id="p_{{attr.Identifier}}" name="p_{{attr.Identifier}}" ng-if="attr.Type=='File'" style="visibility:hidden;position:absolute;" />
                                        </span>

                                        <div style="visibility:hidden;position:absolute;">
                                            <div id="modal-custom-attrs" class="fromMarketplace custom-attrs" ng-if="hasCustomAttributes">
                                                <h1>Preencha as informações abaixo antes de adicionar no carrinho</h1>
                                                <p>Campos com * são obrigatórios.</p>
                                                <div>
                                                    <table>
                                                        <tbody>
                                                            <tr ng-repeat-start="attr in customAttributes" ng-class="{'no-border': attr.Type=='Textarea'}">
                                                                <td><strong ng-bind="attr.Name + (attr.Required ? '*' : '')"></strong></td>
                                                                <td><span ng-bind="attr.Description"></span></td>
                                                                <td>
                                                                    <input type="text" ng-class="{'required-attr': attr.Required}" id="{{attr.Identifier}}"
                                                                        name="{{attr.Identifier}}" required="{{attr.Required}}" placeholder="{{attr.Example}}"
                                                                        onchange="$('#p_'+this.name).val(this.value)" ng-if="attr.Type=='Text'" />

                                                                    <input type="number" ng-class="{'required-attr': attr.Required}" id="{{attr.Identifier}}"
                                                                        name="{{attr.Identifier}}" required="{{attr.Required}}" placeholder="{{attr.Example}}"
                                                                        onchange="$('#p_'+this.name).val(this.value)" ng-if="attr.Type=='Number'" />

                                                                    <input type="file" ng-class="{'required-attr': attr.Required}" id="{{attr.Identifier}}"
                                                                        name="{{attr.Identifier}}" required="{{attr.Required}}" placeholder="{{attr.Example}}"
                                                                        onchange="$('#p_'+this.name)[0].files=this.files" ng-if="attr.Type=='File'" />

                                                                    <span ng-if="attr.Type=='Check'">
                                                                        <input type="checkbox" ng-class="{'required-attr': attr.Required}" id="{{attr.Identifier}}"
                                                                            name="{{attr.Identifier}}" required="{{attr.Required}}" value="true"
                                                                            onchange="$('#p_'+this.name).val(this.value)"/>
                                                                        <label for="{{attr.Identifier}}">Sim</label>
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                            <tr ng-repeat-end ng-if="attr.Type=='Textarea'">
                                                                <td colspan="4">
                                                                    <textarea ng-class="{'required-attr': attr.Required}" id="{{attr.Identifier}}"
                                                                        name="{{attr.Identifier}}" required="{{attr.Required}}" value="true"
                                                                        onchange="$('#p_'+this.name).val(this.value)"></textarea>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    <button type="submit" id="btn-attr-confirm" class="button btn-buy btn-attr-confirm" onclick="sendWithCustomAttributes()">Confirmar e Adicionar no Carrinho</button>
                                                    <div class="validation-summary-errors" id="prod-attrs-msg"></div>
                                                </div>
                                            </div>
                                        </div>
                                    }

                                    @if(Model.Product.CanQuicklyBuy) {
                                        <a href="#" data-fancybox-type="iframe" class="button modal">@Model.ViewStrings.QuickBuyDescription</a>
                                        <p><span title="@Model.ViewStrings.QuickBuyDescription seu produto com apenas um click!" class="tooltip"></span>O que é <strong>@Model.ViewStrings.QuickBuyDescription?</strong></p>
                                    }
                                </div>

                                @if(Model.Product.CanSelectShippingAddress)
                                {
                                <div id="fieldsetShipping">
                                    <div class="div-loading" loader-container is-loading="isCalculatingShipping" img-style="height:100px;margin:1em auto;"></div>
                                    <div class="shippingCalc" style="margin-bottom:1em" ng-show="enderecosEntrega && !isCalculatingShipping">
                                        @if(Model.Product.HasShippingCalculation)
                                        {
                                            <p>Consulte o frete</p>
                                        } else {
                                            <p>Selecione o endereço de entrega</p>
                                        }

                                        <div class="field">
                                            <select id="shippingZipcode" name="cep" placeholder="Escolha" data-placeholder="Escolha"
                                                ng-model="shippingZipcode" @Html.Raw(Model.Product.HasShippingCalculation?"ng-change=\"onCepChange()\"":"ng-change=\"onShippingAddressChange()\"")>
                                                <option></option>
                                                <option ng-repeat="end in enderecosEntrega" value="{{::end.Id}}">{{::end.AddressName}} ({{::end.Cep}})</option>
                                                @if(Model.Product.HasShippingCalculation){
                                                    <option value="-1">Outro endereço</option>
                                                } else {
                                                    <option value="other">Outro endereço</option>
                                                }
                                            </select>
                                        </div>
                                        @if(Model.Product.HasShippingCalculation) {
                                            <div class="other">
                                                <div class="field">
                                                    <input type="text" id="cepAlternativo" name="cepAlternativo" placeholder="Digite o CEP" maxlength="9" class="cep" ng-model="cepAlternativo">
                                                    <button type="button" class="button" ng-click="calculaFrete()"></button>
                                                </div>
                                            </div>
                                        }
                                        @if(Model.Product.CanSelectFactory) {
                                            <div class="variable fabricante">
                                                <h2>Fabricante</h2>
                                                <div class="field">
                                                    <select id="fabricante" name="fabricante" placeholder="Escolha" data-placeholder="Escolha" required>
                                                        <option></option>
                                                        <option ng-repeat="fab in manufacturers | orderBy:'FactoryName'" value="{{fab.FactoryId}}">{{fab.FactoryName}}</option>
                                                    </select>
                                                </div>
                                            </div>
                                        }
                                        @if(Model.Product.HasShippingCalculation) {
                                            <div class="result" ng-if="!calculoFrete.ocorreuErro">
                                                <p>Valor do Frete: <strong>@product.Prices.CoinName.Prefix <span ng-bind="(calculoFrete.valorFrete | currency:'')"></span> @product.Prices.CoinName.Sufix</strong></p>
                                                <p>Previsão de entrega: <strong><span ng-bind="calculoFrete.tempoFrete"></span> dias</strong></p>
                                            </div>
                                        }
                                    </div>
                                </div>
                                }
                            </div>
                        </fieldset>
                    </div>

                    <div id="pDisponivel" style="display:none">
                        <fieldset>
                            <input type="hidden" id="p" name="p" value="@product.Id" />
                            <div class="price sold-out-product">
                            <p><strong>Produto esgotado</strong></p>
                            </div>
                            <div class="alert-stock" ng-if="!erroRegistroAviso && !registroAviso">
                                <h2>Deseja ser avisado quando o produto ficar disponível?</h2>
                                <p>Você será avisado através do seu e-mail cadastrado<br><strong>@ViewBag.email</strong></p>
                                <button type="submit" class="button" ng-click="registraInteresse()" ng-hide="loadingAviso">Avisar</button>
                                <img class="aviso-produto-esgotado" ng-src="/assets/img/fancybox/<EMAIL>" ng-show="loadingAviso" />
                            </div>
                            <div class="validation-summary-errors" ng-if="erroRegistroAviso" ng-bind="::erroRegistroAviso"></div>
                            <div class="validation-summary-success" ng-if="registroAviso" ng-bind="::registroAviso"></div>
                        </fieldset>
                    </div>

                    @if(product.Rankings != null && product.Rankings.Count > 0)
                    {
                        <fieldset style="border-top:1px solid #f2f2f2;">
                            <div style="float:left">
                            @foreach(var rank in product.Rankings) {
                                @if(!string.IsNullOrEmpty(rank.Icon)) {
                                <img class="img-ranking" src="@(rank.Icon)/75/75" title="@rank.Name" />
                                }
                            }
                            </div>
                        </fieldset>
                    }
                    <fieldset>
                    @if(Model.Product.EnableMatchingByEan && !String.IsNullOrEmpty(product.SkuEan))
                    {
                        <div class="fromMarketplace" ng-if="marketplaceFound">
                            <h1>Outras opções para @Model.ViewStrings.BuyActionTypeDescriptionLowercase</h1>
                            <div class="div-loading" loader-container is-loading="loadingMarketplace" img-style="height:20em;">
                                <table>
                                    <tbody>
                                        <tr ng-repeat="mark in marketplace">
                                            <td><p>Vendido e entregue por <strong><span ng-bind="::mark.Store"></span></strong></p></td>
                                            <td>
                                                <p class="price">
                                                    @product.Prices.CoinName.Prefix <strong><span ng-bind="::((mark.ShowPriceWithPurchaseCost ? mark.AvailableBalance : mark.Price) | currency:'')"></span></strong> @product.Prices.CoinName.Sufix
                                                    <span ng-if="mark.ShowPriceWithPurchaseCost"><br/>+ <span class="points-purchase inline">R$ <span ng-bind="::(mark.PointsNeededPurchaseCost | currency:'')">0,00</span></span></span>
                                                </p>
                                            </td>
                                            <td><a href="/produto/detalhes/{{::mark.Url}}" class="button button-outline-primary">@Model.ViewStrings.BuyActionDescription</a></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    }
                    </fieldset>
                </form>
                } else {
                    <form>
                        <fieldset>
                            <input type="hidden" id="p" name="p" value="@product.Id" />
                            <input type="hidden" id="md" name="md" value="@product.SkuCode" />
                            <div class="price">
                            <p><strong>Produto esgotado</strong></p>
                            </div>
                            <div class="alert-stock" ng-if="!erroRegistroAviso && !registroAviso">
                                <h2>Deseja ser avisado quando o produto ficar disponível?</h2>
                                <p>Você será avisado através do seu e-mail cadastrado<br><strong>@ViewBag.email</strong></p>
                                <button type="submit" class="button" ng-click="registraInteresse()" ng-hide="loadingAviso">Avisar</button>
                                <img class="aviso-produto-esgotado" ng-src="/assets/img/fancybox/<EMAIL>" ng-show="loadingAviso" />
                            </div>
                            <div class="validation-summary-errors" ng-if="erroRegistroAviso" ng-bind="::erroRegistroAviso"></div>
                            <div class="validation-summary-success" ng-if="registroAviso" ng-bind="::registroAviso"></div>
                        </fieldset>
                    </form>
                }
            </div>
        </article>

        <article class="product-description">
            <div class="tabs">
                <ul class="tabList">
                    <li><a href="#" data-tab="#tab-1">@product.ProductInformationTabTitle</a></li>
                    @if(product.HasCharacteristicsOrTechnicalSpecs()) {
                        <li><a href="#" data-tab="#tab-2">@product.ProductTechnicalSpecificationsTabTitle</a></li>
                    }
                </ul>
                <div id="tab-1" class="tabContent">
                    @if(!String.IsNullOrEmpty(product.Description)) {
                        <div>@Html.Raw(product.Description)</div>
                    }
                    @if(!String.IsNullOrEmpty(product.Information)) {
                        <div>@Html.Raw(product.Information)</div>
                    }
                </div>
                @if(product.HasCharacteristicsOrTechnicalSpecs()) {
                <div id="tab-2" class="tabContent">
                    <table>
                        @if(product.Characteristics != null) {
                            foreach(var charact in product.Characteristics) {
                                <tr>
                                    <th>@charact.Key</th>
                                    <td>@Html.Raw(charact.Value)</td>
                                </tr>
                            }
                        }
                        @if(product.TechnicalSpecifications != null) {
                            @foreach(var spec in product.TechnicalSpecifications) {
                                <tr>
                                    <th>@spec.Key</th>
                                    <td>@Html.Raw(spec.Value)</td>
                                </tr>
                            }
                        }
                    </table>
                </div>
                }
            </div>
        </article>
            <article class="list-products similares" ng-hide="!hasProduct">
                <header>
                    <h1>Quem viu esse produto, também viu...</h1>
                </header>
                <div class="slider" loader-container is-loading="loadingSimilares" img-style="height:20em;">
                    <ul class="slides products similares">
                        <product-showcase ng-repeat="prod in produtosSimilares" product="prod"></product-showcase>
                    </ul>
                </div>
            </article>
    </div>
</section>
@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript"
    asp-append-version="true"></script>
    <script type="text/javascript" src="@Url.Content("~/js/app/directives/loader-container.js")" asp-append-version="true"></script>
    <script type="text/javascript" src="@Url.Content("~/js/product/product.js")" asp-append-version="true"></script>
}
