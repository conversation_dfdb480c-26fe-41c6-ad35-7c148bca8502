﻿@{
    ViewBag.Title = "FAQ";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"><a href="/Home">Home</a></li>
            <li>Dúvidas Frequentes</li>
        </ul>
    </div>
</div>
<section id="content" ng-controller="faqController" ng-init="init()">
    <div class="container" loader-container is-loading="loadingFaq">
        <article class="faq-search">
            <header>
                <h1>Verifique se já respondemos sua dúvida anteriormente...</h1>
            </header>
            <form>
                <p>Pesquise sua dúvida através de palavras-chaves e veja se já temos uma resposta em Dúvidas Frequentes</p>
                <fieldset>
                    <input type="text" ng-model="faq.term" />
                    <button type="submit" ng-click="findFaqByTerm()">Ok</button>
                </fieldset>
            </form>
        </article>
        <div class="search-results">
            <h1>...ou procure entre as categorias</h1>
            <article class="faq-result">
                <ol>
                    <li ng-repeat="item in faqs">
                        <a href="#">{{item.Title}}</a>
                        <div class="answer" ng-bind-html="item.Answer"></div>
                    </li>
                </ol>
            </article>
        </div>
        <article class="no-answer">
            <header>
                <h1>Não encontrou uma resposta ou continua com dúvida?</h1>
            </header>
            <p>Entre em <a href="/FaleConosco/Formulario">contato conosco</a>, enviando sua pergunta.</p>
        </article>
    </div>
</section>

@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/faq/faqApp.js")"></script>
}
