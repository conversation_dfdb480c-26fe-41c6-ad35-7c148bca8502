@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@inject IHelperWeb _helper;

@model Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations.CampaignSettingsModel

@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Pague Contas";
    ViewBag.ClassBody = "pague-contas";

    var httpContext = RequestContextManager.Instance.CurrentContext;
    var itemCoinPrefix = httpContext.Items["coinPrefix"];
    var itemCoinSufix = httpContext.Items["coinSufix"];
    var coinPrefix = itemCoinPrefix != null ? itemCoinPrefix.ToString() : "";
    var coinSufix = itemCoinSufix != null ? itemCoinSufix.ToString() : "";

    var participant = _helper.GetParticipantSession();
}
@section StylesCustomizados {
    <style>
        .cart-receipt pre { font-family: Courier, monospace; }
        .receipt-confirm p { background: #fff !important; border: solid 1px #ccc; }
        .sidebar { margin-top: 0px !important; width: auto !important; }
        .sidebar a.back { font-size: 0.85em !important; line-height: 1em !important; }
    </style>
    <link href="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.css")" rel="stylesheet">
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"><a href="/">Home</a></li>
            <li>Pague Contas</li>
        </ul>
    </div>
</div>
<div id="mediabox">
    <article class="mediabox">
        <img data-srcset-base="/assets/img/mediabox/" data-srcset-ext=".jpg" data-srcset="201712-pagueContas-wd 1920w 3x, 201712-pagueContas-wd 1680w 3x, 201712-pagueContas-d 1280w 3x, 201712-pagueContas-s 599w 3x">
    </article>
</div>
<div id="pagueContas" class="custom-banner"></div>
<section id="content">
    <div class="container" style="visibility:hidden">
        <article class="payBills" ng-controller="billCtrl"
            ng-init="onInit(@(Model.Parametrizations.AllowBillPaymentSchedulement ? "true" : "false"), '@Model.Parametrizations.BillPaymentMinimumDueDays')">
            <div loader-container is-loading="loading">
                <form id="frmBill" name="frmBill" novalidate ng-if="showForm">
                    <fieldset>
                    <div>
                        <p>Digite a linha digitável e confirme os dados antes do pagamento.</p>

                        @if(Model.Parametrizations.AllowBillPaymentSchedulement || Model.Parametrizations.ShowBillPaymentMessage) {
                        <div class="info">
                            @if(Model.Parametrizations.AllowBillPaymentSchedulement) {
                                @if(!Model.Parametrizations.ShowBillPaymentMessage){
                                    @if(Model.Parametrizations.UseBillPaymentMinimumDueDays) {
                                        @*<p>Só é permitido até @Model.Parametrizations.BillPaymentMinimumDueDays dias antes do vencimento.</p>*@
                                        <p>Obrigatóriamente o agendamento realizado em até  @Model.Parametrizations.BillPaymentMinimumDueDays  dias úteis antes da data de vencimento, caso contrario o boleto não poderá ser pago.</p>
                                    } else {
                                        <p>Deve ser selecionado uma data até o dia do vencimento.</p>
                                    }
                                    @if(Model.Parametrizations.BillPaymentMinimumHour.HasValue && Model.Parametrizations.BillPaymentMaximumHour.HasValue) {
                                        <p>Horário limite para agendamento @(Model.Parametrizations.BillPaymentMinimumHour)h às @(Model.Parametrizations.BillPaymentMaximumHour)h.</p>
                                    }
                                }
                            }
                            @if(Model.Parametrizations.ShowBillPaymentMessage) {
                                <p>@Model.Parametrizations.BillPaymentMessage</p>
                            }
                        </div>
                        }
                    </div>

                    <div class="field">
                        <label for="barcode">Código de barras<span class="required">*</span></label>
                        <input id="barcode" name="barcode" type="text" ng-model="bill.BarCode" ng-required maxlength="55">
                    </div>
                    <div class="field top-p29">
                        <button type="submit" class="button pull-left" ng-click="onBarCodeBlur()">Pesquisar</button>
                    </div>
                    <div class="field">
                        <label for="billCo">Emissor do boleto</label>
                        <input id="billCo" name="billCo" type="text" ng-model="bill.Assignor" readonly>
                    </div>
                    <div class="field">
                        <label for="billPrice">Valor da conta</label>
                        <input id="billPrice" name="billPrice" type="text" value="{{ bill.BillingAmount | currency:'R$ ' }}" readonly>
                    </div>
                    <div class="field">
                        <label for="pointsPrice">Valor total</label>
                        <input id="pointsPrice" name="pointsPrice" type="text" value="{{ bill.ParticipantCostPoints | currency:'' }}" readonly>
                    </div>
                    <div class="field">
                        <label for="data-vencimento">Data de vencimento</label>
                        <input id="data-vencimento" name="data-vencimento" type="text" maxlength="10" placeholder="DD/MM/AAAA"
                            moment-picker="bill.DueDate" start-date="bill.DueDate" ng-model="bill.DueDate"
                            ng-model-options="{updateOn:'blur'}" min-date="bill.minimumDueDate" locale="pt-br" autocomplete="off" change="calculateSchedulingDateLimits()"
                            format="DD/MM/YYYY" start-view="day" keyboard="true" ng-disabled="!canChangeDueDate" ng-required="canChangeDueDate">
                    </div>
                    </fieldset>

                    @if(Model.Parametrizations.AllowBillPaymentSchedulement) {
                    <fieldset ng-if="foundBill">
                        <div class="field" style="float:left">
                            <div>
                                <p>Selecione a data para pagamento da conta.</p>
                            </div>

                            <label for="data-pagto">Data para pagamento</label>
                            <input id="data-pagto" name="data-pagto" type="text" maxlength="10" placeholder="DD/MM/AAAA"
                                moment-picker="bill.ScheduledPaymentDate" ng-model="bill.ScheduledPaymentDate"
                                min-date="bill.MinimumScheduledPaymentDate" max-date="bill.MaximumScheduledPaymentDate"
                                ng-model-options="{updateOn:'blur'}" selectable="isSelectable(date, type)" locale="en" autocomplete="off"
                                format="DD/MM/YYYY" start-view="day" keyboard="true" validator="required">
                        </div>
                    </fieldset>
                    }

                    @if(Model.Parametrizations.AllowBillPaymentSchedulement) {
                        <button type="submit" class="button bottom-m1" ng-if="foundBill" ng-click="schedulePayment(frmBill)">Avançar</button>
                    } else {
                        <button type="submit" class="button bottom-m1" ng-if="foundBill" ng-click="startPayment(frmBill)">Avançar</button>
                    }
                </form>

                <div id="div-confirm" style="visibility:hidden" ng-show="showConfirmation">
                    <header>
                        <h1>Verifique os dados da conta e efetue a confirmação.</h1>
                    </header>
                    <div class="cart-products">
                        <table style="display:table">
                            <thead>
                                <tr>
                                    <th>Produto</th>
                                    <th>Qtde</th>
                                    <th>Valor</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td data-title="Produto">
                                    <div class="product">
                                        <h2>Pague Contas</h2>
                                        <p>Estabelecimento: {{bill.Assignor}}</p>
                                        <p class="shippingDate" ng-show="bill.DueDate">Data de vencimento: <strong>{{ formattedDueDate }}</strong></p>
                                        <p class="shippingDate" ng-show="bill.ScheduledPaymentDate">Data de pagamento agendada: <strong>{{ formattedScheduledPaymentDate }}</strong></p>
                                    </div>
                                    </td>
                                    <td data-title="Qtde">1</td>
                                    <td data-title="Valor Unit.">R$ {{ bill.BillingAmount | currency:'' }}</td>
                                    <td data-title="Total">
                                        <p class="price"><strong>{{ bill.ParticipantCostPoints | currency:'' }}</strong></p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    @if(!Model.Parametrizations.AllowBillPaymentSchedulement) {
                        <div class="extra-service-actions">
                            <button type="submit" class="button bottom-m1" ng-click="confirmPayment()">Confirmar Pagamento</button>
                            <aside class="sidebar" ng-show="ticket.Protocol">
                                <a href="javascript:" class="button back" ng-click="cancelPayment()">Cancelar Pagamento</a>
                            </aside>
                        </div>
                    }
                    <div class="cart-total">
                        <h2>Total do pedido</h2>
                        <table style="display:table">
                            <tbody>
                            <tr>
                                <td>Valor</td>
                                <td>@coinPrefix {{ bill.ParticipantCostPoints | currency:'' }} @coinSufix</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    @if(!Model.Parametrizations.AllowBillPaymentSchedulement) {
                        <div class="cart-receipt receipt-confirm" ng-show="ticket.ProofPayment">
                            <h2>Dados do pagamento</h2>
                            <pre>
                                <p ng-bind="ticket.ProofPayment"></p>
                            </pre>
                        </div>
                    }

                    <div class="extra-service-actions">
                        @if(Model.Parametrizations.AllowBillPaymentSchedulement) {
                            <button type="submit" class="button bottom-m1" ng-click="confirmPaymentScheduling()">Confirmar Agendamento</button>
                        } else {
                            <button type="submit" class="button bottom-m1" ng-click="confirmPayment()">Confirmar Pagamento</button>
                        }
                        <aside class="sidebar" ng-show="ticket.Protocol || schedulingPayment">
                            <a href="javascript:" class="button back" ng-click="cancelPayment()">Cancelar {{ schedulingPayment ? 'Agendamento' : 'Pagamento' }}</a>
                        </aside>
                    </div>
                </div>

                <div id="div-result" style="visibility:hidden" ng-show="showResult">
                    <header>
                        @if(Model.Parametrizations.AllowBillPaymentSchedulement) {
                            <h1>Agendamento realizado para <strong>{{ formattedScheduledPaymentDate }}</strong>!</h1>
                        } else {
                            <h1>Pedido nº {{ticket.Protocol}} finalizado!</h1>
                        }
                    </header>
                    <div class="cart-products">
                        <table style="display:table">
                            <thead>
                                <tr>
                                    <th>Produto</th>
                                    <th>Qtde</th>
                                    <th>Valor</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td data-title="Produto">
                                    <div class="product">
                                        <h2>Pague Contas</h2>
                                        <p>Estabelecimento: {{bill.Assignor}}</p>
                                        <p class="shippingDate" ng-show="bill.DueDate">Data de vencimento: <strong>{{ formattedDueDate }}</strong></p>
                                        <p class="shippingDate" ng-show="bill.ScheduledPaymentDate">Data de pagamento agendada: <strong>{{ formattedScheduledPaymentDate }}</strong></p>
                                    </div>
                                    </td>
                                    <td data-title="Qtde">1</td>
                                    <td data-title="Valor Unit.">R$ {{ bill.BillingAmount | currency:'' }}</td>
                                    <td data-title="Total">
                                        <p class="price"><strong>{{ bill.ParticipantCostPoints | currency:'' }}</strong></p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="cart-total">
                        <h2>Total do pedido</h2>
                        <table style="display:table">
                            <tbody>
                                <tr>
                                    <td>Valor</td>
                                    <td>@coinPrefix {{ bill.ParticipantCostPoints | currency:'' }} @coinSufix</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    @if(!Model.Parametrizations.AllowBillPaymentSchedulement) {
                    <div class="cart-receipt">
                        <h2>Comprovante de pagamento</h2>
                        <pre>
                            <p ng-bind="ticket.ProofPayment"></p>
                        </pre>
                        <a href="#" class="button print" ng-click="printProof()">Imprimir comprovante</a>
                    </div>
                    }
                    <div class="email-alert">
                        @if(participant != null) {
                        <h2>Desabilite seu anti-spam para o e-mail @participant.Email</h2>
                        } else {
                        <h2>Desabilite seu anti-spam para o seu e-mail cadastrado</h2>
                        }
                        <p>Enviaremos um novo e-mail a cada evolução no andamento do seu pedido.</p>
                    </div>
                </div>
            </div>
        </article>
    </div>
</section>

@section ScriptsCustomizados {
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment-with-locales.min.js" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/moment/moment-timezone-with-data-2012-2022.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/extraservices/pague-contas.js")" type="text/javascript" asp-append-version="true"></script>
}