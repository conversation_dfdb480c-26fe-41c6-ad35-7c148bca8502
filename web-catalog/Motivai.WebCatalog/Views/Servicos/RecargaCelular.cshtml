@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@inject IHelperWeb _helper;
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Recarga de Celular";
    ViewBag.ClassBody = "recarga-de-celular";

    var httpContext = RequestContextManager.Instance.CurrentContext;
    var itemCoinPrefix = httpContext.Items["coinPrefix"];
    var itemCoinSufix = httpContext.Items["coinSufix"];
    var coinPrefix = itemCoinPrefix != null ? itemCoinPrefix.ToString() : "";
    var coinSufix = itemCoinSufix != null ? itemCoinSufix.ToString() : "";

    var participant = _helper.GetParticipantSession();
}
@section StylesCustomizados {
    <style>
        .chosen-container .chosen-results { max-height: 180px; }
        .cart-receipt pre { font-family: Courier, monospace; }
        .receipt-confirm p { background: #fff !important; border: solid 1px #ccc; }
        .sidebar { margin-top: 0px !important; width: auto !important; }
        .sidebar a.back { font-size: 0.85em !important; line-height: 1em !important; }
        .field.col-cellphone { width: 31% !important; margin-right: 0.5% !important; }
    </style>
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"><a href="/">Home</a></li>
            <li>Recarga de celular</li>
        </ul>
    </div>
</div>
<div id="recargaCelular" class="custom-banner"></div>
<div id="mediabox">
    <article class="mediabox">
        <img data-srcset-base="/assets/img/mediabox/" data-srcset-ext=".jpg" data-srcset="201712-recargaDeCelular-wd 1920w 3x, 201712-recargaDeCelular-wd 1680w 3x, 201712-recargaDeCelular-d 1280w 3x, 201712-recargaDeCelular-s 599w 3x">
    </article>
</div>
<section id="content">
    <div class="container" style="visibility:hidden">
        <article class="buyPrepaidCredits" ng-controller="phoneRechargeCtrl">
            <div loader-container is-loading="loading">
                <form id="frmRecharge" name="frmRecharge" novalidate ng-if="showForm" ng-init="initPage()">
                    <fieldset>
                        <p>Informe seu DDD e número de celular, então selecione operadora e o valor da recarga.</p>
                        <div class="field col-3">
                            <label for="ddd">DDD<span class="required">*</span></label>
                            <input id="ddd" name="ddd" type="text" placeholder="Ex.: 12" maxlength="2" required ng-model="recharge.cellphoneDdd">
                        </div>
                        <div class="field col-9 col-cellphone">
                            <label for="cellphoneNumber">Celular<span class="required">*</span></label>
                            <input class="fluid" id="cellphoneNumber" name="cellphoneNumber" type="tel" placeholder="Ex.: 9876-54321" maxlength="10" required
                                ng-model="recharge.cellphoneNumber" validator="required,phoneNumber" style="width:80%">
                            <button type="submit" class="button inline btn-fluid" ng-click="dddOnChange()" title="Carregar operadoras"><i class="fa fa-search"></i></button>
                        </div>
                        <div class="field">
                            <label for="operator">Operadora<span class="required">*</span></label>
                            <select id="operator" name="operator" placeholder="Escolha" data-placeholder="Escolha" required class="cellphoneCompany"
                                ng-model="recharge.operator" ng-change="operatorOnChange()" ng-required>
                                <option value="" selected disabled="disabled">Escolha a operadora</option>
                                <option ng-repeat="oper in operators | orderBy:'ProviderName'" value="{{::oper.ProviderId}}">{{::oper.ProviderName}}</option>
                            </select>
                        </div>
                        <div class="field">
                            <label for="rechargeValue">Valor da recarga<span class="required">*</span></label>
                            <select id="rechargeValue" name="rechargeValue" placeholder="Escolha" data-placeholder="Escolha" class="pointsWanted"
                                ng-model="recharge.rechargeValue" ng-change="valueOnChange()" ng-required>
                                <option value="" selected disabled="disabled">Escolha o valor</option>
                                <option ng-repeat="value in rechargeValues" value="{{::value.Token}}">{{::value.Description}}</option>
                            </select>
                        </div>
                        <div class="field">
                            <label for="pointsPrice">Valor total</label>
                            <input id="pointsPrice" name="pointsPrice" type="text" readonly value="{{recharge.RechargeCost | currency:''}}">
                        </div>
                    </fieldset>
                    <button type="submit" class="button bottom-m1" ng-click="submitRecharge(frmRecharge)" ng-if="showForm">Avançar</button>
                </form>

                <div id="div-result-confirm" style="visibility:hidden" ng-show="showConfirmation">
                    <header>
                        <h1>Verifique os dados da recarga e efetue a confirmação.</h1>
                    </header>
                    <div class="cart-products">
                        <table style="display:table">
                            <thead>
                            <tr>
                                <th>Produto</th>
                                <th>Qtde</th>
                                <th>Valor Recarga</th>
                                <th>Total</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td data-title="Produto">
                                <div class="product">
                                    <h2>Recarga de celular</h2>
                                    <p>Operadora: {{ticket.OperatorName}}</p>
                                    <p class="shippingDate">Telefone: <strong>({{recharge.cellphoneDdd}}) {{recharge.cellphoneNumber}}</strong></p>
                                </div>
                                </td>
                                <td data-title="Qtde">1</td>
                                <td data-title="Valor Unit.">R$ {{ ticket.RechargeValue | currency:'' }}</td>
                                <td data-title="Total">
                                <p class="price"><strong>{{ ticket.RechargeCost | currency:'' }}</strong></p>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="cart-total">
                        <h2>Total do pedido</h2>
                        <table style="display:table">
                            <tbody>
                            <tr>
                                <td>Valor</td>
                                <td>@coinPrefix {{ ticket.RechargeCost | currency:'' }} @coinSufix</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="extra-service-actions">
                        <button type="submit" class="button bottom-m1" ng-click="confirmRecharge()">Confirmar Recarga</button>
                        <aside class="sidebar pull-left" ng-show="ticket.Protocol">
                            <a href="javascript:" class="button back" ng-click="cancelRecharge()">Cancelar Recarga</a>
                        </aside>
                    </div>
                    <div class="cart-receipt receipt-confirm">
                        <h2>Dados para recarga</h2>
                        <pre>
                            <p ng-bind="ticket.ProofPayment"></p>
                        </pre>
                    </div>
                    <div class="extra-service-actions">
                        <button type="submit" class="button bottom-m1" ng-click="confirmRecharge()">Confirmar Recarga</button>
                        <aside class="sidebar" ng-show="ticket.Protocol">
                            <a href="javascript:" class="button back" ng-click="cancelRecharge()">Cancelar Recarga</a>
                        </aside>
                    </div>
                </div>
            </div>

            <div id="div-result" style="visibility:hidden" ng-show="showResult">
                <header>
                    <h1>Pedido nº {{ticket.Protocol}} finalizado!</h1>
                </header>
                <div class="cart-products">
                    <table style="display:table">
                        <thead>
                        <tr>
                            <th>Produto</th>
                            <th>Qtde</th>
                            <th>Valor Recarga</th>
                            <th>Total</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td data-title="Produto">
                            <div class="product">
                                <h2>Recarga de celular</h2>
                                <p>Operadora: {{ticket.OperatorName}}</p>
                                <p class="shippingDate">Telefone: <strong>({{recharge.cellphoneDdd}}) {{recharge.cellphoneNumber}}</strong></p>
                            </div>
                            </td>
                            <td data-title="Qtde">1</td>
                            <td data-title="Valor Unit.">R$ {{ ticket.RechargeValue | currency:'' }}</td>
                            <td data-title="Total">
                            <p class="price"><strong>{{ ticket.RechargeCost | currency:'' }}</strong></p>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="cart-total">
                    <h2>Total do pedido</h2>
                    <table style="display:table">
                        <tbody>
                        <tr>
                            <td>Valor</td>
                            <td>@coinPrefix {{ ticket.RechargeCost | currency:'' }} @coinSufix</td>
                        </tr>
                        </tbody>
                    </table>
                    </div>
                    <div class="cart-receipt">
                        <h2>Comprovante de pagamento</h2>
                        <pre>
                            <p ng-bind="ticket.ProofPayment"></p>
                        </pre>
                        <a href="#" class="button print" ng-click="printProof()">Imprimir comprovante</a>
                    </div>
                    <div class="email-alert">
                        @if(participant != null) {
                        <h2>Desabilite seu anti-spam para o e-mail @participant.Email</h2>
                        } else {
                        <h2>Desabilite seu anti-spam para o seu e-mail cadastrado</h2>
                        }
                        <p>Enviaremos um novo e-mail a cada evolução no andamento do seu pedido.</p>
                    </div>
                </div>
            </div>
        </article>
    </div>
</section>
@section ScriptsCustomizados {
    <script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.10.6/moment-with-locales.min.js"></script>
<script src="@Url.Content("~/js/lib/moment/moment-timezone-with-data-2012-2022.min.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/extraservices/recarga-celular.js")" type="text/javascript" asp-append-version="true"></script>
<script type="text/javascript">
    $(document).ready(function() {
        $('#celular-semDDD').mask('9999-9999?9');
    });
</script>
}