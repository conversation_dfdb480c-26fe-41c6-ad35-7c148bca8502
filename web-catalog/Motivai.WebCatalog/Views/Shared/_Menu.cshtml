﻿@using System.Net;
@using System.Linq;
@inject Motivai.WebCatalog.Helpers.IHelperWeb helper;
@{
  var httpContext = Motivai.WebCatalog.Helpers.RequestContextManager.Instance.CurrentContext;
  var catalogMenu = httpContext.Items["catalog-menu"] as Motivai.WebCatalog.Models.Catalog.Menu.CatalogMenu;
  var q = httpContext.Request.Query.Count > 0 ? WebUtility.UrlDecode(httpContext.Request.Query["q"]) : "";
}

@if (!catalogMenu.Hide)
{
  <nav id="primary-navigation" role="navigation" class="site-navigation">
    <div class="container">
      <div class="box-search">
        <div class="menu-toggle">Menu</div>
        <div class="menu-container">
          <ul id="menu" class="nav-menu">
            <li class="dropdown">
              <a href="#">Todos</a>
              <ul>
                @if (catalogMenu.Menu != null)
                {
                  foreach (var menu in catalogMenu.Menu)
                  {
                    if (menu.ShowDepartment)
                    {
                      <li><a href="@menu.Url"><span>@menu.Name</span></a></li>
                    }
                  }
                }
              </ul>
            </li>
            @if (catalogMenu.Menu != null)
            {
              var highlighteds = catalogMenu.Menu.Where(m => m.Highlighted).OrderBy(m => m.Position);
              foreach (var menu in highlighteds)
              {
                if (menu.Highlighted)
                {
                  <li>
                    <a href="@menu.Url">@menu.Name</a>
                    @if (menu.Submenus != null)
                    {
                      <ul>
                        @foreach (var item in menu.Submenus)
                        {
                          @if (!item.Hide)
                          {
                            <li><a href="@item.Url">@item.Name</a></li>
                          }
                        }
                        @if (menu.Submenus.Count > 10)
                        {
                          <li><a href="@menu.Url" class="more">Ver departamento</a></li>
                        }
                      </ul>
                    }
                  </li>
                }
              }
            }
          </ul>
        </div>
        <div class="search">
          <form id="headerSearchForm" method="get" enctype="application/x-www-form-urlencoded" action="@Url.Action("Index", "Busca")">
            <fieldset>
              <input type="text" id="headerQueryInput" name="q" maxlength="50" placeholder="Digite o que você procura" value="@q">
              <button id="headerSearchButton" type="submit">Ok</button>
            </fieldset>
          </form>
        </div>
      </div>
    </div>
  </nav>
}