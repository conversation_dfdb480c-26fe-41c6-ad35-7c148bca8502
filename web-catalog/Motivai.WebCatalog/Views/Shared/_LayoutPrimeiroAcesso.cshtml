﻿@using Motivai.WebCatalog.Helpers;
@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Models.Session;
@inject IHelperWeb _helper;
@model Motivai.WebCatalog.Models.Pages.FirstAccess.FirstAccessStepsViewModel;
@{
    var httpContext = RequestContextManager.Instance.CurrentContext;
    var itemCoinPrefix = httpContext.Items["coinPrefix"];
    var itemCoinSufix = httpContext.Items["coinSufix"];
    var coinPrefix = itemCoinPrefix != null ? itemCoinPrefix.ToString() : "";
    var coinSufix = itemCoinSufix != null ? itemCoinSufix.ToString() : "";
    var participant = _helper.GetParticipantSession();
}
<!DOCTYPE html>
<html lang="pt-br" prefix="og: http://ogp.me/ns#">

<head>
    @{ Html.RenderPartial("_Head"); }
    @RenderSection("StylesCustomizados", false)
</head>

<body class="primeiro-acesso">
    <div id="page">
        <header role="banner" class="site-header">
            @if (_helper.IsCallcenterSession())
            {
                <div class="info alert-stock header-info">
                    <span><strong>Teleresgate: Iniciado em @participant.SessionStart.ToString("dd/MM/yyyy HH:mm") por @_helper.GetCallcenterUsername()</strong></span>
                </div>
            }

            <div class="container">
                <h1 class="logo"><a href="/"></a></h1>
                <div class="cart-items"></div>

                <div class="profile">
                    <div class="photo">
                        <p>@participant.GetDisplayInitials()</p>
                    </div>
                    <div class="text">
                        <h2>
                            Olá, @participant.GetDisplayName()
                        </h2>
                        <p><span class="price" id="userBalanceSpan">@coinPrefix <span id="balanceAmountSpan">@(participant?.Balance.ToCurrency() ?? "0,00")</span> @coinSufix</span></p>
                    </div>
                    <div class="logout">
                        <a class="sign-out" href="@Url.Action("Logout", "MinhaConta")"><p>Sair</p></a>
                    </div>
                </div>
            </div>
        </header>

        <div class="steps">
            <div class="container">
                <ul>
                    @if (Model.IsRegulationStepActive)
                    {
                        <li class="@(Model.IsRegulationStep ? "active" : "")">
                            <strong>@Model.GetAndIncrement()</strong>Aceite de Regulamento</li>
                    }

                    <li class="@(Model.IsPrivacyPolicyStep ? "active" : "")">
                        <strong>@Model.GetAndIncrement()</strong>Política de Privacidade</li>

                    @if (Model.IsRegistrationDataStepActive)
                    {
                        <li class="@(Model.IsRegistrationDataStep ? "active" : "")">
                            <strong>@Model.GetAndIncrement()</strong>Seu Cadastro</li>
                    }

                    @if (Model.IsCardStepActive)
                    {
                        <li class="@(Model.IsCardStep ? "active" : "")"><strong>@Model.GetAndIncrement()</strong>Cartao</li>
                    }
                </ul>
            </div>
        </div>
        <section id="content">
            <div class="container">
                @RenderBody()
            </div>
        </section>
        <div class="footer-fix"></div>
    </div>
    <footer>
        <div class="container footer-container">
            <cache expires-sliding="@TimeSpan.FromDays(1)">
                <div class="copyright">
                    <h1 class="logo"></h1>
                    <p>@DateTime.UtcNow.Year. Todos os direitos reservados.</p>
                </div>
            </cache>
        </div>
    </footer>
</body>
@Html.RenderPartial("_Scripts")
@RenderSection("ScriptsCustomizados", false)

</html>
