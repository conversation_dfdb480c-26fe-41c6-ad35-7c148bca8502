﻿@{
    var httpContext = Motivai.WebCatalog.Helpers.RequestContextManager.Instance.CurrentContext;
    var themeUrl = "~/themes";
    var themeItem = httpContext.Items["themeUrl"];
    if (themeItem != null) {
      themeUrl = themeItem.ToString();
    }
    themeUrl = Url.Content(themeUrl);
}
<title>@ViewBag.Title</title>
<meta content="IE=Edge" http-equiv="X-UA-Compatible">
<meta charset="utf-8">
<meta content="width=device-width, minimum-scale=1.0, maximum-scale=1.0" name="viewport">
<meta content="marketing, promotion, incentive, loyalty, rewards" name="keywords">
<meta content="7 days" name="revisit-after">
<link rel="apple-touch-icon" sizes="57x57" href="@(themeUrl)/assets/favicon/apple-touch-icon-57x57.png">
<link rel="apple-touch-icon" sizes="60x60" href="@(themeUrl)/assets/favicon/apple-touch-icon-60x60.png">
<link rel="apple-touch-icon" sizes="72x72" href="@(themeUrl)/assets/favicon/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="76x76" href="@(themeUrl)/assets/favicon/apple-touch-icon-76x76.png">
<link rel="apple-touch-icon" sizes="114x114" href="@(themeUrl)/assets/favicon/apple-touch-icon-114x114.png">
<link rel="apple-touch-icon" sizes="120x120" href="@(themeUrl)/assets/favicon/apple-touch-icon-120x120.png">
<link rel="apple-touch-icon" sizes="144x144" href="@(themeUrl)/assets/favicon/apple-touch-icon-144x144.png">
<link rel="apple-touch-icon" sizes="152x152" href="@(themeUrl)/assets/favicon/apple-touch-icon-152x152.png">
<link rel="apple-touch-icon" sizes="180x180" href="@(themeUrl)/assets/favicon/apple-touch-icon-180x180.png">
<link rel="icon" type="image/png" href="@(themeUrl)/assets/favicon/favicon-32x32.png" sizes="32x32">
<link rel="icon" type="image/png" href="@(themeUrl)/assets/favicon/android-chrome-192x192.png" sizes="192x192">
<link rel="icon" type="image/png" href="@(themeUrl)/assets/favicon/favicon-96x96.png" sizes="96x96">
<link rel="icon" type="image/png" href="@(themeUrl)/assets/favicon/favicon-16x16.png" sizes="16x16">
<link rel="manifest" href="@(themeUrl)/assets/favicon/manifest.json">
<link rel="mask-icon" href="@(themeUrl)/assets/favicon/safari-pinned-tab.svg" color="#440099">
<meta name="msapplication-TileColor" content="#440099">
<meta name="msapplication-TileImage" content="@(themeUrl)/assets/favicon/mstile-144x144.png">
<meta name="theme-color" content="#440099">
<link href="@Url.Content("~/css/screen.min.css")" media="all" rel="stylesheet" asp-append-version="true">
<environment names="Staging,Production,Proxy">
<link href="@Url.Content("~/css/app.min.css")" media="all" rel="stylesheet" asp-append-version="true">
</environment>
<environment names="Development,Local">
<link href="@Url.Content("~/css/app.css")" media="all" rel="stylesheet">
</environment>
<link href="@(themeUrl)/css/theme.min.css" media="all" rel="stylesheet" asp-append-version="true">
<!--[if lte IE 9]>
<script>
  var $buoop = {c:2};
  function $buo_f(){
  var e = document.createElement("script");
  e.src = "//browser-update.org/update.min.js";
  document.body.appendChild(e);
  };
  try {document.addEventListener("DOMContentLoaded", $buo_f,false)}
  catch(e){window.attachEvent("onload", $buo_f)}
</script><![endif]-->
