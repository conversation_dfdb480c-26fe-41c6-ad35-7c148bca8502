﻿<!DOCTYPE html>
<html lang="pt-br" prefix="og: http://ogp.me/ns#">
<head>
    @{ Html.RenderPartial("_Head"); }
	@RenderSection("StylesCustomizados", false)
</head>
<body class="@ViewBag.ClassBody" ng-app="platformApp">
    <div id="page">
        @{ Html.RenderPartial("_Header"); }
        @RenderBody()
        <div class="footer-fix"></div>
    </div>
    <cache expires-sliding="@TimeSpan.FromMinutes(30)">
    @{ Html.RenderPartial("_Footer"); }
    </cache>
    @{ Html.RenderPartial("_CustomScripts"); }
</body>
<cache expires-sliding="@TimeSpan.FromMinutes(30)">
@{ Html.RenderPartial("_Scripts"); }
</cache>
@RenderSection("ScriptsCustomizados", false)
</html>
