@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@using Motivai.WebCatalog.Models.Session;
@inject IHelperWeb _helper;
@{
    var httpContext = RequestContextManager.Instance.CurrentContext;
    var itemCoinPrefix = httpContext.Items["coinPrefix"];
    var itemCoinSufix = httpContext.Items["coinSufix"];
    var coinPrefix = itemCoinPrefix != null ? itemCoinPrefix.ToString() : "";
    var coinSufix = itemCoinSufix != null ? itemCoinSufix.ToString() : "";
    var participant = _helper.GetParticipantSession();

	var step = 0;
}
<!DOCTYPE html>
<html lang="pt-br" prefix="og: http://ogp.me/ns#">
<head>
    @{ Html.RenderPartial("_Head"); }
	@RenderSection("StylesCustomizados", false)
	<style>
		.steps ul { text-align: center; }
	</style>
</head>
<body class="@ViewBag.ClassBody">
    <div id="page">
		<header role="banner" class="site-header">
			@if(_helper.IsCallcenterSession())
            {
			<div class="info alert-stock header-info">
				<span><strong>Teleresgate: Iniciado em @participant.SessionStart.ToString("dd/MM/yyyy HH:mm") por @_helper.GetCallcenterUsername()</strong></span>
			</div>
			}

			<div class="container">
				<h1 class="logo"><a href="/"></a></h1>
                <div class="cart-items"></div>

                <div class="profile">
                    <div class="photo">
                        <p>@participant.GetDisplayInitials()</p>
                    </div>
                    <div class="text">
                        <h2>
                            Olá, @participant.GetDisplayName()
                        </h2>
                        <p><span class="price" id="userBalanceSpan">@coinPrefix <span id="balanceAmountSpan">@(participant?.Balance.ToCurrency() ?? "0,00")</span> @coinSufix</span></p>
                    </div>
                    <div class="logout">
                        <a class="sign-out" href="@Url.Action("Logout", "MinhaConta")"><p>Sair</p></a>
                    </div>
                </div>
			</div>
		</header>

		<div class="steps">
			<div class="container">
				<ul>
					<li class="@(ViewBag.EtapaResgate == "cart" ? "active" : "")"><strong>@(++step)</strong>Confirmação</li>
					<li class="@(ViewBag.EtapaResgate == "resume" ? "active" : "")"><strong>@(++step)</strong>Finalização</li>
				</ul>
			</div>
		</div>
		<section id="content">
        	@RenderBody()
        </section>
        <div class="footer-fix"></div>
    </div>
    <footer>
		<div class="container footer-container">
			<cache expires-sliding="@TimeSpan.FromDays(1)">
				<div class="copyright">
					<h1 class="logo"></h1>
					<p>@DateTime.UtcNow.Year. Todos os direitos reservados.</p>
				</div>
			</cache>
		</div>
	</footer>
</body>
@{ Html.RenderPartial("_Scripts"); }
@* @{ Html.RenderPartial("_Scripts_Clearsale.cshtml"); } *@
@RenderSection("ScriptsCustomizados", false)
@if(_helper.IsCallcenterSession() && ViewBag.EtapaResgate == "resume") {
	_helper.ClearSession();
}
</html>
