@using System.Net;
@using Microsoft.AspNetCore.Mvc;
@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@using Motivai.WebCatalog.Models.Session;
@inject IHelperWeb _helper;
@{
    var httpContext = RequestContextManager.Instance.CurrentContext;
    var coinPrefix = httpContext.Items["coinPrefix"].ToString();
    var coinSufix = httpContext.Items["coinSufix"].ToString();
    var participant = _helper.GetParticipantSession();
    var cart = _helper.GetSessionCart();
}
<header role="banner" class="site-header">
    @if (_helper.IsCallcenterSession())
    {
        <div class="info alert-stock header-info">
            <span><strong>Teleresgate: Iniciado em @participant.GetTimezonedSessionStart() por @_helper.GetCallcenterUsername()</strong></span>
        </div>
    }

    <div class="container">
        <h1 class="logo"><a href="/"></a></h1>
        <div class="cart-items">
            <a href="/Carrinho/Index"><strong>@(cart?.GetTotalItems() ?? 0)</strong></a>
            @if (cart != null && !cart.IsEmpty())
            {
                <div class="over">
                    <div class="scroll">
                        <div class="scroll-container">
                            <ul>
                                @foreach (var item in cart.GetProducts())
                                {
                                    <li>
                                        <div class="photo">
                                            <a href="/Produto/Detalhes/@item.GetEncodedUrl()" title="@item.ProductName"><img src="@item.Image"></a>
                                        </div>
                                        <div class="product">
                                            <h2><a href="/Produto/Detalhes/@item.GetEncodedUrl()"
                                                    title="@item.ProductName">@item.ProductName</a></h2>
                                            <p>
                                                <span>SKU: @item.SkuCode</span>
                                                @if (!String.IsNullOrEmpty(item.SkuModel))
                                                {
                                                    <br />

                                                    <span>Modelo: @item.SkuModel</span>
                                                }
                                                @if (!String.IsNullOrEmpty(item.SkuColor))
                                                {
                                                    <br />

                                                    <span>Cor: @item.SkuColor</span>
                                                }
                                                @if (!String.IsNullOrEmpty(item.SkuSize))
                                                {
                                                    <br />

                                                    <span>Tamanho: @item.SkuSize</span>
                                                }
                                                @if (!String.IsNullOrEmpty(item.SkuVoltage))
                                                {
                                                    <br />

                                                    <span>Voltagem: @item.SkuVoltage</span>
                                                }
                                                <br><span>Quantidade: @item.Quantity</span>
                                            </p>
                                            @if (item.DynamicPrice)
                                            {
                                                <p class="price">@item.DynamicPriceDescription</p>
                                            }
                                            else
                                            {
                                                <p class="price">@coinPrefix @item.GetSubtotalPoints().ToCurrency() @coinSufix</p>
                                            }
                                        </div>
                                    </li>
                                }
                            </ul>
                        </div>
                    </div>
                    <a href="/Carrinho" class="button">Ver o Carrinho</a>
                </div>
            }
        </div>

        <a href="@Url.Action("Index", "MinhaConta")">
            <div class="profile">
                <div class="photo">
                    <p>@participant.GetDisplayInitials()
                </div>
                <div class="text">
                    <h2>
                        Olá, @participant.GetDisplayName()
                    </h2>
                    <p><span class="price" id="userBalanceSpan">@coinPrefix <span id="balanceAmountSpan">@(participant?.Balance.ToCurrency() ?? "0,00")</span> @coinSufix</span></p>
                </div>
                <div class="logout">
                    <a class="sign-out" href="@Url.Action("Logout", "MinhaConta")"><p>Sair</p></a>
                </div>
            </div>
        </a>
    </div>

    @{ Html.RenderPartial("_Menu"); }
</header>
