@using Microsoft.AspNetCore.Mvc;
@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@inject IHelperWeb _helper;
@{
    var httpContext = RequestContextManager.Instance.CurrentContext;
	var participantSession = _helper.GetParticipantSession();
    var clearSaleAppId = httpContext.Items["clearSaleAppId"]?.ToString();
	var trackingId = participantSession?.TrackingId;
}
<noscript>
    <img src="https://device.clearsale.com.br/p/fp.png?sid=@(trackingId)&app=@(clearSaleAppId)&ns=1" />
</noscript>
<script>
    (function (a, b, c, d, e, f, g) {
    a['CsdpObject'] = e; a[e] = a[e] || function () {
    (a[e].q = a[e].q || []).push(arguments)
    }, a[e].l = 1 * new Date(); f = b.createElement(c),
    g = b.getElementsByTagName(c)[0]; f.async = 1; f.src = d; g.parentNode.insertBefore(f, g)
    })(window, document, 'script', '//device.clearsale.com.br/p/fp.js', 'csdp');
    csdp('app', '@clearSaleAppId');
    csdp('sessionid', '@trackingId');

    addLoaderToButton('#btn-buy');
    waitForElement('#csdp_@trackingId', () => {
        removeLoaderFromButton('#btn-buy');
    });
</script>
<script>
    function checkUrl(url){
        let request = new XMLHttpRequest();
        request.open( "GET", url, true );
        request.send(null);
        request.onerror = (event) => {
            request.open("GET", "https://web.fpcs-monitor.com.br/p/fp.png?sid=@(trackingId)&app=@(clearSaleAppId)&bl=1", false);
            request.send(null);
        }
    }
    checkUrl("https://device.clearsale.com.br/p/fp.png");
</script>