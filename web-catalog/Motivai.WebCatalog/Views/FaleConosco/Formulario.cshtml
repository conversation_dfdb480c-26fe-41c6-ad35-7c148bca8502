﻿@model List<Motivai.WebCatalog.Models.Campaign.SubjectModel>
@{
    ViewBag.Title = "Contato";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"><a href="/">Home</a></li>
            <li>Fale conosco</li>
        </ul>
    </div>
</div>
<section id="content" ng-controller="faleConoscoController" ng-init="carregaDadosParticipante()">
    <div class="container" ng-hide="loading">
        <article class="contact">
            <header>
                <h1>Fale Conosco</h1>
            </header>
            <p>Preencha o formulário abaixo para entrar em contato conosco.</p>
            <form name="frmContact">
                <fieldset>
                    <div class="field">
                        <label for="nome">Nome<span class="required">*</span></label>
                        <input id="nome" name="nome" type="text" placeholder="Ex.: Nome Sobrenome" required ng-model="contato.Name">
                    </div>
                    <div class="field">
                        <label for="document">CPF/CNPJ<span class="required">*</span></label>
                        <input id="document" name="document" type="text" placeholder="Ex.: 12345678900019" maxlength="14"
                            ng-model="contato.Document" ui-mask="99999999999?999" required validator="required,cpfCnpj" />
                    </div>
                </fieldset>
                <fieldset>
                    <div class="field">
                        <label for="foneResidencial">Telefone<span class="required" ng-show="!contato.Cellphone">*</span></label>
                        <input id="foneResidencial" name="foneResidencial" type="tel" placeholder="Ex.: (12)3456-7890"
                            class="telefone" ng-model="contato.Phone" ng-required="!contato.Cellphone"
                            validator="optionalPhone">
                    </div>
                    <div class="field">
                        <label for="cellphone">Celular<span class="required" ng-show="!contato.Phone">*</span></label>
                        <input id="cellphone" name="cellphone" type="tel" placeholder="Ex.: (12)9456-78909"
                            class="celular" ng-model="contato.Cellphone" ng-required="!contato.Phone"
                            validator="optionalCellphone">
                    </div>
                    <div class="field">
                        <label for="Email">E-mail<span class="required">*</span></label>
                        <input id="Email" name="Email" type="email" placeholder="Ex.: <EMAIL>"
                            ng-model="contato.Email" validator="required,email">
                    </div>

                    <div class="field">
                        <label for="assunto">Assunto<span class="required">*</span></label>
                        <select id="assunto" name="assunto" placeholder="Escolha" data-placeholder="Escolha" class="autostart"
                            required ng-model="contato.Subject"
                            asp-items="@(new SelectList(Model, "Id", "Subject"))"
                        >
                            <option value="" selected="selected" disabled="disabled">Selecione um assunto</option>
                        </select>
                    </div>
                    <div class="field">
                        <label for="mensagem">Sua mensagem<span class="required">*</span></label>
                        <textarea id="mensagem" name="mensagem" placeholder="Sua mensagem" rows="1" required ng-model="contato.Message"></textarea>
                    </div>
                </fieldset>
                <button type="submit" class="button" ng-click="enviarMensagem(contato)">Enviar mensagem</button>
            </form>
        </article>
    </div>
    <div loader-container is-loading="loading" div-style=""></div>
</section>

@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/faleconosco/faleConoscoApp.js")" asp-append-version="true"></script>
}


