﻿@{
    ViewBag.Title = "Regulamento";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"> <a href="/">Home</a></li>
            <li>Regulamento</li>
        </ul>
    </div>
</div>
<section id="content">
    <div class="container" ng-controller="institutionalCtrl" ng-init="loadRegulament()">
        <article class="terms" loader-container is-loading="loadingRegulament">
            <h1>Regulamento</h1>
            <div ng-bind-html="regulament"></div>
        </article>
    </div>
</section>
@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/institutional/institutional-app.js")"></script>
}