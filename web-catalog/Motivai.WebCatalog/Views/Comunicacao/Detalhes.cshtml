@model Motivai.WebCatalog.Models.Catalog.MediaBoxModel
@{
    ViewBag.Title = Model.Name;
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"><a href="/">Home</a></li>
            <li class="active">@Model.Name</li>
        </ul>
    </div>
</div>
<section id="content">
    <div class="container">
        <article class="terms">
            <div>@Html.Raw(Model.Message)</div>
        </article>
    </div>
</section>

@section ScriptsCustomizados {
    <script>angular.module("platformApp", ['footerModule']);</script>
    <script>
        $(document).ready(function() {
            pageView('/Comunicacao/@Html.Raw(Model.Name)')
        });
    </script>
}
