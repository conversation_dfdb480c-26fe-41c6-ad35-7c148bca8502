@model Motivai.WebCatalog.Models.Pages.Cards.PrepaidCardPageModel

@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@inject IHelperWeb _helper;
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Cartão " + Model.CardTypeDescription;
    ViewBag.ClassBody = "primeiro-acesso";

    var httpContext = RequestContextManager.Instance.CurrentContext;
    var itemCoinPrefix = httpContext.Items["coinPrefix"];
    var itemCoinSufix = httpContext.Items["coinSufix"];
    var coinPrefix = itemCoinPrefix != null ? itemCoinPrefix.ToString() : "";
    var coinSufix = itemCoinSufix != null ? itemCoinSufix.ToString() : "";

    var participant = _helper.GetParticipantSession();
}
@section StylesCustomizados {
<style>
    .nivo-controlNav,
    .nivo-directionNav {
        display: none;
    }

    body.primeiro-acesso .site-header .profile {
        float: left;
    }

    .shipping-address {
        clear: both;
    }

    .shipping-address img {
        margin-left: 0px !important;
        height: 200px;
    }

    div.text.regulation {
        background: #f2f2f2;
        margin-bottom: 2em;
        padding: 1.5em;
        -moz-border-radius: .25em;
        -webkit-border-radius: .25em;
        border-radius: .25em;
    }

    fieldset {
        margin-bottom: 1em;
    }

    img.card-image {
        max-width: 90%;
        max-height: 80%;
        margin-top: 4.5em;
    }

    .instructions {
        margin-bottom: 2em;
        text-align: justify;
    }

    .instructions ol {
        padding-left: 1em;
    }

    .instructions li {
        list-style: initial;
    }

    .participant-cards label {
        padding-top: 0.5em;
        margin-bottom: 1em;
        font-size: 1em;
    }

    .order-info .box div {
        float: left !important;
    }

    .fees-details tr td {
        width: 20% !important;
    }

    .balance {
        float: left;
    }

    .tabs,
    .container {
        margin-left: 15px;
        margin-right: 15px;
    }

    .inputContainer {
        width: 300px;
        border: 1px;
    }

    .inputContainer span {
        float: left;
        margin-right: 5px;
    }

    .inputContainer div {
        overflow: hidden;
    }

    .inputContainer input {
        width: 100%;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        display: block;
        height: 1.6em;
        box-shadow: none;
        background: #f2f2f200;
        padding: 0em;
        border: 0px solid #ccc;
    }
</style>
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"><a href="/">Home</a></li>
            <li>Cartão</li>
            <li>@Model.CardTypeDescription</li>
        </ul>
    </div>
</div>
<div id="mediabox">
    <article class="mediabox">
        <img data-srcset-base="/assets/img/mediabox/" data-srcset-ext=".jpg"
            data-srcset="cartao_banner_wd 1920w 3x, cartao_banner_wd 1680w 3x, cartao_banner 1280w 3x, cartao_banner_s 599w 3x">
    </article>
</div>

<section id="content" ng-controller="cardsCtrl">
    <div class="container">
        <div class="tabs" ng-class="{ 'tabs': canShowCardSettings() }">
            <ul class="tabList" ng-if="canShowCardSettings()">
                <li><a href="#" data-tab="#tab-1">Emitir Pedido (Novo Cartão, 2ª Via ou Crédito)</a></li>
                <li><a href="#" data-tab="#tab-2">Desbloquear Cartão</a></li>
                <li><a href="#" data-tab="#tab-3"  ng-if="!disableCardDetailsAccess()">Detalhes do Cartão</a></li>
            </ul>
            <div id="tab-1" class="tabContent" ng-class="{ 'tabContent': canShowCardSettings() }">
                <section ng-init="loadPage('@Model.CardType.ToString().ToLower()')">
                    <div ng-if="!occurredError">
                        <div loader-container is-loading="loading">
                            <form name="cardForm" ng-if="!finalStep">
                                <article class="buyPrepaidCredits bottom-m1">
                                    <div class="row">
                                        <div class="col-12 instructions">
                                            <h2>Instruções para o preenchimento</h2>
                                            <ol>
                                                <li>Selecione o cartão em que deseja efetuar o crédito dos
                                                    @Model.ViewStrings.BalanceDescriptionLowerCase. Caso
                                                    não tenha um cartão selecione "Emitir Novo Cartão", caso o
                                                    cartão desejado esteja perdido basta selecioná-lo e marcar a opção
                                                    "Solicitar 2ª Via do Cartão" ao lado dele.</li>
                                                <li>Digite a quantidade de
                                                    @Model.ViewStrings.BalanceDescriptionLowerCase que deseja transferir
                                                    para o cartão.
                                                    Após a digitação, serão calculados a taxa de transferência e o
                                                    valor em reais que será creditado no cartão selecionado.</li>
                                                @if (Model != null && !Model.IsDisableShippingAddress)
                                                {
                                                    <li>Caso tenha selecionado "Emitir Novo Cartão" ou tenha marcado a opção
                                                        "Solicitar 2ª Via do Cartão" será solicitado o endereço de
                                                        entrega do cartão. Você poderá selecionar um endereço já cadastrado
                                                        na sua conta ou poderá preencher um novo endereço. Dica: preencha o
                                                        CEP
                                                        para que os outros campos sejam preenchidos automaticamente, caso o
                                                        endereço não seja encontrado pelo CEP informado então será preciso
                                                        que preencha todos os campos manualmente.</li>
                                                }
                                            </ol>
                                        </div>
                                    </div>

                                    <div>
                                        <div class="col-6">
                                            <div class="field col-12">
                                                <img class="card-image" src="/assets/img/img-loader.gif"
                                                    blazy-src="{{ settings.cardDetailedImage + '/600' }}" />
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <fieldset>
                                                <h2>Cartão para crédito</h2>
                                                <div>
                                                    <div class="field col-12">
                                                        <label>Selecione o cartão para crédito<span
                                                                class="required">*</span></label>
                                                        <div class="field participant-cards">
                                                            <input type="radio" id="new-card" name="selectedCard"
                                                                value="new-card" ng-model="transaction.selectedCard"
                                                                ng-change="onCardSelect()" />
                                                            <label for="new-card">Emitir Novo Cartão</label>
                                                            <div ng-repeat="card in issuedCards">
                                                                <input type="radio" id="{{::card.Id}}"
                                                                    name="selectedCard" value="{{::card.Id}}"
                                                                    ng-model="transaction.selectedCard"
                                                                    ng-change="onCardSelect()" />
                                                                <label for="{{::card.Id}}">{{::card.Number}}</label>
                                                            </div>
                                                            <div
                                                                ng-hide="!issuedCards || !issuedCards.length || transaction.selectedCard=='new-card'">
                                                                <input id="reissueCard" name="reissueCard"
                                                                    type="checkbox" value="true"
                                                                    ng-click="onReissueCardClick()"
                                                                    ng-model="transaction.reissueCard">
                                                                <label for="reissueCard">Emitir 2ª Via</label><br>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </fieldset>
                                            <fieldset>
                                                <h2>@Model.ViewStrings.BalanceDescription para transferência</h2>
                                                <div class="row">
                                                    <div class="field col-12">
                                                        <label>Quantidade de
                                                            @Model.ViewStrings.BalanceDescriptionLowerCase para
                                                            transferência<span class="required">*</span></label>
                                                        <input id="pointsToTransfer" name="pointsToTransfer" type="text"
                                                            maxlength="9" ng-model="transaction.pointsToTransfer"
                                                            ng-blur="calculateFees()" ui-number-mask
                                                            validator="required">
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="field col-12" ng-if="settings.transactionLimit > 0">
                                                        <label>Limite de transferência</label>
                                                        <div class="top-p85">
                                                            <span>{{ settings.transactionLimit }}% de @coinPrefix <span
                                                                    id="balanceAmountSpan">@(participant != null ?
                                                                    participant.Balance.ToCurrency() : "0,00")</span>
                                                                @coinSufix</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="field col-6">
                                                        <label>Total do custo de emissão do cartão (R$)</label>
                                                        <input id="totalFeesAmount" name="totalFeesAmount" type="text"
                                                            maxlength="9" ng-model="transaction.totalFeesAmount"
                                                            ui-number-mask readonly>
                                                    </div>
                                                    <div class="field col-6">
                                                        <label>Valor que será creditado no cartão (R$)</label>
                                                        <input id="creditAmount" name="creditAmount" type="text"
                                                            maxlength="9" ng-model="transaction.creditAmount"
                                                            ui-number-mask readonly>
                                                    </div>
                                                </div>
                                                <div class="row"
                                                    ng-if="showFeesDetails && transaction.detailedFees.isAutoConfiguredCreditAmount">
                                                    <span>Obs.: O valor total de
                                                        @Model.ViewStrings.BalanceDescriptionLowerCase foi atualizado
                                                        com o valor
                                                        máximo permitido após desconto das taxas do pedido.</span>
                                                </div>
                                            </fieldset>
                                        </div>
                                    </div>

                                    <div class="cart-products fees-details" ng-show="showFeesDetails">
                                        <h2>Detalhamento das taxas de emissão/recarga</h2>
                                        <table style="display:table">
                                            <thead>
                                                <tr>
                                                    <th>Custo do Plástico</th>
                                                    <th>Custo do Frete</th>
                                                    <th>Taxa de Carga</th>
                                                    <th>Taxa de Serviço</th>
                                                    <th>Custo Total em @Model.ViewStrings.BalanceDescription</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>R$ {{ transaction.detailedFees.plasticCost | number:'2' }}</td>
                                                    <td>R$ {{ transaction.detailedFees.shippingCost | number:'2' }}</td>
                                                    <td>R$ {{ transaction.detailedFees.chargeCost | number:'2' }}</td>
                                                    <td>R$ {{ transaction.detailedFees.serviceFee | number:'2' }}</td>
                                                    <td>@coinPrefix {{ transaction.orderTotalCost.points | number:'2' }}
                                                        @coinSufix</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    @if (Model != null && !Model.IsDisableShippingAddress)
                                    {
                                        <div class="shipping-address"
                                        ng-if="(transaction.issueNewCard || transaction.reissueCard)">
                                            <h2>Endereço de entrega do cartão</h2>
                                            <div class="row" loader-container is-loading="loadingAddresses">
                                                <div class="field col-6">
                                                    <label>Endereços cadastrados</label>
                                                    <select id="selectedShippingAddress" name="selectedShippingAddress"
                                                    placeholder="Selecione o endereço de entrega do cartão"
                                                    ng-model="transaction.selectedShippingAddress"
                                                    ng-change="onShippingAddressSelect()">
                                                        <option value="" selected disabled="disabled">Escolha o endereço
                                                        </option>
                                                        <option ng-repeat="end in shippingAddresses track by end.Id"
                                                        value="{{::end.Id}}">{{::end.AddressName}} ({{::end.Cep}})
                                                        </option>
                                                        @(Model != null && Model.AllowChangeShippingAddress) {
                                                        <option value="other" data-href="/Carrinho/NovoEndereco">Outro
                                                            endereço</option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>

                                            <fieldset class="address">
                                                <div class="row">
                                                    <div class="field col-3">
                                                        <label for="cep">CEP<span class="required">*</span></label>
                                                        <input id="cep" name="cep" type="text" placeholder="Ex.: 01234-567"
                                                        required
                                                        ng-blur="searchAddressByCep(transaction.shippingAddress)"
                                                        ng-model="transaction.shippingAddress.Cep" ng-minlength="9"
                                                        ui-br-cep-mask validator="required,cep">
                                                        <span
                                                        ng-class="{'CustomValidationError validationFieldRequired': transaction.shippingAddress.cepSearchError}"
                                                        style="display:inline;"
                                                        ng-bind="transaction.shippingAddress.cepSearchMessage"></span>
                                                    </div>
                                                    <div class="field col-9">
                                                        <label for="street">Endereço<span class="required">*</span></label>
                                                        <input id="street" name="street" type="text"
                                                        placeholder="Ex.: Av. Paulista"
                                                        ng-disabled="!transaction.shippingAddress.FilledManually"
                                                        ng-model="transaction.shippingAddress.Street"
                                                        validator="required">
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="field col-3">
                                                        <label for="number">Número<span class="required">*</span></label>
                                                        <input id="number" name="number" type="text"
                                                        placeholder="Ex.: 12.345" required
                                                        ng-model="transaction.shippingAddress.Number"
                                                        validator="required">
                                                    </div>
                                                    <div class="field col-9">
                                                        <label for="complement">Complemento</label>
                                                        <input id="complement" name="complement"
                                                        placeholder="Ex.: Apto. 12 Bloco A" type="text"
                                                        ng-model="transaction.shippingAddress.Complement">
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="field col-6">
                                                        <label for="neighborhood">Bairro<span
                                                            class="required">*</span></label>
                                                        <input id="neighborhood" name="neighborhood" type="text"
                                                        placeholder="Ex.: Bela Vista" required
                                                        ng-disabled="!transaction.shippingAddress.FilledManually"
                                                        ng-model="transaction.shippingAddress.Neighborhood"
                                                        validator="required">
                                                    </div>
                                                    <div class="field col-6">
                                                        <label for="state">Estado<span class="required">*</span></label>
                                                        <select-state class="platform-chosen" select-id="state"
                                                        state="transaction.shippingAddress.State"
                                                        on-update="transaction.shippingAddress.State=$value"
                                                        required="true"
                                                        disabled="!transaction.shippingAddress.FilledManually">
                                                        </select-state>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="field col-6">
                                                        <label for="city">Cidade<span class="required">*</span></label>
                                                        <input id="city" name="city" type="text"
                                                        placeholder="Ex.: São Paulo" required
                                                        ng-disabled="!transaction.shippingAddress.FilledManually"
                                                        ng-model="transaction.shippingAddress.City"
                                                        validator="required">
                                                    </div>
                                                    <div class="field col-6">
                                                        <label for="reference">Ponto de referência</label>
                                                        <input id="reference" name="reference" type="text"
                                                        placeholder="Ex.: Condomínio Edifício Paulista"
                                                        ng-model="transaction.shippingAddress.Reference">
                                                    </div>
                                                </div>
                                            </fieldset>
                                            <fieldset class="recipient">
                                                <h2>Dados de quem receberá o cartão neste endereço</h2>
                                                <div class="row">
                                                    <div class="field col-6">
                                                        <label for="receiverCpf">CPF<span class="required">*</span></label>
                                                        <input id="receiverCpf" name="receiverCpf" type="text"
                                                        placeholder="123.456.789-01" required
                                                        ng-model="transaction.shippingAddress.Receiver.Cpf"
                                                        ui-br-cpf-mask validator="required">
                                                    </div>
                                                    <div class="field col-6">
                                                        <label for="receiverName">Nome<span
                                                            class="required">*</span></label>
                                                        <input id="receiverName" name="receiverName" type="text"
                                                        placeholder="Ex.: Nome Sobrenome" required
                                                        ng-model="transaction.shippingAddress.Receiver.Name"
                                                        validator="required">
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="field col-6">
                                                        <label for="receiverTellphone">Telefone residencial</label>
                                                        <input id="receiverTellphone" name="receiverTellphone" type="text"
                                                        placeholder="(12) 3456-7890" maxlength="14"
                                                        ng-model="transaction.shippingAddress.Receiver.Telephone"
                                                        ui-br-phone-number validator="nonrequired,phone">
                                                    </div>
                                                    <div class="field col-6">
                                                        <label for="receiverCellphone">Celular<span
                                                            class="required">*</span></label>
                                                        <input id="receiverCellphone" name="receiverCellphone" type="text"
                                                        placeholder="(12) 98765-4321" maxlength="15"
                                                        ng-model="transaction.shippingAddress.Receiver.Cellphone"
                                                        ui-br-phone-number validator="required,phone">
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="field col-6">
                                                        <label for="receiverEmail">E-mail<span
                                                            class="required">*</span></label>
                                                        <input id="receiverEmail" name="receiverEmail" type="email"
                                                        placeholder="Ex.: <EMAIL>" required
                                                        ng-model="transaction.shippingAddress.Receiver.Email"
                                                        ng-maxlength="150" validator="required,email">
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                    }
                                </article>
                                <article>
                                    <fieldset>
                                        <h2>Características e Regulamento do Cartão</h2>
                                        <div class="text regulation">
                                            <div class="scroll">
                                                <div class="scroll-container">
                                                    <div ng-bind-html="settings.regulation"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="field">
                                            <input id="accepted" name="accepted" type="checkbox" value="true"
                                                ng-model="transaction.regulationAccepted" required ng-required="true" />
                                            <label for="accepted"><span class="required">*</span> Li e aceito os termos
                                                do regulamento acima.</label>
                                        </div>
                                    </fieldset>
                                </article>

                                <div>
                                    <button type="submit" class="button pull-right"
                                        ng-class="{'processing': processing}" ng-click="continue(cardForm)"
                                        ng-disabled="disableContinueButton()">Continuar</button>

                                    <button type="submit" class="button pull-right" ng-if="canShowCardSettings()"
                                        ng-class="{'processing': processing}" ng-click="retrieveCardTracking()"
                                        ng-disabled="disableTrackingButton()">Rastrear cartão</button>
                                </div>
                            </form>

                            <form ng-if="finalStep">
                                <article class="cart">
                                    <header>
                                        <h1 ng-if="!transaction.created">Confirmação do seu pedido</h1>
                                        <h1 ng-if="transaction.created">Pedido número {{ transaction.orderNumber }}</h1>
                                    </header>
                                    @if (Model != null && !Model.IsDisableShippingAddress)
                                    {
                                        <div class="order-info">
                                            <h2>Veja se todas as informações abaixo estão corretas</h2>
                                            <div class="box" ng-if="transaction.issueNewCard || transaction.reissueCard">
                                                <div class="address" ng-if="transaction.shippingAddress">
                                                    <h3>Endereço de entrega: {{ transaction.shippingAddress.AddressName }}
                                                    </h3>
                                                    <p>{{ transaction.shippingAddress.Street }}, {{
                                                        transaction.shippingAddress.Number }} - {{
                                                        transaction.shippingAddress.Complement }}
                                                        <br>{{ transaction.shippingAddress.Neighborhood }} - {{
                                                        transaction.shippingAddress.City }} - {{
                                                        transaction.shippingAddress.State }}
                                                        <br>CEP {{ transaction.shippingAddress.Cep }}<br>Referência: {{
                                                        transaction.shippingAddress.Reference }}
                                                    </p>
                                                </div>
                                                <div class="recipient" ng-if="transaction.shippingAddress.Receiver">
                                                    <h3>Quem receberá o cartão</h3>
                                                    <p>{{ transaction.shippingAddress.Receiver.Name }}<br>CPF {{
                                                        transaction.shippingAddress.Receiver.Cpf }}
                                                        <br>{{ transaction.shippingAddress.Receiver.Telephone }} / {{
                                                        transaction.shippingAddress.Receiver.Cellphone }}
                                                        <br>{{ transaction.shippingAddress.Receiver.Email }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    <div class="cart-products">
                                        <h2>Dados da transferência</h2>
                                        <table style="display:table">
                                            <thead>
                                                <tr>
                                                    <th colspan="2">Cartão</th>
                                                    <th>@Model.ViewStrings.BalanceDescription p/ Transferir</th>
                                                    <th>Custo do Plástico</th>
                                                    <th>Custo do Frete</th>
                                                    <th>Taxa de Carga</th>
                                                    <th>Taxa de Serviço</th>
                                                    <th>Valor a creditar</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="2"><strong>{{ transaction.card.Number }}</strong> <span
                                                            ng-if="transaction.reissueCard">(2ª Via)</span></td>
                                                    <td style="width:100px">@coinPrefix {{ transaction.pointsToTransfer
                                                        | number:'2' }} @coinSufix</td>
                                                    <td>R$ {{ transaction.detailedFees.plasticCost | number:'2' }}</td>
                                                    <td>R$ {{ transaction.detailedFees.shippingCost | number:'2' }}</td>
                                                    <td>R$ {{ transaction.detailedFees.chargeCost | number:'2' }}</td>
                                                    <td>R$ {{ transaction.detailedFees.serviceFee | number:'2' }}</td>
                                                    <td>R$ {{ transaction.creditAmount | number:'2' }}</td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="4"></td>
                                                    <td colspan="4">
                                                        <table class="subtotal" style="display:table">
                                                            <tr>
                                                                <td>Total das Taxas</td>
                                                                <td>R$ {{ transaction.totalFeesAmount | number:'2' }}
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>Total de
                                                                    @Model.ViewStrings.BalanceDescriptionLowerCase a
                                                                    debitar</td>
                                                                <td>@coinPrefix {{ transaction.orderTotalCost.points |
                                                                    number:'2' }} @coinSufix</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    <div>
                                        <a class="back pull-left" ng-click="back()"
                                            ng-if="!transaction.created">Voltar</a>

                                        <button type="submit" class="button pull-right"
                                            ng-class="{'processing': processing}" ng-click="finalize()"
                                            ng-disabled="processing" ng-if="!transaction.created">Finalizar</button>
                                    </div>
                                </article>
                            </form>
                        </div>
                    </div>
                </section>
            </div>
            <div id="tab-2" class="tabContent" ng-if="canShowCardSettings()">
                <section>
                    <div class="cartoes">
                        <div loader-container is-loading="loading || loadingCardConfiguration">
                            <form class="cardForm" ngForm>
                                <div class="row">
                                    <div class="col-12 instructions">
                                        <h1>Desbloquear Cartão</h1>
                                        <h2>Instruções para o preenchimento</h2>
                                        <ol>
                                            <li>Informe os dados para consultar o cartão, em seguida desbloquea-lo para
                                                poder usa-lo na campanha.</li>
                                            <li ng-if="!disableCardDetailsAccess()">`Para alterar a senha ou bloquear o cartão novamente, do cartão da
                                                campanha, vá à aba "Detalhes do Cartão".</li>
                                        </ol>
                                    </div>
                                </div>
                                <div>
                                    <div class="field col-3">
                                        <label for="data-nascimento">Data de Nascimento<span
                                                class="required">*</span></label>
                                        <input id="data-nascimento" name="data-nascimento" type="text" maxlength="10"
                                            ng-model="card.birthDate" moment-picker="card.birthDate"
                                            start-date="card.birthDate" ng-model-options="{updateOn:'blur'}"
                                            locale="pt-br" format="DD/MM/YYYY" start-view="decade" keyboard="true">
                                    </div>
                                    <div class="field col-3">
                                        <label for="data-validade">Data de Validade<span
                                                class="required">*</span></label>
                                        <input id="data-validade" name="data-validade" type="text" maxlength="10"
                                            ng-model="card.expirationDate" moment-picker="card.expirationDate"
                                            start-date="card.expirationDate" ng-model-options="{updateOn:'blur'}"
                                            locale="pt-br" format="DD/MM/YYYY" start-view="decade" keyboard="true">
                                    </div>
                                    <div class="field col-3">
                                        <label for="firstDigits">Primeiros 4 Dígitos do cartão<span
                                                class="required">*</span></label>
                                        <input id="firstDigits" name="firstDigits" type="text" maxlength="4"
                                            placeholder="Ex.: 1234" ng-model="card.firstDigits"
                                            moment-picker="card.firstDigits" start-date="card.firstDigits"
                                            ng-model-options="{updateOn:'blur'}" locale="pt-br" keyboard="true">
                                    </div>
                                    <div class="field col-3">
                                        <label for="lastDigits">Últimos 4 Dígitos do cartão<span
                                                class="required">*</span></label>
                                        <input id="lastDigits" name="lastDigits" type="text" maxlength="4"
                                            placeholder="Ex.: 1234" ng-model="card.lastDigits"
                                            moment-picker="card.lastDigits" start-date="card.lastDigits"
                                            ng-model-options="{updateOn:'blur'}" locale="pt-br" keyboard="true">
                                    </div>
                                </div>
                                <div>
                                    <button type="submit" class="button pull-right right-m1 bottom-m1"
                                        ng-class="{'processing': processing}" ng-click="findPrepaidCard()"
                                        ng-disabled="disableButtons()">Pesquisar</button>
                                </div>
                            </form>
                            <form class="text-center" ngForm ng-if="hasSearchedCard()">
                                <div class="row">
                                    <div class='col-6 card'>
                                        <div class="field col-12">
                                            <div class='top-block'>
                                                <h3 class='card-name'>Fulano Silva</h3>
                                            </div>
                                            <div class='card-number'>
                                                <p>{{searchedCard.firstDigits}} xxxx xxxx
                                                    {{searchedCard.lastDigits}}</p>
                                            </div>
                                            <div class='bottom-block'>
                                                <div class='balance'>
                                                    <div class="balance-text">Saldo</div>
                                                    <div class='card-balance'>
                                                        R$ {{ searchedCard.balance | currency:'' }}
                                                    </div>
                                                </div>
                                                <div class='card-status'>
                                                    <span
                                                        ng-class="isSearchedCardActive() ? 'dot green-background-color' : 'dot red-background-color'"></span>
                                                    <span class="status">{{selectedCard.cardStatusDescription}}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center" ng-if="!isSearchedCardActive()">
                                    <div class="col-xs-12 col-sm-12">
                                        <button type="submit" class="button pull-right"
                                            ng-class="{'processing': processing}"
                                            ng-click="confirmActiveCard(searchedCard.prepaidCardId)"
                                            ng-disabled="disableButtons()">Desbloquear
                                            Cartão</button>
                                    </div>
                                </div>
                                <div class="text-center" ng-if="isSearchedCardActive() && !isSearchedCardInUse()">
                                    <div class="col-xs-12 col-sm-12">
                                        <button type="submit" class="button pull-right"
                                            ng-class="{'processing': processing}" ng-click="confirmUseCard()"
                                            ng-disabled="disableButtons()">Desbloquear Cartão</button>
                                    </div>
                                </div>
                                <div ng-if="isSearchedCardActive() && isSearchedCardInUse()">
                                    <span class="teste">Obs.: Cartão já está em uso, por favor vá na aba "Detalhes do
                                        Cartão" para ter acesso a suas infomações.</span>
                                </div>
                            </form>
                        </div>
                    </div>
                </section>
            </div>
            <div id="tab-3" class="tabContent" ng-if="canShowCardSettings() && !disableCardDetailsAccess()">
                <div class="container cartoes">
                    <div loader-container is-loading="loading">
                        <div ng-if="!showExtractForm">
                            <div class="row">
                                <div class="col-12 instructions">
                                    <h1>Detalhes do Cartão</h1>
                                    <h2>Instruções para o preenchimento</h2>
                                    <ol>
                                        <li>Selecione o cartão em que deseja visualizar detalhes ou atualizar
                                            informações. Caso não tenha um cartão retorne para a primeira aba para
                                            "Emitir Novo Cartão", caso já tenha emitido e recebido o cartão então
                                            realize o desbloqueio do mesmo na aba "Desbloquear cartão".</li>
                                        <li>Após selecionar o cartão é possível: verificar saldo, bloquear/desbloquear
                                            cartão, alterar senha e visualizar seu extrato.</li>
                                    </ol>
                                </div>
                            </div>
                            <article>
                                <div>
                                    <div>
                                        <div class="row">
                                            <div class='col-6 card'>
                                                <div class="field col-12">
                                                    <div class='top-block'>
                                                        <h3 class='card-name'>Fulano Silva</h3>
                                                    </div>
                                                    <div class='card-number'>
                                                        <p>{{selectedCard.firstDigits}} xxxx xxxx
                                                            {{selectedCard.lastDigits}}</p>
                                                    </div>
                                                    <div class='bottom-block'>
                                                        <div class='balance'>
                                                            <div class="balance-text">Saldo</div>
                                                            <div class='card-balance'>
                                                                R$ {{ selectedCard.balance | currency:'' }}
                                                            </div>
                                                        </div>
                                                        <div class='card-status'>
                                                            <span
                                                                ng-class="isSelectedCardActive() ? 'dot green-background-color' : 'dot red-background-color'"></span>
                                                            <span
                                                                class="status">{{selectedCard.cardStatusDescription}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <fieldset>
                                                    <div class="card-select-form">
                                                        <div class="field col-12">
                                                            <label>Selecione o cartão<span
                                                                    class="required">*</span></label>
                                                            <select id="selectedCardForDetails"
                                                                name="selectedCardForDetails" placeholder="Escolha"
                                                                data-placeholder="Escolha" class="card-select" required
                                                                ng-required="true" validator="required"
                                                                ng-model="transaction.selectedCard"
                                                                ng-options="card.Id as card.Description for card in cardsInUse"
                                                                ng-change="findSelectedPrepaidCardById()">
                                                                <option value="" selected="selected"
                                                                    disabled="disabled">Escolha</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <br>
                                                </fieldset>
                                                <fieldset>
                                                    <form class="user-validation-template" ng-if="showPinForm">
                                                        <fieldset ng-if="isBasicInfoStep()">
                                                            <form method="POST" name="loginForm" autocomplete="off">
                                                                <div class="field">
                                                                    <div id="userValidationRecaptcha"></div>
                                                                </div>
                                                                <div
                                                                    class="user-validation-form-buttons field text-left">
                                                                    <a class="back"
                                                                        ng-class="{'processing': processing}"
                                                                        ng-click="closePinForm()"
                                                                        ng-disabled="disableButtons()"> Cancelar </a>
                                                                    <button type="submit" class="button"
                                                                        ng-class="{'processing': processing}"
                                                                        ng-click="validateReCaptcha()"
                                                                        ng-disabled="disableButtons()">Continuar</button>
                                                                </div>
                                                            </form>
                                                        </fieldset>

                                                        <fieldset ng-if="isTokenOptionStep()">
                                                            <div>
                                                                <h1>Código de Segurança</h1>
                                                                <p ng-if="canChoose()">Selecione a forma que deseja
                                                                    receber o código de segurança.</p>
                                                            </div>
                                                            <div class="field no-bottom"
                                                                ng-if="user.email && canSendEmail()">
                                                                <input id="email" name="sendMethod" type="radio"
                                                                    value="EMAIL" ng-model="user.sendMethod" />
                                                                <label for="email">Por e-mail: {{ user.email }}</label>
                                                            </div>
                                                            <div class="field no-bottom"
                                                                ng-if="user.mobilePhone && canSendSMS()">
                                                                <input id="cellphone" name="sendMethod" type="radio"
                                                                    value="SMS" ng-model="user.sendMethod" />
                                                                <label for="cellphone">Por SMS: {{ user.mobilePhone
                                                                    }}</label>
                                                            </div>
                                                            <div class="user-validation-form-buttons field text-left">
                                                                <a class="back" ng-click="closePinForm()"> Cancelar </a>
                                                                <button type="submit" class="button"
                                                                    ng-class="{'processing': processing}"
                                                                    ng-click="sendAuthorizationCode()">{{!alreadySentToken
                                                                    ? "Enviar Token" : "Reenviar Token"}}</button>
                                                            </div>
                                                        </fieldset>
                                                        <fieldset ng-if="isTokenConfirmationStep()">
                                                            <form class="form-input">
                                                                <div>
                                                                    <h1>Confirmação do código</h1>
                                                                </div>
                                                                <div class="field col-xs-6">
                                                                    <label for="token">Código de segurança
                                                                        recebido</label>
                                                                    <input id="token" type="text" name="token"
                                                                        ng-model="user.code" required>
                                                                </div>
                                                                <div
                                                                    class="user-validation-form-buttons field text-left">
                                                                    <a class="back" ng-click="closePinForm()"> Cancelar
                                                                    </a>
                                                                    <button type="submit" class="button"
                                                                        ng-class="{'processing': processing}"
                                                                        ng-click="validateAuthorizationCode()">Confirmar</button>
                                                                </div>
                                                            </form>
                                                        </fieldset>
                                                        <fieldset ng-if="isFinalStep()">
                                                            <div>
                                                                <div class="field col-xs-12 col-sm-12">
                                                                    <span>Senha deve ser um número de 4 digitos não
                                                                        sequenciais, e também não pode ter digitos
                                                                        repetidos.</span>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <div class="field col-6">
                                                                    <label for="newPassword">Nova senha</label>
                                                                    <input id="newPassword" name="newPassword"
                                                                        type="password" class="password" minlength="4"
                                                                        maxlength="4" autocomplete="off"
                                                                        ng-model="pinRequest.pin" validator="required">
                                                                </div>
                                                                <div class="field col-6">
                                                                    <label for="passwordConfirm">Confirmar senha</label>
                                                                    <input id="passwordConfirm" name="passwordConfirm"
                                                                        type="password" minlength="4" maxlength="4"
                                                                        autocomplete="off"
                                                                        data-rule-equalto="#newPassword"
                                                                        ng-model="pinRequest.confirmedPin"
                                                                        validator="required">
                                                                </div>
                                                            </div>
                                                            <br>
                                                            <div>
                                                                <div>
                                                                    <div class="prepaid-card-menu col-xs-12 col-sm-12">
                                                                        <button type="submit"
                                                                            class="button pull-right right-m1 bottom-m1"
                                                                            ng-class="{'processing': processing}"
                                                                            ng-click="closePinForm()"
                                                                            ng-disabled="disableButtons()">Cancelar</button>

                                                                        <button type="submit"
                                                                            class="button pull-right right-m1 bottom-m1"
                                                                            ng-class="{'processing': processing}"
                                                                            ng-click="updateFormPin()"
                                                                            ng-disabled="disableButtons()">Resetar
                                                                            senha</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </fieldset>
                                                    </form>
                                                </fieldset>
                                                <fieldset>
                                                    <br>
                                                    <div ng-if="hasSelectedCard() && !showPinForm"
                                                        class="prepaid-card-menu">
                                                        <button type="submit" icon="lock"
                                                            class="button pull-right right-m1 bottom-m1"
                                                            ng-class="{'processing': processing}" ng-click="resetPin()"
                                                            ng-if="isSelectedCardActive()"
                                                            ng-disabled="disableButtons()">Resetar Senha</button>

                                                        <button type="submit" icon="ban"
                                                            class="button pull-right right-m1 bottom-m1"
                                                            ng-class="{'processing': processing}"
                                                            ng-click="confirmBlockCard(selectedCard.prepaidCardId)"
                                                            ng-if="isSelectedCardActive() && !parametrizations.disableUpdateCardStatus"
                                                            ng-disabled="disableButtons()">Bloquear</button>

                                                        <button type="submit" icon="check"
                                                            class="button pull-right right-m1 bottom-m1"
                                                            ng-class="{'processing': processing}"
                                                            ng-click="confirmActiveCard(selectedCard.prepaidCardId)"
                                                            ng-if="!isSelectedCardActive() && !parametrizations.disableUpdateCardStatus"
                                                            ng-disabled="disableButtons()">Desbloquear</button>

                                                        <button type="submit" icon="search"
                                                            class="button pull-right right-m1 bottom-m1"
                                                            ng-class="{'processing': processing}"
                                                            ng-click="showExtract()"
                                                            ng-if="!parametrizations.disableCardStatement"
                                                            ng-disabled="disableButtons()">Extrato</button>
                                                    </div>
                                                </fieldset>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </article>
                        </div>
                        <div ng-if="showExtractForm">
                            <article class="pointsSummary">
                                <header>
                                    <h1>Extrato do Cartão</h1>
                                </header>
                                <div>
                                    <div class="summary">
                                        <h2>Saldo atual</h2>
                                        <p>
                                            <strong><span ng-bind="moeda.Prefix"></span> <span ng-bind="(selectedCard.balance |
                                            currency:'')">0</span><span ng-bind="moeda.Sufix"></span></strong>
                                        </p>
                                    </div>
                                </div>
                                <form>
                                    <div class="filters">
                                        <div class="field">
                                            <div class="col-6">
                                                <label for="dataInicial">De</label>
                                                <input id="dataInicial" name="dataInicial" type="text" maxlength="10"
                                                    locale="pt-br" moment-picker ng-model="filters.formattedStartDate"
                                                    ng-model-options="{updateOn:'blur'}" start-view="day"
                                                    format="DD/MM/YYYY" keyboard="true" />
                                            </div>
                                            <div class="col-6">
                                                <label for="dataFinal">Até</label>
                                                <input id="dataFinal" name="dataFinal" type="text" maxlength="10"
                                                    locale="pt-br" moment-picker ng-model="filters.formattedEndPeriod"
                                                    ng-model-options="{updateOn:'blur'}" start-view="day"
                                                    format="DD/MM/YYYY" keyboard="true" />
                                            </div>
                                        </div>
                                        <div class="field">
                                            <button type="submit" class="button" ng-click="getTransactions()"></button>
                                        </div>
                                    </div>
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>Data</th>
                                                <th>Descrição</th>
                                                <th>Valor</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr
                                                ng-show="!statement || !statement.transactions || statement.transactions.length == 0">
                                                <td colspan="3">Não existem lançamentos.</td>
                                            </tr>
                                            <tr ng-repeat="t in statement.transactions">
                                                <td><span ng-bind="::(t.processingDate | date:'dd/MM/yyyy')"></span>
                                                </td>
                                                <td><span ng-bind="::t.description"></span></td>
                                                <td><span ng-bind="::(t.amount | currency:'')"></span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </form>
                            </article>
                            <div>
                                <aside class="sidebar">
                                    <a href="javascript:" class="button back" ng-disabled="disableButtons()"
                                        ng-click="closeExtract()" ng-class="{'processing': processing}">Voltar</a>
                                </aside>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div style="visibility:hidden;position:absolute;">
        <div id="card-tracking-modal" class="container" style="width:1100px;">
            <article class="modal">
                <header>
                    <h1>Rastreio</h1>
                </header>
                <div class="container">

                    <div class="row" ng-if="!hasTracking()">
                        <h4>Cartão ainda não enviado.</h4>
                    </div>

                    <div class="row" ng-if="hasTracking()">
                        <dev class="col-6">
                            <p><strong>Data de Envio: </strong><span
                                    ng-bind="::(tracking.deliveredAt | date:'dd/MM/yyyy \'às\' HH:ss')"></span></p>
                        </dev>
                        <dev class="col-6" ng-if="hasDeliveredCard()">
                            <span><strong>Nome do recebedor:</strong> {{ tracking.receiver }}</span>
                        </dev>
                        <dev class="col-6" ng-if="hasDeliveredCard()">
                            <p><strong>Data de Entrega: </strong><span
                                    ng-bind="::(tracking.pickedUpAt | date:'dd/MM/yyyy \'às\' HH:ss')"></span></p>
                        </dev>
                        <dev class="col-6" ng-if="hasDeliveredCard()">
                            <div class="inputContainer">
                                <span><strong>CPF/CNPJ do recebedor:</strong></span>
                                <div>
                                    <input id="receiverDocument" name="receiverDocument" class="receiverDocument"
                                        type="text" maxlength="20" readonly value="{{tracking.receiverDocument}}">
                                </div>
                            </div>
                        </dev>
                    </div>

                    <div ng-if="hasTrackingEvents()">
                        <br>
                        <h2>Histórico de Rastreio</h2>
                        <div style="height: 150px;overflow-y:scroll;overflow-x:hidden;">
                            <div ng-repeat="event in tracking.events">
                                <div class="row" style="margin-left:1px">
                                    <div class="col-12">
                                        <p><strong><span
                                                    ng-bind="::(event.occurredAt | date:'dd/MM/yyyy \'às\' HH:ss')"></span></strong>
                                            -
                                            <span> {{event.trackingStatusGroupName}} -
                                                {{event.trackingStatusName}}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </div>

</section>

@section ScriptsCustomizados {
<script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment-with-locales.min.js"></script>
<script type="text/javascript" src="~/js/lib/moment/moment-timezone-with-data-2012-2022.min.js"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript"
    asp-append-version="true"></script>
<script src="@Url.Content("~/js/endereco/enderecoApi.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/extraservices/cards.js")" type="text/javascript" asp-append-version="true"></script>
}