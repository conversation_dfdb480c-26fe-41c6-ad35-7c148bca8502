@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@inject IHelperWeb _helper;
@model Motivai.WebCatalog.Models.Pages.Cashback.CashbackViewModel

@{
	Layout = "~/Views/Shared/_Layout.cshtml";
	ViewBag.Title = "Transferência Bancária";
	ViewBag.ClassBody = "primeiro-acesso";

	var httpContext = RequestContextManager.Instance.CurrentContext;
	var itemCoinPrefix = httpContext.Items["coinPrefix"];
	var itemCoinSufix = httpContext.Items["coinSufix"];
	var coinPrefix = itemCoinPrefix != null ? itemCoinPrefix.ToString() : "";
	var coinSufix = itemCoinSufix != null ? itemCoinSufix.ToString() : "";

	var participant = _helper.GetParticipantSession();
}
@section StylesCustomizados {
	<style>
		.nivo-controlNav, .nivo-directionNav {
			display: none;
		}
		body.primeiro-acesso .site-header .profile {
			float: left;
		}
		.shipping-address { clear: both; }
		.shipping-address img { margin-left: 0px !important; height: 200px; }
		div.text.regulation {
			background: #f2f2f2;
			margin-bottom: 2em;
			padding: 1.5em;
			-moz-border-radius: .25em;
			-webkit-border-radius: .25em;
			border-radius: .25em;
		}
		fieldset {
			margin-bottom: 1em;
		}
		.instructions {
			margin-bottom: 2em;
			text-align: justify;
		}
		.instructions ol {
			padding-left: 1em;
		}
		.instructions li {
			list-style: initial;
		}
		.order-info .box div {
			float: left !important;
		}
		.fees-details tr td {
			width: 33% !important;
		}
		.confirmation-table tr td {
			width: 15% !important;
		}
		.confirmation-table tr td.account-info {
			width: 30% !important;
		}
	</style>
}
<div class="breadcrumbs">
	<div class="container">
		<ul>
			<li class="home"><a href="/">Home</a></li>
			<li>Transferência Bancária</li>
		</ul>
	</div>
</div>
<div id="cashBack" class="custom-banner"></div>
<div id="mediabox">
	<article class="mediabox">
		<img data-srcset-base="/assets/img/mediabox/" data-srcset-ext=".jpg" data-srcset="cashback_wd 1920w 3x, cashback_wd 1680w 3x, cashback 1280w 3x, cashback_s 599w 3x">
	</article>
</div>
<section id="content" ng-controller="cardsCtrl" ng-init="loadPage()">
	<div class="container" ng-if="!occurredError">
		<div loader-container is-loading="loading">
			<form name="cardForm" ng-if="!finalStep">
				<article class="buyPrepaidCredits bottom-m1">
					<div class="row">
						<div class="col-12 instructions">
							<h2>Instruções para o preenchimento</h2>
							<ol>
								<li>É permitido apenas transferência para contas nas quais você seja titular.</li>
								<li ng-if="order.bankTransferMethod == 'BANK_TRANSFER'">Selecione o banco em que possui a conta.</li>
								<li ng-if="order.bankTransferMethod == 'BANK_TRANSFER'">Informe os números da sua agência e conta.</li>
								<li ng-if="order.bankTransferMethod == 'PIX'">Informe a chave PIX da conta.</li>
								<li>Confirme seu CNPJ/CPF.</li>
							</ol>
						</div>
					</div>

					<fieldset ng-if="canChooseBankTransferMethod">
						<div class="row">
							<div class="field col-6">
								<label>Forma de transferência</label>
								<select id="bankTransferMethod" name="bankTransferMethod" validator="required" ng-model="order.bankTransferMethod" ng-change="onBankTransferMethodChange()">
									<option value="BANK_TRANSFER">TED</option>
									<option value="PIX">PIX</option>
								</select>
							</div>
						</div>
					</fieldset>

					<fieldset ng-if="order.bankTransferMethod == 'PIX'">
						<div class="row">
							<h2>Dados Bancário</h2>
						</div>

						<div class="row">
							<div class="field col-6">
								<label>Chave PIX<span class="required">*</span></label>
								<input id="bankAccountPixKey" name="bankAccountPixKey" type="text" ng-model="order.bankAccountPixKey" validator="required">
							</div>
						</div>
					</fieldset>

					<fieldset ng-if="order.bankTransferMethod == 'BANK_TRANSFER'">
						<div class="row">
							<h2>Dados Bancário</h2>
						</div>

						<div class="row">
							<div class="field col-6">
								<label>Banco<span class="required">*</span></label>
								<select id="bankCode" name="bankCode" validator="required" ng-model="order.bankAccount.bankCode" ng-change="onBankChange()">
									<option value="" selected disabled="disabled">Selecione</option>
									<option value="117">117 - Advanced Corretora de Câmbio Ltda.</option>
									<option value="172">172 - Albatross Corretora de Câmbio e Valores S.A</option>
									<option value="188">188 - Ativa Investimentos S.A. Corretora de Títulos Câmbio e Valores</option>
									<option value="654">654 - Banco A.J. Renner S.A.</option>
									<option value="246">246 - Banco ABC Brasil S.A.</option>
									<option value="075">075 - Banco ABN Amro S.A.</option>
									<option value="121">121 - Banco Agibank S.A.</option>
									<option value="025">025 - Banco Alfa S.A.</option>
									<option value="641">641 - Banco Alvorada S.A.</option>
									<option value="065">065 - Banco Andbank (Brasil) S.A.</option>
									<option value="213">213 - Banco Arbi S.A.</option>
									<option value="024">024 - Banco Bandepe S.A.</option>
									<option value="740">740 - Banco Barclays S.A.</option>
									<option value="096">096 - Banco BM&FBovespa</option>
									<option value="318">318 - Banco BMG S.A.</option>
									<option value="752">752 - Banco BNP Paribas Brasil S.A.</option>
									<option value="248">248 - Banco Boavista Interatlântico S.A.</option>
									<option value="107">107 - Banco Bocom BBM S.A.</option>
									<option value="063">063 - Banco Bradescard S.A.</option>
									<option value="036">036 - Banco Bradesco BBI S.A.</option>
									<option value="122">122 - Banco Bradesco BERJ S.A.</option>
									<option value="204">204 - Banco Bradesco Cartões S.A.</option>
									<option value="394">394 - Banco Bradesco Financiamentos S.A.</option>
									<option value="237">237 - Banco Bradesco S.A.</option>
									<option value="125">125 - Banco Brasil Plural S.A. – Banco Múltiplo</option>
									<option value="081">081 - Banco Brasileiro de Negócios S.A. – BBN</option>
									<option value="218">218 - Banco BS2 S.A.</option>
									<option value="208">208 - Banco BTG Pactual S.A.</option>
									<option value="473">473 - Banco Caixa Geral – Brasil S.A.</option>
									<option value="412">412 - Banco Capital S.A.</option>
									<option value="040">040 - Banco Cargill S.A.</option>
									<option value="266">266 - Banco Cédula S.A.</option>
									<option value="739">739 - Banco Cetelem S.A.</option>
									<option value="233">233 - Banco Cifra S.A.</option>
									<option value="745">745 - Banco Citibank S.A.</option>
									<option value="241">241 - Banco Clássico S.A.</option>
									<option value="095">095 - Banco Confidence de Câmbio S.A.</option>
									<option value="756">756 - Banco Cooperativo do Brasil S.A. – Bancoob</option>
									<option value="748">748 - Banco Cooperativo Sicredi S.A.</option>
									<option value="222">222 - Banco Credit Agricole Brasil S.A.</option>
									<option value="505">505 - Banco Credit Suisse (Brasil) S.A.</option>
									<option value="069">069 - Banco Crefisa S.A.</option>
									<option value="003">003 - Banco da Amazônia S.A.</option>
									<option value="083">083 - Banco da China Brasil S.A.</option>
									<option value="707">707 - Banco Daycoval S.A.</option>
									<option value="070">070 - Banco de Brasília S.A. (BRB)</option>
									<option value="250">250 - Banco de Crédito e Varejo S.A. – BCV</option>
									<option value="505">505 - Banco de Investimento Credit Suisse (Brasil) S.A.</option>
									<option value="300">300 - Banco de La Nacion Argentina</option>
									<option value="495">495 - Banco de La Provincia de Buenos Aires</option>
									<option value="494">494 - Banco de La Republica Oriental del Uruguay</option>
									<option value="001">001 - Banco do Brasil S.A.</option>
									<option value="047">047 - Banco do Estado de Sergipe S.A.</option>
									<option value="021">021 - Banco do Estado do Espírito Santo – Baneste S.A.</option>
									<option value="037">037 - Banco do Estado do Pará S.A.</option>
									<option value="041">041 - Banco do Estado do Rio Grande do Sul S.A.</option>
									<option value="004">004 - Banco do Nordeste do Brasil S.A.</option>
									<option value="265">265 - Banco Fator S.A.</option>
									<option value="224">224 - Banco Fibra S.A.</option>
									<option value="626">626 - Banco Ficsa S.A.</option>
									<option value="094">094 - Banco Finaxis S.A.</option>
									<option value="612">612 - Banco Guanabara S.A.</option>
									<option value="012">012 - Banco Inbursa S.A.</option>
									<option value="604">604 - Banco Industrial do Brasil S.A.</option>
									<option value="653">653 - Banco Indusval S.A.</option>
									<option value="077">077 - Banco Inter S.A.</option>
									<option value="630">630 - Banco Intercap S.A.</option>
									<option value="719">719 - Banco Internacional do Funchal (Brasil) S.A. – Banif</option>
									<option value="249">249 - Banco Investcred Unibanco S.A.</option>
									<option value="184">184 - Banco Itaú BBA S.A.</option>
									<option value="029">029 - Banco Itaú Consignado S.A.</option>
									<option value="652">652 - Banco Itaú Unibanco Holding S.A.</option>
									<option value="341">341 - Banco Itaú Unibanco S.A.</option>
									<option value="479">479 - Banco ItauBank S.A</option>
									<option value="376">376 - Banco J. P. Morgan S.A.</option>
									<option value="074">074 - Banco J. Safra S.A.</option>
									<option value="217">217 - Banco John Deere S.A.</option>
									<option value="076">076 - Banco KDB S.A.</option>
									<option value="757">757 - Banco Keb Hana do Brasil S.A.</option>
									<option value="600">600 - Banco Luso Brasileiro S.A.</option>
									<option value="243">243 - Banco Máxima S.A.</option>
									<option value="389">389 - Banco Mercantil do Brasil S.A.</option>
									<option value="370">370 - Banco Mizuho do Brasil S.A.</option>
									<option value="746">746 - Banco Modal S.A.</option>
									<option value="066">066 - Banco Morgan Stanley S.A.</option>
									<option value="456">456 - Banco MUFG Brasil S.A.</option>
									<option value="007">007 - Banco Nacional de Desenvolvimento Econômico e Social – BNDES</option>
									<option value="735">735 - Banco Neon S.A.</option>
									<option value="169">169 - Banco Olé Bonsucesso Consignado S.A.</option>
									<option value="079">079 - Banco Original do Agronegócio S.A.</option>
									<option value="212">212 - Banco Original S.A.</option>
									<option value="712">712 - Banco Ourinvest S.A.</option>
									<option value="623">623 - Banco Pan S.A.</option>
									<option value="611">611 - Banco Paulista S.A.</option>
									<option value="643">643 - Banco Pine S.A.</option>
									<option value="747">747 - Banco Rabobank International Brasil S.A.</option>
									<option value="088">088 - Banco Randon S.A.</option>
									<option value="633">633 - Banco Rendimento S.A.</option>
									<option value="741">741 - Banco Ribeirão Preto S.A.</option>
									<option value="120">120 - Banco Rodobens S.A.</option>
									<option value="422">422 - Banco Safra S.A.</option>
									<option value="033">033 - Banco Santander (Brasil) S.A.</option>
									<option value="743">743 - Banco Semear S.A.</option>
									<option value="754">754 - Banco Sistema S.A.</option>
									<option value="366">366 - Banco Société Générale Brasil S.A.</option>
									<option value="637">637 - Banco Sofisa S.A.</option>
									<option value="464">464 - Banco Sumitomo Mitsui Brasileiro S.A.</option>
									<option value="082">082 - Banco Topázio S.A.</option>
									<option value="634">634 - Banco Triângulo S.A.</option>
									<option value="018">018 - Banco Tricury S.A.</option>
									<option value="655">655 - Banco Votorantim S.A.</option>
									<option value="119">119 - Banco Western Union do Brasil S.A.</option>
									<option value="124">124 - Banco Woori Bank do Brasil S.A.</option>
									<option value="755">755 - Bank of America Merrill Lynch Banco Múltiplo S.A.</option>
									<option value="268">268 - Barigui Companhia Hipotecária</option>
									<option value="144">144 - Bexs Banco de Câmbio S.A.</option>
									<option value="253">253 - Bexs Corretora de Câmbio S.A.</option>
									<option value="134">134 - BGC Liquidez Distribuidora de Títulos e Valores Mobiliários Ltda.</option>
									<option value="017">017 - BNY Mellon Banco S.A.</option>
									<option value="126">126 - BR Partners Banco de Investimento S.A.</option>
									<option value="092">092 - Brickell (BRK) S.A. Crédito, Financiamento e Investimento</option>
									<option value="173">173 - BRL Trust Distribuidora de Títulos e Valores Mobiliários S.A.</option>
									<option value="142">142 - Broker Brasil Corretora de Câmbio Ltda.</option>
									<option value="080">080 - BT Corretora de Câmbio Ltda.</option>
									<option value="104">104 - Caixa Econômica Federal</option>
									<option value="130">130 - Caruana S.A. Sociedade de Crédito, Financiamento e Investimento</option>
									<option value="159">159 - Casa do Crédito S.A. Sociedade de Crédito ao Microempreendedor</option>
									<option value="114">114 - Central Cooperativa de Crédito no Estado do Espírito Santo – CECOOP</option>
									<option value="091">091 - Central de Cooperativas de Economia e Crédito Mútuo do Estado RS – Unicred</option>
									<option value="320">320 - China Construction Bank (Brasil) Banco Múltiplo S.A.</option>
									<option value="477">477 - Citibank N.A.</option>
									<option value="127">127 - Codepe Corretora de Valores e Câmbio S.A.</option>
									<option value="163">163 - Commerzbank Brasil S.A. – Banco Múltiplo</option>
									<option value="136">136 - Confederação Nacional das Cooperativas Centrais Unicred Ltda (Unicred do Brasil)</option>
									<option value="060">060 - Confidence Corretora de Câmbio S.A.</option>
									<option value="097">097 - Cooperativa Central de Crédito Noroeste Brasileiro Ltda. (CentralCredi)</option>
									<option value="085">085 - Cooperativa Central de Crédito Urbano – Cecred</option>
									<option value="016">016 - Cooperativa de Crédito Mútuo dos Despachantes de Trânsito de Sta.Catarina e RS</option>
									<option value="089">089 - Cooperativa de Crédito Rural da Região da Mogiana</option>
									<option value="010">010 - Credicoamo Crédito Rural Cooperativa</option>
									<option value="011">011 - Credit Suisse Hedging-Griffo Corretora de Valores S.A.</option>
									<option value="133">133 - Cresol – Confederação Nacional Cooperativas Centrais de Crédito e Econ Familiar</option>
									<option value="182">182 - Dacasa Financeira S/A – Sociedade de Crédito, Financiamento e Investimento</option>
									<option value="487">487 - Deutsche Bank S.A. – Banco Alemão</option>
									<option value="140">140 - Easynvest – Título Corretora de Valores SA</option>
									<option value="149">149 - Facta Financeira S.A. – Crédito Financiamento e Investimento</option>
									<option value="196">196 - Fair Corretora de Câmbio S.A.</option>
									<option value="138">138 - Get Money Corretora de Câmbio S.A.</option>
									<option value="064">064 - Goldman Sachs do Brasil Banco Múltiplo S.A.</option>
									<option value="135">135 - Gradual Corretora de Câmbio, Títulos e Valores Mobiliários S.A.</option>
									<option value="177">177 - Guide Investimentos S.A. Corretora de Valores</option>
									<option value="146">146 - Guitta Corretora de Câmbio Ltda.</option>
									<option value="078">078 - Haitong Banco de Investimento do Brasil S.A.</option>
									<option value="062">062 - Hipercard Banco Múltiplo S.A.</option>
									<option value="189">189 - HS Financeira S/A Crédito, Financiamento e Investimentos</option>
									<option value="269">269 - HSBC Brasil S.A. Banco de Investimento</option>
									<option value="271">271 - IB Corretora de Câmbio, Títulos e Valores Mobiliários Ltda.</option>
									<option value="157">157 - ICAP do Brasil Corretora de Títulos e Valores Mobiliários Ltda.</option>
									<option value="132">132 - ICBC do Brasil Banco Múltiplo S.A.</option>
									<option value="492">492 - ING Bank N.V.</option>
									<option value="139">139 - Intesa Sanpaolo Brasil S.A. – Banco Múltiplo</option>
									<option value="488">488 - JPMorgan Chase Bank, National Association</option>
									<option value="399">399 - Kirton Bank S.A. (Banco Múltiplo)</option>
									<option value="105">105 - Lecca Crédito, Financiamento e Investimento S/A</option>
									<option value="145">145 - Levycam – Corretora de Câmbio e Valores Ltda.</option>
									<option value="113">113 - Magliano S.A. Corretora de Cambio e Valores Mobiliarios</option>
									<option value="128">128 - MS Bank S.A. Banco de Câmbio</option>
									<option value="137">137 - Multimoney Corretora de Câmbio Ltda</option>
									<option value="014">014 - Natixis Brasil S.A. Banco Múltiplo</option>
									<option value="191">191 - Nova Futura Corretora de Títulos e Valores Mobiliários Ltda.</option>
									<option value="753">753 - Novo Banco Continental S.A. – Banco Múltiplo</option>
									<option value="260">260 - Nu Pagamentos S.A</option>
									<option value="111">111 - Oliveira Trust Distribuidora de Títulos e Valores Mobiliários S.A.</option>
									<option value="613">613 - Omni Banco S.A.</option>
									<option value="254">254 - Paraná Banco S.A.</option>
									<option value="194">194 - Parmetal Distribuidora de Títulos e Valores Mobiliários Ltda.</option>
									<option value="174">174 - Pernambucanas Financiadora S.A. Crédito, Financiamento e Investimento</option>
									<option value="100">100 - Planner Corretora de Valores S.A.</option>
									<option value="093">093 - Pólocred Sociedade de Crédito ao Microempreendedor e Empresa de Pequeno Porte</option>
									<option value="108">108 - PortoCred S.A. Crédito, Financiamento e Investimento</option>
									<option value="101">101 - Renascença Distribuidora de Títulos e Valores Mobiliários Ltda.</option>
									<option value="751">751 - Scotiabank Brasil S.A. Banco Múltiplo</option>
									<option value="545">545 - Senso Corretora de Câmbio e Valores Mobiliários S.A.</option>
									<option value="190">190 - Servicoop – Cooperativa de Economia e Crédito Mútuo dos Servidores Públicos Estaduais do Rio</option>
									<option value="183">183 - Socred S.A. – Sociedade de Crédito ao Microempreendedor</option>
									<option value="118">118 - Standard Chartered Bank (Brasil) S.A. Banco de Investimento</option>
									<option value="197">197 - Stone Pagamentos S.A.</option>
									<option value="143">143 - Treviso Corretora de Câmbio S.A.</option>
									<option value="131">131 - Tullett Prebon Brasil Corretora de Valores e Câmbio Ltda.</option>
									<option value="129">129 - UBS Brasil Banco de Investimento S.A.</option>
									<option value="015">015 - UBS Brasil Corretora de Câmbio, Títulos e Valores Mobiliários S.A.</option>
									<option value="099">099 - Uniprime Central – Central Interestadual de Cooperativas de Crédito Ltda.</option>
									<option value="084">084 - Uniprime Norte do Paraná – Cooperativa de Crédito Ltda.</option>
									<option value="102">102 - XP Investimentos Corretora de Câmbio, Títulos e Valores Mobiliários S/A</option>
								</select>
							</div>
						</div>
						<div class="row">
							<div class="field col-6">
								<label>Tipo da conta<span class="required">*</span></label>
								<select id="accountType" name="accountType" validator="required" ng-model="order.bankAccount.type" ng-change="onBankAccountTypeChange()">
									<option value="" selected disabled="disabled">Selecione</option>
									<option value="CheckingAccount">Conta Corrente</option>
									<option value="SavingsAccount">Conta Poupança</option>
								</select>
							</div>
						</div>
						<div class="row">
							<div class="field col-6">
								<label>Agência (sem o dígito)<span class="required">*</span></label>
								<input id="agencyNumber" name="agencyNumber" type="text" maxlength="6" placeholder="01234"
									ng-model="order.bankAccount.agencyNumber" ui-mask="99999" validator="number,required">
								<p>Importante: Informe o número da conta com todos os 0 à esquerda.</p>
							</div>
						</div>
						<div class="row">
							<div class="field col-6">
								<label>Conta (sem o dígito)<span class="required">*</span></label>
								<input id="accountNumber" name="accountNumber" type="text" maxlength="9" placeholder="012345"
									ng-model="order.bankAccount.accountNumber" ui-mask="************" validator="number,required"   ng-blur="validateBankAccountNumber()">
								<p>Importante: Informe o número da conta com todos os 0 à esquerda.</p>
							</div>
							<div class="field col-2">
								<label>Dígito da conta<span class="required">*</span></label>
								<input id="accountDigit" name="accountDigit" type="text" maxlength="2" placeholder="9"
									ng-model="order.bankAccount.accountDigit" ui-mask="0" validator="number,required">
							</div>
						</div>
					</fieldset>

					<fieldset>
						<div class="row">
							<div class="field col-6">
								<label>Nome</label>
								<input id="name" name="name" type="text" maxlength="9" readonly value="{{participant.name}}">
							</div>
							<div class="field col-6">
								<label>CNPJ/CPF</label>
								<input id="cpf" name="cpf" class="cpf" type="text" maxlength="9" readonly value="{{participant.document}}">
							</div>
						</div>
					</fieldset>

					<fieldset>
						<h2>@Model.ViewStrings.BalanceDescription para transferência</h2>
						<div class="row">
							<div class="field col-6">
								<label>Quantidade de @Model.ViewStrings.BalanceDescriptionLowerCase para transferência<span class="required">*</span></label>
								<input id="pointsAmount" name="pointsAmount" type="text" maxlength="9"
									ng-model="order.pointsAmount" ng-blur="calculateFees()" ui-number-mask validator="required">
							</div>
						</div>
						<div class="row">
							<div class="field col-6">
								<label>Total do custo de transferência (R$)</label>
								<input id="totalFeesAmount" name="totalFeesAmount" type="text" maxlength="9"
									ng-model="order.totalFeesAmount" ui-number-mask readonly>
							</div>
							<div class="field col-6">
								<label>Valor que será creditado na conta (R$)</label>
								<input id="transferAmount" name="transferAmount" type="text" maxlength="9"
									ng-model="order.transferAmount" ui-number-mask readonly>
							</div>
						</div>
					</fieldset>

					<div class="cart-products fees-details" ng-show="showFeesDetails">
						<h2>Detalhamento das taxas de transferência</h2>
						<table>
							<thead>
								<tr>
									<th>Custo da Transferência</th>
									<th>Taxa de Serviço</th>
									<th>Custo Total em @Model.ViewStrings.BalanceDescription</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>R$ {{ order.detailedFees.bankTransferFee | number:'2' }}</td>
									<td>R$ {{ (order.detailedFees.additionalFee + order.detailedFees.governmentFee) | number:'2' }}</td>
									<td>@coinPrefix {{ order.totalCost.points | number:'2' }} @coinSufix</td>
								</tr>
							</tbody>
						</table>
					</div>
				</article>

				<article>
					<fieldset>
						<h2>Regulamento do Cash Back</h2>
						<div class="text regulation">
							<div class="scroll">
								<div class="scroll-container">
									<div ng-bind-html="settings.regulation"></div>
								</div>
							</div>
						</div>
						<div class="field">
							<input id="accepted" name="accepted" type="checkbox" value="true" ng-model="order.regulationAccepted"
								required ng-required="true" />
							<label for="accepted"><span class="required">*</span> Li e aceito os termos do regulamento</label>
						</div>
					</fieldset>
				</article>

				<div>
					<button type="submit" class="button pull-right" ng-class="{'processing': processing}"
						ng-click="continue(cardForm)" ng-disabled="disableContinueButton()">Continuar</button>
				</div>
			</form>

			<form ng-if="finalStep">
				<article class="cart">
					<header>
						<h1 ng-if="!order.created">Confirmação do seu pedido</h1>
						<h1 ng-if="order.created">Pedido número {{ order.orderNumber }}</h1>
					</header>
					<div class="cart-products">
						<h2>Dados da transferência</h2>
						<table class="confirmation-table">
							<thead>
								<tr>
									<th>Forma de Transferência</th>
									<th colspan="2">Conta a ser Creditada</th>
									<th>@Model.ViewStrings.BalanceDescription p/ Transferir</th>
									<th>Custo</th>
									<th>Taxa de Serviço</th>
									<th>Valor a creditar</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>
										<span ng-if="order.bankTransferMethod == 'BANK_TRANSFER'">TED</span>
										<span ng-if="order.bankTransferMethod == 'PIX'">PIX</span>
									</td>
									<td colspan="2" class="account-info">
										<div ng-if="order.bankTransferMethod == 'TED'">
											<span>{{ participant.name }} - CNPJ/CPF: {{ participant.document }}</span><br>
											<span>{{ order.bankAccount.bankName }}</span><br>
											<span>{{ order.bankAccount.typeDescription }}</span><br>
											<span>Ag: {{ order.bankAccount.agencyNumber }}</span><br>
											<span>Cc: {{ order.bankAccount.accountNumber }}-{{ order.bankAccount.accountDigit }}</span>
										</div>
										<div ng-if="order.bankTransferMethod == 'PIX'">
											<span>Chave PIX: {{ order.bankAccountPixKey }}</span>
										</div>
									</td>
									<td style="width:100px">@coinPrefix {{ order.pointsAmount | number:'2' }} @coinSufix</td>
									<td>R$ {{ order.detailedFees.bankTransferFee | number:'2' }}</td>
									<td>R$ {{ (order.detailedFees.additionalFee + order.detailedFees.governmentFee) | number:'2' }}</td>
									<td>R$ {{ order.transferAmount | number:'2' }}</td>
								</tr>
							</tbody>
							<tfoot>
								<tr>
									<td colspan="4"></td>
									<td colspan="4">
										<table class="subtotal">
											<tr>
												<td>Total das Taxas</td>
												<td>R$ {{ order.totalFeesAmount | number:'2' }}</td>
											</tr>
											<tr>
												<td>Total de @Model.ViewStrings.BalanceDescriptionLowerCase a debitar</td>
												<td>@coinPrefix {{ order.totalCost.points | number:'2' }} @coinSufix</td>
											</tr>
										</table>
									</td>
								</tr>
							</tfoot>
						</table>
					</div>
					<div>
						<a class="back pull-left" ng-click="back()" ng-if="!order.created">Voltar</a>

						<button type="submit" class="button pull-right" ng-class="{'processing': processing}"
							ng-click="finalize()" ng-disabled="processing" ng-if="!order.created">Finalizar</button>
					</div>
				</article>
			</form>
		</div>
	</div>
</section>
@section ScriptsCustomizados {
<script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.10.6/moment-with-locales.min.js"></script>
<script src="@Url.Content("~/js/lib/moment/moment-timezone-with-data-2012-2022.min.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/extraservices/cashback.js")" type="text/javascript" asp-append-version="true"></script>
}
