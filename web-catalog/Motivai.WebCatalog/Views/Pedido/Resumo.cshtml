@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@model Motivai.WebCatalog.Models.Pages.Pedido.ResumoViewModel
@{
    ViewBag.Title = "Resumo do Pedido";
    Layout = "~/Views/Shared/_LayoutProcessoResgate.cshtml";
    var httpContext = RequestContextManager.Instance.CurrentContext;

    var coinPrefix = httpContext.Items["coinPrefix"];
    var coinSufix = httpContext.Items["coinSufix"];

    var cart = Model.OrderConfirmationPageModel.Cart;
}
<section id="content">
	<div class="container">
		<article class="cart-checkout">
			@if(Model.OrderConfirmationPageModel.ShowMessage)
			{
			<div class="info">
				<p>@Model.OrderConfirmationPageModel.Message</p>
			</div>
			}

			@if(cart.HasNotifications())
			{
			<div>
				<h1>Atenção</h1>
				<div class="info">
					<ul class="listing">
					@foreach(var notif in cart.Notifications) {
						<li>@notif</li>
					}
					</ul>
				</div>
			</div>
			}

			<header>
				@if(cart.OccurredError) {
					<h1>Pedido não finalizado</h1>
				} else {
					<h1>Pedido nº @cart.OrderNumber finalizado!</h1>
				}
			</header>

			@if(cart.OccurredError)
			{
				<div class="validation-summary-errors">
					<p>Seu pedido contém erros e não foi possível ser finalizado.<br>Os produtos não serão entregues e seus @coinPrefix @coinSufix serão preservados. Confira os motivos abaixo.</p>
					<p><span>@cart.ErrorMessage</span></p>
				</div>
			}

			@if(cart.HasProducts())
			{
			<div class="cart-products">
				<h2>Produtos selecionados para @Model.ViewStrings.BuyerDescription</h2>
				@if(cart.RequiresAllChildren) {
				<fieldset>
					<div class="field">
						<input type="checkbox" id="requiresAllChildren" name="requiresAllChildren" value="1" checked disabled="true" />
						<label for="requiresAllChildren"><strong style="font-size:1.1em">Venda casada: </strong>o pedido somente será fechado se todos os fabricantes aprovarem seus pedidos.</label>
					</div>
				</fieldset>
				}
				<table>
					<thead>
						<tr>
							<th colspan="2">Produto</th>
							<th>Qtde</th>
							<th>Valor Unit.</th>
							<th>Total</th>
						</tr>
					</thead>
					<tbody>
						@foreach(var item in cart.ProductsItems) {
						<tr>
							<td colspan="2">
								<div class="photo"><img src="@item.Image"></div>
								<div class="product">
									<h2>@item.Description</h2>
									<p>
										<span>Parceiro: @item.PartnerName</span>
										<br/><span>SKU: @item.SkuCode</span>
									@if(!String.IsNullOrEmpty(item.Model)) {
										<br/><span>@(item.LabelModel ?? "Modelo"): @item.Model</span>
									}
									@if(!String.IsNullOrEmpty(item.Color)) {
										<br/><span>@item.LabelColor: @item.Color</span>
									}
									@if(!String.IsNullOrEmpty(item.Size)) {
										<br/><span>@item.LabelSize: @item.Size</span>
									}
									@if(!String.IsNullOrEmpty(item.Voltage)) {
										<br/><span>@item.LabelVoltage: @item.Voltage</span>
									}
									</p>
									@if(item.Attributes != null) {
									<p>
										@foreach(var at in item.Attributes) {
											<span>@at.Name :
											@if(at.IsFile()) {
												<a href="@at.Value" target="_blank">Baixar</a>
											} else {
												<span>@at.Value</span>
											}
											</span><br/>
										}
									</p>
									}
									@if(!String.IsNullOrEmpty(item.EstimatedDeliveryDays)) {
										<p class="shippingDate">Previsão de entrega: <strong>@item.EstimatedDeliveryDays</strong></p>
									}
									@if(item.OccurredError) {
										<p class="partnerError">@item.ErrorMessage</p>
									}
									@if(item.HasNotifications) {
										foreach(var not in item.Notifications) {
											<p class="partnerError">@not</p>
										}
									}
								</div>
							</td>
							<td>@item.Quantity</td>
							@if(item.DynamicPrice) {
								<td colspan="2" style="text-align:center">@item.DynamicPriceDescription</td>
							} else {
								<td>
									@if(item.DiscountAmount > 0) {
										<span><strike>de @item.DiscountAmount.ToCurrency()</strike><br>por </span>
									}
									@item.UnitPrice.ToCurrency()
								</td>
								<td>
									<p class="price"><strong>@coinPrefix @item.Subtotal.ToCurrency() @coinSufix</strong></p>
								</td>
							}
						</tr>
						}
					</tbody>
				</table>
			</div>
			}

			@if(cart.HasVouchers())
			{
			<div class="cart-products">
				<h2>Vales selecionados para @Model.ViewStrings.BuyerDescription</h2>
				<table>
					<thead>
						<tr>
							<th colspan="2">Vale</th>
							<th>Qtde</th>
							<th>Valor Unit.</th>
							<th>Total</th>
						</tr>
					</thead>
					<tbody>
						@foreach(var item in cart.VouchersItems) {
						<tr>
							<td colspan="2">
								<div class="photo"><img src="@item.Image"></div>
								<div class="product">
									<h2>@item.Description</h2>
									<p>
										<span>Parceiro: @item.PartnerName</span>
										<br/><span>SKU: @item.SkuCode</span>
									@if(!String.IsNullOrEmpty(item.Model)) {
										<br/><span>Modelo: @item.Model</span>
									}
									@if(!String.IsNullOrEmpty(item.Color)) {
										<br/><span>Cor: @item.Color</span>
									}
									@if(!String.IsNullOrEmpty(item.Size)) {
										<br/><span>Tamanho: @item.Size</span>
									}
									@if(!String.IsNullOrEmpty(item.Voltage)) {
										<br/><span>Voltagem: @item.Voltage</span>
									}
									</p>
									@if(item.Attributes != null) {
									<p>
										@foreach(var at in item.Attributes) {
											<span>@at.Name :
											@if(at.IsFile()) {
												<a href="@at.Value" target="_blank">Baixar</a>
											} else {
												<span>@at.Value</span>
											}
											</span><br/>
										}
									</p>
									}
									@if(!String.IsNullOrEmpty(item.EstimatedDeliveryDays)) {
										<p class="shippingDate">Previsão de entrega: <strong>@item.EstimatedDeliveryDays</strong></p>
									}
									@if(item.OccurredError) {
										<p class="partnerError">@item.ErrorMessage</p>
									}
									@if(item.HasNotifications) {
										foreach(var not in item.Notifications) {
											<p class="partnerError">@not</p>
										}
									}
								</div>
							</td>
							<td>@item.Quantity</td>
							@if(item.DynamicPrice) {
								<td colspan="2" style="text-align:center">@item.DynamicPriceDescription</td>
							} else {
								<td>
									@if(item.DiscountAmount > 0) {
										<span><strike>de @item.DiscountAmount.ToCurrency()</strike><br>por </span>
									}
									@item.UnitPrice.ToCurrency()
								</td>
								<td><p class="price"><strong>@coinPrefix @item.Subtotal.ToCurrency() @coinSufix</strong></p></td>
							}
						</tr>
						}
					</tbody>
				</table>
			</div>
			}

			@if(cart.HasDiscount())
			{
			<div class="cart-discount">
				<h2>Cupom de desconto</h2>
				<table>
					<tbody>
						<tr>
							<td>
								<p>Código: <strong class="discount-coupon">@cart.DiscountCouponCode</strong></p>
							</td>
							<td>
								<table class="subtotal">
									<tr>
										<td>Desconto</td>
										<td>@coinPrefix @cart.DiscountAmount.ToCurrency() @coinSufix</td>
									</tr>
								</table>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
			}

			<div class="cart-total">
				<table>
					<tbody>
						<tr>
							<td>Total do pedido</td>
							<td>@coinPrefix @cart.Total.ToCurrency() @coinSufix</td>
						</tr>
					</tbody>
				</table>
			</div>

			<div class="email-alert">
				<h2>Desabilite seu anti-spam para o e-mail @cart.Customer.Email</h2>
				<p>Enviaremos um novo e-mail a cada evolução no andamento do seu pedido.</p>
			</div>

			@if(Model.OrderConfirmationPageModel.ShowCustomMessageAfterOrderConfirmation && Model.OrderConfirmationPageModel.HasCustomization())
            {
				<div class="conteudo-pedido-finalizado" style="margin-top:1em;margin-bottom:1em">
				@foreach (var custom in Model.OrderConfirmationPageModel.PageCustomizations) {
					if (custom.Active) {
						if (custom.IsProductType && cart.ContainsProductType(custom.ProductType)) {
							@Html.Raw(custom.Content);
						}else if (custom.IsLayoutType && cart.ContainsLayoutType(custom.LayoutType)) {
							@Html.Raw(custom.Content);
						}
					}
				}
				</div>
			}

			<a href="/" class="button">Voltar ao Catálogo</a>
		</article>

		<aside class="sidebar cart-brief">
			<nav>
				<header>
					<h1>Resumo</h1>
				</header>

				@if(Model.OrderConfirmationPageModel.EnableSelectshippingAddress)
				{
				<div class="list-address">
					<h2>Endereço de Entrega</h2>
					<div class="address">
						<h3>@cart.ShippingAddress.AddressName</h3>
						<p>@cart.ShippingAddress.Street, @cart.ShippingAddress.Number @cart.ShippingAddress.Complement
							<br>@cart.ShippingAddress.Neighborhood<br>@cart.ShippingAddress.City - @cart.ShippingAddress.State
							<br>CEP @cart.ShippingAddress.Cep
							<br>Referência: @cart.ShippingAddress.Reference
						</p>
					</div>
					<div class="recipient">
						<h3>Quem receberá a entrega</h3>
						<p>@cart.ShippingAddress.Receiver.Name<br>CPF @cart.ShippingAddress.Receiver.Cpf
						<br>@cart.ShippingAddress.Receiver.Telephone / @cart.ShippingAddress.Receiver.Cellphone
						<br>@cart.ShippingAddress.Receiver.Email</p>
					</div>
				</div>
				}

				<div class="list-cart">
					<h2>Produtos</h2>
					<p>
						@coinPrefix @cart.ProductsTotal.ToCurrency() @coinSufix
						@if(Model.OrderConfirmationPageModel.HasShippingCalculation) {
							<span><br/>+ @coinPrefix @cart.ProductsShippingCost.ToCurrency() @coinSufix (Frete)</span>
						}
					</p>
					@if(Model.OrderConfirmationPageModel.HasVouchers()) {
					<h2>Vales</h2>
					<p>
						@cart.VouchersTotal.ToCurrency() @coinSufix
						@if(Model.OrderConfirmationPageModel.HasShippingCalculation) {
							<span><br/>+ @coinPrefix @cart.VouchersShippingCost.ToCurrency() @coinSufix (Frete)</span>
						}
					</p>
					}
				</div>

				<div class="list-cart-total">
					<h2>Desconto</h2>
					<p>@coinPrefix @cart.DiscountAmount.ToCurrency() @coinSufix</p>
					@if(Model.OrderConfirmationPageModel.HasShippingCalculation) {
					<h2>Frete</h2>
					<p>@coinPrefix @cart.ShippingCost.ToCurrency() @coinSufix</p>
					}
					<h2>Total</h2>
					<p><strong>@coinPrefix @cart.Total.ToCurrency() @coinSufix</strong></p>
				</div>
			</nav>
		</aside>
	</div>
</section>

@section ScriptsCustomizados {
	<script>
		$(document).ready(function(){
			sendEvent('Processo Compra', 'Visualização', 'Pedido');
		});
	</script>
}
