@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
@{
	ViewBag.Title = "Política de Entrega e Troca";
	Layout = "~/Views/Shared/_LayoutProcessoResgate.cshtml";
	var acceptRequired = Model.CampaignSettings.Parametrizations.AcceptRegulationRequired;
}
<div class="container" ng-app="platformApp">
	<article class="terms" ng-controller="institutionalCtrl" ng-init="loadShippingPolicy()">
		<header>
			<h1>Leia atentamente as regras abaixo</h1>
			<h2>É necessário aceitá-las para prosseguir com o @Model.ViewStrings.BuyActionTypeDescriptionLowercase</h2>
		</header>
		<div loader-container is-loading="institutionalCtrl">
			<div class="text">
				<div class="scroll">
					<div class="scroll-container">
						<div ng-bind-html="shippingPolicy"></div>
					</div>
				</div>
			</div>
			@using(Html.BeginForm("ContinuarResgate", "Pedido", FormMethod.Post, new { @class = "acceptTerms", @onsubmit = "addLoaderToButton()" })) {
				<input type="hidden" name="content" value="{{contentId}}" />
				<input type="hidden" name="version" value="{{version}}" />
				<input id="termosAceito" name="termosAceito" type="checkbox" value="true" ng-model="accepted"
					@Html.Raw(acceptRequired ? "required ng-required=\"true\"": "") />
				<label for="termosAceito">@(acceptRequired ? "*" : "") Li e aceito os termos do regulamento</label>
				<button id="btn-continue" type="submit" @Html.Raw(acceptRequired ? "ng-disabled=\"!accepted\"" : "") class="button">Continuar com @Model.ViewStrings.BuyActionTypeDescriptionLowercase</button>
			}
		</div>
		@if(Model.CampaignSettings.Parametrizations.RequireCepToFinish()) {
		<a href="@Url.Action("ConfirmarEndereco", "Pedido")" class="back">Voltar</a>
		}
	</article>
</div>
@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/institutional/institutional-app.js")"></script>
}