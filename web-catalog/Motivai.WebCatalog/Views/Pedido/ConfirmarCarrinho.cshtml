@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@model Motivai.WebCatalog.Models.Pages.OrderConfirmationPageModel
@{
	ViewBag.Title = "Confirmação do Carrinho";
	ViewBag.ClassBody = "carrinho";
	Layout = "~/Views/Shared/_LayoutProcessoResgate.cshtml";

	var httpContext = RequestContextManager.Instance.CurrentContext;

	var coinPrefix  = httpContext.Items["coinPrefix"];
	var coinSufix = httpContext.Items["coinSufix"];

	var cart = Model.Cart;
	var hasVouchers = cart.VouchersItems != null && cart.VouchersItems.Count > 0;
}
<section id="content" ng-app="platformApp" ng-controller="orderCtrl" ng-init="init()">
	<div class="container order-confirmation">
		<article class="cart-checkout">
			@if(Model.ShowMessage)
			{
			<div class="info" style="margin-right:2.5%">
				<p>@Html.Raw(Model.Message)</p>
			</div>
			}

			@if(cart.OccurredError) {
			<div class="validation-summary-errors">
				<p>@cart.ErrorMessage</p>
			</div>
			}

			@if(cart.HasNotifications())
			{
			<div>
				<h1>Atenção</h1>
				<div class="info">
					<ul class="listing">
					@foreach(var notif in cart.Notifications) {
						<li>@notif</li>
					}
					</ul>
				</div>
			</div>
			}

			<header>
				<h1>Confirmação do seu pedido</h1>
			</header>

			@using(Html.BeginForm("FinalizarResgate", "Pedido", FormMethod.Post, new { @id = "cartForm" })) {
				<input type="hidden" id="timezoneoffset" name="timezoneoffset" />
				<input type="hidden" id="timezone" name="timezone" />

				<input type="hidden" name="contentId" value="{{contentId}}" />
				<input type="hidden" name="version" value="{{version}}" />
				<input type="hidden" name="accepted" type="checkbox" value="{{accepted}}" />

				<div class="register">
					<fieldset class="recipient">
						<h2>Confirme os dados de quem receberá o pedido no endereço escolhido</h2>

						<div class="row">
							<div class="col-xs-12 col-sm-4">
								<div class="field">
									<label for="cpf">CPF<span class="required">*</span></label>
									<input type="text" id="cpf" name="Cpf" class="cpf" required
										value="@cart.ShippingAddress.Receiver.Cpf" />
								</div>
							</div>
							<div class="col-xs-12 col-sm-8">
								<div class="field">
									<label for="nome">Nome<span class="required">*</span></label>
									<input type="text" id="nome" name="Name" class="nome" required
										value="@cart.ShippingAddress.Receiver.Name" />
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-xs-12 col-sm-4">
								<div class="field">
									<label for="telefone">Telefone</label>
									<input type="text" id="telefone" name="Telephone"
										value="@cart.ShippingAddress.Receiver.Telephone" />
								</div>
							</div>
							<div class="col-xs-12 col-sm-4">
								<div class="field">
									<label for="celular">Celular<span class="required">*</span></label>
									<input type="text" id="celular" name="Cellphone" class="celular" required
										value="@cart.ShippingAddress.Receiver.Cellphone" />
								</div>
							</div>
							<div class="col-xs-12 col-sm-4">
								<div class="field">
									<label for="email">E-mail<span class="required">*</span></label>
									<input type="text" id="email" name="Email" required
										value="@cart.ShippingAddress.Receiver.Email" />
								</div>
							</div>
						</div>
					</fieldset>
				</div>

				<br><br>

				@if(cart.ProductsItems != null && cart.ProductsItems.Count > 0)
				{
				<div class="cart-products">
					<h2>Produtos selecionados para @Model.ViewStrings.BuyActionTypeDescriptionLowercase</h2>
					@if(cart.RequiresAllChildren) {
					<fieldset>
						<div class="field">
							<input type="checkbox" id="requiresAllChildren" name="requiresAllChildren" value="1" checked disabled="true" />
							<label for="requiresAllChildren"><strong style="font-size:1.1em">Venda casada: </strong>o pedido somente será fechado se todos os fabricantes aprovarem seus pedidos.</label>
						</div>
					</fieldset>
					}

					<table>
						<thead>
							<tr>
								<th colspan="2">Produto</th>
								<th>Qtde</th>
								<th>Valor Unit.</th>
								<th>Total</th>
							</tr>
						</thead>
						<tbody>
							@foreach(var item in cart.ProductsItems) {
							<tr>
								<td colspan="2">
									<div class="photo"><img src="@item.Image"></div>
									<div class="product">
										<h2>@item.Description</h2>
										<p>
											<span>Parceiro: @item.PartnerName</span>
											<br/><span>SKU: @item.SkuCode</span>
										@if(!String.IsNullOrEmpty(item.Model)) {
											<br/><span>@(item.LabelModel ?? "Modelo"): @item.Model</span>
										}
										@if(!String.IsNullOrEmpty(item.Color)) {
											<br/><span>@(item.LabelColor ?? "Cor"): @item.Color</span>
										}
										@if(!String.IsNullOrEmpty(item.Size)) {
											<br/><span>@(item.LabelSize ?? "Tamanho"): @item.Size</span>
										}
										@if(!String.IsNullOrEmpty(item.Voltage)) {
											<br/><span>@(item.LabelVoltage ?? "Voltagem"): @item.Voltage</span>
										}
										</p>
										@if(item.Attributes != null) {
										<p>
											@foreach(var at in item.Attributes) {
												<span>@at.Name :
													@if(at.IsFile()) {
														<a href="@at.Value" target="_blank">Baixar</a>
													} else {
														<span>@at.Value</span>
													}
												</span><br/>
											}
										</p>
										}
										@if(!String.IsNullOrEmpty(item.EstimatedDeliveryDays)) {
											<p class="shippingDate">Previsão de entrega: <strong>@item.EstimatedDeliveryDays</strong></p>
										}
										@if(item.OccurredError) {
											<p class="partnerError">@item.ErrorMessage</p>
										}
										@if(item.HasNotifications) {
											foreach(var not in item.Notifications) {
												<p class="partnerError">@not</p>
											}
										}
									</div>
								</td>
								<td>@item.Quantity</td>
								@if(item.DynamicPrice) {
									<td colspan="2" style="text-align:center">@item.DynamicPriceDescription</td>
								} else {
									<td>
										@if(item.DiscountAmount > 0) {
											<span><strike>de @item.DiscountAmount.ToCurrency()</strike><br>por </span>
										}
										@item.UnitPrice.ToCurrency()
									</td>
									<td><p class="price"><strong>@coinPrefix @item.Subtotal.ToCurrency() @coinSufix</strong></p></td>
								}
							</tr>
							}
						</tbody>
					</table>
					@if(Model.HasShippingCalculation) {
					<p><strong>Tipo de entrega: </strong>de acordo com cada parceiro.<br></p>
					}
				</div>
				}

				if(hasVouchers)
				{
				<div class="cart-products">
					<h2>Vales selecionados para @Model.ViewStrings.BuyActionTypeDescriptionLowercase</h2>
					<table>
						<thead>
							<tr>
								<th colspan="2">Vale</th>
								<th>Qtde</th>
								<th>Valor Unit.</th>
								<th>Total</th>
							</tr>
						</thead>
						<tbody>
							@foreach(var item in cart.VouchersItems) {
							<tr>
								<td colspan="2">
									<div class="photo"><img src="@item.Image"></div>
									<div class="product">
										<h2>@item.Description</h2>
										<p>
											<span>Parceiro: @item.PartnerName</span>
											<br/><span>SKU: @item.SkuCode</span>
										@if(!String.IsNullOrEmpty(item.Model)) {
											<br/><span>@(item.LabelModel ?? "Modelo"): @item.Model</span>
										}
										@if(!String.IsNullOrEmpty(item.Color)) {
											<br/><span>@(item.LabelColor ?? "Cor"): @item.Color</span>
										}
										@if(!String.IsNullOrEmpty(item.Size)) {
											<br/><span>@(item.LabelSize ?? "Tamanho"): @item.Size</span>
										}
										@if(!String.IsNullOrEmpty(item.Voltage)) {
											<br/><span>@(item.LabelVoltage ?? "Voltagem"): @item.Voltage</span>
										}
										</p>
										@if(item.Attributes != null) {
										<p>
											@foreach(var at in item.Attributes) {
												<span>@at.Name :
													@if(at.IsFile()) {
														<a href="@at.Value" target="_blank">Baixar</a>
													} else {
														<span>@at.Value</span>
													}
												</span><br/>
											}
										</p>
										}
										@if(!String.IsNullOrEmpty(item.EstimatedDeliveryDays)) {
											<p class="shippingDate">Previsão de entrega: <strong>@item.EstimatedDeliveryDays</strong></p>
										}
										@if(item.OccurredError) {
											<p class="partnerError">@item.ErrorMessage</p>
										}
										@if(item.HasNotifications) {
											foreach(var not in item.Notifications) {
												<p class="partnerError">@not</p>
											}
										}
									</div>
								</td>
								<td>@item.Quantity</td>
								@if(item.DynamicPrice) {
									<td colspan="2" style="text-align:center">@item.DynamicPriceDescription</td>
								} else {
									<td>
										@if(item.DiscountAmount > 0) {
											<span><strike>de @item.DiscountAmount.ToCurrency()</strike><br>por </span>
										}
										@item.UnitPrice.ToCurrency()
									</td>
									<td><p class="price"><strong>@coinPrefix @item.Subtotal.ToCurrency() @coinSufix</strong></p></td>
								}
							</tr>
							}
						</tbody>
					</table>
					@if(Model.HasShippingCalculation) {
					<p><strong>Tipo de entrega: </strong>de acordo com cada parceiro.<br></p>
					}
				</div>
				}

				if(cart.HasDiscount())
				{
				<div class="cart-discount">
					<h2>Cupom de desconto</h2>
					<table>
						<tbody>
							<tr>
								<td>
									<p>Código: <strong class="discount-coupon">@cart.DiscountCouponCode</strong></p>
								</td>
								<td>
									<table class="subtotal">
										<tr>
											<td>Desconto</td>
											<td>@coinPrefix @cart.DiscountAmount.ToCurrency() @coinSufix</td>
										</tr>
									</table>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				}

				<div class="cart-total">
					<table>
						<tbody>
							<tr>
								<td>Total do pedido</td>
								<td>@coinPrefix @cart.Total.ToCurrency() @coinSufix</td>
							</tr>
						</tbody>
					</table>
				</div>

				<article class="terms">
					<header>
						<h1>Leia atentamente os termos da política de entrega e troca abaixo</h1>
						<h2>É necessário aceitá-las para prosseguir com o pedido</h2>
					</header>
					<div id="texto-regulamento"></div>
					<div loader-container is-loading="loadingPolicy">
						<div class="text">
							<div class="scroll">
								<div class="scroll-container">
									<div ng-bind-html="shippingPolicy"></div>
								</div>
							</div>
						</div>
					</div>
				</article>
			}
		</article>

		<aside class="sidebar cart-brief">
			<nav>
				<header>
					<h1>Resumo da @Model.ViewStrings.BuyActionTypeDescriptionLowercase</h1>
				</header>

				@if(Model.EnableSelectshippingAddress) {
				<div class="list-address">
					<h2>Endereço de Entrega</h2>
					<div class="address">
						<h3>@cart.ShippingAddress.AddressName</h3>
						<p>@cart.ShippingAddress.Street, @cart.ShippingAddress.Number @cart.ShippingAddress.Complement
							<br>@cart.ShippingAddress.Neighborhood<br>@cart.ShippingAddress.City - @cart.ShippingAddress.State
							<br>CEP @cart.ShippingAddress.Cep
							<br>Referência: @cart.ShippingAddress.Reference
						</p>
					</div>
				</div>
				}

				<div class="list-cart">
					<h2>Produtos</h2>
					<p>
						@coinPrefix @cart.ProductsTotal.ToCurrency() @coinSufix
						@if(Model.HasShippingCalculation) {
							<span><br/>+ @coinPrefix @cart.ProductsShippingCost.ToCurrency() @coinSufix (Frete)</span>
						}
					</p>
					@if(hasVouchers) {
					<h2>Vales</h2>
					<p>
						@coinPrefix @cart.VouchersTotal.ToCurrency() @coinSufix
						@if(Model.HasShippingCalculation) {
							<span><br/>+ @coinPrefix @cart.VouchersShippingCost.ToCurrency() @coinSufix (Frete)</span>
						}
					</p>
					}
				</div>

				<div class="list-cart-total">
					<h2>Desconto</h2>
					<p>@coinPrefix @cart.DiscountAmount.ToCurrency() @coinSufix</p>
					@if(Model.HasShippingCalculation) {
					<h2>Frete</h2>
					<p>@coinPrefix @cart.ShippingCost.ToCurrency() @coinSufix</p>
					}
					<h2>Total</h2>
					<p><strong>@coinPrefix @cart.Total.ToCurrency() @coinSufix</strong></p>

					<hr><br>

					<div class="terms">
						<form class="acceptTerms">
							<input id="acceptedTerms" name="acceptedTerms" type="checkbox" value="{{accepted}}" ng-click="onAcceptanceToggle()" />
							<label for="acceptedTerms"><span class="required">*</span> Li e aceito os termos da política de entrega e troca</label>
						</form>
						<a href="#texto-regulamento">Leia os termos do regulamento</a>

						<br><br>

						@if(Model.Marketplace)
						{
							<a id="btn-buy" class="button main-action" ng-click="showPointsPurchasePopup()" ng-disabled="!canSubmit">Finalizar @Model.ViewStrings.BuyActionTypeDescriptionLowercase</a>
						} else {
							<a id="btn-buy" class="button main-action" ng-click="confirmOrder()" ng-disabled="!canSubmit">Finalizar @Model.ViewStrings.BuyActionTypeDescriptionLowercase</a>
						}
					</div>
				</div>

			</nav>
			<a href="@Url.Action("Index", "Carrinho")" class="back">Voltar ao Carrinho</a>
		</aside>
	</div>
</section>

@section ScriptsCustomizados {
	<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"></script>
	<script src="@Url.Content("~/js/shopping-cart/order-finalizer.js")"></script>
	<script>
		//contorno para ajustar mascara de telefone fixo sem precisar alterar o form.min.js (la a mascara de telefone está com um digito a mais)
		$(document).ready(function() {
			$('#cpf').mask('999.999.999-99');
			$('#telefone').mask('(99)9999-9999');
		});
	</script>
}
