@using Motivai.SharedKernel.Helpers;
@using Motivai.WebCatalog.Helpers;
@model Motivai.WebCatalog.Models.Order.FactoryOrder
@{
	ViewBag.Title = "Pedido " + Model.OrderNumber;
	Layout = "~/Views/Shared/_LayoutParceiro.cshtml";
}
<div class="container" ng-app="platformApp" ng-controller="orderCtrl" ng-init="init()">
	<article class="cart">
        <br>
		<header>
			<h1>
				<span>Pedido nº @Model.OrderNumber</span>
			</h1>
			<h2 ng-if="factoryOrder.FactoryName">
				Fábrica: <span ng-bind="factoryOrder.FactoryName"></span>
			</h2>
		</header>
		<div loader-container is-loading="loading" div-style=""></div>
		<form ng-show="!loading">
			<input type="hidden" id="token" value="@Model.Token"/>
			<div ng-class="messageClass" ng-show="errorMessage">
				<p><span ng-bind="errorMessage"></span></p>
			</div>
			<div class="order-info">
				<div class="box">
					<div class="buyer" ng-if="factoryOrder.Buyer">
						<h3>Dados do Comprador</h3>
						<p><span ng-bind="factoryOrder.Buyer.Name"></span><br>
						<span ng-bind="factoryOrder.Buyer.Email"></span><br>
						<span ng-bind="factoryOrder.Buyer.Tellphone"></span> / <span ng-bind="factoryOrder.Buyer.Cellphone"></span></p>
					</div>
					<div class="address" ng-if="factoryOrder.ShippingAddress">
						<h3>Endereço de entrega: <span ng-bind="factoryOrder.ShippingAddress.AddressName"></span></h3>
						<p><span ng-bind="factoryOrder.ShippingAddress.Street"></span>, <span ng-bind="factoryOrder.ShippingAddress.Number"></span> <span ng-bind="factoryOrder.ShippingAddress.Complement"></span><br>
						<span ng-bind="factoryOrder.ShippingAddress.Neighborhood"></span> - <span ng-bind="factoryOrder.ShippingAddress.City"></span> - <span ng-bind="factoryOrder.ShippingAddress.State"></span><br>
						CEP <span ng-bind="factoryOrder.ShippingAddress.Cep"></span><br>
						Referência: <span ng-bind="factoryOrder.ShippingAddress.Reference"></span></p>
					</div>
					<div class="recipient" ng-if="factoryOrder.ShippingAddress && factoryOrder.ShippingAddress.Receiver && factoryOrder.ShippingAddress.Receiver.Name">
						<h3>Quem receberá a entrega</h3>
						<p><span ng-bind="factoryOrder.ShippingAddress.Receiver.Name"></span><br>
						<span ng-if="factoryOrder.ShippingAddress.Receiver.Cpf">
							CPF <span ng-bind="factoryOrder.ShippingAddress.Receiver.Cpf"></span><br>
						</span>
						<span ng-bind="factoryOrder.ShippingAddress.Receiver.Telephone"></span> / <span ng-bind="factoryOrder.ShippingAddress.Receiver.Cellphone"></span><br>
						<span ng-bind="factoryOrder.ShippingAddress.Receiver.Email"></span></p>
					</div>
				</div>
			</div>
			<div class="cart-products">
				<h2>Produtos Comprados</h2>
				<table>
					<thead>
						<tr>
							<th>SKU</th>
							<th colspan="2">Produto</th>
							<th>Qtde</th>
							<th>Preço Unit.</th>
							<th>Subtotal</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-if="factoryOrder" ng-repeat="item in factoryOrder.Items">
							<td><span ng-bind="::item.SkuCode"></span></td>
							<td colspan="2">
								<div class="photo"><img src="/assets/img/img-loader.gif" ng-src="{{::item.Image}}"></div>
								<div class="product">
									<h2><span ng-bind="::item.Description"></span></h2>
									<p>
										<span ng-if="item.SkuModel">Modelo: <span ng-bind="::item.SkuModel"></span><br></span>
										<span ng-if="item.SkuColor">Cor: <span ng-bind="::item.SkuColor"></span><br></span>
										<span ng-if="item.SkuSize">Tamanho: <span ng-bind="::item.SkuSize"></span><br></span>
										<span ng-if="item.SkuVoltage">Voltagem: <span ng-bind="::item.SkuVoltage"></span></span>
										<span ng-if="item.ErrorMessage">Status: <strong><span ng-bind="::item.ErrorMessage"></span></strong></span>
									</p>
									<p ng-if="item.Attributes">
										<span ng-repeat="attr in item.Attributes">
											<span ng-bind="attr.Name"></span>:
											<span ng-if="::(attr.Type != 'File')" ng-bind="::attr.Value"></span>
											<span ng-if="::(attr.Type == 'File')"><a href="{{attr.Value}}" target="_blank">Baixar</a></span><br>
										</span>
									</p>
								</div>
							</td>
							<td>
								<span ng-bind="::item.Quantity" ng-if="!item.CanBePriced">0</span>
								<div class="field" ng-if="item.CanBePriced">
									<input type="text" ng-model="item.Quantity" ui-number-mask="0"
										ng-model-options="{updateOn:'default',debounce:{default: 25}}" />
								</div>
							</td>
							<td>
								<p class="price" ng-if="!item.CanBePriced">
									<strong><span ng-bind="::(item.UnitPrice | currency:'')">0</span></strong>
								</p>
								<div class="field" ng-if="item.CanBePriced">
									<input type="text" ng-model="item.UnitPrice" ui-number-mask="2"
										ng-model-options="{updateOn:'default',debounce:{default: 25}}" />
								</div>
							</td>
							<td>
								<p class="price"><strong><span ng-bind="(item.UnitPrice * item.Quantity | currency:'')">0</span></strong></p>
							</td>
						</tr>
					</tbody>
				</table>
				<div ng-if="factoryOrder.CanBePriced">
					<button type="submit" class="button" ng-class="{'processing': sending}" ng-click="onFormSubmit()" ng-disabled="sending">Precificar</button>
				</div>
			</div>
		</form>
	</article>
</div>
@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"></script>
	<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/carrinho/pedido-fabricante.js")" type="text/javascript"></script>
}