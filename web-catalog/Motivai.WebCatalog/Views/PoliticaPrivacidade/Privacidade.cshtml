@{
	ViewBag.Title = "Política de Privacidade";
	Layout = "~/Views/Shared/_LayoutNewPrivacyPolicy.cshtml";
}
@section StylesCustomizados {
<link href="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.css")" media="all" rel="stylesheet" />
}
<article class="terms" ng-app="platformApp" ng-controller="NewPrivacyPolicyCtrl" ng-init="init()">
	<div ng-if="privacyPolicy.loaded">
		<header>
			<h1 style="margin-top: 50px; text-align: center;">Política de Privacidade</h1>
			<br>
			<h2>Leia atentamente as políticas abaixo</h2>
			<h3>É necessário aceitá-las para prosseguir ao catálogo</h3>
		</header>
		<div class="text">
			<div class="scroll">
				<div class="scroll-container">
					<p ng-bind-html="privacyPolicy.content"></p>
				</div>
			</div>
		</div>

		<div class="acceptTerms">
			<p ng-bind-html="privacyPolicy.cookieAcceptanceContent"></p>
			<p> Para saber mais detalhes sobre os tipos de cookies <a href="" ng-click="showCookiesDetails()">veja aqui</a>. </p>
			<p> Selecione quais cookies você deseja permitir. </p>
		</div>

		<form class="acceptTerms" id="acceptableForm" name="acceptableForm" novalidate>
			<input type="hidden" name="contentId" ng-model="privacyPolicy.contentId" />
			<input type="hidden" name="version" ng-model="privacyPolicy.version" />

			<input id="essentialsCookies" type="checkbox" ng-model="privacyPolicy.essentialsCookies" ng-change="verifyAcceptance()">
			<label for="essentialsCookies">Cookies essenciais <span class="required">*</span> </label>

			<input id="analyticsCookies" type="checkbox" ng-model="privacyPolicy.analyticsCookies" ng-change="verifyAcceptance()">
			<label for="analyticsCookies">Cookies Analytics</label>

			<input id="accepted" name="accepted" type="checkbox" ng-model="privacyPolicy.accepted" ng-change="verifyAcceptance()">
			<label for="accepted">Li e aceito os termos do regulamento <span class="required">*</span> </label>

			<button id="Continuar" type="submit" class="button" ng-click="registrarAceitePolitica()" ng-disabled="!privacyPolicy.canSubmit">Continuar</button>
		</form>
	</div>
</article>

@section ScriptsCustomizados {
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript" asp-append-version="true"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment-with-locales.min.js" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/new-privacy-policy/new-privacy-policy.js")" type="text/javascript" asp-append-version="true"></script>
}
