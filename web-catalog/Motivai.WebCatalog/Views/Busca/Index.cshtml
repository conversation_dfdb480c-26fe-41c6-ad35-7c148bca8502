@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel
@{
    ViewBag.Title = "Pesquisa";
    ViewBag.ClassBody = "resultado-de-busca";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"><a href="/">Home</a></li>
            <li><a href="#">Resultados de busca</a></li>
            <li id="breadcrumbQuery"></li>
        </ul>
    </div>
</div>
<section id="content">
    <div class="container" ng-controller="searchCtrl">
        <aside class="sidebar">
            <nav>
                <form id="filtroPontos" class="price-filter">
                    <fieldset>
                        <h2>Valor</h2>
                        <div class="field">
                            <input type="text" name="minimo" placeholder="Mínimo" ng-model="ptMin" ui-number-mask="0">
                        </div>
                        <div class="field">
                            <input type="text" name="maximo" placeholder="Máximo" ng-model="ptMax" ui-number-mask="0">
                        </div>
                    </fieldset>
                    <button type="submit" class="button" ng-click="filtrarPontos(ptMin, ptMax)">Filtrar</button>
                </form>
                <div class="list-category" ng-if="departments && departments.length > 0">
                    <h2>Departamentos</h2>
                    <ul>
                        <li ng-repeat="dep in departments | orderBy:'Description'" ng-class="{'active': dep.isSelected}">
                            <a href="#" ng-click="toggleDepartment(dep)"><span ng-bind="::dep.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-if="categorias && categorias.length > 0">
                    <h2>Categorias</h2>
                    <ul>
                        <li ng-repeat="cat in categorias | orderBy:'Description'" ng-class="{'active': cat.isSelected}">
                            <a href="#" ng-click="toggleCategory(cat)"><span ng-bind="::cat.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-if="subcategories && subcategories.length > 0">
                    <h2>Subcategorias</h2>
                    <ul>
                        <li ng-repeat="cat in subcategories | orderBy:'Description'" ng-class="{'active': cat.isSelected}">
                            <a href="#" ng-click="toggleSubcategory(cat)"><span ng-bind="::cat.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-if="products && products.length > 0">
                    <h2>Parceiros</h2>
                    <ul>
                        <li ng-repeat="par in products | orderBy:'Description'" ng-class="{'active': par.isSelected}">
                            <a href="#" ng-click="togglePartner(par)"><span ng-bind="::par.Description"></span></a>
                        </li>
                    </ul>
                </div>

                <div class="list-category" ng-show="manufacturers && manufacturers.length > 0" >
                    <h2>Fabricantes</h2>
                    <ul>
                        <li ng-repeat="fab in manufacturers | orderBy:'Description'" ng-class="{'active': fab.isSelected}">
                            <a href="#" ng-click="toggleManufacturer(fab)"><span ng-bind="::fab.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-show="colors && colors.length > 0">
                    <h2>Cores</h2>
                    <ul>
                        <li ng-repeat="cor in colors | orderBy:'Description'" ng-class="{'active': cor.isSelected}">
                            <a href="#" ng-click="toggleColor(cor)"><span ng-bind="::cor.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-show="voltages && voltages.length > 0">
                    <h2>Voltagem</h2>
                    <ul>
                        <li ng-repeat="volt in voltages | orderBy:'Description'" ng-class="{'active': volt.isSelected}">
                            <a href="#" ng-click="toggleVoltage(volt)"><span ng-bind="::volt.Description"></span></a>
                        </li>
                    </ul>
                </div>
            </nav>
            <div class="banner loja-especial" ng-if="lojaEspecial">
                <a href="/Loja/Index/{{::lojaEspecial.Url}}">
                    <img src="/assets/img/img-loader.gif" blazy-src="{{::lojaEspecial.LinkBannerImageUrl}}/373/373" caption="{{::lojaEspecial.Name}}" />
                </a>
            </div>
        </aside>

        <article class="list-products">
            @* <div class="filters">
                <div class="orderBy">
                    <select id="orderBy" name="orderBy" class="autostart" placeholder="Ordenar por..." data-placeholder="Ordenar por..."
                            ng-model="sortBy" ng-change="changeSortFilter()">
                        <option value="MostReedems" selected>Mais @Model.ViewStrings.MostRedeemedProductsDescriptionLowerCase</option>
                        <option value="LessPoints">Menos @Model.ViewStrings.BalanceDescriptionLowerCase</option>
                        <option value="MostPoints">Mais @Model.ViewStrings.BalanceDescriptionLowerCase</option>
                    </select>
                </div>
                <div class="pageItens">
                    <select id="pageItens" name="pageItens" class="autostart" placeholder="Itens por página" data-placeholder="Itens por página" onchange="mudaPageSize()">
                        <option value="40" selected>40 itens por página</option>
                        <option value="80">80 itens por página</option>
                        <option value="120">120 itens por página</option>
                    </select>
                </div>
                <div class="viewType">
                    <ul>
                        <li class="active"><a href="#" class="gridView">Ver em Grade</a></li>
                        <li><a href="#" class="listView">Ver em Lista</a></li>
                    </ul>
                </div>
            </div> *@

            <div loader-Container is-loading="loadingProds">
                <ul class="products" style="text-align:left;" ng-show="!loadingProds">
                    <product-showcase ng-repeat="prod in products" product="prod"></product-showcase>
                </ul>
                <div ng-show="!hasProducts">
                    <h2>Nenhum produto encontrado...</h2>
                </div>
            </div>

            <div class="pagination" ng-show="!loadingProds && hasProducts">
                <ul style="padding-bottom:5px;">
                    <li class="previous" ng-class="{disabled: !showPrevious}" ng-click="!showPrevious||goToPage(currentPage-1)"><a href="#">Anterior</a></li>
                    <li ng-repeat="pLink in pageLinks" ng-class="{active: pLink === currentPage}" ng-click="goToPage(pLink)"><a href="#">{{::pLink}}</a></li>
                    <li class="next" ng-class="{disabled: !showNext}" ng-click="!showNext||goToPage(currentPage+1)"><a href="#">Próximo</a></li>
                </ul>
            </div>
        </article>
    </div>
</section>

@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/catalog/catalog-services.js")" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/catalog/catalog-app.js")" asp-append-version="true"></script>
}
