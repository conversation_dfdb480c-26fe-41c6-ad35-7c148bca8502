@{
	ViewBag.Title = "Primeiro Acesso - Cartão";
	Layout = "~/Views/Shared/_LayoutPrimeiroAcesso.cshtml";
}

@section StylesCustomizados {
<link href="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.css")" media="all" rel="stylesheet" />
}
<article class="register" ng-app="platformApp" ng-controller="PrimeiroAcessoCtrl">
	<form id="registerCardForm" name="registerCardForm" novalidate>
		<fieldset class="personalData">
			<div class="field">
				<label for="nome-cartao">Nome Cartão<span class="required">*</span></label>
				<input id="nome-cartao" name="holderName" type="text" validator="required"
					ng-model="dadosCartao.nome_impressao">
			</div>
			<div class="field">
				<label for="numero-cartao">Numero do Cartão<span class="required">*</span></label>
				<input id="numero-cartao" name="number" type="text" validator="number,required"
					placeholder="0000 0000 0000 0000" maxlength="16" ng-model="dadosCartao.numero_cartao">
			</div>
			<div class="field">
				<label for="expiracao-cartao">Expiração do Cartão<span class="required">*</span></label>
				<input id="expiracao-cartao" name="expiration" type="text" ui-date-mask="MM/YY" parse="false"
					validator="required" placeholder="MM/AA" maxlength="5" ng-model="dadosCartao.expiration">
			</div>
			<div class="field">
				<label for="cvv-cartao">CVV<span class="required">*</span></label>
				<input id="cvv-cartao" name="cvv" type="text" validator="number,required" placeholder="000"
					maxlength="3" ng-model="dadosCartao.cvv">
			</div>
		</fieldset>
		<button type="submit" class="button" ng-click="registrarCartaoDireto(dadosCartao)">Continuar</button>
	</form>
</article>
@section ScriptsCustomizados {
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript"
	asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript"
	asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript"
	asp-append-version="true"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment-with-locales.min.js"
	asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"
	asp-append-version="true"></script>
<script src="@Url.Content("~/js/endereco/enderecoApi.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/first-access/first-access.js")" type="text/javascript"
	asp-append-version="true"></script>
}
