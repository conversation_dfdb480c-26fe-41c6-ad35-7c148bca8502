@model Motivai.WebCatalog.Models.Pages.FirstAccess.FirstAccessStepsViewModel;
@using Motivai.WebCatalog.Models.Pages;
@using Motivai.WebCatalog.Helpers.Html;
@{
	ViewBag.Title = "Primeiro Acesso - Cadastro";
	Layout = "~/Views/Shared/_LayoutPrimeiroAcesso.cshtml";

	var firstAccessSettings = (FirstAccessPageModel) Model.PageModel;
	var ngRequired = @"ng-required=""required""";
	var ngDisabled = @"ng-disabled=""true""";
}
@section StylesCustomizados {
	<link href="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.css")" media="all" rel="stylesheet" />
	<style>
		.ng-invalid, .chosen-required .chosen-container { border-color: #d05151 !important; border-width: 2px !important; }
		.CustomValidationError,.validation-invalid { color: #F83C3C; }
		.chosen-container { margin-bottom:0px; }
	</style>
}
<article class="register" ng-app="platformApp" ng-controller="PrimeiroAcessoCtrl" ng-init="init()">
	<form id="registerForm" name="registerForm" novalidate>
		<fieldset class="personalData" ng-if="canViewDocumentField">
			<div class="field">
				<label for="selectedPersonType">Selecione o tipo de pessoa<span class="required">*</span></label>
				<select id="selectedPersonType" name="selectedPersonType" placeholder="Escolha" data-placeholder="Escolha"
					ng-model="selectedPersonType" ng-change="toggleUserDocument(selectedPersonType)">
					<option value="">Escolha</option>
					<option value="cpf">Pessoa Fisica</option>
					<option value="cnpj">Pessoa Juridica</option>
				</select>
			</div>
		</fieldset>

		<fieldset class="personalData" ng-if="dadosCadastro.IsPessoaFisica">
			<h1>@firstAccessSettings.ParticipantSettings.PersonalDataTitle</h1>
			@if(firstAccessSettings.ParticipantSettings.PersonalData.Name.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.PersonalData.Name, "dadosCadastro.Name", "nome", "Ex.: Nome Sobrenome", "150")
			}
			@if(firstAccessSettings.ParticipantSettings.PersonalData.Cpf.Visible) {
				@Html.CpfField(firstAccessSettings.ParticipantSettings.PersonalData.Cpf, "dadosCadastro.Cpf", "cpf")
			}
			@if(firstAccessSettings.ParticipantSettings.PersonalData.Rg.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.PersonalData.Rg, "dadosCadastro.Rg", "rg", "Ex.: 1234567890", "12")
			}
			@if(firstAccessSettings.ParticipantSettings.PersonalData.BirthDate.Visible) {
			<div class="field">
				@Html.Label(firstAccessSettings.ParticipantSettings.PersonalData.BirthDate, "data-nascimento")
				<input id="data-nascimento" name="data-nascimento" type="text" maxlength="10"
					moment-picker="dadosCadastro.BirthDate" start-date="startBirthDate"
					ng-model="dadosCadastro.BirthDate" ng-model-options="{updateOn:'blur'}" locale="pt-br"
					format="DD/MM/YYYY" start-view="decade" keyboard="true">
			</div>
			}

			@if(firstAccessSettings.ParticipantSettings.PersonalData.MaritalStatus.Visible)
			{
			<div class="field" ng-class="{'chosen-required':registerForm.MaritalStatus.$error.required && registerForm.MaritalStatus.$dirty}">
				@Html.Label(firstAccessSettings.ParticipantSettings.PersonalData.MaritalStatus, "estadocivil")
				<select id="estadocivil" name="estadocivil" placeholder="Escolha" data-placeholder="Escolha"
					ng-model="dadosCadastro.MaritalStatus" ng-options="estado for estado in estadosCivil | orderBy:estado"
					@(firstAccessSettings.ParticipantSettings.PersonalData.MaritalStatus.Required ? ngRequired : "")>
					<option value="" selected="selected" disabled="disabled">Escolha</option>
				</select>
				<span class="val-msg" ng-show="registerForm.MaritalStatus.$error.required">
					<span class="CustomValidationError validationFieldRequired" style="display:inline;">Estado civil é obrigatório</span>
				</span>
			</div>
			}
			@if(firstAccessSettings.ParticipantSettings.PersonalData.Gender.Visible) {
			<div class="field" ng-class="{'chosen-required':registerForm.Gender.$error.required && registerForm.Gender.$dirty}">
				@Html.Label(firstAccessSettings.ParticipantSettings.PersonalData.Gender, "sexo")
				<select id="sexo" name="sexo" placeholder="Escolha" data-placeholder="Escolha"
					ng-model="dadosCadastro.Gender" @(firstAccessSettings.ParticipantSettings.PersonalData.Gender.Required ? ngRequired : "")
					@(firstAccessSettings.ParticipantSettings.PersonalData.Gender.Disabled ? ngDisabled : "")>
					<option value="" selected="selected" disabled="disabled">Escolha</option>
					<option value="M">Masculino</option>
					<option value="F">Feminino</option>
				</select>
				<span class="val-msg" ng-show="registerForm.Gender.$error.required">
					<span class="CustomValidationError validationFieldRequired" style="display:inline;">Sexo é obrigatório</span>
				</span>
			</div>
			}
		</fieldset>

		<fieldset class="corporateData" ng-if="dadosCadastro.IsPessoaJuridica">
			<h1>Dados da Empresa</h1>
			@if(firstAccessSettings.ParticipantSettings.PersonalData.Cnpj.Visible) {
				@Html.CnpjField(firstAccessSettings.ParticipantSettings.PersonalData.Cnpj, "dadosCadastro.Cnpj", "cnpj")
			}
			@if(firstAccessSettings.ParticipantSettings.PersonalData.CompanyName.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.PersonalData.CompanyName, "dadosCadastro.CompanyName", "razao-social", "Ex.: Noma da empresa Ltda", "150")
			}
			@if(firstAccessSettings.ParticipantSettings.PersonalData.TradingName.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.PersonalData.TradingName, "dadosCadastro.Name", "nome-fantasia", "Ex.: Noma da empresa", "150")
			}
			@if(firstAccessSettings.EnableAccountRepresentative) {
			   <div class="field">
					<label for="cpf-pedidos">CPF para pedidos<span class="required">*</span></label>
					<input id="cpf-pedidos" name="cpf-pedidos" type="text"  placeholder="123.456.789-01" class="cpf" maxlength="14"
						ng-model="dadosCadastro.AccountRepresentative.Document" ng-blur="pesquisarPessoaPeloDocumento()" validator="required,cpf">
				</div>
				<div  class="field">
					<label for="nome-pedidos">Nome<span class="required">*</span></label>
					<input id="nome-pedidos" name="nome-pedidos" type="text"  ng-model="dadosCadastro.AccountRepresentative.Name">
				</div>
				<div  class="field">
					<label for="email-pedidos">E-mail<span class="required">*</span></label>
					<input id="email-pedidos" name="email-pedidos" type="text" ng-model="dadosCadastro.AccountRepresentative.Email">
				</div>
				<div  class="field">
					<label for="telefone-pedidos">Telefone<span class="required">*</span></label>
					<input id="telefone-pedidos" name="telefone-pedidos" type="text" ng-model="dadosCadastro.AccountRepresentative.Telephone">
				</div>
			}

			@if(firstAccessSettings.ParticipantSettings.PersonalData.StateInscription.Visible || firstAccessSettings.ParticipantSettings.PersonalData.StateInscriptionUf.Visible)
			{
			<div class="field state-inscription-exempt">
				<input id="stateInscriptionExempt" name="stateInscriptionExempt" type="checkbox" value="true"
					ng-model="dadosCadastro.StateInscriptionExempt" ng-change="exempt=!exempt">
				<label for="stateInscriptionExempt">Inscrição estadual isenta?</label>
			</div>
			}

			<div ng-if="!exempt">
				@if(firstAccessSettings.ParticipantSettings.PersonalData.StateInscription.Visible) {
					<div class="field"  ng-class="{'chosen-required':registerForm.StateInscription.$error.required && registerForm.StateInscription.$dirty}">
						@Html.Label(firstAccessSettings.ParticipantSettings.PersonalData.StateInscription, "ie")
						<input id="ie" name="ie" type="text" placeholder="Ex.: 1234567890" maxlength="20"
							ng-model="dadosCadastro.StateInscription" ui-br-ie-mask="dadosCadastro.StateInscriptionUf"
							ui-hide-group-sep ng-disabled="exempt"
							@(firstAccessSettings.ParticipantSettings.PersonalData.StateInscription.Required ? ngRequired : "")
							@(firstAccessSettings.ParticipantSettings.PersonalData.StateInscription.Disabled ? ngDisabled : "")
						>
						@if(firstAccessSettings.ParticipantSettings.PersonalData.StateInscription.Required) {
						<span class="val-msg" ng-show="registerForm.StateInscription.$error.required">
							<span class="CustomValidationError validationFieldRequired" style="display:inline;">Inscrição Estadual é obrigatória</span>
						</span>
						}
					</div>
				}

				@if(firstAccessSettings.ParticipantSettings.PersonalData.StateInscriptionUf.Visible)
				{
				<div class="field">
					@Html.Label(firstAccessSettings.ParticipantSettings.PersonalData.StateInscriptionUf, "ieEstado")
					<select id="ieEstado" name="ieEstado" ng-model="dadosCadastro.StateInscriptionUf"
						@(firstAccessSettings.ParticipantSettings.PersonalData.StateInscriptionUf.Required ? ngRequired : "")
						@(firstAccessSettings.ParticipantSettings.PersonalData.StateInscriptionUf.Disabled ? ngDisabled : "")>
						<option value="AC">AC</option>
						<option value="AL">AL</option>
						<option value="AP">AP</option>
						<option value="AM">AM</option>
						<option value="BA">BA</option>
						<option value="CE">CE</option>
						<option value="DF">DF</option>
						<option value="ES">ES</option>
						<option value="GO">GO</option>
						<option value="MA">MA</option>
						<option value="MT">MT</option>
						<option value="MS">MS</option>
						<option value="MG">MG</option>
						<option value="PR">PR</option>
						<option value="PB">PB</option>
						<option value="PA">PA</option>
						<option value="PE">PE</option>
						<option value="PI">PI</option>
						<option value="RJ">RJ</option>
						<option value="RN">RN</option>
						<option value="RS">RS</option>
						<option value="RO">RO</option>
						<option value="RR">RR</option>
						<option value="SC">SC</option>
						<option value="SE">SE</option>
						<option value="SP">SP</option>
						<option value="TO">TO</option>
					</select>
				</div>
				}
			</div>
		</fieldset>

		@if(firstAccessSettings.EnableMetadata) {
			<fieldset class="personalData">
			@foreach(var field in firstAccessSettings.ParticipantSettings.Metadata) {
				<div class="field field-metadata">
					<label for="@field.property">@field.description</label>
					<input type="text" name="@field.property" placeholder="@field.placeholder"
						ng-disabled="true" ng-required="@field.required"
						ng-model="dadosCadastro.Metadata['@field.property']" ng-change="dadosCadastro.Metadata['@field.property']=$event" />
				</div>
			}
			</fieldset>
		}

		@if(firstAccessSettings.ParticipantSettings.EnableHomeAddress)
		{
		<fieldset class="address">
			<h1>@firstAccessSettings.ParticipantSettings.HomeAddressTitle</h1>
			<input type="hidden" name="nomeResidencial" value="Casa" ng-model="dadosCadastro.HomeAddress.Name">
			@if(firstAccessSettings.ParticipantSettings.Address.Street.Visible) {
			<div class="field">
				@Html.Label(firstAccessSettings.ParticipantSettings.Address.Cep, "cep")
				<input id="cep" name="cep" type="text" placeholder="Ex.: 01234-567" required ng-blur="pesquisaCep(dadosCadastro.HomeAddress)"
					ng-model="dadosCadastro.HomeAddress.Cep" ng-minlength="9" validator="required,cep" ui-br-cep-mask>
				<span ng-class="{'CustomValidationError validationFieldRequired': dadosCadastro.HomeAddress.cepSearchError}"
					ng-bind="dadosCadastro.HomeAddress.cepSearchMessage"></span>
			</div>
			}
			@if(firstAccessSettings.ParticipantSettings.Address.Street.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.Street, "dadosCadastro.HomeAddress.Street", "endereco", "Ex.: Av. Paulista", "150", "!dadosCadastro.HomeAddress.FilledManually")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.Number.Visible) {
				@Html.NumberField(firstAccessSettings.ParticipantSettings.Address.Number, "dadosCadastro.HomeAddress.Number", "numero", "Ex.: 1500", "10")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.Complement.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.Complement, "dadosCadastro.HomeAddress.Complement", "complemento", "Ex.: Apto. 12 Bloco A", "100")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.Neighborhood.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.Neighborhood, "dadosCadastro.HomeAddress.Neighborhood", "bairro", "Ex.: Bela Vista", "100", "!dadosCadastro.HomeAddress.FilledManually")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.State.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.State, "dadosCadastro.HomeAddress.State", "estado", "Ex.: São Paulo", "50", "!dadosCadastro.HomeAddress.FilledManually")
				@*<select-state class="platform-chosen" select-id="estado" state="endereco.State" on-update="endereco.State=$value"
					required="true" disabled="!endereco.FilledManually"></select-state>*@
			}
			@if(firstAccessSettings.ParticipantSettings.Address.City.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.City, "dadosCadastro.HomeAddress.City", "cidade", "Ex.: São Paulo", "50", "!dadosCadastro.HomeAddress.FilledManually")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.Reference.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.Reference, "dadosCadastro.HomeAddress.Reference", "pontoReferencia", "Ex.: Condomínio Edifício Paulista", "100")
			}
		</fieldset>
		}

		@if(firstAccessSettings.ParticipantSettings.EnableBusinessAddress) {
		<fieldset class="address">
			<h1>@firstAccessSettings.ParticipantSettings.BusinessAddressTitle</h1>
			<input type="hidden" name="nomeComercial" value="Trabalho" ng-model="dadosCadastro.CommercialAddress.Name">
			@if(firstAccessSettings.ParticipantSettings.Address.Street.Visible) {
			<div class="field">
				@Html.Label(firstAccessSettings.ParticipantSettings.Address.Cep, "cepComercial")
				<input id="cepComercial" name="cepComercial" type="text" placeholder="Ex.: 01234-567" ng-blur="pesquisaCep(dadosCadastro.CommercialAddress)"
					ng-model="dadosCadastro.CommercialAddress.Cep" ng-minlength="9" validator="nonrequired,cep" ui-br-cep-mask>
				<span ng-class="{'CustomValidationError validationFieldRequired': dadosCadastro.CommercialAddress.cepSearchError}" style="display:inline;"
					ng-bind="dadosCadastro.CommercialAddress.cepSearchMessage"></span>
			</div>
			}
			@if(firstAccessSettings.ParticipantSettings.Address.Street.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.Street, "dadosCadastro.CommercialAddress.Street", "enderecoComercial", "Ex.: Av. Paulista", "150", "!dadosCadastro.CommercialAddress.FilledManually")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.Number.Visible) {
				@Html.NumberField(firstAccessSettings.ParticipantSettings.Address.Number, "dadosCadastro.CommercialAddress.Number", "numeroComercial", "Ex.: 1500", "10")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.Complement.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.Complement, "dadosCadastro.CommercialAddress.Complement", "complementoComercial", "Ex.: Apto. 12 Bloco A", "100")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.Neighborhood.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.Neighborhood, "dadosCadastro.CommercialAddress.Neighborhood", "bairroComercial", "Ex.: Bela Vista", "100", "!dadosCadastro.CommercialAddress.FilledManually")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.State.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.State, "dadosCadastro.CommercialAddress.State", "estadoComercial", "Ex.: São Paulo", "50", "!dadosCadastro.CommercialAddress.FilledManually")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.City.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.City, "dadosCadastro.CommercialAddress.City", "cidadeComercial", "Ex.: São Paulo", "50", "!dadosCadastro.CommercialAddress.FilledManually")
			}
			@if(firstAccessSettings.ParticipantSettings.Address.Reference.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Address.Reference, "dadosCadastro.CommercialAddress.Reference", "pontoReferenciaComercial", "Ex.: Condomínio Edifício Paulista", "100")
			}
		</fieldset>
		}

		@if(firstAccessSettings.ParticipantSettings.EnableTelephones) {
		<fieldset class="phones">
			<h1>@firstAccessSettings.ParticipantSettings.TelephonesTitle</h1>
			@if(firstAccessSettings.ParticipantSettings.Telephones.MainPhone.Visible) {
				@Html.PhoneField(firstAccessSettings.ParticipantSettings.Telephones.MainPhone, "dadosCadastro.Contact.MainPhone", "fonePrincipal", "Ex.: (12) 3456-7890", "14")
			}
			@if(firstAccessSettings.ParticipantSettings.Telephones.HomePhone.Visible) {
				@Html.PhoneField(firstAccessSettings.ParticipantSettings.Telephones.HomePhone, "dadosCadastro.Contact.HomePhone", "foneResidencial", "Ex.: (12) 3456-7890", "14")
			}
			@if(firstAccessSettings.ParticipantSettings.Telephones.MobilePhone.Visible) {
				@Html.PhoneField(firstAccessSettings.ParticipantSettings.Telephones.MobilePhone, "dadosCadastro.Contact.MobilePhone", "celular", "Ex.: (12) 93456-7890", "15")
			}
			@if(firstAccessSettings.ParticipantSettings.Telephones.PhoneCarrier.Visible) {
			<div class="field" ng-class="{'chosen-required':registerForm.MobileOperator.$error.required && registerForm.MobileOperator.$dirty}">
				@Html.Label(firstAccessSettings.ParticipantSettings.Telephones.PhoneCarrier, "operadora")
				<select id="operadora" name="operadora" placeholder="Escolha" data-placeholder="Escolha"
					ng-model="dadosCadastro.Contact.MobileOperator" ng-options="oper for oper in operadoras | orderBy:oper"
					@(firstAccessSettings.ParticipantSettings.Telephones.PhoneCarrier.Required ? ngRequired : "")
					@(firstAccessSettings.ParticipantSettings.Telephones.PhoneCarrier.Disabled ? ngDisabled : "")>
					<option value="" selected="selected" disabled="disabled">Escolha</option>
				</select>
				<span class="val-msg" ng-show="registerForm.MobileOperator.$error.required">
					<span class="CustomValidationError validationFieldRequired" style="display:inline;">Operadora é obrigatório</span>
				</span>
			</div>
			}
			@if(firstAccessSettings.ParticipantSettings.Telephones.BusinessPhone.Visible) {
				@Html.PhoneField(firstAccessSettings.ParticipantSettings.Telephones.BusinessPhone, "dadosCadastro.Contact.CommercialPhone", "foneComercial", "Ex.: (12) 3456-7890", "14")
			}
			@if(firstAccessSettings.ParticipantSettings.Telephones.TalkTo.Visible) {
				@Html.Field(firstAccessSettings.ParticipantSettings.Telephones.TalkTo, "dadosCadastro.Contact.TalkTo", "falarCom", "Ex.: Fulano", "20")
			}
		</fieldset>
		}
		@if(firstAccessSettings.ParticipantSettings.EnableEmails) {
		<fieldset class="emails">
			<h1>@firstAccessSettings.ParticipantSettings.EmailsTitle</h1>
			@if(firstAccessSettings.ParticipantSettings.Emails.MainEmail.Visible) {
				@Html.EmailField(firstAccessSettings.ParticipantSettings.Emails.MainEmail, "dadosCadastro.Contact.MainEmail", "emailPrincipal")
			}
			@if(firstAccessSettings.ParticipantSettings.Emails.PersonalEmail.Visible) {
				@Html.EmailField(firstAccessSettings.ParticipantSettings.Emails.PersonalEmail, "dadosCadastro.Contact.PersonalEmail", "emailPessoal")
			}
			@if(firstAccessSettings.ParticipantSettings.Emails.BusinessEmail.Visible) {
				@Html.EmailField(firstAccessSettings.ParticipantSettings.Emails.BusinessEmail, "dadosCadastro.Contact.CommercialEmail", "emailComercial")
			}
			@if(firstAccessSettings.HasNewsletters) {
			<div class="field">
				<input id="promo-GP" name="promo-GP" type="checkbox" value="true" ng-model="dadosCadastro.GpInf">
				<label for="promo-GP">Desejo receber informa&ccedil;&otilde;s e promo&ccedil;&otilde;es por e-mail.</label><br>
				<input id="promo-partners" name="promo-partners" type="checkbox" value="true" ng-model="dadosCadastro.GpPartnerInf">
				<label for="promo-partners">Desejo receber informa&ccedil;&otilde;s e promo&ccedil;&otilde;es de parceiros por e-mail.</label>
			</div>
			}
		</fieldset>
		}

		@if(firstAccessSettings.ParticipantSettings.EnablePassword) {
		<fieldset class="password">
			<h1>Altere sua senha de acesso</h1>
			<h2>Para maior segurança, a sua senha deve ter no mínimo 6 caracteres, letras maiúsculas e minúsculas, números e caracteres especiais.</h2>
			<div class="field">
				<label for="newPassword">Senha</label>
				<input id="newPassword" name="newPassword" type="password" class="password" minlength="4" maxlength="20" autocomplete="off"
					ng-model="dadosCadastro.Password" validator="required">
			</div>
			<div class="field">
				<label for="passwordConfirm">Confirmar senha</label>
				<input id="passwordConfirm" name="passwordConfirm" type="password" minlength="4" maxlength="20" autocomplete="off"
					data-rule-equalto="#newPassword" ng-model="dadosCadastro.PasswordConfirmation" validator="required">
			</div>
			<div class="field">
				<div class="passwordStrength">
					<ul>
						<li>Fraco</li>
						<li>Médio</li>
						<li>Forte</li>
					</ul>
					<div class="progress">
						<div class="bar"></div>
					</div>
				</div>
			</div>
		</fieldset>
		}

		<button type="submit" class="button" ng-click="completar(dadosCadastro)">Continuar</button>
	</form>
</article>

@section ScriptsCustomizados {
	<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript" asp-append-version="true"></script>
	<script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment-with-locales.min.js" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/endereco/enderecoApi.js")" type="text/javascript" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/first-access/first-access.js")" type="text/javascript" asp-append-version="true"></script>
}
