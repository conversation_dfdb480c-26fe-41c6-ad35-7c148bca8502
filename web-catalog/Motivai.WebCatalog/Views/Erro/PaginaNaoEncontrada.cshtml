﻿@{
	Layout = null;
	var withoutHeaderAndFooter = ViewBag.OnlyContent != null && ViewBag.OnlyContent == true;
}
<!DOCTYPE html>
<html lang="pt-br" prefix="og: http://ogp.me/ns#">
@if(!withoutHeaderAndFooter) {
<head>
    @{ Html.RenderPartial("_Head"); }
</head>
}
<body class="error" ng-app="footerModule">
	<div id="page">
		@if(!withoutHeaderAndFooter) {
		<header role="banner" class="site-header">
			<div class="container">
				<h1 class="logo"><a href="/"></a></h1>
			</div>
		</header>
		}
		<section id="content">
			<div class="container">
				<article class="box not-found">
					<div class="panel">
						<header>
							<h1>Página não encontrada</h1>
						</header>
						<p>Você tentou acessar uma página que não existe ou seu acesso não foi permitido.</p>
						<a href="/" type="submit" class="button back">Voltar para a home</a>
					</div>
				</article>
			</div>
		</section>
		<div class="footer-fix"></div>
	</div>
	@if(!withoutHeaderAndFooter) {
		Html.RenderPartial("_Footer");
	}
</body>
</html>
