﻿@model Motivai.WebCatalog.Models.Base.ErrorModel
@{
	Layout = null;
	var withoutHeaderAndFooter = ViewBag.OnlyContent != null && ViewBag.OnlyContent == true;
}
<!DOCTYPE html>
<html lang="pt-br" prefix="og: http://ogp.me/ns#">
@if(!withoutHeaderAndFooter) {
<head>
    @{ Html.RenderPartial("_Head"); }
</head>
}
<body class="error" ng-app="footerModule">
	<div id="page">
		@if(!withoutHeaderAndFooter) {
		<header role="banner" class="site-header">
			<div class="container">
				<h1 class="logo"><a href="/"></a></h1>
			</div>
		</header>
		}
		<section id="content">
			<div class="container">
				<article class="box server">
					<div class="panel" style="min-height:270px;">
						<header>
							<h1>@(Model != null && !string.IsNullOrEmpty(Model.ErrorMessage) ? Model.ErrorMessage : "Não foi possível processar sua requisição =(")</h1>
						</header>
						@if(Model != null && Model.DetailedError != null) {
							<p>@Model.DetailedError</p>
						} else {
							<p>Aconteceu um erro inesperado em nosso servidor. Já fomos avisados e iremos trabalhar para que esse problema seja solucionado.</p>
						}
						@if(Model != null && Model.HelpMessage != null) {
							@Model.HelpMessage
						} else {
							<p>Portanto, pedimos para que tente acessar novamente mais tarde.</p>
						}
					</div>
				</article>
			</div>
		</section>
		<div class="footer-fix"></div>
	</div>
	@if(!withoutHeaderAndFooter) {
		Html.RenderPartial("_Footer");
	}
</body>
</html>
