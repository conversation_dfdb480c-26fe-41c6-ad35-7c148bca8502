@model Motivai.WebCatalog.Models.Pages.ComprarPontos.PointsPurchaseViewModel
@using Motivai.SharedKernel.Helpers;
@{
	ViewBag.Title = $"{Model.ViewStrings.BalancePurchaseActionDescriptionLowerCase} de {Model.ViewStrings.BalanceDescriptionLowerCase}";
	Layout = null;
}
<article class="buyPoints" ng-init="initForm('@Model.PaymentOptions.Cpf','@Model.PaymentOptions.Phone','@Model.PaymentOptions.Email')" ng-if="showForm">
  <header>
    <h1>@Model.ViewStrings.BalancePurchaseActionDescription @Model.ViewStrings.BalanceDescriptionLowerCase</h1>
  </header>
  <div loader-container is-loading="loading">
    <form id="frmPointsPurchase" name="frmPointsPurchase" novalidate>
      <input type="hidden" id="cnP" value="@Model.PaymentOptions.CoinName.Prefix" />
      <input type="hidden" id="cnS" value="@Model.PaymentOptions.CoinName.Sufix" />
      <input type="hidden" id="aid" name="aid" value="@Model.PaymentOptions.PublicToken" />
      @if(Model.PaymentOptions.Ticket != null) {
        <input type="hidden" id="t" name="t" value="@Model.PaymentOptions.Ticket.Token" />
      }
      <fieldset>
        <p>Aqui você pode comprar @Model.ViewStrings.BalanceDescriptionLowerCase para completar seu saldo e @Model.ViewStrings.BuyActionDescriptionLowerCase o que deseja.</p>
        <div class="field">
          <label for="pointsWanted">Quantos @Model.ViewStrings.BalanceDescriptionLowerCase deseja comprar?<span class="required">*</span></label>
          @if(Model.PaymentOptions.PointsOptions != null && Model.PaymentOptions.PointsOptions.Count > 0) {
          <select class="pointsWanted" id="pointsWanted" name="pointsWanted" placeholder="Escolha" data-placeholder="Escolha" required
            ng-model="pointsOption" ng-change="pesquisarOpcoes(pointsOption)">
            <option value="" selected disabled="disabled">Escolha</option>
            @foreach(var opt in Model.PaymentOptions.PointsOptions) {
              <option value="@<EMAIL>">@Model.PaymentOptions.CoinName.Prefix @opt.Key.ToCurrency() @Model.PaymentOptions.CoinName.Sufix</option>
            }
          </select>
          } else {
            <input id="pointsWanted" name="pointsWanted" type="text" class="pointsWanted" maxlength="10"
                ng-model="pointsOption" onblur="pesquisaOpcoesManual()" ui-number-mask validator="required">
          }
        </div>
        <div class="field">
          <label for="pointsPrice">Preço</label>
          <input id="pointsPrice" name="pointsPrice" type="text" value="R$ @(Model.PaymentOptions.Ticket != null ? Model.PaymentOptions.Ticket.CurrencyValue.ToCurrency() : "0,00")" readonly />
        </div>
      </fieldset>
      <fieldset>
        <h1>Forma de pagamento</h1>
        <div class="field">
          <label for="creditCardNumber">Número do cartão<span class="required">*</span></label>
          <input id="creditCardNumber" name="creditCardNumber" type="text" required class="creditCard" maxlength="16"
            ng-model="card.number" validator="required"><span class="cardBrand"></span>
          <span class="val-msg"></span>
        </div>
        <div class="field">
          <label for="creditCardName">Nome do titular<span class="required">*</span></label>
          <input id="creditCardName" name="creditCardName" type="text" placeholder="Exatamente como está escrito no cartão" required
            ng-model="card.holder" validator="required">
        </div>
        <div class="field">
          <label for="creditCardDate">Data de validade<span class="required">*</span></label>
          <input id="creditCardDate" name="creditCardDate" type="text" placeholder="MM/AA" maxlength="5" required
            ng-model="card.expiration" ui-date-mask="MM/YY" parse="false" validator="required">
        </div>
        <div class="field">
          <label for="creditCardValidation">Código de Segurança<span class="required">*</span></label>
          <input id="creditCardValidation" name="creditCardValidation" type="text" required class="creditCardSecurity"
            maxlength="3" ng-model="card.cvv">
        </div>
        <div class="field" ng-class="{'chosen-required':dirtyWithError(frmPointsPurchase.creditCardInstallments, 'required')}">
          <label for="creditCardInstallments">Número de parcelas<span class="required">*</span></label>
          <select id="creditCardInstallments" name="creditCardInstallments" placeholder="Escolha" data-placeholder="Escolha" required class="creditCardInstallments"
            ng-model="dadosCompra.installment" validator="required">
            <option value="" selected disabled="disabled">Escolha</option>
            @if(Model.PaymentOptions.Installments != null) {
              @foreach(var installment in Model.PaymentOptions.Installments) {
                <option value="@installment.Token">@(installment.Count)x de R$ @installment.Value.ToCurrency()</option>
              }
            } else {
              <option ng-repeat="frm in formas | orderBy:'Count'" value="{{frm.Token}}">{{frm.Count}}x de R$ {{frm.Value | currency:''}}</option>
            }
          </select>
          <span ng-show="dirtyWithError(frmPointsPurchase.creditCardInstallments, 'required')" class="CustomValidationError validationFieldRequired val-msg">Número de parcelas é obrigatório</span>
          <p class="note" style="padding-top:10px;">Para verificar as possibilidades de parcelamento, selecione a bandeira do cartão. O limite disponível do seu cartão deve ser maior que o valor total da compra.</p>
        </div>
      </fieldset>
      <fieldset>
        <h1>Dados de cobrança do titular do cartão</h1>
        <div class="field">
          <label for="cpf">CPF<span class="required">*</span></label>
          <input id="cpf" name="cpf" type="text" placeholder="123.456.789-01" class="cpf" maxlength="14"
            ng-model="dadosCompra.Cpf" ui-br-cpf-mask validator="required,cpf" />
        </div>
        <div class="field">
          <label for="telefone">Telefone<span class="required">*</span></label>
          <input id="telefone" name="telefone" type="tel" maxlength="15" placeholder="(01) 2345-6789" class="telefone"
            ng-model="dadosCompra.Phone" ui-br-phone-number validator="required,phone">
        </div>
        <div class="field">
          <label for="email">E-mail<span class="required">*</span></label>
          <input ng-model="dadosCompra.Email" id="email" name="email" type="text"
            maxlength="150" class="email" placeholder="<EMAIL>" validator="required,email">
        </div>
        <div class="field">
          <label for="cep">CEP<span class="required">*</span></label>
          <input id="cep" name="cep" type="text" placeholder="01234-567" required
              ng-model="dadosCompra.BillingAddress.Cep" ng-minlength="9" ng-blur="pesquisaCep(dadosCompra.BillingAddress.Cep)"
              ui-br-cep-mask validator="required,cep" />
          <span ng-class="{'CustomValidationError validationFieldRequired': dadosCompra.BillingAddress.cepSearchError}" style="display:inline;"
            ng-bind="dadosCompra.BillingAddress.cepSearchMessage"></span>
        </div>
        <div class="field">
          <label for="endereco">Endereço<span class="required">*</span></label>
          <input id="endereco" name="endereco" type="text" placeholder="Ex.: Av. Paulista" ng-disabled="!dadosCompra.BillingAddress.FilledManually"
              ng-model="dadosCompra.BillingAddress.Street" validator="required">
        </div>
        <div class="field">
            <label for="numero">Número<span class="required">*</span></label>
            <input id="numero" name="numero" type="text" placeholder="Ex.: 12.345" required
                ng-model="dadosCompra.BillingAddress.Number" validator="required">
        </div>
        <div class="field">
            <label for="complemento">Complemento<span class="required">*</span></label>
            <input id="complemento" name="complemento" placeholder="Ex.: Apto. 12 Bloco A" type="text" required
                ng-model="dadosCompra.BillingAddress.Complement" validator="required">
        </div>
        <div class="field">
            <label for="bairro">Bairro<span class="required">*</span></label>
            <input id="bairro" name="bairro" type="text" placeholder="Ex.: Bela Vista" required ng-disabled="!dadosCompra.BillingAddress.FilledManually"
                ng-model="dadosCompra.BillingAddress.Neighborhood" validator="required">
        </div>
        <div class="field">
            <label for="estado">Estado<span class="required">*</span></label>
            <select-state select-id="estado" state="dadosCompra.BillingAddress.State" required="true"
              on-update="dadosCompra.BillingAddress.State=$value" disabled="!dadosCompra.BillingAddress.FilledManually"></select-state>
        </div>
        <div class="field">
            <label for="cidade">Cidade<span class="required">*</span></label>
            <input id="cidade" name="cidade" type="text" placeholder="Ex.: São Paulo" required ng-disabled="!dadosCompra.BillingAddress.FilledManually"
                ng-model="dadosCompra.BillingAddress.City" validator="required">
        </div>
      </fieldset>
      <fieldset>
        @if(Model.IsRewards)
        {
        <h1>Observações Importantes</h1>
        <ul>
          <li>Os pontos comprados serão creditados em até 24 horas após a confirmação de pagamento;</li>
          <li>Os pontos comprados são válidos por 2 (dois) anos a partir da data de compra.</li>
        </ul>
        }
      </fieldset>
      <button type="submit" class="button" style="margin-bottom:1em;" ng-click="comprar(frmPointsPurchase)">Comprar</button>
    </form>
  </div>
</article>

<article class="buyPoints" ng-if="showTable">
  <header>
    <h1>Compra finalizada!</h1>
  </header>
  <div class="order-info">
    <div class="box">
      <div class="address">
        <h3>Dados de cobrança</h3>
        <p><span ng-bind="dadosCompra.BillingAddress.Street"></span>, <span ng-bind="dadosCompra.BillingAddress.Number"></span> - <span ng-bind="dadosCompra.BillingAddress.Complement"></span><br>
        <span ng-bind="dadosCompra.BillingAddress.Neighborhood"></span> - <span ng-bind="dadosCompra.BillingAddress.City"></span> - <span ng-bind="dadosCompra.BillingAddress.State"></span><br>
        CEP <span ng-bind="dadosCompra.BillingAddress.Cep"></span></p>
      </div>
    </div>
  </div>
  <div class="cart-products">
    <h2>Produtos @Model.ViewStrings.MostRedeemedProductsDescriptionLowerCase</h2>
    <table>
      <thead>
        <tr>
          <th>Produto</th>
          <th>Qtde</th>
          <th>Valor Unit.</th>
          <th>Total</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td data-title="Produto">
            <div class="product">
              <h2>Crédito de @Model.ViewStrings.BalanceDescriptionLowerCase</h2>
            </div>
          </td>
          <td data-title="Qtde">@Model.PaymentOptions.CoinName.Prefix <span ng-bind="(pontosCompra.pontos | currency:'')"></span> @Model.PaymentOptions.CoinName.Sufix</td>
          <td data-title="Valor Unit."><span ng-bind="(pontosCompra.valorUnit | currency:'R$ ')"></span></td>
          <td data-title="Total">
            <p class="price"><strong><span ng-bind="(pontosCompra.total | currency:'R$ ')"></span></strong></p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="cart-total">
    <h2>Total do pedido</h2>
    <table>
      <tbody>
        <tr>
          <td>Valor</td>
          <td><span ng-bind="(pontosCompra.total | currency:'R$ ')"></span></td>
        </tr>
      </tbody>
    </table>
  </div>
</article>
@section ScriptsCustomizados {
  <script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.10.6/moment-with-locales.min.js"></script>
  <script src="@Url.Content("~/js/lib/moment/moment-timezone-with-data-2012-2022.min.js")" type="text/javascript"></script>
  <script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" type="text/javascript" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/app/app.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/endereco/enderecoApi.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/comprapontos/controllers.js")" type="text/javascript" asp-append-version="true"></script>
}
@if(Model.PaymentOptions.PointsOptions == null || Model.PaymentOptions.PointsOptions.Count < 1) {
<script>
  function pesquisaOpcoesManual() {
    var scope = angular.element(document.getElementById("pointsWanted")).scope();
    scope.$apply(function() {
      scope.pesquisarOpcoesManual(scope.pointsOption);
    });
  }
</script>
}