@using System.Linq;
@model Motivai.WebCatalog.Models.Pages.ComprarPontos.PointsPurchaseViewModel
@using Motivai.SharedKernel.Helpers;
@{
	ViewBag.Title = $"{Model.ViewStrings.BalancePurchaseActionDescriptionLowerCase} de {Model.ViewStrings.BalanceDescriptionLowerCase}";
	Layout = "~/Views/Shared/_LayoutPopups.cshtml";
}
@section StylesCustomizados {
  <style>
    html {
      height: 480px;
      min-width: 280px;
      max-width: 100%;
    }
    .chosen-container {
      width: 100% !important;
    }
  </style>
}
<div id="page" ng-app="platformApp" ng-controller="compraCtl" ng-init="initForm('@Model.PaymentOptions.Cpf','@Model.PaymentOptions.Phone', '@Model.PaymentOptions.Email')">
  <section id="content">
    <div class="container">
      <article class="modal buyPoints">
        <header>
        @if(Model.IsMarketplace) {
          <h1>Efetuar pagamento</h1>
        } else {
          <h1>@Model.ViewStrings.BalancePurchaseActionDescription @Model.ViewStrings.BalanceDescriptionLowerCase</h1>
        }
        </header>
        <div loader-container is-loading="loading">
          <form id="frmPointsPurchase" name="frmPointsPurchase" novalidate>
            <input type="hidden" id="aid" name="aid" value="@Model.PaymentOptions.PublicToken" />
            <input type="hidden" id="t" name="t" value="@Model.PaymentOptions.Ticket.Token" />
            <fieldset ng-if="showForm">
              @if(Model.IsMarketplace) {
                <p>Preecha a forma de pagamento e os dados de cobrança para prosseguir com a compra.</p>
              } else {
                <p>Aqui você pode @Model.ViewStrings.BalancePurchaseActionDescriptionLowerCase @Model.ViewStrings.BalanceDescriptionLowerCase para completar seu saldo e @Model.ViewStrings.BuyActionDescriptionLowerCase o que deseja.</p>
              }

              @if(Model.IsRewards) {
              <div class="field">
                <label for="pointsWanted">@Model.ViewStrings.BalanceDescription necessários para @Model.ViewStrings.BuyActionTypeDescriptionLowercase</label>
                <input id="pointsPrice" name="pointsNeeded" type="text" value="@Model.PaymentOptions.CoinName.Prefix @Model.PaymentOptions.Ticket.PointsToBuy.ToCurrency() @Model.PaymentOptions.CoinName.Sufix" readonly />
              </div>
              }

              <div class="field">
                <label for="pointsPrice">@(Model.IsRewards ? "Preço" : "Valor total")</label>
                @if(Model.PaymentOptions.Installments != null && Model.PaymentOptions.Installments.Count > 0) {
                  <input id="pointsPrice" name="pointsPrice" type="text" value="R$ @(Model.PaymentOptions.Installments.Where(i => i.Count == 1).Select(i => i.Total).FirstOrDefault().ToCurrency())" readonly />
                } else {
                  <input id="pointsPrice" name="pointsPrice" type="text" value="R$ @(Model.PaymentOptions.Ticket.CurrencyValue.ToCurrency())" readonly />
                }

              </div>
            </fieldset>
            <fieldset ng-if="showForm">
              <h1>Forma de pagamento</h1>
              <div class="field">
                <label for="creditCardNumber">Número do cartão<span class="required">*</span></label>
                <input id="creditCardNumber" name="creditCardNumber" type="text" required class="creditCard" maxlength="16"
                  ng-model="card.number" validator="required"><span class="cardBrand"></span>
                <span class="val-msg"></span>
              </div>
              <div class="field">
                <label for="creditCardName">Nome do titular<span class="required">*</span></label>
                <input id="creditCardName" name="creditCardName" type="text" placeholder="Exatamente como está escrito no cartão" required
                  ng-model="card.holder" validator="required">
              </div>
              <div class="field">
                <label for="creditCardDate">Data de validade<span class="required">*</span></label>
                <input id="creditCardDate" name="creditCardDate" type="text" placeholder="MM/AA" maxlength="5" required
                  ng-model="card.expiration" ui-date-mask="MM/YY" parse="false" validator="required">
              </div>
              <div class="field">
                <label for="creditCardValidation">Código de segurança<span class="required">*</span></label>
                <input id="creditCardValidation" name="creditCardValidation" type="text" required class="creditCardSecurity"
                  maxlength="3" ng-model="card.cvv">
              </div>
              <div class="field" ng-class="{'chosen-required':dirtyWithError(frmPointsPurchase.creditCardInstallments, 'required')}">
                <label for="creditCardInstallments">Número de parcelas<span class="required">*</span></label>
                <select id="creditCardInstallments" name="creditCardInstallments" placeholder="Escolha" data-placeholder="Escolha" required class="creditCardInstallments"
                  ng-model="dadosCompra.installment" validator="required">
                  <option value="" selected disabled="disabled">Escolha</option>
                  @foreach(var installment in Model.PaymentOptions.Installments) {
                    <option value="@installment.Token">@(installment.Count)x de R$ @installment.Value.ToCurrency()</option>
                  }
                </select>
                <span ng-show="dirtyWithError(frmPointsPurchase.creditCardInstallments, 'required')" class="CustomValidationError validationFieldRequired val-msg">Número de parcelas é obrigatório</span>
                <p class="note" style="padding-top:10px;">Para verificar as possibilidades de parcelamento, selecione a bandeira do cartão. O limite disponível do seu cartão deve ser maior que o valor total da compra.</p>
              </div>
            </fieldset>
            <fieldset ng-if="showForm">
              <h1>Dados de cobrança do titular do cartão</h1>
              <div class="field">
                <label for="cpf">CPF<span class="required">*</span></label>
                <input id="cpf" name="cpf" type="text" placeholder="123.456.789-01" class="cpf" maxlength="14"
                  ng-model="dadosCompra.Cpf" ui-br-cpf-mask validator="required,cpf" />
              </div>
              <div class="field">
                <label for="telefone">Telefone<span class="required">*</span></label>
                <input id="telefone" name="telefone" type="tel" maxlength="15" placeholder="(01) 2345-6789" class="telefone"
                  ng-model="dadosCompra.Phone" ui-br-phone-number validator="required,phone">
              </div>
              <div class="field">
                <label for="email">E-mail<span class="required">*</span></label>
                <input ng-model="dadosCompra.Email" id="email" name="email" type="text"
                  maxlength="150" class="email" placeholder="<EMAIL>" validator="required,email">
              </div>
              <div class="field">
                <label for="cep">CEP<span class="required">*</span></label>
                <input id="cep" name="cep" type="text" placeholder="01234-567" required
                    ng-model="dadosCompra.BillingAddress.Cep" ng-minlength="9" ng-blur="pesquisaCep(dadosCompra.BillingAddress.Cep)"
                    ui-br-cep-mask validator="required,cep" />
                <span ng-class="{'CustomValidationError validationFieldRequired': dadosCompra.BillingAddress.cepSearchError}"
                  style="display:inline;" ng-bind="dadosCompra.BillingAddress.cepSearchMessage"></span>
              </div>
              <div class="field">
                <label for="endereco">Endereço<span class="required">*</span></label>
                <input id="endereco" name="endereco" type="text" placeholder="Ex.: Av. Paulista" ng-disabled="!dadosCompra.BillingAddress.FilledManually"
                    ng-model="dadosCompra.BillingAddress.Street" validator="required">
              </div>
              <div class="field">
                  <label for="numero">Número<span class="required">*</span></label>
                  <input id="numero" name="numero" type="text" placeholder="Ex.: 12.345" required
                      ng-model="dadosCompra.BillingAddress.Number" validator="required">
              </div>
              <div class="field">
                  <label for="complemento">Complemento<span class="required">*</span></label>
                  <input id="complemento" name="complemento" placeholder="Ex.: Apto. 12 Bloco A" type="text" required
                      ng-model="dadosCompra.BillingAddress.Complement" validator="required">
              </div>
              <div class="field">
                  <label for="bairro">Bairro<span class="required">*</span></label>
                  <input id="bairro" name="bairro" type="text" placeholder="Ex.: Bela Vista" required ng-disabled="!dadosCompra.BillingAddress.FilledManually"
                      ng-model="dadosCompra.BillingAddress.Neighborhood" validator="required">
              </div>
              <div class="field">
                  <label for="estado">Estado<span class="required">*</span></label>
                  <select-state class="platform-chosen" select-id="estado" state="dadosCompra.BillingAddress.State" required="true"
                    on-update="dadosCompra.BillingAddress.State=$value" disabled="!dadosCompra.BillingAddress.FilledManually"></select-state>
              </div>
              <div class="field">
                  <label for="cidade">Cidade<span class="required">*</span></label>
                  <input id="cidade" name="cidade" type="text" placeholder="Ex.: São Paulo" required ng-disabled="!dadosCompra.BillingAddress.FilledManually"
                      ng-model="dadosCompra.BillingAddress.City" validator="required">
              </div>
            </fieldset>
            <fieldset>
              @if(Model.IsRewards)
              {
              <h1>Observações Importantes</h1>
              <ul>
                <li>Os pontos comprados serão creditados em até 24 horas após a confirmação de pagamento;</li>
                <li>Os pontos comprados são válidos por 2 (dois) anos a partir da data de compra.</li>
              </ul>
              }
            </fieldset>
            <button type="submit" class="button" style="margin-bottom:1em;" ng-click="comprar(frmPointsPurchase)" ng-if="showForm">Comprar</button>
          </form>
        </div>
      </article>
    </div>
  </section>
</div>
@section ScriptsCustomizados {
  <script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment-with-locales.min.js"></script>
  <script src="@Url.Content("~/js/lib/moment/moment-timezone-with-data-2012-2022.min.js")" type="text/javascript"></script>
  <script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" type="text/javascript" asp-append-version="true"></script>
	<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/app/app.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/endereco/enderecoApi.js")" type="text/javascript" asp-append-version="true"></script>
  <script src="https://assets.pagar.me/pagarme-js/3.4/pagarme.min.js" asp-append-version="true"></script>
  <script src="@Url.Content("~/js/comprapontos/controllers.js")" type="text/javascript" asp-append-version="true"></script>
}