@model Motivai.WebCatalog.Models.Pages.CatalogPageViewModel

<article class="order" ng-init="init()">
	<div ng-class="messageClass" ng-show="occurredError"><span ng-bind="errorMessage"></span></div>
	<div loader-container is-loading="loading" div-style=""></div>

	<div ng-if="pedido && pedido.OrderNumber" ng-show="!loading">
		<header>
			<h1>Pedido nº <span ng-bind="::pedido.OrderNumber"></span></h1>
			<h2>Data do pedido: <span ng-bind="::(pedido.OrderDate|date:'dd/MM/yyyy')"></span></h2>
		</header>
		<div class="order-info">
			<div class="box">
				<div class="buyer" ng-if="pedido.Customer">
					<h3>Dados do @Model.ViewStrings.BuyerDescription</h3>
					<p><span ng-bind="pedido.Customer.Nome"></span><br>
						<span ng-bind="pedido.Customer.Telefone"></span> / <span
							ng-bind="pedido.Customer.Celular"></span>
					</p>
				</div>
				<div class="address" ng-if="pedido.ShippingAddress">
					<h3>Endereço de entrega: <span ng-bind="pedido.ShippingAddress.AddressName"></span></h3>
					<p><span ng-bind="pedido.ShippingAddress.Street"></span>, <span
							ng-bind="pedido.ShippingAddress.Number"></span> <span
							ng-bind="pedido.ShippingAddress.Complement"></span><br>
						<span ng-bind="pedido.ShippingAddress.Neighborhood"></span> - <span
							ng-bind="pedido.ShippingAddress.City"></span> - <span
							ng-bind="pedido.ShippingAddress.State"></span><br>
						CEP <span ng-bind="pedido.ShippingAddress.Cep"></span><br>
						Referência: <span ng-bind="pedido.ShippingAddress.Reference"></span>
					</p>
				</div>
				<div class="recipient"
					ng-if="pedido.ShippingAddress && pedido.ShippingAddress.Receiver && pedido.ShippingAddress.Receiver.Name">
					<h3>Quem receberá a entrega</h3>
					<p><span ng-bind="pedido.ShippingAddress.Receiver.Name"></span><br>
						CPF <span ng-bind="pedido.ShippingAddress.Receiver.Cpf"></span><br>
						<span ng-bind="pedido.ShippingAddress.Receiver.Telephone"></span> / <span
							ng-bind="pedido.ShippingAddress.Receiver.Cellphone"></span><br>
						<span ng-bind="pedido.ShippingAddress.Receiver.Email"></span>
					</p>
				</div>
			</div>
		</div>

		<div class="cart-products" ng-if="pedido.ProductsItems && pedido.ProductsItems.length > 0">
			<h2>Itens do pedido</h2>
			<table>
				<thead>
					<tr>
						<th colspan="2">Produto</th>
						<th>Qtde</th>
						<th>Valor Unit.</th>
						<th>Total</th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat-start="item in pedido.ProductsItems">
						<td colspan="2">
							<div class="photo"><img src="/assets/img/img-loader.gif" ng-src="{{::item.Image}}"></div>
							<div class="product">
								<h2><span ng-bind="::item.Description"></span></h2>
								<p>
									<span>Parceiro: <span ng-bind="::item.PartnerName"></span></span><br>
									<span>SKU: <span ng-bind="::item.SkuCode"></span></span><br>
									<span ng-if="item.Model"> <span ng-bind="(item.LabelModel || 'Modelo')"></span>: <span ng-bind="::item.Model"></span> <br></span>
									<span ng-if="item.Color"> <span ng-bind="(item.LabelColor || 'Cor')"></span>: <span ng-bind="::item.Color"></span><br></span>
									<span ng-if="item.Size"> <span ng-bind="(item.LabelSize || 'Tamanho')"></span>: <span ng-bind="::item.Size"></span><br></span>
									<span ng-if="item.Voltage"><span ng-bind="(item.LabelVoltage || 'Voltagem')"></span>: <span ng-bind="::item.Voltage"></span></span>
								</p>
								<p ng-if="item.Attributes">
									<span ng-repeat="attr in item.Attributes">
										<span ng-bind="attr.Nome"></span>:
										<span ng-if="::(attr.Tipo != 'File')" ng-bind="::attr.Valor"></span>
										<span ng-if="::(attr.Tipo == 'File')"><a href="{{attr.Valor}}" target="_blank">Baixar</a></span>
										<br>
									</span>
								</p>
							</div>
						</td>
						<td><span ng-bind="::item.Quantity">0</span></td>
						<td ng-if="::(!item.DynamicPrice)"><span ng-bind="::(item.UnitPrice | currency:'')">0</span>
						</td>
						<td ng-if="::(!item.DynamicPrice)">
							<p class="price"><strong><span ng-bind="::pedido.CoinName.Prefix"></span> <span
										ng-bind="::(item.Subtotal | currency:'')">0</span> <span
										ng-bind="::pedido.CoinName.Sufix"></span></strong></p>
						</td>
						<td colspan="2" style="text-align:center" ng-if="::item.DynamicPrice"><span
								ng-bind="::item.DynamicPriceDescription"></span></td>
					</tr>
					<tr class="status" ng-show="::(!item.OccurredError && (item.EstimatedDeliveryDays || item.StatusItem))">
						<td colspan="3">
							<dl>
								<dt ng-show="::item.EstimatedDeliveryDays">Previsão de entrega: </dt>
								<dd ng-show="::item.EstimatedDeliveryDays"><span ng-bind="::item.EstimatedDeliveryDays"></span></dd>
								<dt ng-show="::item.StatusItem">Status: </dt>
								<dd ng-show="::item.StatusItem"><span ng-bind="::item.StatusItem"></span></dd>
							</dl>
						</td>
						<td colspan="3">
							<a href="javascript:" class="button modal btn-tracking" ng-show="::item.TrackingEvents"
								ng-click="showTracking(item)">Ver status completo</a>
						</td>
					</tr>
					<tr class="status" ng-repeat-end ng-show="::(item.OccurredError)">
						<td colspan="9">
							<strong><span ng-bind="::item.ErrorMessage"></span></strong>
						</td>
					</tr>
				</tbody>
				<tfoot>
					<tr>
						<td colspan="2">
							@if (Model.Parametrizations.EnableShippingCalculation)
							{
								<p><strong>Tipo de entrega: </strong>de acordo com cada parceiro.</p>
							}
						</td>
						<td colspan="4">
							<table class="subtotal" style="display:table">
								@if (Model.Parametrizations.EnableShippingCalculation)
								{
									<tr>
										<td>Frete dos produtos</td>
										<td><span ng-bind="::pedido.CoinName.Prefix"></span> <span
											ng-bind="::(pedido.ProductsShippingCost | currency:'')">0</span> <span
											ng-bind="::pedido.CoinName.Sufix"></span></td>
									</tr>
								}
								<tr>
									<td>Subtotal dos <span
											ng-bind="pedido.FormatStringCartViewModel | lowercase"></span></td>
									<td><span ng-bind="::pedido.CoinName.Prefix"></span> <span
											ng-bind="::(pedido.ProductsTotal | currency:'')">0</span> <span
											ng-bind="::pedido.CoinName.Sufix"></span></td>
								</tr>
							</table>
						</td>
					</tr>
				</tfoot>
			</table>
		</div>

		<div class="cart-products" ng-if="pedido.VouchersItems && pedido.VouchersItems.length > 0">
			<div>
				<h2>Vales @Model.ViewStrings.BuyerDescription</h2>

				<table>
					<thead>
						<tr>
							<th colspan="2">Vale</th>
							<th>Qtde</th>
							<th>Valor Unit.</th>
							<th>Total</th>
							<th>Ação</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-repeat-start="item in pedido.VouchersItems">
							<td colspan="2">
								<div class="photo"><img src="/assets/img/img-loader.gif" ng-src="{{::item.Image}}">
								</div>
								<div class="product">
									<h2><span ng-bind="::item.Description"></span></h2>
									<p>
										<span>Parceiro: <span ng-bind="::item.PartnerName"></span></span><br>
										<span>SKU: <span ng-bind="::item.SkuCode"></span><br></span>
										<span ng-if="item.Model">Modelo: <span ng-bind="::item.Model"></span><br></span>
										<span ng-if="item.Color">Cor: <span ng-bind="::item.Color"></span><br></span>
										<span ng-if="item.Size">Tamanho: <span ng-bind="::item.Size"></span><br></span>
										<span ng-if="item.Voltage">Voltagem: <span ng-bind="::item.Voltage"></span></span>
									</p>
									<p ng-if="item.Attributes">
										<span ng-repeat="attr in item.Attributes">
											<span ng-bind="attr.Nome"></span>:
											<span ng-if="::(attr.Tipo != 'File')" ng-bind="::attr.Valor"></span>
											<span ng-if="::(attr.Tipo == 'File')"><a href="{{attr.Valor}}" target="_blank">Baixar</a></span>
											<br>
										</span>
									</p>
								</div>
							</td>
							<td><span ng-bind="::item.Quantity">0</span></td>
							<td ng-if="::(!item.DynamicPrice)"><span ng-bind="::(item.UnitPrice | currency:'')">0</span></td>
							<td ng-if="::(!item.DynamicPrice)">
								<p class="price"><strong><span ng-bind="::pedido.CoinName.Prefix"></span> <span
											ng-bind="::(item.Subtotal | currency:'')">0</span> <span
											ng-bind="::pedido.CoinName.Sufix"></span></strong></p>
							</td>
							<td colspan="2" style="text-align:center" ng-if="::item.DynamicPrice"><span
									ng-bind="::item.DynamicPriceDescription"></span></td>
							<td>
								<div ng-if="temValesVirtuais && !disabled(item, pedido.Vouchers)">
									<a href="javascript:" class="icon" style="float:right;top:-0.5em"
										ng-click="disabled(item, pedido.Vouchers) ? $event.preventDefault() : consultVouchersLink(item)">
										<i class="fa fa-ticket fa-2x"></i>
									</a>
								</div>
							</td>
						</tr>
						<tr class="status" ng-show="::(!item.OccurredError && (item.EstimatedDeliveryDays || item.StatusItem))">
							<td colspan="3">
								<dl>
									<dt ng-show="::item.EstimatedDeliveryDays">Previsão de entrega: </dt>
									<dd ng-show="::item.EstimatedDeliveryDays"><span ng-bind="::item.EstimatedDeliveryDays"></span>
									</dd>
									<dt ng-show="::item.StatusItem">Status: </dt>
									<dd ng-show="::item.StatusItem"><span ng-bind="::item.StatusItem"></span></dd>
								</dl>
							</td>
							<td colspan="3">
								<a href="javascript:" class="button modal btn-tracking" ng-show="::item.TrackingEvents"
									ng-click="showTracking(item)">Ver status completo</a>
							</td>
						</tr>
						<tr class="status" ng-repeat-end ng-show="::(item.OccurredError)">
							<td colspan="6">
								<strong><span ng-bind="::item.ErrorMessage"></span></strong>
							</td>
						</tr>
					</tbody>
					<tfoot>
						<tr>
							<td colspan="2">
								@if (Model.Parametrizations.EnableShippingCalculation)
								{
									<p><strong>Tipo de entrega: </strong>de acordo com cada parceiro.</p>
								}
							</td>
							<td colspan="4">
								<table class="subtotal" style="display:table">
									<tr>
										<td>Frete dos <span ng-bind="pedido.FormatVoucherCart | lowercase"></span> </td>
										<td><span ng-bind="::pedido.CoinName.Prefix"></span> <span
												ng-bind="::(pedido.VouchersShippingCost | currency:'')">0</span> <span
												ng-bind="::pedido.CoinName.Sufix"></span></td>
									</tr>
									<tr>
										<td>Subtotal dos <span ng-bind="pedido.FormatVoucherCart | lowercase"></span>
										</td>
										<td><span ng-bind="::pedido.CoinName.Prefix"></span> <span
												ng-bind="::(pedido.VouchersTotal | currency:'')">0</span> <span
												ng-bind="::pedido.CoinName.Sufix"></span></td>
									</tr>
								</table>
							</td>
						</tr>
					</tfoot>
				</table>
			</div>
		</div>

		<div class="cart-discount" ng-if="pedido.DiscountAmount > 0">
			<h2>Cupom de desconto</h2>
			<table>
				<tbody>
					<tr>
						<td>
							<p>Código: <strong><span ng-bind="::pedido.DiscountCouponCode"></span></strong></p>
						</td>
						<td>
							<table class="subtotal">
								<tr>
									<td>Desconto</td>
									<td><span ng-bind="::pedido.CoinName.Prefix"></span> <span ng-bind="::(pedido.DiscountAmount | currency:'')">0</span> <span ng-bind="::pedido.CoinName.Sufix"></span></td>
								</tr>
							</table>
						</td>
					</tr>
				</tbody>
			</table>
		</div>

		<div class="cart-total">
			<table>
				<tbody>
					<tr>
						<td>Total do pedido</td>
						<td><span ng-bind="::pedido.CoinName.Prefix"></span> <span
								ng-bind="::(pedido.Total | currency:'')">0</span> <span
								ng-bind="::pedido.CoinName.Sufix"></span></td>
					</tr>
				</tbody>
			</table>
		</div>
		<div ng-if="pedido.CanApprove">
			<button type="submit" class="button pull-right" ng-click="approve()" ng-disabled="sending">Aprovar</button>
			<aside class="sidebar">
				<a href="javascript:" class="button back" ng-click="refuse()" ng-disabled="sending">Reprovar</a>
			</aside>
		</div>
	</div>

	@*<div style="visibility:hidden;position:absolute;">
		<div id="modal-voucher" class="container" style="width:720px;"><article class="modal">
		<header>
		<h1>Código do seu cupom de desconto</h1>
		</header>
		<div class="container">
		<p>{{voucher.Description}}</p>
		<h2>{{voucher.VoucherCode}}</h2>
		</div>
		</div>
		</div>*@

	<div style="visibility:hidden;position:absolute;">
		<div id="modal-tracking" class="container" style="width:720px;">
			<article class="modal">
				<header>
					<h1>Status completos do tracking</h1>
				</header>
				<div class="scroll">
					<div class="scroll-container">
						<p ng-show="previsaoEntrega">
							Previsão de entrega:
							<span ng-bind="previsaoEntrega"></span> - <span
								ng-bind="(dataPrevisaoEntrega | date:'dd/MM/yyyy')"></span>
						</p>
						<p ng-show="codigoRastreio">Código de rastreio: <span ng-bind="codigoRastreio"></span></p>
						<p ng-show="linkRastreio">Link de rastreio: <span ng-bind="linkRastreio"></span></p>
						<table>
							<thead>
								<tr>
									<th>Data/hora</th>
									<th>Situação</th>
								</tr>
							</thead>
							<tbody>
								<tr ng-repeat="event in eventos">
									<td ng-bind="::(event.date | date:'dd/MM/yyyy')"></td>
									<td>
										<span ng-bind="::event.description"></span>
										<br />
										<span ng-if="::event.imageUrl">
											<a href="{{event.imageUrl + '/800/600'}}" target="_blank"
												title="Visualizar em nova aba">
												<img ng-src="{{(event.imageUrl + '/400/200')}}" width="400px"
													height="200px" />
											</a>
										</span>
									</td>
								</tr>
								<tr ng-show="!eventos || eventos.length == 0">
									<td colspan="2">Sem atualizações</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</article>
		</div>
	</div>

	<div style="visibility:hidden;position:absolute;">
		<div id="modal-vouchers-link" style="width:720px;">
			<article class="modal">
				<header>
					<h1>Vales Virtuais Gerados</h1>
				</header>
				<div>
					<div class="row" ng-repeat="voucher in vouchersLink">
						<div class="col-6">
							<p><strong>Nome:</strong> <span ng-bind="::voucher.name"></span></p>
						</div>
						<div class="col-6 row" ng-if="isLink(voucher.link)">
							<a class="btn btn-primary" target="_blank" href="{{voucher.link}}">
								<i class="fa fa-ticket fa-2x"></i> <span class="left-p1">Ver Vale</span>
							</a>
						</div>

						<div class="col-6" ng-if="!isLink(voucher.link)">
							<div class="container" ng-mouseenter="showDecriptedCode(voucher.link)" ng-mouseleave="showEncriptedCode(voucher.link)" ng-init="showEncriptedCode(voucher.link)">
       	 						<p> <i ng-class="faEyeClass"></i> <span ng-bind="voucherLink"></span> </p>
    						</div>
						</div>
					</div>

					<div class="row">
						<div ng-if="vouchersPartnerName == 'Uber'">
							<p><small>Acesse aqui os <a href="https://www.uber.com/legal/en/document/?country=brazil&lang=pt-br&name=uber-gift-cards-terms-of-use" target="_blank">Termos e Condições de Uso do Gift Card</a>.</small></p>
							<p>Para resgatar:</p>
							<ul>
								<li>1. Abra o menu da Conta e toque em Carteira.</li>
								<li>2. Toque no botão + "Adicionar saldo" em "Uber Credits".</li>
								<li>3. Toque em Gift Card.</li>
								<li>4. Insira o código do presente e toque em Adicionar.</li>
							</ul>
						</div>
						<p ng-if="!vouchersLink || !vouchersLink.length">
							<small>Caso o vale comprado ainda não esteja na listagem acima, aguarde um pouco mais até que seja gerado.</small>
						</p>
					</div>
				</div>
			</article>
		</div>
	</div>
</article>
