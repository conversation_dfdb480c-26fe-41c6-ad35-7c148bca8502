<article class="register" ng-if="!requireMfaValidationAtRegistration() || !isMfaValidationStep()">
    <form ng-hide="loadingSenha" autocomplete="off">
        <fieldset class="password">
            <h1><PERSON><PERSON></h1>

            <div ng-if="requireMfaValidationAtRegistration() && disabled()" id="auth-mfa-content">
                <div class="col-12 instructions">
                    <a class="mfa-require-information">Necessário autenticação do usuário para alteração da senha. </a>
                </div>

                <div class="col-12">
                    <a class="button" id="auth" href="#/auth">Autenticar</a>
                </div>
            </div>

            <p>Se você deseja alterar a sua senha de acesso, digite a senha atual e, em seguida, a nova senha nos campos correspondentes.</p>

            <div class="field" ng-if="!disabled()">
                <label for="password"><PERSON>ha atual</label>
                <input id="password" name="password" type="password" ng-model="senha.senhaAntiga" required
                    ng-disabled="disabled()">
                <span ng-class="{'CustomValidationError validationFieldRequired': currentPasswordInvalid}" ng-bind="passwordChangeErrorMessageAtual"></span>
            </div>
            <div class="field">
                <label for="newPassword">Nova senha</label>
                <input id="newPassword" name="newPassword" ng-keyup="validatePassword()" type="password"
                    ng-model="senha.novaSenha" required ng-disabled="disabled()">
                <span ng-class="{'CustomValidationError validationFieldRequired': newPasswordInvalid}" ng-bind="passwordChangeErrorMessage"></span>
            </div>
            <div class="field">
                <label for="passwordConfirm">Confirmar senha</label>
                <input id="passwordConfirm" name="passwordConfirm" ng-keyup="validarConfirmarSenha()" type="password"
                    data-rule-equalto="#newPassword" ng-model="senha.confirmaSenha" required ng-disabled="disabled()">
                <span ng-class="{'CustomValidationError validationFieldRequired': passwordConfirmInvalid}" ng-bind="confirmPasswordErrorMessage"></span>
            </div>

            <div class="field">
                <p>
                    <strong>A senha deve conter ao menos:</strong>
                    <ul>
                        <li>Um número</li>
                        <li>Uma letra maiúscula</li>
                        <li>Uma letra minúscula</li>
                        <li>No mínimo 6 caracteres</li>
                    </ul>
                </p>
            </div>

        </fieldset>
        <button type="submit" class="button" ng-click="alterarSenha(senha)" ng-disabled="disabled()">Confirmar
            dados</button>
    </form>
    <div loader-container is-loading="loading() || loadingSenha" div-style=""></div>
</article>
