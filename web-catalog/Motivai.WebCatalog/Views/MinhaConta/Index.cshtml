@using Motivai.WebCatalog.Helpers;
@inject IHelperWeb _helper;
@model Motivai.WebCatalog.Models.Catalog.Menu.MyAccountMenuViewModel
@{
  Layout = "~/Views/Shared/_Layout.cshtml";
  ViewBag.Title = "Minha Conta";
  ViewBag.ClassBody = "minha-conta";
  var participante = _helper.GetParticipantSession();
}
@section StylesCustomizados {
<link href="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.css")" asp-append-version="true" rel="stylesheet">
<style>
  .ng-dirty.ng-invalid {
    border-color: #d05151 !important;
    border-width: 2px !important;
  }

  .chosen-container {
    display: inline-block !important;
  }

  .align-left {
    text-align: left;
  }

  .chosen-container {
    margin-bottom: 0px !important;
  }

  .latestCredits,
  .latestDebits {
    float: left !important;
  }

  .td-event-data {
    width: 135px;
    padding: 10px;
  }

  .order .sidebar {
    margin-top: 0px !important;
    float: right !important;
  }

  .order .sidebar a.back {
    font-size: 1.125em !important;
    line-height: 1em !important;
    padding: 0.6em;
  }

  .card-order-info .box div {
    float: left !important;
  }

  table.cashback-details td.bank-account {
    min-width: 300px;
  }

  table.cashback-details td {
    min-width: 200px;
  }

  .mfa-require-information {
    margin-bottom: 10px;
    display: inline-block;
    padding: 20px;
    text-transform: uppercase;
    text-align: center;
    border: 1px solid #fff;
    background: #ffe20054;
    border-radius: 10px;
    width: 100%;
    pointer-events: none;
    color: black;
    float: left;
    width: 100%;
  }

  #auth-mfa-content .col-12 {
    float: left;
    width: 100%;
    margin-left: 0%;
    margin-right: 0%;
  }

  aside.sidebar a.back {
    font-size: .875em;
    line-height: -1em;
    display: block;
    text-align: left;
    padding: 10px;
    padding-left: 15px;
  }

  .button#auth {
    text-align: left;
    padding: 10px;
    margin-bottom: 10px;
  }
</style>
}
<div class="breadcrumbs">
  <div class="container">
    <ul>
      <li class="home"><a href="/">Home</a></li>
      <li>Minha Conta</li>
    </ul>
  </div>
</div>
<section id="content">
  <div class="container">
    <aside class="sidebar" ng-controller="minhaContaCtrl">
      <nav>
        <header>
          <h1>Olá, @participante.Name!</h1>
        </header>
        <div class="list-buttons">
          <ul>
            <li id="li-principal"><a href="#/principal">Minha Conta</a></li>
            @if (Model.EnableParticipantData)
            {
              <li id="li-cadastro"><a href="#/cadastro">Meu Cadastro</a></li>
            }
            @if (Model.EnableAddresses)
            {
              <li id="li-enderecos"><a href="#/enderecos">Meus Endereços</a></li>
            }
            @if (Model.EnablePassword && !participante.IsParticipantMigrated())
            {
              <li id="li-senha"><a href="#/senha">Minha senha</a></li>
            }
            @if (Model.EnableAuthenticationMfa)
            {
              <li id="li-dados-dupla-autenticacao"><a href="#/dados-dupla-autenticacao">Dados de Dupla Autenticação</a>
              </li>
            }
            @if (Model.EnableOrders)
            {
              <li id="li-pedidos"><a href="#/pedidos">Meus pedidos</a></li>
            }
            @if (Model.EnableExtract)
            {
              <li id="li-extrato"><a href="#/extrato">Meu Extrato</a></li>
            }
            @if (Model.EnablePointsToExpireAndBlocked)
            {
              <li id="li-bloqueados"><a href="#/bloqueados">@Model.ViewStrings.BalanceDescription bloqueados</a></li>
              <li id="li-expiracao"><a href="#/expiracao">@Model.ViewStrings.BalanceDescription a expirar</a></li>
            }
            @if (Model.IsRewards && Model.AllowBuyPoints)
            {
              <li id="li-comprar"><a href="#/comprar">Comprar @Model.ViewStrings.BalanceDescription</a></li>
            }

            @* @if (Model.CanLoggedParticipantAccessMenuService)
            {
              <li id="li-recarga-celular"><a href="/recargacelular">Recarga de Celular</a></li>
              <li id="li-pague-contas"><a href="/paguecontas">Pague Contas</a></li>
            } *@

            <li id="li-fale-conosco"><a href="/FaleConosco/Formulario">Fale Conosco</a></li>
            @if (Model.EnableFaq)
            {
              <li id="li-duvidas"><a href="/Faq">Dúvidas</a></li>
            }
          </ul>
        </div>
      </nav>
    </aside>
    <div class="contentPlaceholder" ng-view></div>
  </div>
</section>
@section ScriptsCustomizados {
<script src="@Url.Content("~/js/lib/angularjs/angular-route.min.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/lib/angularjs/angular-resource.min.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation.min.js")" type="text/javascript"></script>
<script src="@Url.Content("~/js/lib/angular-validation/angular-validation-rule.min.js")"
  type="text/javascript"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment-with-locales.min.js"></script>
<script src="@Url.Content("~/js/lib/moment/moment-timezone-with-data-2012-2022.min.js")"
  type="text/javascript"></script>
<script src="@Url.Content("~/js/lib/moment-picker/angular-moment-picker.min.js")" type="text/javascript"
  asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/filters/brasilFilters.js")" type="text/javascript"
  asp-append-version="true"></script>
<script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"
  asp-append-version="true"></script>
<script src="@Url.Content("~/js/endereco/enderecoApi.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/minhaconta/minhacontaApiService.js")" type="text/javascript"
  asp-append-version="true"></script>
<script src="https://assets.pagar.me/pagarme-js/3.4/pagarme.min.js" asp-append-version="true"></script>
<script src="@Url.Content("~/js/minhaconta/controllers.js")" type="text/javascript" asp-append-version="true"></script>
<script src="@Url.Content("~/js/minhaconta/routes.js")" type="text/javascript" asp-append-version="true"></script>
}
