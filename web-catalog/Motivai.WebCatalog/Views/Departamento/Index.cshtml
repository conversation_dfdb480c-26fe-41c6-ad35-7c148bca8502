@model Motivai.WebCatalog.Models.Pages.Departments.DepartmentViewModel
@{
    ViewBag.Title = Model.Department.Name;
    ViewBag.ClassBody = "departamentos";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"><a href="/">Home</a></li>
            <li>@Model.Department.Name</li>
        </ul>
    </div>
</div>

<section id="content" ng-controller="departmentCtrl" ng-init="init('@Model.Department.Id')">
    <div class="container">
        <aside class="sidebar">
            <nav>
                <header>
                    <h1 class="icon"><a href="#"><i class="fa @Model.Department.Icon"></i>@Model.Department.Name</a></h1>
                </header>
                <form class="price-filter">
                    <fieldset>
                        <h2>Valor</h2>
                        <div class="field">
                            <input type="text" placeholder="Mínimo" ng-model="ptMin" ui-number-mask="0">
                        </div>
                        <div class="field">
                            <input type="text" placeholder="Máximo" ng-model="ptMax" ui-number-mask="0">
                        </div>
                    </fieldset>
                    <button type="submit" class="button" ng-click="filtrarPontos(ptMin, ptMax)">Filtrar</button>
                </form>
                <div class="list-category" ng-if="categorias && categorias.length > 0">
                    <h2>Categorias</h2>
                    <ul>
                        <li ng-repeat="cat in categorias | orderBy:'Description'" ng-class="{'active': cat.isSelected}">
                            <a href="#" ng-click="toggleCategory(cat)"><span ng-bind="::cat.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-if="subcategories && subcategories.length > 0">
                    <h2>Subcategorias</h2>
                    <ul>
                        <li ng-repeat="cat in subcategories | orderBy:'Description'" ng-class="{'active': cat.isSelected}">
                            <a href="#" ng-click="toggleSubcategory(cat)"><span ng-bind="::cat.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-show="products && products.length > 0">
                    <h2>Parceiros</h2>
                    <ul>
                        <li ng-repeat="par in products | orderBy:'Description'" ng-class="{'active': par.isSelected}">
                            <a href="#" ng-click="togglePartner(par)"><span ng-bind="::par.Description"></span></a>
                        </li>
                    </ul>
                </div>

                <div class="list-category" ng-show="manufacturers && manufacturers.length > 0">
                    <h2>Fabricantes</h2>
                    <ul>
                        <li ng-repeat="fab in manufacturers | orderBy:'Description'" ng-class="{'active': fab.isSelected}">
                            <a href="#" ng-click="toggleManufacturer(fab)"><span ng-bind="::fab.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-show="colors && colors.length > 0">
                    <h2>Cores</h2>
                    <ul>
                        <li ng-repeat="cor in colors | orderBy:'Description'" ng-class="{'active': cor.isSelected}">
                            <a href="#" ng-click="toggleColor(cor)"><span ng-bind="::cor.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-show="voltages && voltages.length > 0">
                    <h2>Voltagem</h2>
                    <ul>
                        <li ng-repeat="volt in voltages | orderBy:'Description'" ng-class="{'active': volt.isSelected}">
                            <a href="#" ng-click="toggleVoltage(volt)"><span ng-bind="::volt.Description"></span></a>
                        </li>
                    </ul>
                </div>
            </nav>
            <div style='margin-top: 20px' class="banner loja-especial" ng-if="lojaEspecial">
                <a href="/Loja/Index/{{::lojaEspecial.Url}}">
                    <img src="/assets/img/img-loader.gif" blazy-src="{{::lojaEspecial.LinkBannerImageUrl}}/373/373/crop" title="{{::lojaEspecial.Name}}" />
                </a>
            </div>
        </aside>

        <article class="list-products vitrine">
            <div id="mediabox" ng-controller="departmentBannersCtrl" ng-init="init('@Model.Department.Id')">
                <article ng-if="mediaBoxes.length > 0" class="mediabox page-banner">
                    <a ng-repeat="item in mediaBoxes | orderBy:'Position'" target="{{::item.OpenInNewTab ? '_blank' : '' }}" id="{{::item.ElemId}}" href="{{ ::item.Link }}">
                        <img ng-if="item.ContentType == 'BANNER'"
                            data-srcset="{{item.ImageUrl}}/1920 2600w 3x, {{item.ImageUrl}}/1680 1920w 3x, {{item.ImageUrl}}/1680 1680w 3x, {{item.ImageUrl}}/1680 1400w 3x, {{item.MediumImageUrl}}/1280 1280w 3x, {{item.MediumImageUrl}}/1280 1024w 3x, {{item.SmallImageUrl}}/599 599w 3x, {{item.SmallImageUrl}}/599 425w 3x, {{item.SmallImageUrl}}/599 360w 3x">

                        <img ng-if="item.ContentType == 'PRODUCT'" title="#{{ ::item.FeaturedProduct.Id }}"
                            data-srcset-base="/assets/img/mediabox/"
                            data-srcset-ext=".jpg"
                            data-srcset="bg-product-wd 1920w 3x, bg-product-wd 1680w 3x, bg-product-d 1280w 3x, bg-product-s 599w 3x" />
                    </a>
                </article>

                <div class="nivo-html-caption mediabox-products" ng-repeat="prod in mediaboxProducts" id="{{ ::prod.Id }}">
                    <div class="container">
                        <product-showcase hide-button="false" product="prod"></product-showcase>
                    </div>
                </div>
            </div>

            <div loader-container is-loading="loadingProds">
                <ul class="products vitrine" ng-show="!loadingProds">
                    <product-showcase ng-repeat="prod in products" product="prod"></product-showcase>
                </ul>

                <div class="load-more" ng-if="hasMoreItems" ng-class="{'processing': fetching}">
                    <a href="javascript:" class="button display-block" ng-disabled="fetching" ng-click="fetchMoreItens()" ng-show="!fetching">Carregar mais</a>
                    <a href="javascript:" class="button display-block" ng-show="fetching">Carregando</a>
                </div>

                <div ng-show="!hasProducts">
                    <h2>Nenhum produto encontrado...</h2>
                </div>
            </div>

            <div class="list-products mais-resgatados" ng-if="hasBuyedProducts">
                <header>
                    <h1>Mais @Model.ViewStrings.MostRedeemedProductsDescriptionLowerCase</h1>
                </header>
                <div class="slider" loader-container is-loading="loadingMostBuyedProducts">
                    <ul class="slides products mais-resgatados">
                        <product-showcase ng-repeat="prod in mostBuyedProducts" product="prod"></product-showcase>
                    </ul>
                </div>
            </div>

            <div class="list-products ofertas" ng-if="hasOffersProducts">
                <header>
                    <h1>Ofertas</h1>
                </header>
                <div class="slider" loader-container is-loading="loadingOffers">
                    <ul class="slides products ofertas">
                        <product-showcase ng-repeat="prod in offersProducts" product="prod"></product-showcase>
                    </ul>
                </div>
            </div>
        </article>
    </div>
</section>

@section ScriptsCustomizados {
    <script src="~/js/app/directives/loader-container.js" type="text/javascript" asp-append-version="true"></script>
    <script src="~/js/lib/input-masks/masks.js" type="text/javascript" asp-append-version="true"></script>
    <script src="~/js/catalog/department-service.js" asp-append-version="true"></script>
    <script src="~/js/catalog/department-app.js" asp-append-version="true"></script>
    <script>
        $(document).ready(function() {
            pageView('/Departamento/@Html.Raw(Model.Department.Name)');
        });
    </script>
}
