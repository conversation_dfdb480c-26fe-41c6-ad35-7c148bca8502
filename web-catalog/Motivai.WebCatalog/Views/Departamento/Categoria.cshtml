@model Motivai.WebCatalog.Models.Pages.Departments.CategoryViewModel
@{
    ViewBag.ClassBody = "categorias";
    if (Model.Category.SubcategoryName == null) {
        ViewBag.Title = String.Format("{0} | {1}", Model.Category.Department.Name, Model.Category.Name);
    } else {
        ViewBag.Title = String.Format("{0} | {1} | {2}", Model.Category.Department.Name, Model.Category.Name, Model.Category.SubcategoryName);
    }
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="breadcrumbs">
    <div class="container">
        <ul>
            <li class="home"><a href="/">Home</a></li>
            <li>@Html.ActionLink(Model.Category.Department.Name, "Index", "Departamento", new { id = Model.Category.Department.Id }, null)</li>
            <li>@Model.Category.Name</li>
        </ul>
    </div>
</div>

<section id="content" ng-controller="categoryCtrl" ng-init="initCategoria('@Model.Category.Department.Id', '@Model.Category.Name', '@Model.Category.SubcategoryName')">
    <div class="container">
        <aside class="sidebar">
            <nav>
                <header>
                    <h1 class="icon"><a href="#"><i class="fa @Model.Category.Department.Icon"></i>@Model.Category.Name</a></h1>
                </header>
                <form class="price-filter">
                    <fieldset>
                        <h2>@Model.ViewStrings.RangePointsDescription</h2>
                        <div class="field">
                            <input type="text" placeholder="Mínimo" ng-model="ptMin" ui-number-mask="0">
                        </div>
                        <div class="field">
                            <input type="text" placeholder="Máximo" ng-model="ptMax" ui-number-mask="0">
                        </div>
                    </fieldset>
                    <button type="submit" class="button" ng-click="filtrarPontos(ptMin, ptMax)">Filtrar</button>
                </form>

                <div class="list-category">
                    <h2>Categorias</h2>
                    <ul>
                        <li class="active">
                            @Html.ActionLink(Model.Category.Name, "Index", "Departamento", new { id = Model.Category.Department.Id }, null)
                        </li>
                    </ul>
                </div>

                <div class="list-category" ng-show="subcategories && subcategories.length > 0">
                    <h2>Subcategorias</h2>
                    <ul>
                        <li ng-repeat="cat in subcategories | orderBy:'Description'" ng-class="{'active': cat.isSelected}">
                            <a href="#" ng-click="toggleSubcategory(cat)"><span ng-bind="::cat.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-show="products && products.length > 0">
                    <h2>Parceiros</h2>
                    <ul>
                        <li ng-repeat="par in products | orderBy:'Description'" ng-class="{'active': par.isSelected}">
                            <a href="#" ng-click="togglePartner(par)"><span ng-bind="::par.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-show="manufacturers && manufacturers.length > 0">
                    <h2>Fabricantes</h2>
                    <ul>
                        <li ng-repeat="fab in manufacturers | orderBy:'Description'" ng-class="{'active': fab.isSelected}">
                            <a href="#" ng-click="toggleManufacturer(fab)"><span ng-bind="::fab.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-show="colors && colors.length > 0">
                    <h2>Cores</h2>
                    <ul>
                        <li ng-repeat="cor in colors | orderBy:'Description'" ng-class="{'active': cor.isSelected}">
                            <a href="#" ng-click="toggleColor(cor)"><span ng-bind="::cor.Description"></span></a>
                        </li>
                    </ul>
                </div>
                <div class="list-category" ng-show="voltages && voltages.length > 0">
                    <h2>Voltagem</h2>
                    <ul>
                        <li ng-repeat="volt in voltages | orderBy:'Description'" ng-class="{'active': volt.isSelected}">
                            <a href="#" ng-click="toggleVoltage(volt)"><span ng-bind="::volt.Description"></span></a>
                        </li>
                    </ul>
                </div>
            </nav>

            <div class="banner loja-especial" ng-if="lojaEspecial">
                <a href="/Loja/Index/{{::lojaEspecial.Url}}">
                    <img src="/assets/img/img-loader.gif" blazy-src="{{::lojaEspecial.LinkBannerImageUrl}}/373/373" caption="{{::lojaEspecial.Name}}" />
                </a>
            </div>
        </aside>

        <article class="list-products">
            @*
            <div class="filters">
                <div class="orderBy">
                    <select class="autostart" id="orderBy" name="orderBy" placeholder="Ordenar por..." data-placeholder="Ordenar por..."
                            ng-model="sortBy" ng-change="changeSortFilter()">
                        <option value="MostReedems" selected>Mais @Model.ViewStrings.MostRedeemedProductsDescriptionLowerCase</option>
                        <option value="LessPoints">Menos @Model.ViewStrings.BalanceDescriptionLowerCase</option>
                        <option value="MostPoints">Mais @Model.ViewStrings.BalanceDescriptionLowerCase</option>
                    </select>
                </div>

                <div class="pageItens">
                    <select class="autostart" id="pageItens" name="pageItens" placeholder="Itens por página" data-placeholder="Itens por página" onchange="mudaPageSize()">
                        <option value="40" selected>40 itens por página</option>
                        <option value="80">80 itens por página</option>
                        <option value="120">120 itens por página</option>
                    </select>
                </div>

                <div class="viewType">
                    <ul>
                        <li class="active"><a href="#" class="gridView">Ver em Grade</a></li>
                        <li><a href="/Produto/Detalhes/{{::prod.Url}}" class="listView">Ver em Lista</a></li>
                    </ul>
                </div>
            </div>
            *@

            <div loader-Container is-loading="loadingProds">
                <ul class="products" ng-show="!loadingProds">
                    <product-showcase ng-repeat="prod in products" product="prod"></product-showcase>
                </ul>
                <div ng-show="!hasProducts">
                    <h2>Nenhum produto encontrado...</h2>
                </div>
            </div>

            <div class="pagination" ng-show="!loadingProds && hasProducts">
                <ul style="padding-bottom:5px;">
                    <li class="previous" ng-class="{disabled: !showPrevious}" ng-click="!showPrevious||goToPage(currentPage-1)"><a href="#">Anterior</a></li>
                    <li ng-repeat="pLink in pageLinks" ng-class="{active: pLink === currentPage}" ng-click="goToPage(pLink)"><a href="#">{{::pLink}}</a></li>
                    <li class="next" ng-class="{disabled: !showNext}" ng-click="!showNext||goToPage(currentPage+1)"><a href="#">Próximo</a></li>
                </ul>
            </div>
        </article>
    </div>
</section>

@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/lib/input-masks/masks.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/js/catalog/department-service.js")"></script>
    <script src="@Url.Content("~/js/catalog/category-app.js")"></script>
    <script>
        $(document).ready(function() {
            pageView('/Departamento/@Html.Raw(Model.Category.Department.Name)/@Html.Raw(Model.Category.Name)@Html.Raw(Model.Category.SubcategoryName != null ? "/" + Model.Category.SubcategoryName : "")');
        });
    </script>
}
