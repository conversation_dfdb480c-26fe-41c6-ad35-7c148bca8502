﻿@using System;
@model Motivai.WebCatalog.Models.Catalog.SpecialShopModel
@using Motivai.SharedKernel.Helpers;
@{
    ViewBag.Title = Model.Name;
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@if(Model.PrincipalBannerImageUrl != null) {
<div class="lojaespecial-header">
    <article class="lojaespecial page-banner">
        <img src="@(Model.PrincipalBannerImageUrl)/1280" />
    </article>
</div>
}

@if(!string.IsNullOrEmpty(Model.PageContent)) {
    <div class="special-shop-dynamic-content">
        @Html.Raw(Model.PageContent)
    </div>
}

@if(Model.HasProducts()) {
<section id="content">
    <div class="container">
        <article class="list-products vitrine">
            <header>
              <h1>@Model.Name</h1>
            </header>
            <ul class="products vitrine">
                @foreach(var prod in Model.Products)
                {
                    <li>
                        <div class="photo">
                            <a href="/Produto/Detalhes/@prod.Url" title="@prod.Name">
                                <img src="/assets/img/img-loader.gif" blazy-src="@prod.ImageUrl">
                            </a>
                        </div>
                        <div class="variable"></div>
                        <div class="variable" ng-show="::prod.Rankings">
                            <ul>
                                <li ng-repeat="rank in prod.Rankings"><img src="{{::(rank.Icon + '/45/45')}}" title="{{::rank.Name}}"></li>
                            </ul>
                        </div>
                        <h2><a href="/Produto/Detalhes/@prod.Url" title="@prod.Name"><span>@prod.Name</span></a></h2>
                        @if(prod.IsPromotional())
                        {
                            <p class="price">
                                <span class="promo-d">@prod.CoinName.Prefix @prod.PriceFrom.ToCurrency() @prod.CoinName.Sufix</span>
                                <span><strong>@prod.CoinName.Prefix @prod.Price.ToCurrency() @prod.CoinName.Sufix</strong></span>
                            </p>
                        }
                        else
                        {
                            <p class="price"><strong>@prod.CoinName.Prefix @prod.Price.ToCurrency() @prod.CoinName.Sufix</strong></p>
                        }
                    </li>
                }
            </ul>
        </article>
    </div>
</section>
}

@section ScriptsCustomizados {
    <script src="@Url.Content("~/js/app/directives/loader-container.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/catalog/departament-service.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/catalog/departament-app.js")" type="text/javascript" asp-append-version="true"></script>
    <script src="@Url.Content("~/js/loja/special-shop.js")" type="text/javascript" asp-append-version="true"></script>
    <script>
        $(document).ready(function() {
            pageView('/LojaEspecial/@Html.Raw(Model.Name)');
        });
    </script>
}
