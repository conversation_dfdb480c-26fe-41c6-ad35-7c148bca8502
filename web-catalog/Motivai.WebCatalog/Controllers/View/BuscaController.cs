using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Pages;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Controllers
{
    public class BuscaController : BaseController
    {
        private readonly IHelperWeb _helperWeb;
        private readonly CampaignRepository _campaignRepository;

        public BuscaController(IHelperWeb helperWeb, CampaignRepository campaignRepository)
        {
            this._helperWeb = helperWeb;
            this._campaignRepository = campaignRepository;
        }

        [HttpGet]
        public async Task<ActionResult> Index()
        {
            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
            var model = new CatalogPageViewModel(campaignSettings);
            return View(model);
        }
    }
}