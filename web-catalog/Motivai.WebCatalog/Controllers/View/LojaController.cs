using System;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.SharedKernel.Domain.Enums.Campaigns;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Catalog;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Controllers
{
    public class LojaController : BaseController
    {
        private readonly IHelperWeb _helperWeb;
        private readonly ICryptography _cryptography;
        private readonly CatalogRepository _catalogRepository;

        public LojaController(IHelperWeb helperWeb, ICryptography cryptography, CatalogRepository catalogRepository)
        {
            _helperWeb = helperWeb;
            _cryptography = cryptography;
            _catalogRepository = catalogRepository;
        }

        [HttpGet]
        public async Task<IActionResult> Index(string id, [FromQuery] string origin)
        {
            Guid specialShopId = ParserHelper.DecryptGuid(_cryptography, id);
            SpecialShopModel specialShop = null;
            var originEnum = CampaignParametrizationOrigin.CAMPAIGN;
            if (origin == "P")
            {
                originEnum = CampaignParametrizationOrigin.GENERAL_PLATFORM;
            }
            try
            {
                var participant = _helperWeb.GetParticipantSession();
                specialShop = await _catalogRepository.GetSpecialShopById(_helperWeb.GetCampaignId(),
                    participant.UserId, participant.ParticipantId, specialShopId, originEnum);
            }
            catch (Exception ex)
            {
                await LogException(ex);
                return ForwardToError("Ocorreu um erro ao carregar a loja especial =(",
                    "Se o erro persistir, por favor, contate o administrador.");
            }
            return View(specialShop);
        }
    }
}
