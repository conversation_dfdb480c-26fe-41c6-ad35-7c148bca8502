using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Controllers
{
    public class FAQController : BaseController
    {
        private readonly IHelperWeb _helperWeb;
        private readonly CampaignRepository _campaignRepository;

        public FAQController(IHelperWeb helperWeb, CampaignRepository campaignRepository)
        {
            _helperWeb = helperWeb;
            _campaignRepository = campaignRepository;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var parametrizations = await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
            if (!parametrizations.PagesSettings.EnableFaq)
            {
                return ForwardToNotFound();
            }
            return View();
        }

        public async Task<JsonResult> CarregarFaq()
        {
            return await ExecuteAndGetJson(_campaignRepository.GetFaqs(await _helperWeb.GetCampaignIdForCurrentDomain(), _helperWeb.GetUserId()));
        }

        [HttpPost]
        public async Task<JsonResult> BuscarFaqPeloTermo(string term)
        {
            return await ExecuteAndGetJson(_campaignRepository.GetFaqs(await _helperWeb.GetCampaignIdForCurrentDomain(), _helperWeb.GetUserId(), term));
        }
    }
}