using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.Campaign;
using Motivai.WebCatalog.Models.Catalog;
using Motivai.WebCatalog.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.WebCatalog.Controllers
{
	public class FaleConoscoController : BaseController
	{
		private readonly ICryptography _cryptography;
		private readonly IHelperWeb _helperWeb;
		private readonly CampaignRepository _campaignRepository;
		private readonly CatalogRepository _catalogRepository;

		public FaleConoscoController(IHelperWeb helperWeb, ICryptography cryptography,
			CampaignRepository campaignRepository, CatalogRepository catalogRepository)
		{
			_helperWeb = helperWeb;
			_cryptography = cryptography;
			_campaignRepository = campaignRepository;
			_catalogRepository = catalogRepository;
		}

		[HttpGet]
		public async Task<IActionResult> Formulario()
		{
			var pagesSettings = await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
			if (!pagesSettings.PagesSettings.EnableContact)
			{
				return ForwardToNotFound();
			}

			try
			{
				var subjects = await _campaignRepository.GetSubjects(await _helperWeb.GetCampaignIdForCurrentDomain());
				if (subjects == null)
				{
					subjects = new List<SubjectModel>();
				}
				else
				{
					subjects.ForEach(subject =>
					{
						if (!string.IsNullOrEmpty(subject.Id))
							subject.Id = _cryptography.Encrypt(subject.Id);
					});
				}
				return View(subjects);
			}
			catch (Exception ex)
			{
				await LogException(ex);
				return ForwardToError("Ocorreu um erro durante o carregamento",
					"Por favor, tente novamente, se o erro persistir entre em contato com nosso atendimento.",
					"");
			}
		}

		[HttpPost]
		public async Task<JsonResult> EnviarMensagem([FromBody] ContactUsModel contact)
		{
			var pagesSettings = await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
			if (!pagesSettings.PagesSettings.EnableContact)
			{
				return AjaxReturn.FromError("Contato está desabilitado.");
			}

			var ajaxReturn = new AjaxReturn();
			try
			{
				if (contact == null)
					return AjaxReturn.FromError("Preencha os campos corretamente.");
				contact.Validate();

				if (_helperWeb.HasParticipantSession())
				{
					contact.UserId = _helperWeb.GetUserId();
				}

				var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
				if (!string.IsNullOrEmpty(contact.Subject))
					contact.Subject = ParserHelper.DecryptGuid(_cryptography, contact.Subject).ToString();

				var result = await _catalogRepository.SendContactUsFormMessage(campaignId, contact);
				if (result)
					ajaxReturn.Alert = "Contato enviado com sucesso. Em breve entraremos em contato com mais detalhes sobre sua solicitação!";
			}
			catch (Exception ex)
			{
				await LogException(ex);
				if (ex is MotivaiException)
					ajaxReturn.SetError(ex.Message);
				else
					ajaxReturn.SetError("Não foi possível enviar a mensagem, por favor, tente novamente.");
			}
			return ajaxReturn.GetJsonResult();
		}
	}
}