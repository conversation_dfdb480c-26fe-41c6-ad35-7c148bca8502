using System;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Controllers
{
    public class RegulamentoController : BaseController
    {
        private readonly IHelperWeb _helperWeb;
        private readonly CampaignRepository _campaignRepository;

        public RegulamentoController(IHelperWeb helperWeb, CampaignRepository campaignRepository)
        {
            this._helperWeb = helperWeb;
            this._campaignRepository = campaignRepository;
        }

        [HttpGet]
        public async Task<ActionResult> Index()
        {
            var campaignSettings = await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
            if (!campaignSettings.PagesSettings.EnableRegulation)
            {
                return ForwardToNotFound();
            }
            return View();
        }

        [HttpGet]
        public async Task<JsonResult> CarregaRegulamento()
        {
            var campaignSettings = await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
            if (!campaignSettings.PagesSettings.EnableRegulation)
            {
                return AjaxReturn.FromError("Regulamento está desabilitado.");
            }
            try
            {
                var groupRegulation = await _campaignRepository.GetRegulation(await _helperWeb.GetCampaignIdForCurrentDomain(), _helperWeb.GetUserId());
                return AjaxReturn.GetJsonResult(groupRegulation.regulationContent);
            }
            catch (Exception ex)
            {
                await LogException(ex, "Erro durante o carregamento do regulamento", true);
                return AjaxReturn.FromError("Não foi possível carregar o regulamento, por favor, tente novamente.");
            }
        }
    }
}