﻿using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.WebCatalog.Models.Pages;
using Motivai.WebCatalog.Services.CampaignSettingsService;

namespace Motivai.WebCatalog.Controllers
{
    public class HomeController : Controller
    {
        private readonly CampaignSettingsService _campaignSettingsService;

        public HomeController(CampaignSettingsService campaignSettingsService)
        {
            _campaignSettingsService = campaignSettingsService;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var campaignSettings = await _campaignSettingsService.GetCampaignSettings();
            return View(CatalogPageViewModel.OfCampaignSettings(campaignSettings));
        }
    }
}