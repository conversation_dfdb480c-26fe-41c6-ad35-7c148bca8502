using System;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Controllers
{
    public class ComunicacaoController : BaseController
    {
        private readonly ICryptography _cryptography;
        private readonly IHelperWeb _helperWeb;
        private readonly CampaignRepository _campaignRepository;

        public ComunicacaoController(ICryptography cryptography, IHelperWeb helperWeb, CampaignRepository campaignRepository)
        {
            this._cryptography = cryptography;
            this._helperWeb = helperWeb;
            this._campaignRepository = campaignRepository;
        }

        [HttpGet]
        public async Task<ActionResult> Detalhes(string id, string _)
        {
            Guid communicationId = ParserHelper.DecryptGuid(_cryptography, id);
            var communication = await _campaignRepository.GetCommunicationById(await _helperWeb.GetCampaignIdForCurrentDomain(),
                communicationId, _helperWeb.GetParticipantId());
            return View(communication);
        }
    }
}