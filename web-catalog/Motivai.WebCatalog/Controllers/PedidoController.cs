using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Addresses;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Models.Pages;
using Motivai.WebCatalog.Models.Session;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.Cart;
using Motivai.WebCatalog.Services.Order;
using Motivai.WebCatalog.Models.Pages.Pedido;

namespace Motivai.WebCatalog.Controllers
{
    public class PedidoController : BaseController {
		private readonly IHelperWeb _helperWeb;
		private readonly ShoppingCartValidator cartValidator;
		private readonly CartManager _cartService;
		private readonly CampaignRepository _campaignRepository;
		private readonly UserParticipantRepository _participantRepository;
		private readonly CatalogRepository _catalogRepository;

		public PedidoController(IHelperWeb helperWeb, ShoppingCartValidator cartValidator,
			CartManager cartService, CampaignRepository campaignRepository,
			UserParticipantRepository participantRepository, CatalogRepository catalogRepository) {
			this._helperWeb = helperWeb;
			this.cartValidator = cartValidator;
			this._cartService = cartService;
			this._campaignRepository = campaignRepository;
			this._participantRepository = participantRepository;
			this._catalogRepository = catalogRepository;
		}

		private void SetLayoutVars(CampaignCatalogSettings catalogSettings) {
			ViewBag.EnableShippingPolicy = catalogSettings.PagesSettings.EnableShippingPolicy;
		}

		[HttpPost]
		public async Task<IActionResult> IniciarResgate(string shippingZipcode, string requiresAllChildren) {
			// Seta o endereço de destino escolhido no Carrinho
			var cart = _helperWeb.GetSessionCart();

			try {
				cart.CampaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
				cart.UserId = _helperWeb.GetUserId();

				await cartValidator.ValidateCartForOrder(cart, shippingZipcode, "1" == requiresAllChildren);
				_helperWeb.SetSessionCart(cart);

				var catalogSettings = await _campaignRepository.GetPagesSettings(cart.CampaignId);
				SetLayoutVars(catalogSettings);

				return RedirectToAction("ConfirmarCarrinho");

			} catch (InvalidCardException ex) {
				await LogException(ex, "Order - Shipping Address", ex.Message);
				cart.SetError(ex.Message);
				_helperWeb.SetSessionCart(cart);
				return RedirectToAction("Index", "Carrinho");

			} catch (MotivaiException ex) {
				await LogException(ex, "Order", "Erro ao iniciar resgate");
				cart.SetError(ex.Message);
				_helperWeb.SetSessionCart(cart);
				return RedirectToAction("Index", "Carrinho");

			} catch (Exception ex) {
				await LogException(ex);
				await ExceptionLogger.LogException(ex, "Order", "Erro ao iniciar resgate");
				cart.SetError("Ocorreu um erro ao iniciar o resgate, se persistir contate o administrador do sistema.");
				_helperWeb.SetSessionCart(cart);
				return RedirectToAction("Index", "Carrinho");
			}
		}

		/* [HttpGet] */
		public async Task<IActionResult> ConfirmarEndereco() {
			ViewBag.EtapaResgate = "address";
			var catalogSettings = await _campaignRepository.GetPagesSettings(await _helperWeb.GetCampaignIdForCurrentDomain());
			SetLayoutVars(catalogSettings);
			var cart = _helperWeb.GetSessionCart();
			if (cart == null) {
				return RedirectToAction("Index", "Carrinho");
			}
			if (cart.ShippingAddress == null) {
				cart.ShippingAddress = new AddressModel();
			}
			if (cart.ShippingAddress.Receiver == null) {
				cart.ShippingAddress.Receiver = new ReceiverModel();
			}
			return View(cart);
		}

		/* [HttpPost] */
		public async Task<IActionResult> ConfirmarEndereco(ReceiverModel model) {
			var cart = _helperWeb.GetSessionCart();
			try {
				model.Validate();

				cart.ShippingAddress.Receiver = model;

				await _cartService.CalculateCart(cart);

				_helperWeb.SetSessionCart(cart);

				var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
				var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
				SetLayoutVars(catalogSettings);

				return RedirectToAction("ConfirmarCarrinho");
			} catch (MotivaiException ex) {
				ViewBag.Mensagem = ex.Message;
				await LogException(ex);
				return await ConfirmarEndereco();

			} catch (Exception ex) {
				await LogException(ex, "Pedido - Confirmar Endereço", "Erro ao confirmar endereço de entrega");
				cart.SetError("Ocorreu um erro ao confirmar o endereço de entrega, se persistir contate o administrador do sistema.");
				_helperWeb.SetSessionCart(cart);
				return RedirectToAction("Index", "Carrinho");
			}
		}

		/* [HttpGet] */
		public async Task<IActionResult> PoliticaEntregaTroca() {
			ViewBag.EtapaResgate = "policy";
			var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
			SetLayoutVars(catalogSettings);
			if (!catalogSettings.PagesSettings.EnableShippingPolicy)
				return RedirectToAction("ConfirmarCarrinho");
			var model = await _campaignRepository.GetCampaignSettings(campaignId);
			return View(model);
		}

		/* [HttpPost] */
		public async Task<IActionResult> ContinuarResgate([FromForm] string content, [FromForm] string version, [FromForm] string termosAceito) {
			Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var campaignParams = await _campaignRepository.GetCampaignSettings(campaignId);
			var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
			SetLayoutVars(catalogSettings);
			if (campaignParams.Parametrizations.AcceptRegulationRequired && string.IsNullOrEmpty(termosAceito)) {
				return RedirectToAction("PoliticaEntregaTroca", "Pedido");
			}
			// TODO: registrar o Content e Version da Política que foi aceita
			if (!string.IsNullOrEmpty(termosAceito)) {
				var cart = _helperWeb.GetSessionCart();
				if (cart == null) {
					return RedirectToAction("Index", "Carrinho");
				}
				cart.AcceptShippingPolicy(content, version);
				_helperWeb.SetSessionCart(cart);
			}
			return RedirectToAction("ConfirmarCarrinho", "Pedido");
		}

		[HttpGet]
		public async Task<ActionResult> ConfirmarCarrinho() {
			ViewBag.EtapaResgate = "cart";

			var cart = _helperWeb.GetSessionCart();
			if (cart == null)
			{
				return RedirectToAction("Index", "Carrinho");
			}

			var participantSession = _helperWeb.GetParticipantSession();
			try {
				cart.SetParticipantInfo(await LoadContact(participantSession));
				var pageModel = new OrderConfirmationPageModel(new CartViewModel(cart));

				var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
				var campaignParams = await _campaignRepository.GetCampaignSettings(campaignId);

				// Valida se o token foi confirmado
				if (campaignParams.Parametrizations.RequireRedeemToken && !cart.WasTokenConfirmed()) {
					throw MotivaiException.ofValidation("É necessário confirmar o código de segurança para prosseguir com o pedido.");
				}
				_helperWeb.SetSessionCart(cart);

				var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
				pageModel.SetParameters(campaignParams, catalogSettings);
				return View(pageModel);
			} catch (Exception ex) {
				await LogException(ex, "Pedido - Confirmação", "Erro ao carregar confirmação do carrinho");
				cart.SetErrorIfMotivaiException(ex, "Ocorreu um erro ao carregar confirmação do carrinho, se persistir contate o nosso atendimento.");
				_helperWeb.SetSessionCart(cart);
				return RedirectToAction("Index", "Carrinho");
			}
		}

		private async Task<ParticipantDataModel> LoadContact(UserPrincipal participantSession)
		{
			var contact = await _participantRepository.GetPrincipalContact(
					await _helperWeb.GetCampaignIdForCurrentDomain(), participantSession.UserId);
			if (contact == null)
			{
				return new ParticipantDataModel()
				{
					Documento = participantSession.Document,
					Nome = participantSession.Name,
					Email = participantSession.Email
				};
			}
			return new ParticipantDataModel()
			{
				Documento = contact.Document,
				Nome = contact.Name,
				Telefone = contact.Telephone,
				Celular = contact.Cellphone,
				Email = contact.Email
			};
		}

		[HttpPost]
		public async Task<ActionResult> FinalizarResgate([FromForm] ReceiverModel receiver,
				[FromForm] string contentId, [FromForm] string version,
				[FromForm] string timezoneoffset, [FromForm] string timezone, [FromForm] bool accepted)
		{
			var cartSession = _helperWeb.GetSessionCart();
			if (cartSession == null)
				return RedirectToAction("Index", "Carrinho");

			if (!accepted)
			{
				cartSession.SetError("É necessário aceitar os termos da política de entrega e troca para finalizar.");
				_helperWeb.SetSessionCart(cartSession);
				return RedirectToAction("ConfirmarCarrinho", "Pedido");
			}
			var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);

			if (campaignSettings.Parametrizations.RequireCepToFinish())
			{
				try
				{
					receiver.Validate();
				}
				catch (Exception ex)
				{
					await LogException(ex, "Pedido - Dados Recebedor");
					cartSession.SetError(ex.Message);
					_helperWeb.SetSessionCart(cartSession);
					return RedirectToAction("ConfirmarCarrinho", "Pedido");
				}
			}

			// Impedir recriar o pedido
			if (!string.IsNullOrEmpty(cartSession.InternalOrderNumber))
				return RedirectToAction("Resumo", "Pedido");

			var participantSession = _helperWeb.GetParticipantSession();

			PrepareCartSessionToFinish(cartSession, participantSession, receiver, contentId, version, timezoneoffset, timezone);

			try
			{
				await _catalogRepository.CreateOrder(campaignId, cartSession);
				_helperWeb.SetSessionCart(cartSession);
			}
			catch (Exception ex)
			{
				await LogException(ex, "Pedido - Finalizar", "Erro ao finalizar resgate", true);
				cartSession.SetErrorIfMotivaiException(ex, "Ocorreu um erro durante o resgate, por favor, tente novamente.");
				_helperWeb.SetSessionCart(cartSession);
				return RedirectToAction("Index", "Carrinho");
			}
			await UpdateParticipantBalance(participantSession);
			return RedirectToAction("Resumo", "Pedido");
		}

		private void PrepareCartSessionToFinish(CartModel cartSession, UserPrincipal participantSession,
				ReceiverModel receiver, string contentId, string version, string timezoneoffset, string timezone)
		{
			cartSession.SessionId = participantSession.SessionId;
			cartSession.SessionTrackingId = participantSession.TrackingId;
			cartSession.CreationDate = DateTime.UtcNow;
			cartSession.AcceptShippingPolicy(contentId, version);
			cartSession.ShippingAddress.Receiver = receiver;
			cartSession.LocationInfo = SetConnectionInfo(cartSession.LocationInfo);

			if (participantSession.IsAccountOperator())
			{
				cartSession.AccountOperator = participantSession.GetAccountOperator();
			}

			if (string.IsNullOrEmpty(participantSession.Timezone))
			{
				cartSession.Timezone = timezone;
				cartSession.LocationInfo.Timezone = timezone;
			}
			else
			{
				cartSession.Timezone = participantSession.Timezone;
				cartSession.LocationInfo.Timezone = participantSession.Timezone;
			}

			if (int.TryParse(timezoneoffset, out var timezoneOffset))
			{
				cartSession.TimezoneOffset = timezoneOffset;
			}
			// Pega o ID do Usuário Call Center caso tenha um
			cartSession.CallCenterUserId = _helperWeb.GetCallCenterUserId();
			if (cartSession.IsFromCallCenter())
			{
				cartSession.SessionStartDate = participantSession.SessionStart;
				cartSession.SessionEndDate = DateTime.UtcNow;
			}
		}

		private async Task UpdateParticipantBalance(UserPrincipal participantSession)
		{
			// Atualiza o saldo disponível na sessão
			try
			{
				decimal balance = await _participantRepository.GetAvailableBalance(await _helperWeb.GetCampaignIdForCurrentDomain(), _helperWeb.GetUserId());
				participantSession.Balance = balance;
				_helperWeb.SetParticipantSession(participantSession);
			}
			catch (Exception ex)
			{
				await LogException(ex, "Pedido - Finalizar Resgate", "Erro ao consultar e atualizar saldo disponível após o pedido.");
			}
		}

		[HttpGet]
		public async Task<ActionResult> Resumo() {
			var cart = _helperWeb.GetSessionCart();
			if (cart == null)
				return RedirectToAction("Index", "Carrinho");
			ViewBag.EtapaResgate = "resume";
			_helperWeb.RemoveCartFromSession();
			if (_helperWeb.IsCallcenterSession()) {
				_helperWeb.RemoveCallcenterSession();
			}
			var pageModel = new OrderConfirmationPageModel(new CartViewModel(cart));
			var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var campaignParams = await _campaignRepository.GetCampaignSettings(campaignId);
			var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);

			SetLayoutVars(catalogSettings);
			pageModel.SetParameters(campaignParams, catalogSettings);
			if (catalogSettings.PagesSettings.ShowCustomMessageAfterOrderConfirmation) {
				pageModel.PageCustomizations = await _campaignRepository.GetCampaignCatalogPageCustomizations(campaignId);
			}
			return View(new ResumoViewModel(campaignParams, pageModel));
		}
	}
}
