using System;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.Catalog;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Controllers
{
    [Produces("application/json")]
    public class CatalogController : BaseController
    {
        private const int PRODUCTS_LISTING_SIZE = 18;

        private readonly IHelperWeb _helperWeb;
        private readonly CampaignRepository _campaignRepository;
        private readonly CatalogRepository _catalogRepository;

        public CatalogController(IHelperWeb helperWeb, CampaignRepository campaignRepository,
            CatalogRepository catalogRepository)
        {
            this._helperWeb = helperWeb;
            this._campaignRepository = campaignRepository;
            this._catalogRepository = catalogRepository;
        }

        [HttpGet]
        public async Task<JsonResult> Footer()
        {
            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            CampaignFooterSettings catalogFooterSettings = null;
            try
            {
                catalogFooterSettings = await _campaignRepository.GetFooterSettings(campaignId);
            }
            catch (Exception ex)
            {
                await LogException(ex, "Catálogo - Footer", "Erro ao carregar o footer");
            }
            if (catalogFooterSettings == null)
                return AjaxReturn.FromError("Não foi possível carregar os dados do rodapé.");

            return AjaxReturn.GetJsonResult(new
            {
                catalogFooterSettings.SocialMedia,
                catalogFooterSettings.AttendancePeriod,
                catalogFooterSettings.AttendancePhoneNumber,
                catalogFooterSettings.PagesSettings.EnablePolicy,
                catalogFooterSettings.PagesSettings.EnableShippingPolicy,
                catalogFooterSettings.PagesSettings.EnableRegulation,
                catalogFooterSettings.PagesSettings.EnableFaq,
                catalogFooterSettings.PagesSettings.EnableContact
            });
        }

        [HttpGet]
        public async Task<JsonResult> SpecialShops()
        {
            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_catalogRepository.GetSpecialShops(campaignId, participant.UserId, participant.ParticipantId));
        }

        [HttpGet]
        public async Task<JsonResult> FeaturedMediaboxes()
        {
            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_catalogRepository.GetMediaBoxesForHome(campaignId, participant.UserId, participant.ParticipantId));
        }

        [HttpGet]
        public async Task<JsonResult> FeatureHomeProducts()
        {
            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_catalogRepository.GetFeaturedProductsForHome(campaignId, participant.UserId,
                participant.ParticipantId, participant.Balance));
        }

        [HttpGet]
        public async Task<JsonResult> FeaturedVouchers()
        {
            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            var participant = _helperWeb.GetParticipantSession();

            var searchProductsResult = await _catalogRepository.SearchProducts(campaignId, participant.UserId, participant.ParticipantId, null,
                    null, null, null, null, null, null, null, null, "ValeVirtual", null, null, null, participant.Balance, 0, 10);

            return AjaxReturn.GetJsonResult(searchProductsResult.Result);
        }

        [HttpGet]
        public async Task<JsonResult> RandomSpecialShop()
        {
            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_catalogRepository.GetRandomSpecialShop(campaignId, participant.UserId, participant.ParticipantId));
        }

        [HttpGet]
        public async Task<JsonResult> HomeLayoutSettings()
        {
            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
            return AjaxReturn.GetJsonResult(catalogSettings.PagesSettings.ShowSpecialShopAsFeaturedInHomePage);
        }

        [HttpPost]
        public async Task<JsonResult> Search([FromBody] SearchFilterModel filters)
        {
            var jsonResult = new AjaxReturn<SearchModel>();
            try
            {
                var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
                var participant = _helperWeb.GetParticipantSession();
                jsonResult.Return = await _catalogRepository.SearchProducts(campaignId, participant.UserId, participant.ParticipantId, null,
                    filters.q, filters.d, filters.c, filters.sb, filters.p, filters.f, filters.cr, filters.v, null, filters.srt,
                    filters.pd, filters.pa, participant.Balance, filters.fr ?? 0, filters.tk ?? PRODUCTS_LISTING_SIZE);
            }
            catch (Exception ex)
            {
                await LogException(ex);
                if (ex is MotivaiException)
                    jsonResult.SetError(ex.Message);
                else
                    jsonResult.SetError("Ocorreu um erro durante a pesquisa de produto, por favor, tente novamente.");
            }
            return jsonResult.GetJsonResult();
        }
    }
}
