using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Services.Security;

namespace Motivai.WebCatalog.Controllers
{
    [Produces("application/json")]
    [Route("settings/security")]
    public class CampaignSecurityController : BaseController
    {
        private readonly IHelperWeb helperWeb;
        private readonly CampaignSecurityService authenticationService;

        public CampaignSecurityController(IHelperWeb helperWeb, CampaignSecurityService authenticationService)
        {
            this.helperWeb = helperWeb;
            this.authenticationService = authenticationService;
        }

        [HttpGet("authentication-mfa")]
        public async Task<JsonResult> GetAuthenticationMfaParameters()
        {
            return await ExecuteAndGetJson(this.authenticationService.GetAuthenticationMfaParameters(await this.helperWeb.GetCampaignIdForCurrentDomain()),
                "Não foi possível consultar os parametros da camapanha, por favor, tente novamente.");
        }
    }
}