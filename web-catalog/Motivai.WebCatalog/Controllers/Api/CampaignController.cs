using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Controllers
{
    [Produces("application/json")]
    [Route("campaigns")]
    public class CampaignController : BaseController
    {
        private readonly IHelperWeb helperWeb;
        private readonly CampaignRepository campaignRepository;

        public CampaignController(IHelperWeb helperWeb, CampaignRepository campaignRepository)
        {
            this.helperWeb = helperWeb;
            this.campaignRepository = campaignRepository;
        }

        [HttpGet("coinname")]
        public async Task<JsonResult> GetCoinName()
        {
            return await ExecuteAndGetJson(this.campaignRepository.GetCoinName(await this.helperWeb.GetCampaignIdForCurrentDomain()),
                "Não foi possível carregar as configurações da campanha, por favor, tente novamente.");
        }

        [HttpGet("cards/prepaid/parametrizations")]
        public async Task<JsonResult> GetCardParametrizations()
        {
            return await ExecuteAndGetJson(this.campaignRepository.GetCardParametrizations(await this.helperWeb.GetCampaignIdForCurrentDomain()),
                "Não foi possível carregar as configurações do cartão, por favor, tente novamente.");
        }
    }
}