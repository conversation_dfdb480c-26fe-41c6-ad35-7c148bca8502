using System;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Repositories;

namespace Motivai.WebCatalog.Controllers
{
    public class AddressesController : BaseController
    {
        private readonly CorreiosRepository _correiosRepository;

        public AddressesController(CorreiosRepository correiosRepository)
        {
            _correiosRepository = correiosRepository;
        }

        [HttpGet]
        public async Task<JsonResult> Query(string id)
        {
            if (String.IsNullOrEmpty(id) || id.Replace("-", "").Length != 8)
                return AjaxReturn.FromError("CEP inválido.");

            return await ExecuteAndGetJson(_correiosRepository.QueryCep(id.Replace("-", "")));
        }
    }
}