using System;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Services.Security;

namespace Motivai.WebCatalog.Controllers
{
    public class TokenController : BaseController
    {
        private readonly TokenService _tokenService;

        public TokenController(TokenService tokenService)
        {
            this._tokenService = tokenService;
        }

        [HttpGet("token/methods/info")]
        public async Task<JsonResult> GetObfuscatedSecurityInfo()
        {
            return await ExecuteAndGetJson(_tokenService.GetTokenSecurityInfo(), "Não foi possível carregar os dados de contato.");
        }

        [HttpPost("token/order/issue")]
        public async Task<JsonResult> IssueShoppingCartSecurityToken()
        {
            return await ExecuteAndGetJson(_tokenService.IssueShoppingCartSecurityToken(), "Não foi possível enviar o código de segurança.");
        }

        [HttpPut("token/order/confirmation")]
        public async Task<JsonResult> ConfirmShoppingCartSecurityToken([FromBody] dynamic payload)
        {
            string token = payload.token;
            if (string.IsNullOrEmpty(token))
                return AjaxReturn.FromError("Código de segurança inválido.");
            try
            {
                _tokenService.ConfirmShoppingCartSecurityToken(token);
                return AjaxReturn.GetJsonResult(true);
            }
            catch (MotivaiException ex)
            {
                return AjaxReturn.FromError(ex.Message);
            }
            catch (Exception ex)
            {
                await LogException(ex);
                return AjaxReturn.FromError("Não foi possível confirmar o código de segurança, por favor, tente novamente.");
            }
        }
    }
}