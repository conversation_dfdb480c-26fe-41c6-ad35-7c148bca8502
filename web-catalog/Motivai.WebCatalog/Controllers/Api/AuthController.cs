using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Security;
using Motivai.WebCatalog.Services.Auth;

namespace Motivai.WebCatalog.Controllers
{
    [Produces("application/json")]
    [Route("auth")]
    public class AuthController : BaseController
    {
        private readonly IHelperWeb _helperWeb;
        private readonly AuthenticationService _authenticationService;

        public AuthController(IHelperWeb helperWeb, AuthenticationService authenticationService)
        {
            this._helperWeb = helperWeb;
            this._authenticationService = authenticationService;
        }

        [HttpPost("authentication/code")]
        public async Task<JsonResult> SendAuthorizationCode([FromBody] SimpleSecurityCodeRequest securityCodeRequest)
        {
            return await ExecuteAndGetJson(this._authenticationService.SendAuthorizationCode(await this._helperWeb.GetCampaignIdForCurrentDomain(), _helperWeb.GetUserId(), securityCodeRequest),
                "Não foi possível enviar o código de autenticação, por favor, tente novamente.");
        }

        [HttpPut("authentication/code")]
        public async Task<JsonResult> ValidateAuthorizationCode([FromBody] SimpleSecurityCodeValidation securityCodeRequest)
        {
            return await ExecuteAndGetJson(this._authenticationService.ValidateAuthorizationCode(await this._helperWeb.GetCampaignIdForCurrentDomain(), _helperWeb.GetUserId(), securityCodeRequest),
                "Não foi confirmar o código de autenticação, por favor, tente novamente.");
        }
    }
}