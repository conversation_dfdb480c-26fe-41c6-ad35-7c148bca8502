using System;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Storage;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Models.Pages;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.Cart;
using Microsoft.AspNetCore.Mvc;

namespace Motivai.WebCatalog.Controllers
{
	public class CarrinhoController : BaseController {
		private readonly IHelperWeb _helperWeb;
		private readonly ICryptography _cryptography;
		private readonly IStorageIntegrator _storage;
		private readonly CartManager _cartService;
		private readonly CampaignRepository _campaignRepository;
		private readonly CatalogRepository _catalogRepository;
		private readonly UserParticipantRepository _participantRepository;
		private readonly ProductRepository _productRepository;

		public CarrinhoController(IHelperWeb helperWeb, ICryptography cryptography, IStorageIntegrator storage,
			CartManager cartService,
			CampaignRepository campaignRepository, CatalogRepository catalogRepository,
			UserParticipantRepository participantRepository, ProductRepository productRepository) {
			this._helperWeb = helperWeb;
			this._cryptography = cryptography;
			this._storage = storage;
			this._cartService = cartService;
			this._campaignRepository = campaignRepository;
			this._catalogRepository = catalogRepository;
			this._participantRepository = participantRepository;
			this._productRepository = productRepository;
		}

		[HttpGet]
		public async Task<IActionResult> Index() {
			Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
			var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
			var catalogSettings = await _campaignRepository.GetPagesSettings(campaignId);
			campaignSettings.Pages = catalogSettings.PagesSettings;
			return View(new CatalogPageViewModel(campaignSettings));
		}

		// Popup cadastro de endereco
		[HttpGet]
		public async Task<ActionResult> NovoEndereco() {
			var campaignSettings = await _campaignRepository.GetCampaignSettings(_helperWeb.GetCampaignId());
			if (!campaignSettings.Parametrizations.AllowChangeShippingAddress) {
				return ForwardToNotFound(true);
			}
			return View(campaignSettings);
		}

		// Popup de informacoes de entrega
		[HttpGet]
		public ActionResult InformacoesEntrega() => View();

		[HttpGet]
		public ActionResult TokenPedido() => View();

		protected async Task<CartModel> GetCart() {
			var cartModel = await _helperWeb.GetSessionCartAsync();
			if (cartModel != null)
				return cartModel;
			var campaignId = _helperWeb.GetCampaignId();
			var participant = _helperWeb.GetParticipantSession();
			var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
			cartModel = CartModel.For(campaignId, campaignSettings.Type, participant);
			_helperWeb.SetSessionCart(cartModel);
			return cartModel;
		}

		protected void UpdateCart(CartModel cartModel) {
			_helperWeb.SetSessionCart(cartModel);
		}

		///<summary>
		/// Cria o modelo para a view.
		/// Se a compra de pontos estiver habilitada então verifica se precisa comprar e quanto.
		/// Caso o carrinho esteja com erro é carregado a mensagem e marcada como "mostrada".
		///</summary>
		private async Task<CartViewModel> CreateCartViewModel(CartModel cart) {
			var viewModel = CartViewModel.From(cart, await _campaignRepository.GetCoinName(cart.CampaignId));

			// Encriptar ID para o carrinho
			if (viewModel.ShippingAddress != null && viewModel.ShippingAddress.Id != null) {
				viewModel.ShippingAddress.Id = _cryptography.Encrypt(viewModel.ShippingAddress.Id);
			}
			var campaignSettings = await _campaignRepository.GetCampaignSettings(cart.CampaignId);

			// Verifica se permite e se precisa comprar pontos
			if (campaignSettings.Parametrizations.AllowBuyPoints && cart.GetCartTotal() > 0) {
				var participant = _helperWeb.GetParticipantSession();
				if (cart.GetCartTotal() > participant.Balance) {
					viewModel.ComprarPontos = true;
					viewModel.CurrentBalance = participant.Balance;
					viewModel.AmountToPurchase = cart.GetCartTotal() - participant.Balance;
				}
			}
			if (campaignSettings.Parametrizations.HasMaximumNumberCartItems() && cart.GetTotalItems() == 1) {
				viewModel.ShippingHint = cart.GetProducts().FirstOrDefault().ShippingHint;
			}
			return viewModel;
		}

		[HttpPost]
		public async Task<JsonResult> CalculaFreteItem([FromBody] dynamic payload) {
			// Pode ser o ID encriptado do endereço cadastrado ou um CEP digitado
			string cep = payload.c;
			if (String.IsNullOrEmpty(cep))
				return AjaxReturn.FromError("CEP inválido.");

			string elasticId = payload.id;
			string skuCode = payload.md;
			int quantity = 1;
			if (!string.IsNullOrEmpty((string) payload.q) && !int.TryParse((string) payload.q, out quantity))
				quantity = 1;

			try {
				ShippingCostResult shippingPrice = null;

				if (cep.Length == 8 || cep.Length == 9) {
					cep = cep.Replace("-", "");
					shippingPrice = await _cartService.CalculateItemShippingCostForCep(cep, quantity, elasticId, skuCode);
				} else {
					Guid addressId = ParserHelper.DecryptGuid(_cryptography, cep);
					shippingPrice = await _cartService.CalculateItemShippingCostForAddress(addressId, quantity, elasticId, skuCode);
				}
				if (shippingPrice.OccurredError)
					throw MotivaiException.ofValidation(shippingPrice.ErrorMessage);
				if (shippingPrice.Factories != null) {
					shippingPrice.Factories.ForEach(f => {
						f.CompanyId = _cryptography.Encrypt(f.CompanyId);
						f.FactoryId = _cryptography.Encrypt(f.FactoryId);
					});
				}
				return AjaxReturn.GetJsonResult<DetailsShippingCostModel>(DetailsShippingCostModel.From(shippingPrice));
			} catch (Exception ex) {
				await LogException(ex);
				if (ex is MotivaiException)
					return AjaxReturn.FromError(ex.Message);
				return AjaxReturn.FromError("Ocorreu um erro durante o cálculo do frete para o item.");
			}
		}

		[HttpPost]
		public async Task<ActionResult> Add(string id, string md, string fabricante, int qtde, string cep, string priceDefinedByParticipant) {
			if (qtde <= 0) {
				qtde = 1;
			}
			try {
				 await _cartService.AddItemToCart(id, md, fabricante, qtde, cep, Request.Form, priceDefinedByParticipant);
			} catch (Exception ex) {
				await ExceptionLogger.LogException(ex, "Cart - Add Item", "Erro ao adicionar item no carrinho.");
			}
			return RedirectToAction("Index", "Carrinho");
		}

		[HttpPost]
		public async Task<JsonResult> AtualizaQtde(string id, [FromBody] dynamic jsonPayload) {
			int q = -1;
			if (!int.TryParse((string) jsonPayload.q, out q)) {
				return AjaxReturn.FromError("Informe a quantidade que deseja atualizar.");
			}
			var retorno = new AjaxReturn<CartViewModel>();
			try {
				var cart = await GetCart();
				cart.UpdateItemQuantityIfAllowed(id, q);
				await _cartService.CalculateCart(cart);
				retorno.Return = await CreateCartViewModel(cart);
				UpdateCart(cart);
			} catch (Exception ex) {
				if (ex is MotivaiException)
					retorno.SetError(ex.Message);
				else
					retorno.SetError("Ocorreu um erro durante a atualização do item, por favor, tente novamente.");
				await LogException(ex);
			}
			return retorno.GetJsonResult();
		}

		[HttpDelete]
		public async Task<JsonResult> Remove(string id) {
			var result = new AjaxReturn<CartViewModel>();
			try {
				var cart = await _cartService.RemoveItemFromCart(id);
				result.Return = await CreateCartViewModel(cart);
			} catch (Exception ex) {
				await LogException(ex, "Cart - Remove Item", "Erro ao remover item do carrinho");
				result.SetError(ex, "Ocorreu um erro durante a remoção do item, por favor, tente novamente");
			}
			return result.GetJsonResult();
		}

		[HttpGet]
		public async Task<JsonResult> GetItensCarrinho() {
			var result = new AjaxReturn<CartViewModel>();
			try {
				var cart = await GetCart();
				result.Return = await CreateCartViewModel(cart);
				UpdateCart(cart);
			} catch (Exception ex) {
				await LogException(ex, "Cart - Get", "Erro ao carregar o carrinho", true);
				return AjaxReturn.FromError("Ocorreu um erro ao carregar o carrinho, se o erro persistir contate o suporte.");
			}
			return result.GetJsonResult();
		}

		[HttpPost]
		public async Task<JsonResult> RecalcularFreteCarrinho(string id) {
			if (string.IsNullOrEmpty(id))
				return AjaxReturn.FromError("Endereço de entrega inválido.");
			var result = new AjaxReturn<CartViewModel>();
			try {
				var cart = await GetCart();
				Guid addressId = ParserHelper.DecryptGuid(_cryptography, id);
				await _cartService.SetShippingAddressById(cart, addressId);
				await _cartService.CalculateCart(cart);
				result.Return = await CreateCartViewModel(cart);
				UpdateCart(cart);
			} catch (Exception ex) {
				if (ex is MotivaiException)
					result.SetError(ex.Message);
				else
					result.SetError("Ocorreu um erro durante o cálculo do frete, por favor, tente novamente.");
				await LogException(ex);
			}
			return result.GetJsonResult();
		}

		[HttpPost]
		public async Task<JsonResult> Desconto([FromBody] dynamic payload) {
			string discountCoupon = payload.coupon;
			var result = new AjaxReturn<CartViewModel>();
			try {
				var cart = await _cartService.ApplyDiscountCoupon(discountCoupon);
				result.Return = await CreateCartViewModel(cart);
			} catch (Exception ex) {
				result.SetError(ex is MotivaiException ? ex.Message : "Ocorreu um erro ao aplicar o desconto, por favor, tente novamente.");
				await LogException(ex);
			}
			return result.GetJsonResult();
		}
	}
}