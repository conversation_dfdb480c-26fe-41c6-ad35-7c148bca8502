using System;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.Session;

namespace Motivai.WebCatalog.Controllers
{
    public class BaseController : Controller
    {
        ///<summary>
        /// Efetua log da exception.
        /// Se for uma MotivaiException é verificado se precisa ser logado (método IsLoggable).
        ///</summary>
        public async Task LogException(Exception ex, string additionalInformation = null, bool forceLog = false)
        {
            await LogException(ex, null, additionalInformation, forceLog);
        }

        public async Task LogException(Exception ex, string processName, string additionalInformation, bool forceLog = false)
        {
            if (ex is MotivaiException && !forceLog && !(ex as MotivaiException).IsLoggable())
            {
                return;
            }
            await ExceptionLogger.LogException(ex, processName, additionalInformation, forceLog);
        }

        public ConnectionInfo GetConnectionInfo()
        {
            return ConnectionInfo.OfHttpContext(HttpContext);
        }

        public LocationInfo SetConnectionInfo(LocationInfo locationInfo)
        {
            if (locationInfo == null)
            {
                locationInfo = new LocationInfo();
            }
            locationInfo.ConnectionInfo = GetConnectionInfo();

            return locationInfo;
        }

        public LocationInfo CreateLocationInfo(string timezone = null)
        {
            return LocationInfo.Of(timezone, HttpContext);
        }

        public LocationInfo CreateLocationInfo(Guid sessionId, string timezone = null)
        {
            return LocationInfo.Of(sessionId.ToString(), timezone, HttpContext);
        }

        public LocationInfo CreateLocationInfo(UserPrincipal userPrincipal)
        {
            return LocationInfo.Of(userPrincipal.SessionId.ToString(), userPrincipal.Timezone, HttpContext);
        }

        ///<summary>
        /// Executa a Task recebida e retorna um JsonResult com seu retorno.
        /// Se ocorreu algum erro é efetuado o log.
        ///</summary>
        public async Task<JsonResult> ExecuteAndGetJson<T>(Task<T> func, string defaultMessageForException = null, string messageAlert = null)
        {
            JsonResult jsonResult = null;
            try
            {
                var funcResult = await AjaxReturn<T>.Execute(func, defaultMessageForException, messageAlert);
                if (funcResult != null)
                    jsonResult = funcResult.GetJsonResult();
            }
            catch (Exception ex)
            {
                await LogException(ex);
                if (ex is MotivaiException)
                    jsonResult = AjaxReturn.FromError(ex.Message);
            }
            return jsonResult;
        }

        ///<summary>
        /// Executa a Task recebida e retorna um JsonResult com seu retorno.
        /// Se ocorreu algum erro é efetuado o log.
        ///</summary>
        public async Task<JsonResult> ExecuteAndGetJsonWithRedirect<T>(Task<T> func, string pathToRedirectTo,
                string defaultMessageForException = null, string messageAlert = null)
        {
            JsonResult jsonResult = null;
            try
            {
                var funcResult = await AjaxReturn<T>.ExecuteAndReturnRedirect(func, pathToRedirectTo, defaultMessageForException, messageAlert);
                if (funcResult != null)
                    jsonResult = funcResult.GetJsonResult();
            }
            catch (Exception ex)
            {
                await LogException(ex);
                if (ex is MotivaiException)
                    jsonResult = AjaxReturn.FromError(ex.Message);
            }
            return jsonResult;
        }

        public ActionResult RedirectToNotFound()
        {
            return RedirectToAction("PaginaNaoEncontrada", "Erro");
        }

        public ActionResult ForwardToNotFound(bool withoutHeaderAndFooter = false)
        {
            if (withoutHeaderAndFooter)
            {
                ViewBag.OnlyContent = true;
            }
            return View("~/Views/Erro/PaginaNaoEncontrada.cshtml");
        }

        public ActionResult ForwardToError(string errorMessage, string helpMessage, string detailedMessage = null,
            string code = null, bool withoutHeaderAndFooter = false, bool withoutCampaignFooter = false)
        {
            if (withoutHeaderAndFooter)
            {
                ViewBag.OnlyContent = true;
            }
            else if (withoutCampaignFooter)
            {
                ViewBag.WithoutCampaignFooter = true;
            }
            return View("~/Views/Erro/Index.cshtml", ErrorModel.Of(code, errorMessage, detailedMessage, helpMessage));
        }
    }
}
