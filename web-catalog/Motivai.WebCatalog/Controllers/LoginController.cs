using System;
using System.Linq;
using System.Security;
using System.Threading.Tasks;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Security;
using Motivai.SharedKernel.Domain.Services.Security;
using Motivai.SharedKernel.Helpers;
using Motivai.SharedKernel.Helpers.Api;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.SharedKernel.Helpers.Values;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.Login;
using Motivai.WebCatalog.Models.MyAccount;
using Motivai.WebCatalog.Repositories;
using Microsoft.AspNetCore.Mvc;


namespace Motivai.WebCatalog.Controllers
{
    public class LoginController : BaseController
    {
        private const string RECOVERY_SESSION_KEY = "pwd-rec-ssn";
        private readonly IHelperWeb _helperWeb;
        private readonly LoginRepository _loginRepository;
        private readonly CampaignRepository _campaignRepository;
        private readonly ConnectionAllowListValidator _connectionAllowListValidator;

        public LoginController(IHelperWeb helperWeb, LoginRepository loginRepository,
            CampaignRepository campaignRepository)
        {
            _helperWeb = helperWeb;
            _loginRepository = loginRepository;
            _campaignRepository = campaignRepository;
            _connectionAllowListValidator = ConnectionAllowListValidator.FromEnvironmentVariable("LOGIN_RPA_ALLOWED_IP");
        }

        [HttpGet]
        [HttpPost]
        public async Task<ActionResult> Index()
        {
            Guid campaignId = Guid.Empty;
            try
            {
                if (_connectionAllowListValidator.IsAllowed(GetConnectionInfo()))
                {
                    HttpContext.Items["loginRpa"] = true;
                }

                ClearPasswordRecoverSession();
                campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            }
            catch (Exception ex)
            {
                await ExceptionLoggerMiddleware.HandleException(ex);
                return ForwardToError("Tivemos um problema durante o carregamento", "Tente novamente em alguns instantes",
                    "Se o erro persistir, por favor, contante o administrador.");
            }
            if (campaignId == Guid.Empty)
            {
                return ForwardToError("Campanha inválida", "Contate o atendimento para maiores informações", "Campanha não está configurada corretamente.");
            }
            var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
            // Verifica se a página de login está ativa
            if (!campaignSettings.Parametrizations.EnableLoginCatalog)
            {
                return ForwardToError("Login está desabilitado nesta campanha", "Contate o atendimento para maiores informações", "Não é permitido efetuar login.");
            }
            return View();
        }

        private void SetupFirstAccess(UserParticipantModel user, CampaignCatalogSettings catalogsSettings, CampaignSettingsModel campaignSettings, AjaxReturn<string> ajaxReturn)
        {
            // Flag a Sessão para obrigar o primeiro acesso
            _helperWeb.SetSessionAsFirstAccess();
            if (user.NeedSetupAuthenticationMfa)
            {
                _helperWeb.SetSessionAsNeedSetupAuthenticationMfa();
            }
            if (user.NeedAuthenticationMfa)
            {
                _helperWeb.SetSessionAsNeedAuthenticationMfa();
            }
            if (catalogsSettings.PagesSettings.EnableRegulation && campaignSettings.Parametrizations.AcceptRegulationRequired)
            {
                ajaxReturn.Redirect = Url.Action("Regulamento", "PrimeiroAcesso");
            }
            else
            {
                ajaxReturn.Redirect = Url.Action("Privacidade", "PrimeiroAcesso");
            }
        }

        private void SetupNewPrivacyPolicyAcceptance(UserParticipantModel user, CampaignCatalogSettings catalogsSettings, AjaxReturn<string> ajaxReturn)
        {
            // Flag a Sessão para obrigar o aceite na nova politica
            _helperWeb.SetNewPrivacyPolicy();

            if (user.NeedSetupAuthenticationMfa)
            {
                _helperWeb.SetSessionAsNeedSetupAuthenticationMfa();
            }
            if (user.NeedAuthenticationMfa)
            {
                _helperWeb.SetSessionAsNeedAuthenticationMfa();
            }
            ajaxReturn.Redirect = Url.Action("Privacidade", "PoliticaPrivacidade");
        }

        private void SetupNeedAuthenticationMfa(AjaxReturn<string> ajaxReturn)
        {
            _helperWeb.SetSessionAsNeedAuthenticationMfa();
            ajaxReturn.Redirect = Url.Action("AutenticacaoMfa", "Autenticacao");
        }

        private void SetupNeedSetupAuthenticationMfa(AjaxReturn<string> ajaxReturn)
        {
            _helperWeb.SetSessionAsNeedSetupAuthenticationMfa();
            ajaxReturn.Redirect = Url.Action("ConfiguracaoMfa", "Autenticacao");
        }

        private async Task DefineNextPage(Guid campaignId, CampaignSettingsModel campaignSettings, UserParticipantModel user, AjaxReturn<string> ajaxReturn)
        {
            var catalogsSettings = await _campaignRepository.GetPagesSettings(campaignId);
            // Verificar se o primeiro acesso está habilitado na campanha
            if (user.FirstAccess && campaignSettings.Parametrizations.EnableFirstAccess)
            {
                SetupFirstAccess(user, catalogsSettings, campaignSettings, ajaxReturn);

            }
            else if (user.NeedAcceptPrivacyPolicy)
            {
                SetupNewPrivacyPolicyAcceptance(user, catalogsSettings, ajaxReturn);

            }
            else if (campaignSettings.Parametrizations.EnableAuthenticationMfa)
            {
                if (user.NeedSetupAuthenticationMfa)
                {
                    SetupNeedSetupAuthenticationMfa(ajaxReturn);
                    return;
                }
                if (user.NeedAuthenticationMfa)
                {
                    SetupNeedAuthenticationMfa(ajaxReturn);
                    return;
                }
            }
            else
            {
                switch (catalogsSettings.PagesSettings.PageAfterLogin)
                {
                    case CatalogPage.MyAccount:
                        ajaxReturn.Redirect = Url.Action("Index", "MinhaConta");
                        break;
                    case CatalogPage.BillPayments:
                        ajaxReturn.Redirect = Url.Action("Index", "PagueContas");
                        break;
                    default:
                        ajaxReturn.Redirect = Url.Action("Index", "Home");
                        break;
                }
            }
        }

        [HttpPost]
        public async Task<JsonResult> Logar([FromBody] LoginModel model)
        {
            return await this.Authenticate(model);
        }

        [HttpPost]
        public async Task<JsonResult> RpaLogar([FromBody] LoginModel model)
        {
            return await this.Authenticate(model);
        }

        private async Task<JsonResult> Authenticate([FromBody] LoginModel model)
        {
            var loginResult = new AjaxReturn<string>();
            try
            {
                var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
                if (campaignId == Guid.Empty)
                {
                    throw MotivaiException.of(ErrorType.Configuration, "Campanha não configurada, por favor, contate o administrador.");
                }
                var campaignSettings = await _campaignRepository.GetCampaignSettings(campaignId);
                // Verifica se a página de login está ativa
                if (!campaignSettings.Parametrizations.EnableLoginCatalog)
                {
                    throw MotivaiException.ofValidation("O login está desabilitado nesta campanha.");
                }

                if (model == null || string.IsNullOrEmpty(model.Login) || string.IsNullOrEmpty(model.Password))
                {
                    throw MotivaiException.ofValidation("Preencha o usuário e senha.");
                }

                model.Origin = "Catálogo";
                model.ConnectionInfo = ConnectionInfo.OfHttpContext(HttpContext);

                var user = await _loginRepository.AuthenticateParticipant(campaignId, model);
                if (user == null)
                    throw new SecurityException();
                user.Timezone = model.Timezone;
                _helperWeb.ClearSession();
                _helperWeb.CreateParticipantSession(campaignId, user);
                await DefineNextPage(campaignId, campaignSettings, user, loginResult);
            }
            catch (SecurityException)
            {
                loginResult.SetError("Usuário e/ou senha inválido(s).");
            }
            catch (MotivaiException ex)
            {
                await LogException(ex, "Login", "Erro durante a autenticação.");
                if (ex.ErrorType == ErrorType.Timeout)
                {
                    loginResult.SetError("Tempo de espera da autenticação excedido, por favor, tente novamente.");
                }
                else
                {
                    loginResult.SetError(ex.Message);
                }
            }
            catch (Exception ex)
            {
                await LogException(ex, "Login", "Erro durante a autenticação.");
                loginResult.SetError(ex.Message);
            }
            return loginResult.GetJsonResult();
        }



        public async Task<JsonResult> RelembrarSenha([FromBody] dynamic payload)
        {
            if (payload.login == null || payload.email == null)
            {
                return AjaxReturn.FromError("Preencha o login e e-mail para verificação.");
            }
            string login = payload.login;
            string email = payload.email;
            try
            {
                return AjaxReturn.GetJsonResult(await _loginRepository.ResetPassword(await _helperWeb.GetCampaignIdForCurrentDomain(), login, email));
            }
            catch (Exception ex)
            {
                await LogException(ex);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Não foi possível verificar usuário, por favor, tente novamente.");
            }
        }

        private bool HasValidPasswordRecoverySession()
        {
            var recoverySession = _helperWeb.GetFromSession<PasswordRecoverySession>(RECOVERY_SESSION_KEY);
            return recoverySession != null && recoverySession.IsValid();
        }

        private void StartPasswordRecoverySession(PasswordRecoveryContact participant)
        {
            var recoverySession = PasswordRecoverySession.OfContact(participant);
            _helperWeb.AddToSession<PasswordRecoverySession>(RECOVERY_SESSION_KEY, recoverySession);
        }

        private PasswordRecoverySession GetPasswordRecoverySession()
        {
            return _helperWeb.GetFromSession<PasswordRecoverySession>(RECOVERY_SESSION_KEY);
        }

        private void UpdatePasswordRecoverySession(PasswordRecoverySession recoverySession)
        {
            _helperWeb.AddToSession<PasswordRecoverySession>(RECOVERY_SESSION_KEY, recoverySession);
        }

        private void ClearPasswordRecoverSession()
        {
            _helperWeb.RemoveFromSession(RECOVERY_SESSION_KEY);
        }

        public async Task<JsonResult> RecuperacaoSenha([FromBody] dynamic payload)
        {
            if (payload.login == null || payload.email == null)
            {
                return AjaxReturn.FromError("Preencha o login e e-mail para verificação.");
            }
            string login = payload.login;
            string email = payload.email;
            try
            {
                var participantContact = await _loginRepository.SearchParticipantToPasswordRecovery(await _helperWeb.GetCampaignIdForCurrentDomain(), login, email);
                if (participantContact == null)
                    return AjaxReturn.FromError("Usuário não encontrado. Verifique seu login e e-mail.");
                StartPasswordRecoverySession(participantContact);
                return AjaxReturn.GetJsonResult(new
                {
                    canUseEmail = true,
                    canUseSms = false,
                    email = Obfuscator.ObfuscateEmail(participantContact.Email),
                    telefone = TelephoneHelper.FormatNumber(Obfuscator.ObfuscateMobilePhone(participantContact.MobilePhone))
                });
            }
            catch (Exception ex)
            {
                await LogException(ex);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Não foi possível verificar usuário, por favor, tente novamente.");
            }
        }

        public async Task<JsonResult> EnviaToken([FromBody] dynamic payload)
        {
            if (!HasValidPasswordRecoverySession())
                return AjaxReturn.FromError("Sessão de recuperação de senha inválida, por favor, atualize para maior segurança.");

            if (payload.formaEnvio == null)
                return AjaxReturn.FromError("Selecione a forma de envio do código de segurança.");
            string sendForm = payload.formaEnvio;
            if (sendForm == "SMS")
            {
                return AjaxReturn.FromError("Forma de envio não permitida.");
            }
            try
            {
                var recoverySession = GetPasswordRecoverySession();
                recoverySession.SendForm = sendForm;
                var token = await _loginRepository.SendPasswordRecoveryToken(await _helperWeb.GetCampaignIdForCurrentDomain(), recoverySession);
                recoverySession.SecurityToken = token;
                UpdatePasswordRecoverySession(recoverySession);
                return AjaxReturn.GetJsonResult(true);
            }
            catch (Exception ex)
            {
                await LogException(ex);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Não foi possível enviar o token, por favor, tente novamente.");
            }
        }

        public async Task<JsonResult> ValidaToken([FromBody] dynamic payload)
        {
            if (!HasValidPasswordRecoverySession())
                return AjaxReturn.FromError("Sessão de recuperação de senha inválida, por favor, atualize para maior segurança.");
            string token = payload.token;
            try
            {
                var recoverySession = GetPasswordRecoverySession();
                recoverySession.EnteredToken = token;
                UpdatePasswordRecoverySession(recoverySession);
                if (recoverySession.HasEnteredCorrectToken())
                    return AjaxReturn.GetJsonResult(true);
                return AjaxReturn.FromError("Código de segurança inválido.");
            }
            catch (Exception ex)
            {
                await LogException(ex);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Não foi possível enviar o código de segurança, por favor, tente novamente.");
            }
        }

        public async Task<JsonResult> AlteraSenha([FromBody] dynamic payload)
        {
            if (!HasValidPasswordRecoverySession())
                return AjaxReturn.FromError("Sessão de recuperação de senha inválida ou expirada, por favor, atualize a página para maior segurança.");

            if (string.IsNullOrEmpty((string)payload.novaSenha) || string.IsNullOrEmpty((string)payload.confirmacaoSenha))
            {
                return AjaxReturn.FromError("Preencha a senha e a confirmação de senha.");
            }
            try
            {
                var recoverySession = GetPasswordRecoverySession();
                if (!recoverySession.HasEnteredCorrectToken())
                {
                    ClearPasswordRecoverSession();
                    return AjaxReturn.FromError("Código de segurança inválido, por favor, atualize a página para maior segurança.");
                }
                recoverySession.Origin = "Catálogo";
                recoverySession.NewPassword = payload.novaSenha;
                recoverySession.ConfirmPassword = payload.confirmacaoSenha;
                var result = await _loginRepository.UpdatePassword(await _helperWeb.GetCampaignIdForCurrentDomain(), recoverySession);
                if (result)
                {
                    ClearPasswordRecoverSession();
                }
                return AjaxReturn.GetJsonResult(result);
            }
            catch (Exception ex)
            {
                await LogException(ex);
                if (ex is MotivaiException)
                    return AjaxReturn.FromError(ex.Message);
                return AjaxReturn.FromError("Não foi possível alterar a senha, por favor, tente novamente.");
            }
        }
    }
}