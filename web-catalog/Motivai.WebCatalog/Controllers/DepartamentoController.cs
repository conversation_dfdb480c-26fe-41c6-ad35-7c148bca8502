using System;
using System.Threading.Tasks;

using Microsoft.AspNetCore.Mvc;

using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.WebCatalog.Helpers;
using Motivai.WebCatalog.Models.Base;
using Motivai.WebCatalog.Models.Catalog;
using Motivai.WebCatalog.Models.Pages.Departments;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.CampaignSettingsService;

namespace Motivai.WebCatalog.Controllers
{
    [Produces("application/json")]
    public class DepartamentoController : BaseController
    {
        private const int PRODUCT_LISTING_SIZE = 10;

        private readonly IHelperWeb _helperWeb;
        private readonly ICryptography _cryptography;
        private readonly CampaignSettingsService _campaignSettingsService;
        private readonly CatalogRepository _catalogRepository;

        public DepartamentoController(IHelperWeb helperWeb, ICryptography cryptography,
                CampaignSettingsService campaignSettingsService, CatalogRepository catalogRepository)
        {
            this._helperWeb = helperWeb;
            this._cryptography = cryptography;
            this._campaignSettingsService = campaignSettingsService;
            this._catalogRepository = catalogRepository;
        }

        [HttpGet]
        public async Task<ActionResult> Index(string id)
        {
            Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            Guid departmentId = ParserHelper.DecryptGuid(_cryptography, id);
            DepartmentModel department = null;
            try
            {
                department = await _catalogRepository.GetDepartmentById(campaignId, departmentId, _helperWeb.GetParticipantId());
                if (department == null)
                    return RedirectToAction("DepartamentoNaoEncontrado", "Erro");
                department.Id = id;
            }
            catch (Exception ex)
            {
                await LogException(ex, "Web - Departamento", $"Erro ao carregar o departamento. Cmp: {campaignId} - Dpt: {departmentId}", true);
                return ForwardToError("Ocorreu um erro ao carregar o departamento =(",
                    "Se o erro persistir, por favor, contate o administrador do sistema.",
                    null);
            }
            var campaignSettings = await _campaignSettingsService.GetCampaignSettings();
            return View(new DepartmentViewModel(campaignSettings, department));
        }

        [HttpGet]
        public async Task<ActionResult> Categoria(string id)
        {
            Guid campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            Guid categoryId = ParserHelper.DecryptGuid(_cryptography, id);
            CategoryModel category;

            try
            {
                category = await _catalogRepository.GetCategoryById(campaignId, categoryId, _helperWeb.GetParticipantId());
                if (category == null)
                    return RedirectToAction("DepartamentoNaoEncontrado", "Erro");
                category.Id = id;
                if (category.SubcategoryId != null)
                    category.SubcategoryId = _cryptography.Encrypt(category.SubcategoryId);
                category.Department.Id = _cryptography.Encrypt(category.Department.Id);
            }
            catch (Exception ex)
            {
                await LogException(ex, "Web - Categoria", $"Erro ao carregar a categoria. Cmp: {campaignId} - Dpt: {categoryId}", true);
                return ForwardToError("Ocorreu um erro ao carregar a categoria =(",
                    "Se o erro persistir, por favor, contate o administrador do sistema.",
                    null);
            }
            var campaignSettings = await _campaignSettingsService.GetCampaignSettings();
            return View("~/Views/Departamento/Categoria.cshtml", new CategoryViewModel(campaignSettings, category));
        }

        [HttpGet]
        public async Task<ActionResult> Subcategoria(string id)
        {
            return await Categoria(id);
        }

        [HttpGet]
        public async Task<JsonResult> CarregaMediaBox(string id)
        {
            Guid departmentId = ParserHelper.DecryptGuid(_cryptography, id);
            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_catalogRepository.GetMediaBoxesForDepartment(await _helperWeb.GetCampaignIdForCurrentDomain(),
                departmentId, participant.UserId, participant.ParticipantId));
        }

        [HttpPost]
        public async Task<JsonResult> CarregaProdutos([FromBody] SearchFilterModel filter)
        {
            if (filter == null || !filter.HasAtLeastOneDepartment())
                return AjaxReturn.FromError("Departamento inválido.");
            var campaignId = await _helperWeb.GetCampaignIdForCurrentDomain();
            var participant = _helperWeb.GetParticipantSession();
            // Carrega o ID do departamento
            var departmentId = ParserHelper.DecryptGuid(_cryptography, filter.d[0]);
            return await ExecuteAndGetJson(_catalogRepository.SearchProducts(campaignId, participant.UserId, participant.ParticipantId,
                departmentId, filter.q, filter.d, filter.c, filter.sb, filter.p, filter.f, filter.cr, filter.v, null,
                filter.srt, filter.pd, filter.pa, participant.Balance, filter.fr ?? 0, filter.tk ?? PRODUCT_LISTING_SIZE));
        }

        [HttpGet]
        public async Task<JsonResult> ProdutosMaisResgatados(string id)
        {
            Guid departmentId = ParserHelper.DecryptGuid(_cryptography, id);
            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_catalogRepository.GetMostRedeemedProducts(_helperWeb.GetCampaignId(),
                participant.UserId, participant.ParticipantId, departmentId, participant.Balance));
        }

        [HttpGet]
        public async Task<JsonResult> ProdutosMaisVistos(string id)
        {
            Guid departmentId = ParserHelper.DecryptGuid(_cryptography, id);
            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_catalogRepository.GetMostSeenProducts(_helperWeb.GetCampaignId(),
                participant.UserId, participant.ParticipantId, departmentId, participant.Balance));
        }

        [HttpGet]
        public async Task<JsonResult> ProdutosOfertas(string id)
        {
            Guid departmentId = ParserHelper.DecryptGuid(_cryptography, id);
            var participant = _helperWeb.GetParticipantSession();
            return await ExecuteAndGetJson(_catalogRepository.GetProductsOffersByDepartment(_helperWeb.GetCampaignId(),
                participant.UserId, participant.ParticipantId, departmentId, participant.Balance));
        }
    }
}