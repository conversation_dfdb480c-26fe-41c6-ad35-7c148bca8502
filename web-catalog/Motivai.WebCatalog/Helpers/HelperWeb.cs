﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Motivai.SharedKernel.Helpers.Cache;
using Motivai.SharedKernel.Helpers.Exceptions;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.WebCatalog.Models.MyAccount;
using Motivai.WebCatalog.Models.Order;
using Motivai.WebCatalog.Models.Session;
using Motivai.WebCatalog.Repositories;
using Motivai.WebCatalog.Services.Cache;
using Microsoft.AspNetCore.Http;

namespace Motivai.WebCatalog.Helpers
{
    public class HelperWeb : IHelperWeb
    {
        public const string USERPRINCIPAL_KEY = "User_Principal";
        public const string CALLCENTER_SESSION_KEY = "callcnt-user";

        public const int FIRSTACCESS_VALUE = 1;
        public const string FIRSTACCESS_FLAG = "FirstAccess";
        public const int NEWPRIVACY_VALUE = 1;
        public const string NEWPRIVACY_FLAG = "NewPrivacy";
        public const int NEED_AUTHENTICATION_MFA_VALUE = 1;
        public const string NEED_AUTHENTICATION_MFA_FLAG = "NeedAuthMfa";
        public const int NEED_SETUP_AUTHENTICATION_MFA_VALUE = 1;
        public const string NEED_SETUP_AUTHENTICATION_MFA_FLAG = "NeedSetupAuthMfa";
        private const string CART_KEY = "Cart";

        private readonly CustomCache _cache;
        private readonly CampaignRepository _campaignRepository;

        public HelperWeb(CustomCache cache, CampaignRepository campaignRepository)
        {
            this._cache = cache;
            this._campaignRepository = campaignRepository;
        }

        public async Task<Guid> GetCampaignIdForCurrentDomain()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            var host = httpContext.Request.Host;
            try
            {
                return await GetCampaignIdForDomainAsync(host.Host);
            }
            catch (Exception ex)
            {
                await ExceptionLogger.LogException(ex, "Catálogo - Identificação da Campanha", $"Erro ao carregar campanha da URL {host.Host}", true);
                throw MotivaiException.ofException("Ocorreu um erro ao carregar a campanha, se persistir, entre em contato com nosso atendimento.", ex);
            }
        }

        private Task<Guid> GetCampaignIdForDomainAsync(string domain)
        {
            if (domain == "localhost")
            {
                return Task.FromResult(Guid.Parse("f18716da-fffd-4ab5-866b-7b12b10dec39"));
            }
            return _cache.GetOrCreate(SharedCacheKeysPrefix.WEB_CAMPAIGN_ID_BY_CATALOG_DOMAIN + domain, async () =>
            {
                return await _campaignRepository.GetCampaignIdByDomain(domain);
            });
        }

        public void AddToSession<T>(string sessionKey, T value)
        {
            RequestContextManager.Instance.CurrentContext.Session.Set<T>(sessionKey, value);
        }

        public T GetFromSession<T>(string sessionKey)
        {
            return RequestContextManager.Instance.CurrentContext.Session.Get<T>(sessionKey);
        }

        public bool HasItemInSession(string sessionKey)
        {
            return RequestContextManager.Instance.CurrentContext.Session.Keys.Contains(sessionKey);
        }

        public void RemoveFromSession(string sessionKey)
        {
            RequestContextManager.Instance.CurrentContext.Session.Remove(sessionKey);
        }

        public void StartCallcenterSession(Guid callcenterUserId, string callcenterUsername)
        {
            if (callcenterUserId == Guid.Empty)
            {
                this.ClearSession();
                throw MotivaiException.ofValidation("Usuário do call center inválido.");
            }
            var httpContext = RequestContextManager.Instance.CurrentContext;
            httpContext.Session.Set<Guid>(CALLCENTER_SESSION_KEY, callcenterUserId);
            httpContext.Session.SetString("callcenter-username", callcenterUsername);
            httpContext.Session.SetInt32("callcenter-session-flag", 1);
        }

        public Guid GetCallCenterUserId()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            if (httpContext.Session.Keys.Contains(CALLCENTER_SESSION_KEY))
            {
                return httpContext.Session.Get<Guid>(CALLCENTER_SESSION_KEY);
            }
            return Guid.Empty;
        }

        public bool IsCallcenterSession()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            return httpContext.Session.GetInt32("callcenter-session-flag") == 1;
        }

        public void RemoveCallcenterSession()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            httpContext.Session.Remove(CALLCENTER_SESSION_KEY);
        }

        public string GetCallcenterUsername()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            return httpContext.Session.GetString("callcenter-username");
        }

        public void CreateParticipantSession(Guid campaignId, UserParticipantModel user)
        {
            SetParticipantSession(new UserPrincipal(campaignId, user));
        }

        public void SetParticipantSession(UserPrincipal user)
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            httpContext.Session.Set<UserPrincipal>(USERPRINCIPAL_KEY, user);
        }

        private void RemoveParticipantSession()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            httpContext.Session.Remove(USERPRINCIPAL_KEY);
        }

        public UserPrincipal GetParticipantSession()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            return httpContext.Session.Get<UserPrincipal>(USERPRINCIPAL_KEY);
        }



        public async Task<UserPrincipal> GetParticipantSessionAsync()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            await httpContext.Session.LoadAsync();
            return httpContext.Session.Get<UserPrincipal>(USERPRINCIPAL_KEY);
        }

        public Guid GetCampaignId()
        {
            var user = GetParticipantSession();
            if(user == null)
                throw MotivaiException.ofValidation("Sessão inválida.");
            return user == null ? Guid.Empty : user.CampaignId.Value;
        }

        public Guid GetUserId()
        {
            var user = GetParticipantSession();
            return user == null ? Guid.Empty : user.UserId;
        }

        public Guid GetParticipantId()
        {
            var user = GetParticipantSession();
            return user == null ? Guid.Empty : user.ParticipantId;
        }

        public bool HasParticipantSession()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            // Não deserializar por performance
            var userLogged = httpContext.Session.GetString(USERPRINCIPAL_KEY);
            return !string.IsNullOrEmpty(userLogged);
        }

        public CartModel GetSessionCart()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            return httpContext.Session.Get<CartModel>(CART_KEY);
        }

        public async Task<CartModel> GetSessionCartAsync()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            await httpContext.Session.LoadAsync();
            return httpContext.Session.Get<CartModel>(CART_KEY);
        }

        public void SetSessionCart(CartModel cartModel)
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            httpContext.Session.Set<CartModel>(CART_KEY, cartModel);
        }

        public void RemoveCartFromSession()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            httpContext.Session.Remove(CART_KEY);
        }

        public bool IsFirstAccessSession()
        {
            var firstAccessValue = GetFromSession<int?>(FIRSTACCESS_FLAG);
            return firstAccessValue.HasValue && firstAccessValue == FIRSTACCESS_VALUE;
        }

        public bool IsNewPrivacyPolicySession()
        {
            var newPrivacyPolicy = GetFromSession<int?>(NEWPRIVACY_FLAG);
            return newPrivacyPolicy.HasValue && newPrivacyPolicy == NEWPRIVACY_VALUE;
        }

        public void ClearSession()
        {
            var httpContext = RequestContextManager.Instance.CurrentContext;
            httpContext.Session.Clear();
        }

        #region Primeiro Acesso

        public void SetSessionAsFirstAccess()
        {
            AddToSession(FIRSTACCESS_FLAG, FIRSTACCESS_VALUE);
        }

        public void SetNewPrivacyPolicy()
        {
            AddToSession(NEWPRIVACY_FLAG, NEWPRIVACY_VALUE);
        }

        public void CleanFirstAccess()
        {
            RemoveFromSession(FIRSTACCESS_FLAG);
        }

        public void CleanNewPrivacyPolicy()
        {
            RemoveFromSession(NEWPRIVACY_FLAG);
        }

        #endregion

        #region MFA

        public void SetSessionAsNeedAuthenticationMfa()
        {
            AddToSession(NEED_AUTHENTICATION_MFA_FLAG, NEED_AUTHENTICATION_MFA_VALUE);
        }

        public bool IsNeedAuthenticationMfaSession()
        {
            var needAuthenticationValue = GetFromSession<int?>(NEED_AUTHENTICATION_MFA_FLAG);
            return needAuthenticationValue.HasValue && needAuthenticationValue == NEED_AUTHENTICATION_MFA_VALUE;
        }

        public void SetSessionAsNeedSetupAuthenticationMfa()
        {
            AddToSession(NEED_SETUP_AUTHENTICATION_MFA_FLAG, NEED_SETUP_AUTHENTICATION_MFA_VALUE);
        }

        public bool IsNeedSetupAuthenticationMfaSession()
        {
            var needSetupAuthenticationValue = GetFromSession<int?>(NEED_SETUP_AUTHENTICATION_MFA_FLAG);
            return needSetupAuthenticationValue.HasValue && needSetupAuthenticationValue == NEED_SETUP_AUTHENTICATION_MFA_VALUE;
        }

        public void CleanAuthenticationMfa()
        {
            RemoveFromSession(NEED_AUTHENTICATION_MFA_FLAG);
            RemoveFromSession(NEED_SETUP_AUTHENTICATION_MFA_FLAG);
        }

        #endregion
    }
}