using System;

using Motivai.SharedKernel.Domain.Entities.References.Users;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.WebCatalog.Models.MyAccount;

namespace Motivai.WebCatalog.Models.Session
{
    public class UserPrincipal
    {
        ///<summary>
        /// ID da sessão gerado no login.
        ///</summary>
        public Guid SessionId { get; set; }
        ///<summary>
        /// ID de rastreio da ação para ClearSale.
        ///</summary>
        public string TrackingId { get; set; }

        public string Login { get; set; }
        public Guid UserId { get; set; }
        public Guid ParticipantId { get; set; }
        public string Document { get; set; }
        public PersonType Type { get; set; }
        public string Timezone { get; set; }
        public Guid? CampaignId { get; set; }
        public Guid? RankingId { get; set; }
        public decimal Balance { get; set; }
        public string Name { get; set; }
        public string Initials { get; set; }
        public string Email { get; set; }

        public Guid? UserAdminId { get; set; }
        public DateTime SessionStart { get; set; }
        public DateTime? SessionEnd { get; set; }

        ///<summary>
        /// Dados do operador da conta.
        ///</summary>
        public Guid? AccountOperatorId { get; set; }
        public Guid? AccountOperatorLoginId { get; set; }
        public string AccountOperatorDocument { get; set; }
        public string AccountOperatorEmail { get; set; }
        public AuthenticationAccess AuthenticationAccess { get; set; }
        public string RedirectUrl { get; set; }

        ///<summary>
        /// Flags da autenticação MFA.
        ///</summary>
        public bool NeedAuthenticationMfa { get; set; }
        public bool NeedSetupAuthenticationMfa { get; set; }

        public string NextUrl { get; set; }

        public UserPrincipal()
        {
        }

        public UserPrincipal(Guid campaignId, UserParticipantModel user)
        {
            SessionId = user.SessionId;
            TrackingId = Guid.NewGuid().ToString();
            CampaignId = campaignId;
            UserId = user.UserId;
            ParticipantId = user.ParticipantId;
            Timezone = user.Timezone;
            if (!string.IsNullOrEmpty(user.Name))
            {
                Name = user.Name;
                var parts = user.Name.Split(' ');
                if (parts.Length > 1)
                {
                    Initials = parts[0].Substring(0, 1) + parts[1].Substring(0, 1);
                }
                else
                {
                    Initials = parts[0].Substring(0, 1);
                }
            }
            else
            {
                Name = "Cliente";
                Initials = "C";
            }
            Email = user.Email;
            Document = user.Document;
            Type = user.Type;
            Balance = user.Balance;
            RankingId = user.RankingId;
            SessionStart = DateTime.UtcNow;
            AccountOperatorId = user.AccountOperatorId;
            AccountOperatorLoginId = user.AccountOperatorLoginId;
            AccountOperatorDocument = user.AccountOperatorDocument;
            AccountOperatorEmail = user.AccountOperatorEmail;
            NeedAuthenticationMfa = user.NeedAuthenticationMfa;
            NeedSetupAuthenticationMfa = user.NeedSetupAuthenticationMfa;
            AuthenticationAccess = user.AuthenticationAccess;
        }

        public string GetTimezonedSessionStart()
        {
            var saoPauloTimeZone = TimeZoneInfo.FindSystemTimeZoneById(Timezone ?? "America/Sao_Paulo");
            return TimeZoneInfo.ConvertTime(DateTime.Now, saoPauloTimeZone).ToString("dd/MM/yyyy HH:mm");
        }

        public bool IsAccountOperator()
        {
            return AccountOperatorId.HasValue && AccountOperatorId != Guid.Empty
                && AccountOperatorLoginId.HasValue && AccountOperatorLoginId != Guid.Empty;
        }

        public bool IsMigratedOperator()
        {
            if (AuthenticationAccess == null)
                return false;
            return IsAccountOperator() && AuthenticationAccess.Migrated;
        }

        public AccountOperator GetAccountOperator()
        {
            if (!IsAccountOperator())
            {
                return null;
            }
            return new AccountOperator
            {
                AccountOperatorId = AccountOperatorId.Value,
                AccountOperatorLoginId = AccountOperatorLoginId ?? Guid.Empty,
                AccountOperatorDocument = AccountOperatorDocument,
                AccountOperatorEmail = AccountOperatorEmail
            };
        }

        public string GetRedirectUrlByAuthenticationType()
        {
            if (this.AuthenticationAccess == null)
                return "";

            switch (this.AuthenticationAccess.AuthenticationType)
            {
                default:
                    return "";
            }

        }

        public bool IsParticipantMigrated()
        {
            return AuthenticationAccess != null && AuthenticationAccess.Migrated;
        }
    }

    public static class UserPrincipalHelper
    {
        public static string GetDisplayName(this UserPrincipal userPrincipal)
        {
            if (userPrincipal != null || !userPrincipal.NeedAuthenticationMfa && !userPrincipal.NeedSetupAuthenticationMfa)
                return userPrincipal.Name;
            return "Cliente";
        }

        public static string GetDisplayInitials(this UserPrincipal userPrincipal)
        {
            if (userPrincipal != null || !userPrincipal.NeedAuthenticationMfa && !userPrincipal.NeedSetupAuthenticationMfa)
                return userPrincipal.Initials;
            return "C";
        }
    }
}