using System;
using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities.References.Campaign;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;
using Motivai.SharedKernel.Domain.ValuesObject;
using Motivai.SharedKernel.Helpers.Cryptography;
using Motivai.SharedKernel.Helpers.Logger;
using Motivai.SharedKernel.Helpers.Strings;
using Motivai.WebCatalog.Models.Addresses;

namespace Motivai.WebCatalog.Models.Order
{
	public class CartViewModel
	{
		public string OrderNumber { get; set; }
		public DateTime? OrderDate { get; set; }
		public string Timezone { get; set; }
		public CoinName CoinName { get; set; }

		public decimal ProductsTotal { get; set; }
		public decimal ProductsShippingCost { get; set; }

		public decimal VouchersTotal { get; set; }
		public decimal VouchersShippingCost { get; set; }

		public decimal ShippingCost { get; set; }
		public decimal Total { get; set; }

		public bool ComprarPontos { get; set; }
		public decimal? CurrentBalance { get; set; }
		public decimal? AmountToPurchase { get; set; }

		public string DiscountCouponCode { get; set; }
		public decimal DiscountAmount { get; set; }

		public bool RequiresAllChildren { get; set; }
		public List<ItemViewModel> ProductsItems { get; set; }
		public List<ItemViewModel> VouchersItems { get; set; }
		public List<VirtualItem> Vouchers { get; set; }

		public string ShippingHint { get; set; }
		public AddressModel ShippingAddress { get; set; }
		public ParticipantDataModel Customer { get; set; }

		public bool? CanApprove { get; set; }

		public bool OccurredError { get; set; }
		public string ErrorMessage { get; set; }

		public string Notification { get; set; }
		public List<string> Notifications { get; set; }

		CartViewModel() { }

		public bool ContainsProductType(ProductType productType) {
			if (!HasProducts()  && !HasVouchers()) {
				return false;
			}

			return (HasProducts() && ProductsItems.Any(p => p.ProductType == productType)) ||  (HasVouchers() && VouchersItems.Any(p => p.ProductType == productType));
		}

		public bool ContainsLayoutType(ProductLayoutType layoutType) {
			if (!HasProducts()  && !HasVouchers()) {
				return false;
			}

			return (HasProducts() && ProductsItems.Any(p => p.LayoutType == layoutType)) ||  (HasVouchers() && VouchersItems.Any(p => p.LayoutType == layoutType));
		}

		public bool HasProducts() {
			return ProductsItems != null && ProductsItems.Count > 0;
		}

		public bool HasVouchers() {
			return VouchersItems != null && VouchersItems.Count > 0;
		}


		public CartViewModel(CartModel cart, ICryptography cryptography = null)
		{
			if (!string.IsNullOrEmpty(cart.InternalOrderNumber))
			{
				OrderNumber = cart.InternalOrderNumber;
				OrderDate = cart.CreationDate;
				Timezone = cart.Timezone;
			}
			DiscountCouponCode = cart.DiscountCoupon;
			DiscountAmount = cart.Discount.GetPointsOrZero();
			RequiresAllChildren = cart.RequiresAllChildren;

			if (cart.HasErrorToShow())
			{
				OccurredError = true;
				cart.HasShownError = true;
			}
			// Sempre seta para mostrar no carrinho (sem popup)
			ErrorMessage = cart.GetErrorDescription();
			Notification = cart.Notification;
			Notifications = cart.Notifications;

			var items = cart.GetProductsAsQueryable();

			var products = items.AsQueryable()
				.Where(i => i.ProductType == ProductType.Produto)
				.ToList();
			if (products.Count > 0)
			{
				ProductsItems = products.Select(i => new ItemViewModel(i)).ToList();
				ProductsShippingCost = products.Sum(p => p.ShippingCost.GetPointsOrZero());
				ProductsTotal = products.Sum(p => p.GetSubtotalPoints());
				if (DiscountAmount == 0)
				{
					DiscountAmount = ProductsItems.Sum(p => p.DiscountAmount);
				}
			}

			// Se não houver desconto então remove o cupom
			if (DiscountAmount == 0)
			{
				DiscountCouponCode = null;
			}

			var vouchers = items.AsQueryable()
				.Where(i => i.ProductType == ProductType.ValeVirtual || i.ProductType == ProductType.ValeFisico)
				.ToList();
			if (vouchers.Count > 0)
			{
				VouchersItems = vouchers.Select(i => new ItemViewModel(i, cryptography)).ToList();
				VouchersShippingCost = vouchers.Sum(p => p.ShippingCost.GetPointsOrZero());
				VouchersTotal = vouchers.Sum(p => p.GetSubtotalPoints());
			}
			if (cart.ChildrenCarts.Any(cc => cc.Vouchers != null && cc.Vouchers.Count > 0))
			{
				Vouchers = cart.ChildrenCarts
					.Where(cc => cc.Vouchers != null)
					.SelectMany(cc => cc.Vouchers).ToList();
			}

			ShippingCost = ProductsShippingCost + VouchersShippingCost;
			Total = cart.GetCartTotal();

			if (cart.RequiresAllChildren || cart.ChildrenCarts.Count == 1)
			{
				if (cart.ChildrenCarts.All(c => c.Status == PartnerOrderStatus.UnderAnalysis))
				{
					CanApprove = items.All(i => (!i.DynamicPrice && i.Status == OrderItemStatus.Validated)
						|| (i.Status == OrderItemStatus.UnderAnalysis && i.DynamicPrice && i.Priced));
				}
			}
			else
			{
				// Se tiver pelo menos um pedido filho em análise e precificado
				CanApprove = cart.ChildrenCarts.Any(c => c.Status == PartnerOrderStatus.UnderAnalysis
					&& c.Products.All(i => (!i.DynamicPrice && i.Status == OrderItemStatus.Validated)
						|| (i.Status == OrderItemStatus.UnderAnalysis && i.DynamicPrice && i.Priced)));
			}

			if (cart.ShippingAddress != null)
			{
				ShippingAddress = new AddressModel()
				{
					Id = cart.ShippingAddress.Id,
					AddressName = cart.ShippingAddress.AddressName,
					Cep = cart.ShippingAddress.Cep,
					Street = cart.ShippingAddress.Street,
					Number = cart.ShippingAddress.Number,
					Complement = cart.ShippingAddress.Complement,
					Neighborhood = cart.ShippingAddress.Neighborhood,
					City = cart.ShippingAddress.City,
					State = cart.ShippingAddress.State,
					Reference = cart.ShippingAddress.Reference,
					Receiver = cart.ShippingAddress.Receiver
				};
				if (ShippingAddress.Receiver != null)
				{
					if (!string.IsNullOrEmpty(cart.ShippingAddress.Receiver.Cpf))
					{
						ShippingAddress.Receiver.Cpf = new Cpf(Extractor.RemoveMasks(cart.ShippingAddress.Receiver.Cpf)).GetCpfFormatado();
					}
					ShippingAddress.Receiver.Telephone = cart.ShippingAddress.Receiver.GetTelefoneFormatado();
					ShippingAddress.Receiver.Cellphone = cart.ShippingAddress.Receiver.GetCelularFormatado();
				}
			}
			if (cart.Participant != null)
			{
				Customer = new ParticipantDataModel()
				{
					Nome = cart.Participant.Nome,
					Email = cart.Participant.Email,
					Telefone = cart.Participant.GetTelefoneFormatado(),
					Celular = cart.Participant.GetCelularFormatado()
				};
			}
		}

		public static CartViewModel From(CartModel cart, CoinName coinName, ICryptography cryptography = null)
		{
			return new CartViewModel(cart, cryptography)
			{
				CoinName = coinName
			};
		}

		public bool HasDiscount()
		{
			return DiscountAmount > 0;
		}

		public bool HasNotifications()
		{
			return Notifications != null && Notifications.Count > 0;
		}
	}

	public class ItemViewModel
	{
		public string Id { get; set; }
		public string PartnerId { get; }
		public string PartnerName { get; set; }
		public bool CartItemLimitEnabled { get; set; }
		public int? CartItemLimit { get; set; }
		public string Image { get; set; }
		public string Description { get; set; }

		public string SkuCode { get; set; }

		public string Model { get; set; }
		public string Color { get; set; }
		public string Size { get; set; }
		public string Voltage { get; set; }
		public string LabelModel { get; set; }
		public string LabelColor { get; set; }
		public string LabelSize { get; set; }
		public string LabelVoltage { get; set; }

		public List<CustomAttributoModel> Attributes { get; set; }
		public ProductType ProductType { get; set; }
		public int Quantity { get; set; }
		public bool DynamicPrice { get; set; }
		public string DynamicPriceDescription { get; set; }
		public decimal UnitPrice { get; set; }
		public decimal Subtotal { get; set; }
		public decimal DiscountAmount { get; set; }

		public string EstimatedDeliveryDays { get; set; }
		public DateTime? EstimatedDeliveryDate { get; set; }
		public string TrackingCode { get; set; }
		public string TrackingLink { get; set; }
		public List<dynamic> TrackingEvents { get; set; }
		public string LinkCupom { get; set; }

		public string StatusItem { get; set; }

		public bool OccurredError { get; set; }
		public string ErrorMessage { get; set; }

		public bool HasNotifications { get; set; }
		public List<string> Notifications { get; set; }

		public ProductLayoutType LayoutType { get; set; }

		public ItemViewModel(CartItem i, ICryptography cryptography = null)
		{
			// Id = i.ElasticId;
			Id = i.ItemId;
			if (cryptography != null)
			{
				PartnerId = cryptography.Encrypt(i.GetPartnerId());
			}

			LayoutType = i.LayoutType;
			PartnerName = i.PartnerName;
			CartItemLimitEnabled = i.PartnerSettings != null && i.PartnerSettings.CartItemLimitEnabled;
			CartItemLimit = i.PartnerSettings != null ? i.PartnerSettings.CartItemLimit : int.MaxValue;
			Image = i.Image;
			Description = i.ProductName;
			SkuCode = i.SkuCode;
			if (i.Offline)
				Model = i.SkuModel;
			Color = i.SkuColor;
			Size = i.SkuSize;
			Voltage = i.SkuVoltage;
			ProductType = i.ProductType;
			DynamicPrice = i.DynamicPrice && (!i.IsDynamicPriceDefinedByParticipant || i.UnitPrices.GetPoints() <= 0);
			DynamicPriceDescription = i.DynamicPriceDescription;
			LabelModel = i.ProductModelLabel;
			LabelColor = i.ProductColorLabel;
			LabelSize = i.ProductSizeLabel;
			LabelVoltage = i.ProductVoltageLabel;
			UnitPrice = i.UnitPrices.GetPoints();
			Quantity = i.Quantity;
			DiscountAmount = i.Discount.GetPointsOrZero();
			Subtotal = i.GetSubtotalPoints();
			EstimatedDeliveryDays = i.GetEstimatedDeliveryDays();
			EstimatedDeliveryDate = i.GetEstimatedDeliveryDate();
			TrackingCode = i.TrackingCode;
			TrackingLink = i.TrackingLink;
			if (i.TrackingEvents != null && i.TrackingEvents.Count > 0)
			{
				TrackingEvents = i.TrackingEvents.Where(d => d.active != null && d.active == true).OrderBy(d => d.date).ToList();
			}

			if (i.ProductType.IsVoucher())
			{
				LinkCupom = i.VoucherLink;
				// CodigoCupom = i.VoucherCode;
			}

			HasNotifications = i.HasNotification();
			Notifications = i.Notifications;
			StatusItem = i.Status.ToCatalogStatus();
			OccurredError = i.OccurredError;
			ErrorMessage = i.ErrorMessage;

			try
			{
				if (i.HasCustomAttributes())
				{
					Attributes = i.CustomAttributes
						.Where(a => a != null)
						.Select(a => new CustomAttributoModel(a))
						.ToList();
				}
			}
			catch (Exception ex)
			{
				LoggerFactory.GetLogger().Error("Item {} - Erro durante durante montagem da view model: {}", i.SkuCode, ex.Message);
			}
		}

		public decimal GetSubtotal()
		{
			return Quantity * UnitPrice;
		}
	}

	public class CustomAttributoModel
	{
		public CustomAttributeType Type { get; set; }
		public string Name { get; set; }
		public string Value { get; set; }

		public CustomAttributoModel() { }

		public CustomAttributoModel(CustomAttribute attr)
		{
			Type = attr.Type;
			Name = attr.Name;
			Value = attr.Type == CustomAttributeType.Check ? (attr.Value == "true" ? "Sim" : "Não") : attr.Value;
		}

		public bool IsFile()
		{
			return Type == CustomAttributeType.File;
		}
	}
}