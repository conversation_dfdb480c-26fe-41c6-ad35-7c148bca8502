using System.Collections.Generic;
using System.Linq;
using Motivai.SharedKernel.Domain.Entities.References.Campaign.Parametrizations;
using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.WebCatalog.Models.Addresses;
using Motivai.WebCatalog.Models.Order;

namespace Motivai.WebCatalog.Models.Pages
{
	public class OrderConfirmationPageModel
	{
		public bool Rewards { get; set; }
		public bool Marketplace { get; set; }

		public bool HasShippingCalculation { get; set; }
		public bool EnableSelectshippingAddress { get; set; }
		public bool EnableDiscountCoupon { get; set; }
		public bool ShowMessage { get; set; }
		public string Message { get; set; }
		public bool RequireRegulationAcceptance { get; set; }
		public bool EnableShippingPolicy { get; set; }

		public bool ShowCustomMessageAfterOrderConfirmation { get; set; }

		public CartViewModel Cart { get; set; }

		public MarketplaceViewStringsParametrization ViewStrings { get; set; }

		public List<CampaignsCatalogPageCustomization> PageCustomizations { get; set; }


		public bool HasCustomization() {
			return PageCustomizations != null && PageCustomizations.Count > 0;
		}


		public bool HasVouchers() {
			return Cart.VouchersItems != null && Cart.VouchersItems.Count > 0;
		}

		public bool HasProduct() {
			return Cart.ProductsItems != null && Cart.ProductsItems.Count > 0;
		}

		public bool HasProductLayout() {
			return Cart.ProductsItems.Any(p => p.LayoutType == ProductLayoutType.PRODUCT);
		}

		public bool HasServiceLayout() {
			return Cart.ProductsItems.Any(p => p.LayoutType == ProductLayoutType.BASIC_SERVICE);
		}


		public OrderConfirmationPageModel(CartViewModel cart)
		{
			if (cart.ShippingAddress == null)
			{
				cart.ShippingAddress = new AddressModel();
			}
			if (cart.ShippingAddress.Receiver == null)
			{
				cart.ShippingAddress.Receiver = new ReceiverModel();
			}
			this.Cart = cart;
		}

		public void SetParameters(CampaignSettingsModel settings, CampaignCatalogSettings catalogSettings)
		{
			this.Rewards = settings.IsRewards();
			this.Marketplace = settings.IsMarketplace();
			ViewStrings = new MarketplaceViewStringsParametrization(settings);

			this.HasShippingCalculation = settings.Parametrizations.EnableShippingCalculation;
			this.EnableDiscountCoupon = settings.Parametrizations.EnableDiscountCoupon;
			this.EnableSelectshippingAddress = settings.Parametrizations.RequireCepToFinish();
			this.RequireRegulationAcceptance = settings.Parametrizations.AcceptRegulationRequired;
			this.EnableShippingPolicy = catalogSettings.PagesSettings.EnableShippingPolicy;
			this.ShowCustomMessageAfterOrderConfirmation = catalogSettings.PagesSettings.ShowCustomMessageAfterOrderConfirmation;

			if (catalogSettings.PagesSettings.ShowOrderMessage && !string.IsNullOrEmpty(catalogSettings.PagesSettings.OrderMessage))
			{
				this.ShowMessage = true;
				this.Message = catalogSettings.PagesSettings.OrderMessage;
			}
		}
	}
}