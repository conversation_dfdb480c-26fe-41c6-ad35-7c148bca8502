using Motivai.SharedKernel.Domain.Entities.References.Products;
using Motivai.SharedKernel.Domain.Enums;

namespace Motivai.WebCatalog.Models.Pages {


    public class CampaignsCatalogPageCustomization
    {
        public bool Active { get; set; }

        public bool IsLayoutType { get; set; }
        public bool IsProductType { get; set; }
        public ProductType ProductType { get; set; }
        public ProductLayoutType LayoutType { get; set; }

        public string Content { get; set; }

        public bool HasVouchers() {
            return IsProductType && ProductType.IsVoucher();
        }

        public bool HasProduct() {
            return IsProductType && ProductType == ProductType.Produto;
        }

        public bool HasLayoutProduct() {
            return IsLayoutType && LayoutType == ProductLayoutType.PRODUCT;
        }

        public bool HasLayoutService() {
            return IsProductType && LayoutType == ProductLayoutType.BASIC_SERVICE;
        }
    }

}