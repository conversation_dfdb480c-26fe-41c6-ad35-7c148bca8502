$("#content .container").css("visibility", "visible");

angular.module("platformApp", ['footerModule', 'validation', 'validation.rule', 'ui.utils.masks', 'brasil.filters', 'ngLoaderContainer'])
.factory('httpRequestInterceptor', ['$q', '$window', httpRequestInterceptor])
.config(['$httpProvider', function($httpProvider) {
	$httpProvider.interceptors.push('httpRequestInterceptor');
}])
.service('RechargeService', ['$http', function($http) {
	const config = { headers : { 'Content-Type': 'application/json' } };
	this.getMainCellphone = function() {
		return $http.get("/recargacelular/celularprincipal");
	};
	this.getOperatorsByDdd = function(ddd) {
		return $http.get("/recargacelular/ddds/" + ddd + "/operadoras");
	};
	this.getRechargeValuesByPartnerId = function(ddd, partnerId) {
		return $http.get("/recargacelular/ddds/" + ddd + "/operadoras/" + partnerId + "/options");
	};
	this.startRecharge = function(data) {
		return $http.post("/recargacelular/inicia", data, config);
	};
	this.confirmRrecharge = function(data) {
		return $http.post("/recargacelular/confirma", data, config);
	};
	this.cancelRecharge = function(data) {
		return $http.post("/recargacelular/cancela", data, config);
	};
}])
.controller('phoneRechargeCtrl', ['RechargeService', 'logger', '$scope', '$injector', '$timeout', '$filter', function (service, logger, $scope, $injector, $timeout, $filter) {
	$scope.showForm = true;
	$scope.showResult = false;

	$scope.loading = false;

	$scope.operators = [];
	$scope.rechargeValues = [];

	$scope.recharge = { RechargeCost: 0 };

	const $validationProvider = $injector.get('$validation');
	const currencyFilter = $filter('currency');

	const showInfo = function(message) {
		$scope.loading = false;
		showSuccessSwal('', message);
	};

	const showError = function(message) {
		$scope.loading = false;
		showErrorSwal('Ops', message);
	};

	const resetValues = function() {
		$scope.rechargeValues = [];
		$scope.recharge.rechargeValue = null;
		$scope.recharge.RechargeCost = 0;
	};

	const loadMainPhone = function() {
		$scope.loading = true;
		service.getMainCellphone()
			.then(function(response) { return response.data; })
			.then(function(response) {
				$scope.loading = false;
				if (!response) return;
				if (response.Success) {
					if (response.Return) {
						$scope.recharge.cellphoneDdd = response.Return.ddd;
						$scope.recharge.cellphoneNumber = response.Return.number;
						if ($scope.recharge.cellphoneDdd) {
							loadOperators($scope.recharge.cellphoneDdd);
						}
						$timeout(function() {
							$scope.$evalAsync(function() {
								$('#cellphoneNumber').mask('9999-9999?9');
							});
						});
					}
				} else {
					showError(response.Error);
				}
			})
			.catch(function(resp) {
				$scope.loading = false;
				showError("Não foi possível carregar o celular cadastrado, por favor, insira manualmente o DDD e o número.");
				logger.logResponse(resp);
			});
	};

	const loadOperators = function(ddd) {
		if (!ddd) return;
		$scope.loading = true;
		$scope.recharge.operator = null;
		$scope.operators = [];
		resetValues();
		service.getOperatorsByDdd(ddd)
			.then(function(response) { return response.data; })
			.then(function(response) {
				$scope.loading = false;
				if (!response) return;
				if (response.Success) {
					if (response.Return) {
						$scope.operators = response.Return;
						$timeout(function() {
							$scope.$evalAsync(function() {
								$("#operator").trigger("chosen:updated");
							});
						});
					}
				} else {
					showError(response.Error);
				}
			})
			.catch(function(resp) {
				$scope.loading = false;
				showError("Não foi possível carregar as operadoras, por favor, tente novamente.");
				logger.logResponse(resp);
			});
	};

	const loadRechargeValues = function(ddd, partnerId) {
		if (!ddd || !partnerId) return;
		$scope.loading = true;
		resetValues();
		service.getRechargeValuesByPartnerId(ddd, partnerId)
			.then(function(response) { return response.data; })
			.then(function(response) {
				$scope.loading = false;
				if (!response) return;
				if (response.Success) {
					if (response.Return) {
						$scope.rechargeValues = response.Return;
						$timeout(function() {
							$scope.$evalAsync(function() {
								$("#rechargeValue").trigger("chosen:updated");
							});
						});
						if ($scope.operators) {
							const op = $scope.operators.find(function(o){ return (o && o.ProviderId == partnerId ? o : null); });
							if (op)
								$scope.recharge.operatorName = op.ProviderName;
						}
					}
				} else {
					showError(response.Error);
				}
			})
			.catch(function(resp) {
				$scope.loading = false;
				showError("Não foi possível carregar as opções de recarga da operadora, por favor, tente novamente.");
				logger.logResponse(resp);
			});
	};

	const startRecharge = function(frmPointsPurchase) {
		angular.forEach(frmPointsPurchase.$error, function(field) {
			angular.forEach(field, function(errorField) { errorField.$setTouched(); })
		});
		$validationProvider.validate(frmPointsPurchase)
			.then(function() {
				$scope.loading = true;
				$scope.LocationInfo = {
					timezone: moment.tz.guess()
				}
				service.startRecharge($scope.recharge)
					.then(function(response) { return response.data; })
					.then(function(rechargeTicket) {
						$scope.loading = false;
						if (rechargeTicket.Success) {
							$scope.ticket = rechargeTicket.Return;
							$scope.showForm = false;
							$scope.showConfirmation = true;
							$scope.showResult = false;
							$('#div-result-confirm').css('visibility', 'visible');
						} else if (rechargeTicket.Success == false) {
							showError(rechargeTicket.Error);
						} else {
							showError('Não foi possível efetuar a recarga, por favor, tente novamente.');
						}
					})
					.catch(function(resp) {
						showError("Ocorreu um erro ao efetuar a recarga, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.");
						logger.logResponse(resp);
					});
			})
			.catch(function() {
				showError("Preencha os campos corretamente para prosseguir com a compra.");
			});
	};

	const confirmRecharge = function() {
		const confirmReq = {
			token: $scope.recharge.rechargeValue,
			confirmationToken: $scope.ticket.ConfirmationToken,
			cellphoneDdd: $scope.recharge.cellphoneDdd,
			cellphoneNumber: $scope.recharge.cellphoneNumber,
			operatorName: $scope.ticket.OperatorName,
			rechargeValue: $scope.ticket.RechargeValue,
			proofPayment: $scope.ticket.ProofPayment,
			locationInfo: {
				timezone: moment.tz.guess()
			}
		};
		$scope.loading = true;
		service.confirmRrecharge(confirmReq)
			.then(function(response) { return response.data; })
			.then(function(confirmResult) {
				$scope.loading = false;
				if (confirmResult.Success) {
					$scope.confirmResult = confirmResult.Return;
					if (confirmResult.Return.UpdatedBalance) {
						$("#balanceAmountSpan").html(currencyFilter(confirmResult.Return.UpdatedBalance, ''));
					}
					$scope.showForm = false;
					$scope.showConfirmation = false;
					$scope.showResult = true;
					$('#div-result').css('visibility', 'visible');
				} else if (confirmResult.Success == false) {
					showError(confirmResult.Error);
				} else {
					showError('Não foi possível efetuar a recarga, por favor, tente novamente.');
				}
			})
			.catch(function(resp) {
				showError("Ocorreu um erro ao efetuar a recarga, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.");
				logger.logResponse(resp);
			});
	};

	const cancelRecharge = function() {
		if (!$scope.recharge || !$scope.ticket) return;
		showConfirmSwal('Deseja cancelar a recarga?')
			.then(function(wasConfirmed) {
				if (!wasConfirmed) return;
				const cancelReq = {
					token: $scope.recharge.rechargeValue,
					confirmationToken: $scope.ticket.ConfirmationToken,
					cellphoneDdd: $scope.recharge.cellphoneDdd,
					cellphoneNumber: $scope.recharge.cellphoneNumber
				};
				$scope.loading = true;
				service.cancelRecharge(cancelReq)
					.then(function(response) { return response.data; })
					.then(function(confirmResult) {
						$scope.loading = false;
						if (confirmResult.Success) {
							$scope.confirmResult = confirmResult.Return;
							$scope.showForm = true;
							$scope.showConfirmation = false;
							$scope.showResult = false;
							showInfo('Recarga cancelada com sucesso.');
							$scope.recharge = {};
							$scope.ticket = null;
						} else if (confirmResult.Success == false) {
							showError(confirmResult.Error);
						} else {
							showError('Não foi possível cancelar a recarga, por favor, tente novamente.');
						}
					})
					.catch(function(resp) {
						showError("Ocorreu um erro ao cancelar a recarga, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.");
						logger.logResponse(resp);
					});
			});
	};

	$scope.printProof = function() {
		const printPopup = window.open('', 'printPopup', 'width=340,height=500');
		printPopup.document.title = 'Comprovante de Recarga';
		printPopup.document.write('<pre>')
		printPopup.document.write($scope.ticket.ProofPayment);
		printPopup.document.write('</pre>');
		printPopup.document.close();
		printPopup.focus();
		printPopup.print();
		printPopup.close();
	};

	$scope.dddOnChange = function() {
		loadOperators($scope.recharge.cellphoneDdd);
	};

	$scope.operatorOnChange = function() {
		loadRechargeValues($scope.recharge.cellphoneDdd, $scope.recharge.operator);
	};

	$scope.valueOnChange = function() {
		if (!$scope.recharge.rechargeValue) return;
		var item = $scope.rechargeValues.find(function(it){ return it.Token == $scope.recharge.rechargeValue ? it : null; });
		if (item) {
			$scope.recharge.RechargeCost = item.Cost;
		} else {
			$scope.recharge.RechargeCost = 0;
		}
	}

	$scope.submitRecharge = function(frmPointsPurchase) {
		startRecharge(frmPointsPurchase);
	};

	$scope.confirmRecharge = function() {
		confirmRecharge();
	};

	$scope.cancelRecharge = function() {
		cancelRecharge();
	};

	$scope.initPage = function() {
		loadMainPhone();
		$.fn.initChosen('#operator');
		$.fn.initChosen('#rechargeValue');
	};
}]);