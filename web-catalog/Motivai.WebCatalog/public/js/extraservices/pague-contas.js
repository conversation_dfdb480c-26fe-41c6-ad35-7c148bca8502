$("#content .container").css("visibility", "visible");

angular.module("platformApp", ['footerModule', 'platformComponents', 'validation', 'validation.rule', 'ui.utils.masks', 'brasil.filters',
'moment-picker', 'ngLoaderContainer'])
.factory('httpRequestInterceptor', ['$q', '$window', httpRequestInterceptor])
.config(['$httpProvider', function($httpProvider) {
	$httpProvider.interceptors.push('httpRequestInterceptor');
}])
.service('BillService', ['$http', function($http) {
	const config = { headers : { 'Content-Type': 'application/json' } };
	this.getDetails = function(barCode) {
		return $http.get("/paguecontas/detalhes/" + barCode);
	};
	this.startPayment = function(data) {
		return $http.post("/paguecontas/inicia", data, config);
	};
	this.confirmPayment = function(data) {
		return $http.post("/paguecontas/confirma", data, config);
	};
	this.confirmSchedulePayment = function(data) {
		return $http.post("/paguecontas/agendar", data, config);
	};
	this.cancelPayment = function(data) {
		return $http.post("/paguecontas/cancela", data, config);
	};
}])
.controller('billCtrl', ['BillService', 'logger', '$scope', '$injector', '$filter', '$timeout', function (service, logger, $scope, $injector, $filter, $timeout) {
	$scope.isSelectable = function (date, type) {
		return type != 'day' || (date.format('dddd') != 'Sunday' && date.format('dddd') != 'Saturday');
	};

	$scope.showForm = true;
	$scope.showResult = false;

	$scope.loading = false;

	$scope.bill = {};
	$scope.canChangeDueDate = false;

	const $validationProvider = $injector.get('$validation');
	const currencyFilter = $filter('currency');

	const today = new Date();
	$scope.minimumDaysBeforeDueDate = 0;
	$scope.today = moment().add(1, 'day');

	const showInfo = function(message) {
		$scope.loading = false;
		showSuccessSwal('', message);
	};

	const showError = function(message) {
		$scope.loading = false;
		showErrorSwal('Ops', message);
	};

	const displayConfirmationSection = function() {
		$scope.showForm = false;
		$scope.showConfirmation = true;
		$scope.showResult = false;
		$('#div-confirm').css('visibility', 'visible');
	};

	const displayResultSection = function() {
		$scope.showForm = false;
		$scope.showConfirmation = false;
		$scope.showResult = true;
		$('#div-result').css('visibility', 'visible');
	};

	const loadBillDetails = function(barCode) {
		if (!barCode) {
			showError('Código de barras inválido.');
			return;
		}

		barCode = barCode.replace(/\D/g,'');
		if (!barCode) {
			showError('Código de barras inválido.');
			return;
		}

		$scope.loading = true;
		$scope.feedbackMessage = null;

		$scope.bill = {
			BarCode: barCode
		};
		$scope.foundBill = false;
		service.getDetails(barCode)
			.then(function(response) { return response.data; })
			.then(function(result) {
				$scope.loading = false;
				if (result.Success) {
					if (result.Return) {
						$scope.bill = result.Return;
						if ($scope.bill.DueDate) {
							$scope.bill.DueDate = moment(new Date($scope.bill.DueDate));
							$scope.canChangeDueDate = false;
						} else {
							// $scope.bill.DueDate = moment(today).add(1, 'day');
							$scope.bill.minimumDueDate = moment(today).add(1, 'day');
							$scope.bill.filledManually = true;
							$scope.canChangeDueDate = true;
						}

						if ($scope.bill.MinimumScheduledPaymentDate) {
							$scope.bill.MinimumScheduledPaymentDate = moment($scope.bill.MinimumScheduledPaymentDate);
						} else {
							$scope.bill.MinimumScheduledPaymentDate = moment(today).add(1, 'day');
						}
						if ($scope.bill.MaximumScheduledPaymentDate) {
							$scope.bill.MaximumScheduledPaymentDate = moment($scope.bill.MaximumScheduledPaymentDate);
							$scope.bill.maximumSchedulingDateText = $scope.bill.MaximumScheduledPaymentDate.format('DD/MM/YYYY');
						}
						$scope.foundBill = true;
					} else {
						$scope.bill = {};
						showError('Não foi encontrada uma conta para pagamento pelo código de barras informado.');
					}
				} else if (result.Success == false) {
					showError(result.Error);
				} else {
					showError("Não foi possível carregar os detalhes da conta para pagamento, por favor, tente novamente.");
				}
			})
			.catch(function(resp) {
				$scope.loading = false;
				showError("Não foi possível carregar os detalhes da conta para pagamento, por favor, tente novamente.");
				logger.logResponse(resp);
			});
	};

	const schedulePayment = function(frmBill) {
		$scope.feedbackMessage = '';
		angular.forEach(frmBill.$error, function(field) {
			angular.forEach(field, function(errorField) { errorField.$setTouched(); })
		});

		$validationProvider.validate(frmBill)
			.then(function() {
				$scope.formattedDueDate = moment(new Date($scope.bill.DueDate)).format('DD/MM/YYYY');
				$scope.formattedScheduledPaymentDate = moment(new Date($scope.bill.ScheduledPaymentDate)).format('DD/MM/YYYY');
				displayConfirmationSection();
			})
			.catch(function() {
				showError("Preencha os campos corretamente para prosseguir com o agendamento.");
			});
	};

	const confirmPaymentScheduling = function() {
		$scope.loading = true;
		$scope.bill.LocationInfo = {
			timezone: moment.tz.guess()
		}
		service.confirmSchedulePayment($scope.bill)
			.then(function(response) { return response.data; })
			.then(function(confirmResult) {
				$scope.loading = false;
				if (confirmResult.Success) {
					if (confirmResult.Return && confirmResult.Return.UpdatedBalance) {
						$("#balanceAmountSpan").html(currencyFilter(confirmResult.Return.UpdatedBalance, ''));
					}
					displayResultSection();
				} else if (confirmResult.Success == false) {
					showError(confirmResult.Error);
				} else {
					showError('Não foi possível agendar o pagamento, por favor, tente novamente.');
				}
			})
			.catch(function (err, status) {
				showError("Ocorreu um erro ao agendar o pagamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.");
				logger.log(err, status);
			});
	};

	const startPayment = function(frmBill) {
		$scope.feedbackMessage = '';
		angular.forEach(frmBill.$error, function(field) {
			angular.forEach(field, function(errorField) { errorField.$setTouched(); })
		});
		$validationProvider.validate(frmBill)
			.then(function() {
				$scope.loading = true;
				service.startPayment($scope.bill)
					.then(function(response) { return response.data; })
					.then(function(billTicket) {
						$scope.loading = false;
						if (billTicket.Success) {
							$scope.ticket = billTicket.Return;
							$scope.showForm = false;
							$scope.showConfirmation = true;
							$scope.showResult = false;
							$scope.formattedDueDate = moment(new Date($scope.bill.DueDate)).format('DD/MM/YYYY');
							$('#div-confirm').css('visibility', 'visible');
						} else if (billTicket.Success == false) {
							showError(billTicket.Error);
						} else {
							showError('Não foi possível iniciar o pagamento, por favor, tente novamente.');
						}
					})
					.catch(function(resp) {
						showError("Ocorreu um erro ao iniciar o pagamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.");
						logger.logResponse(resp);
					});
			})
			.catch(function() {
				showError("Preencha os campos corretamente para prosseguir com o pagamento.");
			});
	};

	const confirmPayment = function() {
		const confirmReq = {
			token: $scope.bill.Token,
			billDetailsQueryPartner: $scope.bill.BillDetailsQueryPartner,
			billPaymentPartner: $scope.ticket.BillPaymentPartner,
			confirmationToken: $scope.ticket.ConfirmationToken,
			assignor: $scope.bill.Assignor,
			barCode: $scope.bill.BarCode,
			billingAmount: $scope.bill.BillingAmount,
			proofPayment: $scope.ticket.ProofPayment,
			locationInfo: {
				timezone: moment.tz.guess()
			}
		};
		$scope.loading = true;
		service.confirmPayment(confirmReq)
			.then(function(response) { return response.data; })
			.then(function(confirmResult) {
				$scope.loading = false;
				if (confirmResult.Success) {
					$scope.confirmResult = confirmResult.Return;
					if (confirmResult.Return.UpdatedBalance) {
						$("#balanceAmountSpan").html(currencyFilter(confirmResult.Return.UpdatedBalance, ''));
					}
					displayResultSection();
				} else if (confirmResult.Success == false) {
					showError(confirmResult.Error);
				} else {
					showError('Não foi possível confirmar o pagamento, por favor, tente novamente.');
				}
			})
			.catch(function(resp) {
				showError("Ocorreu um erro ao confirmar o pagamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.");
				logger.logResponse(resp);
			});
	};

	const clearPaymentForm = function() {
		$scope.showForm = true;
		$scope.foundBill = false;
		$scope.showConfirmation = false;
		$scope.showResult = false;
		$scope.bill = {};
	};

	const cancelOnlinePayment = function() {
		const cancelReq = {
			token: $scope.bill.Token,
			billDetailsQueryPartner: $scope.bill.BillDetailsQueryPartner,
			billPaymentPartner: $scope.ticket.BillPaymentPartner,
			confirmationToken: $scope.ticket.ConfirmationToken,
			assignor: $scope.bill.Assignor
		};
		$scope.loading = true;
		service.cancelPayment(cancelReq)
			.then(function(response) { return response.data; })
			.then(function(confirmResult) {
				$scope.loading = false;
				if (confirmResult.Success) {
					$scope.confirmResult = confirmResult.Return;
					clearPaymentForm();
					showInfo('Pagamento cancelado com sucesso.');
				} else if (confirmResult.Success == false && confirmResult.Error) {
					showError(confirmResult.Error);
				} else {
					showError('Não foi possível cancelar o pagamento, por favor, tente novamente.');
				}
			})
			.catch(function(resp) {
				showError("Ocorreu um erro ao cancelar o pagamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.");
				logger.logResponse(resp);
			});
	};

	const cancelPayment = function() {
		if (!$scope.bill) {
			return;
		}
		if (!$scope.schedulingPayment && !$scope.ticket) {
			return;
		}
		showConfirmSwal('Deseja cancelar o pagamento?')
			.then(function(wasConfirmed) {
				if (!wasConfirmed) return;
				if ($scope.schedulingPayment === true) {
					$timeout(function() {
						clearPaymentForm();
						showInfo('Pagamento cancelado com sucesso.');
					}, 500);
				} else {
					cancelOnlinePayment();
				}
			});
	};

	$scope.calculateMaximumDate = function(date) {
		if (!date)
			return today;
		if ($scope.minimumDaysBeforeDueDate && $scope.minimumDaysBeforeDueDate > 0) {
			return moment(date).subtract($scope.minimumDaysBeforeDueDate, 'day');
		}
		return date;
	};

	$scope.calculateSchedulingDateLimits = function () {
		if (!$scope.canChangeDueDate) {
			return;
		}
		$scope.bill.MaximumScheduledPaymentDate = moment($scope.bill.DueDate);
		$scope.bill.maximumSchedulingDateText = $scope.bill.MaximumScheduledPaymentDate.format('DD/MM/YYYY');
		$scope.bill.ScheduledPaymentDate = null;
	};

	$scope.printProof = function() {
		const printPopup = window.open('', 'printPopup', 'width=340,height=500');
		printPopup.document.title = 'Comprovante do Pagamento da Conta';
		printPopup.document.write('<pre>')
		printPopup.document.write($scope.ticket.ProofPayment);
		printPopup.document.write('</pre>');
		printPopup.document.close();
		printPopup.focus();
		printPopup.print();
		printPopup.close();
	};

	$scope.onBarCodeBlur = function() {
		loadBillDetails($scope.bill.BarCode);
	};

	$scope.schedulePayment = function(frmBill) {
		schedulePayment(frmBill);
	};

	$scope.confirmPaymentScheduling = function() {
		confirmPaymentScheduling();
	};

	$scope.startPayment = function(frmBill) {
		startPayment(frmBill);
	};

	$scope.confirmPayment = function() {
		confirmPayment();
	};

	$scope.cancelPayment = function() {
		cancelPayment();
	};

	$scope.onInit = function(schedulingPayment, minimumDaysBeforeDueDate) {
		$scope.schedulingPayment = schedulingPayment === true;
		if (minimumDaysBeforeDueDate && minimumDaysBeforeDueDate > 0) {
			$scope.minimumDaysBeforeDueDate = minimumDaysBeforeDueDate;
		}
	};
}]);