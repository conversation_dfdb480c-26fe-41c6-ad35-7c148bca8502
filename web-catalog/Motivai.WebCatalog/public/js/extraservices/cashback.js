angular.module('platformApp', ['footerModule', 'platformComponents', 'validation', 'validation.rule', 'ui.utils.masks', 'brasil.filters', 'ngLoaderContainer'])
  .service('CashbackService', ['$http', function($http) {
    const config = { headers: { 'Content-Type': 'application/json' } };
    this.loadPageConfig = function() {
      return $http.get('/cashback/configuracoes');
    };
    this.loadParticipantConfig = function() {
      return $http.get('/MinhaConta/PrincipalData');
    };
    this.calculateFees = function(transaction) {
      return $http.post('/cashback/taxas', transaction, config);
    };
    this.transferPoints = function(transaction) {
      return $http.post('/cashback/transferir', transaction, config);
    };
  }])
  .controller('cardsCtrl', ['CashbackService', 'logger', '$scope', '$injector', '$timeout', '$sce', function(service, logger, $scope, $injector, $timeout, $sce) {
    $scope.loading = false;
    $scope.showParticipantInfo = false;

    $scope.order = {
      bankTransferMethod: 'BANK_TRANSFER',
      bankAccount: {},
      pointsAmount: null,
      totalFeesAmount: 0,
      transferAmount: 0,
      totalCost: {
        points: 0
      }
    };
    $scope.finalStep = false;
    $scope.calculated = false;

    const $validationProvider = $injector.get('$validation');

    const showErrorMessage = function(message) {
      showErrorSwal('Ops', message);
    };

    const loadPageConfig = function() {
      $scope.loading = true;
      service.loadPageConfig()
        .then(function(response) { return response.data; })
        .then(function(response) {
          $scope.loading = false;
          if (response) {
            if (response.Success) {
              $scope.settings = response.Return;

              if ($scope.settings.transferMethodAllowed == 'BANK_TRANSFER') {
                $scope.order.bankTransferMethod = 'BANK_TRANSFER';
              } else if ($scope.settings.transferMethodAllowed == 'PIX') {
                $scope.order.bankTransferMethod = 'PIX';
              } else {
                $scope.canChooseBankTransferMethod = $scope.settings.transferMethodAllowed == 'BOTH';
                if ($scope.settings.transferMethodAllowed == 'BOTH') {
                  $timeout(function() {
                    $scope.$evalAsync(function() {
                      startOrUpdateChosen('bankTransferMethod');
                    });
                  }, 50);
                }
              }

              if ($scope.settings.regulation) {
                $scope.settings.regulation = $sce.trustAsHtml($scope.settings.regulation);
              }
            } else {
              $scope.occurredError = true;
              showErrorMessage(response.Error);
            }
          } else {
            $scope.occurredError = true;
            showErrorMessage('Ocorreu um erro durante o carregamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          }
          startComboboxes();
        })
        .catch(function(resp) {
          $scope.loading = false;
          $scope.occurredError = true;
          showErrorMessage('Ocorreu um erro durante o carregamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          logger.logResponse(resp);
        });
    };

    const startComboboxes = function() {
      $timeout(function() {
        $scope.$evalAsync(function() {
          startOrUpdateChosen('bankCode');
          startOrUpdateChosen('accountType');
        });
      }, 50);
    };

    const loadParticipant = function() {
      $scope.loading = true;
      service.loadParticipantConfig()
        .then(function(response) { return response.data; })
        .then(function(response) {
          $scope.loading = false;
          if (response) {
            if (response.Success) {
              $scope.participant = response.Return;
              $scope.showParticipantInfo = true;
              setTimeout(function() {
                if ($scope.participant.document && $scope.participant.document.length == 14) {
                  $('#cpf').mask('99.999.999/9999-99');
                } else {
                  $('#cpf').mask('999.999.999-99');
                }
              }, 500);
            } else {
              $scope.occurredError = true;
              showErrorMessage(response.Error);
            }
          } else {
            $scope.occurredError = true;
            showErrorMessage('Ocorreu um erro durante o carregamento do nome e CPF.');
          }
        })
        .catch(function(resp) {
          $scope.loading = false;
          $scope.occurredError = true;
          showErrorMessage('Ocorreu um erro durante o carregamento do nome e CPF.');
          logger.logResponse(resp);
        });
    };

    $scope.disableContinueButton = function() {
      return $scope.processing || !$scope.calculated || !$scope.order.regulationAccepted;
    };

    $scope.onBankTransferMethodChange = function() {
      startComboboxes();
    };

    $scope.onCardSelect = function() {
      $timeout(function() {
        if ($scope.order.pointsAmount) {
          $scope.calculateFees();
        }
      }, 50);
    };

    const resetFees = function() {
      $scope.calculated = false;
      $scope.showFeesDetails = false;
      $scope.order.totalFeesAmount = 0;
      $scope.order.transferAmount = 0;
      $scope.order.totalCost = { points: 0 }
    };

    const createCashbackOrder = function() {
      $scope.loading = true;
      try {
        $scope.order.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      } catch (ex) { }

      $scope.order.locationInfo = {
        timezone: moment.tz.guess()
      }

      service.transferPoints($scope.order)
        .then(function(response) { return response.data; })
        .then(function(response) {
          $scope.loading = false;
          if (response) {
            if (response.Success) {
              showSuccessSwal('', 'Pedido realizado com sucesso. O número do seu pedido é: ' + response.Return);
              $scope.order.orderNumber = response.Return;
              $scope.order.created = !!$scope.order.orderNumber;
            } else {
              showErrorMessage(response.Error);
            }
          } else {
            showErrorMessage('Ocorreu um erro durante o processamento da requisição, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          }
        })
        .catch(function(resp) {
          $scope.loading = false;
          showErrorMessage('Ocorreu um erro durante o processamento da requisição, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          logger.logResponse(resp);
        });
    };

    $scope.onBankChange = function() {
      if ($scope.order.bankAccount.bankCode) {
        $scope.order.bankAccount.bankName = $('#bankCode :selected').text();
      } else {
        $scope.order.bankAccount.bankName = '';
      }
    };

    $scope.validateBankAccountNumber = function() {
      if ($scope.order.bankAccount.accountNumber && $scope.order.bankAccount.accountNumber.includes("-")) {
        $scope.order.bankAccount.accountNumber = '';
        showErrorMessage('Preencha o número da conta sem o dígito!');
        return;
      }
      return;
    };

    $scope.onBankAccountTypeChange = function() {
      if ($scope.order.bankAccount.type) {
        $scope.order.bankAccount.typeDescription = $('#accountType :selected').text();
      } else {
        $scope.order.bankAccount.typeDescription = '';
      }
    };

    $scope.calculateFees = function() {
      if (!$scope.order.pointsAmount) return;
      if (!($scope.order.pointsAmount > 0)) {
        showErrorMessage('Preencha um valor de transferência válido.');
        return;
      }
      $scope.loading = true;
      $scope.order.locationInfo = {
        timezone: moment.tz.guess()
      }
      service.calculateFees($scope.order)
        .then(function(response) { return response.data; })
        .then(function(response) {
          $scope.loading = false;
          if (response) {
            if (response.Success) {
              $scope.order.totalFeesAmount = response.Return.totalFeesAmount;
              $scope.order.transferAmount = response.Return.transferAmount;
              $scope.order.totalCost = response.Return.totalCost;
              $scope.order.detailedFees = response.Return.detailedFees;
              if ($scope.order.detailedFees) {
                $scope.showFeesDetails = true;
              }
              if (response.Return.occurredError) {
                showErrorMessage(response.Return.errorMessage);
              } else {
                $scope.calculated = $scope.order.transferAmount > 0;
              }
            } else {
              showErrorMessage(response.Error);
              resetFees();
            }
          } else {
            showErrorMessage('Ocorreu um erro durante o cálculo das taxas, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
            resetFees();
          }
        })
        .catch(function(resp) {
          $scope.loading = false;
          showErrorMessage('Ocorreu um erro durante o cálculo das taxas, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          resetFees();
          logger.logResponse(resp);
        });
    };

    $scope.continue = function(cardForm) {
      $scope.processing = true;
      $validationProvider.validate(cardForm)
        .then(function() {
          $scope.processing = false;
          $scope.finalStep = true;
        })
        .catch(function() {
          $scope.processing = false;
          showErrorMessage('Preencha todos os campos corretamente para prosseguir.');
        });
    };

    $scope.back = function() {
      $scope.finalStep = false;
    };

    // const validateAccount = function() {
    // 	Moip.BankAccount.validate({
    // 		bankNumber         : $scope.order.bankAccount.bankCode,
    // 		agencyCheckNumber  : 0,
    // 		agencyNumber       : $scope.order.bankAccount.agencyNumber,
    // 		accountNumber      : $scope.order.bankAccount.accountNumber,
    // 		accountCheckNumber : $scope.order.bankAccount.accountDigit,
    // 		valid: function() {
    // 			alert("Conta bancária válida");
    // 		},
    // 		invalid: function(data) {
    // 			if (data.errors && data.errors.length == 1) {
    // 				// Aceita a conta sem o dígito da agência
    // 				if (data.errors[0].code == 'INVALID_AGENCY_NUMBER') {
    // 					return;
    // 				}
    // 			}

    // 			var errorMessage = "Conta bancária inválida: \n";
    // 			for(i in data.errors){
    // 				if (data.errors[0].code == 'INVALID_AGENCY_NUMBER') {
    // 					continue;
    // 				}
    // 				errorMessage += data.errors[i].description + "-" + data.errors[i].code + ")\n";
    // 			}
    // 			showErrorSwal('Informações Inválidas', errorMessage);
    // 		}
    // 	});
    // };

    $scope.finalize = function() {
      const accountInfoBox = document.createElement('div');
      accountInfoBox.align = 'left';
      accountInfoBox.innerHTML = $('.account-info').html();
      swal("Por favor, confirme os dados da conta bancária para transferência:", {
        buttons: {
          cancel: 'Não',
          confirm: 'Confirmar'
        },
        content: accountInfoBox
      })
        .then(function(confirmed) {
          if (confirmed) {
            createCashbackOrder();
          }
        });
    };

    $scope.loadPage = function() {
      startOrUpdateChosen('bankCode');
      startOrUpdateChosen('accountType');
      loadPageConfig();
      loadParticipant();
    };
  }]);
