var userValidationRecaptcha = null;
function loadReCaptcha() {
	userValidationRecaptcha = null;
	userValidationRecaptcha = grecaptcha.render('userValidationRecaptcha', { 'sitekey': '6Ld1yb8qAAAAABB9QlKzBXTDuMoL-Jg-N8WhPIKy' });
}

angular.module('platformApp', ['footerModule', 'platformComponents', 'validation', 'validation.rule', 'ui.utils.masks', 'brasil.filters', 'ngLoaderContainer', 'EnderecoModule'])
	.service('CardsService', ['$http', function ($http) {
		const config = { headers: { 'Content-Type': 'application/json' } };
		this.getShippingAddreses = function () {
			return $http.get("/MinhaConta/EnderecosEntrega");
		};
		this.loadPageConfig = function (cardType) {
			return $http.get('/cartoes/' + cardType + '/configuracoes');
		};
		this.getIssuedCards = function (cardType) {
			return $http.get('/cartoes/' + cardType + '/gerados');
		};
		this.calculateFees = function (cardType, transaction) {
			return $http.post('/cartoes/' + cardType + '/taxas', transaction, config);
		};
		this.transferPoints = function (cardType, transaction) {
			return $http.post('/cartoes/' + cardType + '/transferir', transaction, config);
		};
		this.getCoinName = function () {
			return $http.get('/campaigns/coinname', config);
		};
		this.getCardParametrizations = function () {
			return $http.get(`/campaigns/cards/prepaid/parametrizations`);
		}
		this.getPrincipalContact = function () {
			return $http.get('/MinhaConta/PrincipalData');
		}
		this.sendAuthorizationCode = function (sendMethod) {
			return $http.post('/auth/authentication/code', { "sendMethod": sendMethod }, 30000);
		}
		this.validateAuthorizationCode = function (code) {
			return $http.put('/auth/authentication/code', { "code": code }, 30000);
		}

		//Cartões Pré-Pago do Parceiro
		this.findPrepaidCard = function (birthDate, firstDigits, lastDigits, expirationDate) {
			return $http.get(`/partners/prepaidcards/search-one-by-query?birthDate=${birthDate}&firstDigits=${firstDigits}&lastDigits=${lastDigits}&expirationDate=${expirationDate}`, config);
		};
		this.findPrepaidCardsInUse = function () {
			return $http.get('/partners/prepaidcards/in-use');
		};
		this.findPrepaidCardById = function (cardId) {
			return $http.get('/partners/prepaidcards/' + cardId, config);
		};
		this.activeCard = function (cardId) {
			return $http.put('/partners/prepaidcards/' + cardId + '/active', config);
		};
		this.blockCard = function (cardId) {
			return $http.put('/partners/prepaidcards/' + cardId + '/block', config);
		};
		this.useCard = function (cardId) {
			return $http.put('/partners/prepaidcards/' + cardId + '/use', config);
		};
		this.resetPin = function (cardId, pin) {
			return $http.put('/partners/prepaidcards/' + cardId + '/pin', { "pin": pin }, config);
		};
		this.getPrepaidCardStatement = function (cardId, startPeriod, endPeriod, limit) {
			return $http.get('/partners/prepaidcards/' + cardId + `/statement?startPeriod=${startPeriod}&endPeriod=${endPeriod}&limit=${limit}`, config);
		};
		this.getBalance = function (cardId) {
			return $http.get('/partners/prepaidcards/' + cardId + '/balance', config);
		};
		this.retrieveCardTracking = function (cardId) {
			return $http.get('/partners/prepaidcards/' + cardId + '/tracking', config);
		};
	}])
	.controller('cardsCtrl', ['CardsService', 'EnderecoApi', 'logger', '$scope', '$injector', '$timeout', '$sce', function (service, addressService, logger, $scope, $injector, $timeout, $sce) {
		$scope.cardType = null;
		$scope.occurredError = false;
		$scope.loading = false;
		$scope.loadingCardConfiguration = false;
		$scope.issuedCards = [];
		$scope.shippingAddresses = [];
		$scope.transaction = {
			pointsToTransfer: null,
			totalFeesAmount: 0,
			creditAmount: 0,
			orderTotalCost: {
				points: 0
			},
			issueNewCard: false,
			selectedShippingAddress: null
		};
		$scope.finalStep = false;
		$scope.calculated = false;

		$scope.loading = null;
		$scope.card = {
			birthDate: null,
			firstDigits: null,
			lastDigits: null,
			expirationDate: null,
		};
		$scope.searchedCard = {
			prepaidCardId: null,
			birthDate: null,
			firstDigits: null,
			lastDigits: null,
			expirationDate: null,
			active: false,
			cardStatusDescription: "Bloqueado",
			inUse: false,
			balance: 0.00
		};
		$scope.selectedCard = {
			prepaidCardId: null,
			firstDigits: null,
			lastDigits: null,
			active: false,
			cardStatusDescription: "Bloqueado",
			balance: 0.00
		};
		$scope.showPinForm = false;
		$scope.showExtractForm = false;
		$scope.parametrizations = {
			disableCardDetailsAccess: false,
			disableCardStatement: false,
			disableUpdateCardStatus: false,
			userValidationType: null
		};
		$scope.parametrizationsLoaded = false;
		$scope.cardsInUse = null;
		$scope.selectedCardDescription = null;
		$scope.detailsSelectedCard = null;
		$scope.pinRequest = {
			pin: null,
			confirmedPin: null
		};
		$scope.filters = {
			startPeriod: null,
			endPeriod: null,
			limit: 200,
			formattedStartDate: null,
			formattedEndPeriod: null,
		};
		$scope.dataFinalRange = null;
		$scope.moeda = {
			Prefix: '',
			Sufix: ''
		};
		$scope.statement = {
			transactions: [],
			totalAmount: 0
		};
		$scope.passwordResetStep = "BASIC_INFO";
		$scope.passwordResetSteps = {
			BASIC_INFO: "BASIC_INFO",
			TOKEN_OPTION: "TOKEN_OPTION",
			TOKEN_CONFIRMATION: "TOKEN_CONFIRMATION",
			USER_CONFIRMED: "USER_CONFIRMED"
		};
		$scope.sendType = {
			ALL: "ALL",
			EMAIL: "EMAIL",
			SMS: "SMS"
		};
		$scope.user = {};
		$scope.alreadySentToken = false;
		$scope.tracking = {};

		const $validationProvider = $injector.get('$validation');

		const showErrorMessage = function (message) {
			showErrorSwal('Ops', message);
		};

		const showInfo = function (message) {
			$scope.loading = false;
			showSuccessSwal('', message);
		};

		const loadPageConfig = function () {
			$scope.loading = true;
			service.loadPageConfig($scope.cardType)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							$scope.settings = response.Return;
							if ($scope.settings.regulation) {
								$scope.settings.regulation = $sce.trustAsHtml($scope.settings.regulation);
							}
							if ($scope.settings.cardDetailedImage) {
								startBLazyLoading('.card-image');
							}
							loadIssuedCards();
							getCoinName();
							if ($scope.canUpdateCardSettings()) {
								getCardParametrizations();
							}
						} else {
							$scope.occurredError = true;
							showErrorMessage(response.Error);
						}
					} else {
						$scope.occurredError = true;
						showErrorMessage('Ocorreu um erro durante o carregamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					$scope.occurredError = true;
					showErrorMessage('Ocorreu um erro durante o carregamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		const updateCardSelect = function () {
			$timeout(function () {
				$scope.$evalAsync(function () {
					startOrUpdateChosen('participantCard');
				});
			}, 50);
		};

		const loadIssuedCards = function () {
			$scope.loading = true;
			service.getIssuedCards($scope.cardType)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							$scope.issuedCards = response.Return;
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante o carregamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
					updateCardSelect();
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante o carregamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
					updateCardSelect();
				});
		};

		$scope.canShowCardSettings = function () {
			return $scope.canUpdateCardSettings() && $scope.parametrizationsLoaded;
		};

		$scope.canUpdateCardSettings = function () {
			return $scope.settings && $scope.settings.provider == "CONTA_SWAP";
		};

		$scope.disableCardDetailsAccess = function () {
			return $scope.parametrizations && $scope.parametrizations.disableCardDetailsAccess;
		};

		const updateShippingAddressSelect = function () {
			$timeout(function () {
				$scope.$evalAsync(function () {
					startOrUpdateChosen('selectedShippingAddress');
				});
			}, 50);
		};

		const loadShippingAddresses = function () {
			$scope.loadingAddresses = true;
			service.getShippingAddreses()
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loadingAddresses = false;
					if (response) {
						if (response.Success) {
							$scope.shippingAddresses = response.Return;
							updateShippingAddressSelect();
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante o carregamento dos endereços de entrega, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (resp) {
					// $scope.loadingAddresses = false;
					showErrorMessage('Ocorreu um erro durante o carregamento dos endereços de entrega, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(resp);
				});
		};

		const checkIfNeedsToShowShippingAddress = function () {
			if ($scope.transaction.issueNewCard || $scope.transaction.reissueCard) {
				$scope.transaction.shippingAddress = { receiver: {} };
				if ($scope.transaction.issueNewCard) {
					$scope.transaction.card = {
						Id: 'new-card',
						Number: 'Novo Cartão'
					};
				}
				if ($scope.shippingAddresses && $scope.shippingAddresses.length) {
					updateShippingAddressSelect();
				} else {
					loadShippingAddresses();
				}
			} else {
				$scope.transaction.shippingAddress = null;
				$scope.transaction.card = $scope.issuedCards.find(function (c) { return c.Id == $scope.transaction.selectedCard; });
			}
		};

		const showNewAddressPopup = function () {
			$.fancybox({
				maxWidth: 720,
				maxHeight: 480,
				padding: 0,
				href: "/Carrinho/NovoEndereco",
				type: 'iframe',
				beforeShow: function () {
					this.maxWidth = 720;
					this.maxHeight = 480;
					this.width = $(".fancybox-iframe").contents().find("html").width();
					this.height = $(".fancybox-iframe").contents().find("html").height();
				},
				afterClose: function () {
					$scope.shippingAddresses = [];
					loadShippingAddresses();
					return;
				}
			});
			$scope.transaction.selectedShippingAddress = null;
			$scope.transaction.shippingAddress = { receiver: {} };
		};

		$scope.disableContinueButton = function () {
			return $scope.processing || !$scope.calculated || !$scope.transaction.regulationAccepted;
		};

		$scope.disableTrackingButton = function () {
			return $scope.processing || !$scope.transaction.selectedCard;
		};

		$scope.onCardSelect = function () {
			if (!$scope.transaction.selectedCard) return;

			$scope.transaction.issueNewCard = $scope.transaction.selectedCard == 'new-card';
			if ($scope.transaction.issueNewCard) {
				$scope.transaction.reissueCard = false;
			}
			$scope.transaction.selectedShippingAddress = null;
			checkIfNeedsToShowShippingAddress();
			$timeout(function () {
				if ($scope.transaction.selectedCard && $scope.transaction.pointsToTransfer) {
					$scope.calculateFees();
				}
			}, 50);
		};

		$scope.onReissueCardClick = function () {
			checkIfNeedsToShowShippingAddress();
			$timeout(function () {
				if ($scope.transaction.selectedCard && $scope.transaction.pointsToTransfer) {
					$scope.calculateFees();
				}
			}, 50);
		};

		$scope.onShippingAddressSelect = function () {
			if (!$scope.transaction.selectedShippingAddress) {
				$scope.transaction.shippingAddress = {
					receiver: {}
				};
			} else {
				if ($scope.transaction.selectedShippingAddress == 'other') {
					showNewAddressPopup();
				} else {
					const selectedAddress = $scope.shippingAddresses.find(function (a) { return a.Id == $scope.transaction.selectedShippingAddress; });
					if (selectedAddress) {
						$scope.transaction.shippingAddress = selectedAddress;
					}
				}
			}
		};

		$scope.searchAddressByCep = function (address) {
			addressService.fillAddressByCep(address.Cep, address);
		};

		$scope.calculateFees = function () {
			if (!$scope.transaction.pointsToTransfer) return;
			if (!($scope.transaction.pointsToTransfer > 0)) {
				showErrorMessage('Preencha um valor de transferência válido.');
				return;
			}
			if (!$scope.transaction.selectedCard) {
				showErrorMessage('Selecione um cartão válido ou selecione emitir um novo.');
				return;
			}
			$scope.loading = true;
			service.calculateFees($scope.cardType, $scope.transaction)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							$scope.transaction.totalFeesAmount = response.Return.totalFeesAmount;
							$scope.transaction.creditAmount = response.Return.creditAmount;
							$scope.transaction.orderTotalCost = response.Return.totalCost;
							$scope.transaction.detailedFees = response.Return.detailedFees;
							if ($scope.transaction.detailedFees) {
								$scope.showFeesDetails = true;
							}
							if (response.Return.occurredError) {
								showErrorMessage(response.Return.errorMessage);
							} else {
								$scope.calculated = $scope.transaction.creditAmount > 0;
							}
						} else {
							showErrorMessage(response.Error);
							resetFees();
						}
					} else {
						showErrorMessage('Ocorreu um erro durante o cálculo das taxas, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
						resetFees();
					}
				})
				.catch(function (resp) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante o cálculo das taxas, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					resetFees();
					logger.logResponse(resp);
				});
		};

		const resetFees = function () {
			$scope.calculated = false;
			$scope.showFeesDetails = false;
			$scope.transaction.totalFeesAmount = 0;
			$scope.transaction.creditAmount = 0;
			$scope.transaction.orderTotalCost = { points: 0 }
		};

		$scope.continue = function (cardForm) {
			$scope.processing = true;
			$validationProvider.validate(cardForm)
				.then(function () {
					$scope.processing = false;
					$scope.finalStep = true;
				})
				.catch(function () {
					$scope.processing = false;
					showErrorMessage('Preencha todos os campos corretamente para prosseguir.');
				});
		};

		$scope.retrieveCardTracking = function () {
			$scope.loading = true;
			service.retrieveCardTracking($scope.transaction.selectedCard)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							$scope.tracking = response.Return;
							showTrackingModal();
							setTimeout(function () {
								if ($scope.tracking.receiverDocument && $scope.tracking.receiverDocument.length == 14) {
									$('#receiverDocument').mask('99.999.999/9999-99');
								} else {
									$('#receiverDocument').mask('999.999.999-99');
								}
							}, 500);
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante o rastreio do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante o rastreio do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		const showTrackingModal = function () {
			$timeout(function () {
				$scope.$evalAsync(function () {
					$.fancybox.open('#card-tracking-modal');
				});
			}, 50);
		}

		$scope.back = function () {
			$scope.finalStep = false;
		};

		$scope.finalize = function () {
			$scope.loading = true;
			if ($scope.transaction.selectedCard && $scope.transaction.selectedCard != 'new-card') {
				$scope.transaction.prepaidCardId = $scope.transaction.selectedCard;
			}

			try {
				$scope.transaction.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
				$scope.transaction.locationInfo = {
					timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
				}
			} catch (ex) { }

			service.transferPoints($scope.cardType, $scope.transaction)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							showSuccessSwal('', 'Pedido realizado com sucesso. O número do seu pedido é: ' + response.Return);
							$scope.transaction.orderNumber = response.Return;
							$scope.transaction.created = !!$scope.transaction.orderNumber;
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante o processamento da requisição, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (resp) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante o processamento da requisição, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(resp);
				});
		};

		$scope.loadPage = function (cardType) {
			$scope.cardType = cardType;
			loadPageConfig();
		};

		const getCoinName = function () {
			$scope.loading = true;
			service.getCoinName()
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							$scope.moeda = response.Return;
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante a consulta do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante a consulta do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};


		// Cartões Pré-Pago do parceiro
		$scope.findPrepaidCard = function () {
			if (!$scope.card.birthDate) {
				showErrorMessage("Preencha a Data de Nascimento!");
				return;
			}
			$scope.filters.birthDateFormatted = moment($scope.card.birthDate).format("YYYY-MM-DD");

			if (!$scope.card.expirationDate) {
				showErrorMessage("Preencha a Data de Validade!");
				return;
			}

			if (!$scope.card.firstDigits) {
				showErrorMessage("Preencha os Primeiros 4 Dígitos do cartão!");
				return;
			}

			if (!$scope.card.lastDigits) {
				showErrorMessage("Preencha os Últimos 4 Dígitos do cartão!");
				return;
			}
			searchPrepaidCard();
		};

		const updateCard = function () {
			if ($scope.hasSelectedCard()) {
				$scope.findSelectedPrepaidCardById();
			}

			if ($scope.hasSearchedCard()) {
				searchPrepaidCard();
			}
		}

		const searchPrepaidCard = function () {
			$scope.loading = true;
			service.findPrepaidCard($scope.filters.birthDateFormatted, $scope.card.firstDigits, $scope.card.lastDigits, $scope.card.expirationDate)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							var partnerPrepaidCard = response.Return;
							if (partnerPrepaidCard.PrepaidCard) {
								updateSearchedCard(partnerPrepaidCard.PrepaidCard);
							}
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante a consulta do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante a consulta do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		const updateSearchedCard = function (card) {
			$scope.searchedCard = card;
			$scope.searchedCard.prepaidCardId = card.Id;
			$scope.searchedCard.firstDigits = card.FirstDigits ? card.FirstDigits : "xxxx";
			$scope.searchedCard.lastDigits = card.LastDigits ? card.LastDigits : "xxxx";
			$scope.searchedCard.active = card.Active;
			$scope.searchedCard.cardStatusDescription = card.Active ? "Desbloqueado" : "Bloqueado";
			$scope.searchedCard.inUse = card.InUse;
			getBalance(card.Id, false);
		}

		findPrepaidCardsInUse = function () {
			$scope.loading = true;
			service.findPrepaidCardsInUse()
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							$scope.cardsInUse = response.Return;
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante a consulta dos cartões, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante a consulta dos cartões, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		$scope.findSelectedPrepaidCardById = function () {
			if (!$scope.transaction.selectedCard) return;
			$scope.loading = true;
			service.findPrepaidCardById($scope.transaction.selectedCard)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							var partnerPrepaidCard = response.Return;
							if (partnerPrepaidCard.PrepaidCard) {
								updateSelectedCard(partnerPrepaidCard.PrepaidCard);
							}
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante o carregamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante o carregamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		const updateSelectedCard = function (card) {
			$scope.selectedCard.prepaidCardId = card.Id;
			$scope.selectedCard.firstDigits = card.FirstDigits ? card.FirstDigits : "xxxx";
			$scope.selectedCard.lastDigits = card.LastDigits ? card.LastDigits : "xxxx";
			$scope.selectedCard.active = card.Active;
			$scope.selectedCard.cardStatusDescription = card.Active ? "Desbloqueado" : "Bloqueado";
			getBalance(card.Id, true);
		}

		$scope.confirmActiveCard = function (cardId) {
			const accountInfoBox = document.createElement('div');
			accountInfoBox.align = 'left';
			accountInfoBox.innerHTML = $('.account-info').html();
			swal("Por favor, confirme o desbloqueio do cartão:", {
				buttons: {
					cancel: 'Não',
					confirm: 'Confirmar'
				}
			})
				.then(function (confirmed) {
					if (confirmed) {
						activeCard(cardId);
					}
				});
		};

		const activeCard = function (cardId) {
			$scope.loading = true;
			$scope.loading = true;
			service.activeCard(cardId)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							showInfo("Cartão desbloqueado com sucesso!");
							updateCard();
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante a ativação do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante a ativação do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		$scope.confirmBlockCard = function (cardId) {
			const accountInfoBox = document.createElement('div');
			accountInfoBox.align = 'left';
			accountInfoBox.innerHTML = $('.account-info').html();
			swal("Por favor, confirme o bloqueio do cartão:", {
				buttons: {
					cancel: 'Não',
					confirm: 'Confirmar'
				}
			})
				.then(function (confirmed) {
					if (confirmed) {
						blockCard(cardId);
					}
				});
		};

		const blockCard = function (cardId) {
			$scope.loading = true;
			service.blockCard(cardId)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							showInfo("Cartão bloqueado com sucesso!");
							updateCard();
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante o bloqueio do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante o bloqueio do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		$scope.confirmUseCard = function () {
			const accountInfoBox = document.createElement('div');
			accountInfoBox.align = 'left';
			accountInfoBox.innerHTML = $('.account-info').html();
			swal("Por favor, confirme o desbloqueio do cartão para uso:", {
				buttons: {
					cancel: 'Não',
					confirm: 'Confirmar'
				}
			})
				.then(function (confirmed) {
					if (confirmed) {
						useCard();
					}
				});
		};

		const useCard = function () {
			$scope.loading = true;
			service.useCard($scope.searchedCard.prepaidCardId)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							showInfo("Cartão desbloqueado com sucesso!");
							searchPrepaidCard();
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante o carregamento, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
					updateCardSelect();
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante o carregamento do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
					updateCardSelect();
				});
		};

		$scope.updateFormPin = function () {
			if (!$scope.pinRequest.pin) {
				showErrorMessage("Preencha os campos corretamente para prosseguir!");
				return;
			}

			if (!$scope.pinRequest.confirmedPin) {
				showErrorMessage("Confirme a senha corretamente para prosseguir!");
				return;
			}

			if ($scope.pinRequest.pin != $scope.pinRequest.confirmedPin) {
				showErrorMessage('As senham não conferem.');
				return;
			}
			confirmResetPin();
		}

		const confirmResetPin = function () {
			const accountInfoBox = document.createElement('div');
			accountInfoBox.align = 'left';
			accountInfoBox.innerHTML = $('.account-info').html();
			swal("Por favor, confirme a alteração de senha do cartão:", {
				buttons: {
					cancel: 'Não',
					confirm: 'Confirmar'
				}
			})
				.then(function (confirmed) {
					if (confirmed) {
						resetPin();
					}
				});
		};

		const resetPin = function () {
			$scope.loading = true;
			service.resetPin($scope.selectedCard.prepaidCardId, $scope.pinRequest.pin)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							showInfo("Senha alterada com sucesso!");
							closePinForm();
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante a alteração de senha, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante a alteração de senha, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		$scope.getTransactions = function () {
			if (!$scope.filters) {
				$scope.filters = {};
			}

			if (!$scope.filters.formattedStartDate) {
				$scope.filters.startPeriod = moment().add(-30, 'days').format("YYYY-MM-DD");
				$scope.filters.formattedStartDate = moment($scope.filters.startPeriod, 'YYYY-MM-DD').format("DD/MM/YYYY");
			} else {
				$scope.filters.startPeriod = moment($scope.filters.formattedStartDate, "DD/MM/YYYY").format("YYYY-MM-DD");
			}

			if (!$scope.filters.formattedEndPeriod) {
				$scope.filters.endPeriod = moment().format("YYYY-MM-DD");
				$scope.filters.formattedEndPeriod = moment($scope.filters.endPeriod, 'YYYY-MM-DD').format("DD/MM/YYYY");
			} else {
				$scope.filters.endPeriod = moment($scope.filters.formattedEndPeriod, "DD/MM/YYYY").format("YYYY-MM-DD");
			}

			if (!$scope.filters.limit) {
				$scope.filters.limit = 10000;
			}

			getStatement();
		}

		const getStatement = function () {
			if (!$scope.filters.startPeriod) {
				showErrorMessage('Escolha a data inicial do período para consultar extrato.')
				return;
			}

			if ($scope.filters.startPeriod && !$scope.filters.endPeriod) {
				showErrorMessage('Escolha a data final do período para consultar extrato.')
				return;
			}

			if (monthDiff(
				new Date(moment($scope.filters.startPeriod).format('YYYY-MM-DD')),
				new Date(moment($scope.filters.endPeriod).format('YYYY-MM-DD'))
			) > 3) {
				showErrorMessage('Só é possível consultar extrato de um período com no máximo 3 meses.');
				return;
			}

			getPrepaidCardStatement();
		}

		const monthDiff = function (d1, d2) {
			var months;
			months = (d2.getFullYear() - d1.getFullYear()) * 12;
			months -= d1.getMonth();
			months += d2.getMonth();
			return months <= 0 ? 0 : months;
		}

		const getPrepaidCardStatement = function () {
			$scope.loading = true;
			service.getPrepaidCardStatement($scope.selectedCard.prepaidCardId, $scope.filters.startPeriod, $scope.filters.endPeriod, $scope.filters.limit)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							$scope.statement = response.Return;
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante a consulta do extrato do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante a consulta do extrato do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		const getBalance = function (cardId, isForSelectedCard) {
			$scope.loading = true;
			service.getBalance(cardId)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							var balance = response.Return.amount ? response.Return.amount : 0;
							if (isForSelectedCard) {
								$scope.selectedCard.balance = balance;
							} else {
								$scope.searchedCard.balance = balance;
							}
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante a consulta do saldo do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante a consulta do saldo do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		const getCardParametrizations = function () {
			$scope.loadingCardConfiguration = true;
			service.getCardParametrizations()
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loadingCardConfiguration = false;
					if (response) {
						if (response.Success) {
							$scope.parametrizations = response.Return;
							$scope.parametrizationsLoaded = true;
							if ($scope.parametrizations && $scope.parametrizations.disableCardDetailsAccess) {
								return;
							}
							findPrepaidCardsInUse();
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante a consulta das parametrizações do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loadingCardConfiguration = false;
					showErrorMessage('Ocorreu um erro durante a consulta das parametrizações do cartão, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		}

		$scope.disableButtons = function () {
			return $scope.processing || $scope.loading || $scope.loadingCardConfiguration;
		};

		$scope.hasSelectedCard = function () {
			return $scope.selectedCard && $scope.selectedCard.prepaidCardId && $scope.selectedCard.firstDigits;
		};

		$scope.isSelectedCardActive = function () {
			return $scope.hasSelectedCard() && $scope.selectedCard.active;
		};

		$scope.hasSearchedCard = function () {
			return $scope.searchedCard && $scope.searchedCard.prepaidCardId && $scope.searchedCard.firstDigits;
		};

		$scope.isSearchedCardActive = function () {
			return $scope.hasSearchedCard() && $scope.searchedCard.active;
		};

		$scope.isSearchedCardInUse = function () {
			return $scope.hasSearchedCard() && $scope.searchedCard.inUse;
		};

		$scope.updateSearchedCardStatus = function () {
			return $scope.hasSearchedCard() && $scope.searchedCard.inUse;
		};

		$scope.width = function () {
			return window.innerWidth;
		};

		$scope.resetPin = function () {
			$scope.showPinForm = true;
			prepCaptcha();
		}

		const prepCaptcha = function () {
			if (userValidationRecaptcha === null)
				$.getScript("https://www.google.com/recaptcha/api.js?onload=loadReCaptcha");
			else {
				$('#userValidationRecaptcha').html('');
				$.getScript("https://www.google.com/recaptcha/api.js?onload=loadReCaptcha");
			}
		}

		$scope.closePinForm = function () {
			closePinForm();
		}

		const closePinForm = function () {
			$scope.passwordResetStep = $scope.passwordResetSteps.BASIC_INFO;
			$scope.showPinForm = false;
			$scope.pinRequest = {
				pin: null,
				confirmedPin: null
			};
		}

		$scope.showExtract = function () {
			$scope.showExtractForm = true;
			$scope.getTransactions();
		}

		$scope.closeExtract = function () {
			$scope.showExtractForm = false;
		}

		$scope.validateReCaptcha = function () {
			if (!isReCaptchaValid()) return;
			if (!$scope.parametrizations) {
				getCardParametrizations();
			}
			loadUserInfo();
			$scope.passwordResetStep = $scope.passwordResetSteps.TOKEN_OPTION;
		}

		const isReCaptchaValid = function () {
			const token = grecaptcha.getResponse(userValidationRecaptcha);
			if (token.length > 0) {
				return true;
			} else {
				showErrorMessage("Preencha o reCaptcha para prosseguir.");
				return false;
			}
		};

		const loadUserInfo = function () {
			$scope.loading = true;
			service.getPrincipalContact()
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							$scope.user = response.Return;
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante a consulta do usuário, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante a consulta do usuário, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		}


		$scope.sendAuthorizationCode = function () {
			if (!$scope.user.sendMethod) {
				showErrorMessage('Selecione a forma de envio do código de segurança.');
				return;
			}

			$scope.loading = true;
			service.sendAuthorizationCode($scope.user.sendMethod)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							$scope.passwordResetStep = $scope.passwordResetSteps.TOKEN_CONFIRMATION;
						} else {
							showErrorMessage(response.Error);
							$scope.alreadySentToken = true;
						}
					} else {
						showErrorMessage('Ocorreu um erro durante o envio do código de autenticação, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
						$scope.alreadySentToken = true;
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante o envio do código de autenticação, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		$scope.validateAuthorizationCode = function () {
			if (!$scope.user.code) {
				showErrorMessage('Preencha o código de segurança.');
				return;
			}

			$scope.loading = true;
			service.validateAuthorizationCode($scope.user.code)
				.then(function (response) { return response.data; })
				.then(function (response) {
					$scope.loading = false;
					if (response) {
						if (response.Success) {
							$scope.passwordResetStep = $scope.passwordResetSteps.USER_CONFIRMED;
						} else {
							showErrorMessage(response.Error);
						}
					} else {
						showErrorMessage('Ocorreu um erro durante a confirmação do código de autenticação, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					}
				})
				.catch(function (response) {
					$scope.loading = false;
					showErrorMessage('Ocorreu um erro durante a confirmação do código de autenticação, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
					logger.logResponse(response);
				});
		};

		$scope.isBasicInfoStep = function () {
			return $scope.passwordResetStep == $scope.passwordResetSteps.BASIC_INFO;
		};

		$scope.isTokenOptionStep = function () {
			return $scope.passwordResetStep == $scope.passwordResetSteps.TOKEN_OPTION;
		};

		$scope.isTokenConfirmationStep = function () {
			return $scope.passwordResetStep == $scope.passwordResetSteps.TOKEN_CONFIRMATION;
		};

		$scope.isFinalStep = function () {
			return $scope.passwordResetStep == $scope.passwordResetSteps.USER_CONFIRMED;
		};

		$scope.canChoose = function () {
			return $scope.parametrizations.userValidationType == $scope.sendType.ALL || !$scope.parametrizations.userValidationType;
		}

		$scope.canSendEmail = function () {
			return $scope.parametrizations.userValidationType == $scope.sendType.EMAIL || $scope.canChoose();
		}

		$scope.canSendSMS = function () {
			return $scope.parametrizations.userValidationType == $scope.sendType.SMS || $scope.canChoose();
		}

		$scope.hasTracking = function () {
			return $scope.tracking && $scope.tracking.deliveredAt;
		}

		$scope.hasTrackingEvents = function () {
			return $scope.tracking && $scope.tracking.events && $scope.tracking.events.length;
		}

		$scope.hasDeliveredCard = function () {
			return $scope.tracking && $scope.tracking.pickedUpAt;
		}
	}]);
