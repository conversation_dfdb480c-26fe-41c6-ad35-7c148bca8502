angular
  .module("platformApp", [
    "footerModule",
    "platformComponents",
    "validation",
    "validation.rule",
    "moment-picker",
    "ngLoaderContainer",
  ])
  .service("AutenticacaoMfaApiService", [
    "$http",
    function ($http) {
      this.sendTokenFromValidate = function (dados) {
        return $http.post(
          "/Autenticacao/SendAuthenticationMfaToValidate",
          dados
        );
      };

      this.validateAuthenticationMfaToken = function (dados) {
        return $http.post(
          "/Autenticacao/ValidateAuthenticationMfaToken",
          dados
        );
      };

      this.enviarMfaToken = function (dados) {
        return $http.post("/Autenticacao/EnviarMfaToken", dados);
      };

      this.validarTokenDuplaAutenticacao = function (dados) {
        return $http.post("/Autenticacao/ValidarTokenDuplaAutenticacao", dados);
      };
    },
  ])
  .controller("AutenticacaoMfaCtrl", [
    "AutenticacaoMfaApiService",
    "logger",
    "$scope",
    "$timeout",
    "$injector",
    function (service, logger, $scope, $timeout, $injector) {
      $scope.dados = {};
      $scope.tokenSended = false;
      $scope.counter = 0;
      $scope.userToken = {};

      $scope.init = function () {};

      $scope.sendTokenFromValidate = function (dados) {
        //! TODO: Revisar como passar @Model no ng-click
        $scope.sendingToken = true;
        service
          .sendTokenFromValidate($scope.dados)
          .then(function (response) {
            return response.data;
          })
          .then(function (retorno) {
            $scope.sendingToken = false;
            if (retorno.Success && retorno.Return) {
              showSuccessSwal(
                "Info",
                "Código de segurança enviado com sucesso"
              );
              $scope.tokenSended = retorno.Return;
              $scope.startCountdown(60);
            } else {
              $scope.sendingToken = false;
              logger.log(retorno.Error);
              showErrorSwal("Ops", retorno.Error);
              $scope.tokenSended = false;
            }
          })
          .catch(function (retorno) {
            $scope.tokenSended = false;
            logger.logResponse(retorno);
            showErrorSwal(
              "Ops",
              "Ocorreu um erro ao enviar o código de segurança, por favor, tente novamente."
            );
          });
      };

      $scope.validateTokenAndRefresh = function () {
        if ($scope.userToken.token === undefined) {
          showErrorSwal("Ops", "Informe o código de segurança");
          return;
        }
        $scope.validatingToken = true;
        service
          .validateAuthenticationMfaToken($scope.userToken)
          .then(function (response) {
            return response.data;
          })
          .then(function (retorno) {
            $scope.validatingToken = false;
            if (retorno.Success) {
              showSuccessSwal(
                "Info",
                "Código de segurança validado com sucesso"
              );
              if (retorno.Redirect) {
                location.href = retorno.Redirect;
              }
            } else {
              showErrorSwal("Ops", retorno.Error);
            }
          })
          .catch(function (retorno) {
            $scope.validatingToken = false;
            logger.logResponse(retorno);
            showErrorSwal(
              "Ops",
              "Ocorreu um erro ao enviar o código de segurança, por favor, tente novamente."
            );
          });
      };

      $scope.startCountdown = function (seconds) {
        // Using timeout.
        $scope.counter = seconds;
        $scope.Timeout = function () {
          $scope.counter--;
          timeout = $timeout($scope.Timeout, 1000);
          if ($scope.counter == 0) {
            $timeout.cancel(timeout);
          }
        };
        var timeout = $timeout($scope.Timeout, 1000);
      };
    },
  ])
  .controller("ConfiguracaoMfaCtrl", [
    "AutenticacaoMfaApiService",
    "logger",
    "$scope",
    "$timeout",
    "$injector",
    function (service, logger, $scope, $timeout, $injector) {
      $scope.participantMfaSettings = {};
      $scope.tokenSended = false;
      $scope.counter = 0;
      $scope.userToken = {};
      $scope.loading = false;
      $scope.candEditAuthenticationFormat = false;

      $scope.init = function () {};

      $scope.sendToken = function () {
        $scope.sendingToken = true;
        service
          .enviarMfaToken($scope.participantMfaSettings)
          .then(function (response) {
            return response.data;
          })
          .then(function (retorno) {
            $scope.sendingToken = false;
            if (retorno.Success && retorno.Return) {
              showSuccessSwal(
                "Info",
                "Código de segurança enviado com sucesso"
              );
              $scope.tokenSended = retorno.Return;
              $scope.startCountdown(60);
            } else {
              $scope.sendingToken = false;
              logger.log(retorno.Error);
              showErrorSwal("Ops", retorno.Error);
              $scope.tokenSended = false;
            }
          })
          .catch(function (retorno) {
            $scope.tokenSended = false;
            logger.logResponse(retorno);
            showErrorSwal(
              "Ops",
              "Ocorreu um erro ao enviar o código de segurança, por favor, tente novamente."
            );
          });
      };

      $scope.validateToken = function () {
        if ($scope.userToken.token === undefined) {
          showErrorSwal("Ops", "Informe o código de segurança");
          return;
        }
        $scope.userToken.email = $scope.participantMfaSettings.email;
        $scope.userToken.mobilePhone =
          $scope.participantMfaSettings.mobilePhone;
        $scope.userToken.authenticationMfaFormat =
          $scope.participantMfaSettings.authenticationMfaFormat;

        $scope.validatingToken = true;
        service
          .validarTokenDuplaAutenticacao($scope.userToken)
          .then(function (response) {
            return response.data;
          })
          .then(function (retorno) {
            $scope.validatingToken = false;
            if (retorno.Success) {
              showSuccessSwal(
                "Info",
                "Código de segurança validado com sucesso"
              );
            } else {
              showErrorSwal("Ops", retorno.Error);
            }

            if (retorno.Redirect) {
              location.href = retorno.Redirect;
            }
          })
          .catch(function (retorno) {
            $scope.validatingToken = false;
            logger.logResponse(retorno);
            showErrorSwal(
              "Ops",
              "Ocorreu um erro ao validar o código de segurança, por favor, tente novamente."
            );
          });
      };

      $scope.startCountdown = function (seconds) {
        // Using timeout.
        $scope.counter = seconds;
        $scope.Timeout = function () {
          $scope.counter--;
          timeout = $timeout($scope.Timeout, 1000);
          if ($scope.counter == 0) {
            $timeout.cancel(timeout);
          }
        };
        var timeout = $timeout($scope.Timeout, 1000);
      };

      $scope.init = function (authenticationMfaFormat) {
        $scope.candEditAuthenticationFormat =
          authenticationMfaFormat == "ALWAYS_SELECT" || "SELECT_ONCE"
            ? true
            : false;

        if (!$scope.candEditAuthenticationFormat) {
          $scope.participantMfaSettings.authenticationMfaFormat = authenticationMfaFormat;
        }

        $.fn.initChosen("select.autostart");
        $.fn.initChosen("#authFormat");
        $("#authFormat").trigger("chosen:updated");
      };
    },
  ]);
