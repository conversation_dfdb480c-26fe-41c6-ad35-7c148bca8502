﻿angular.module("platformApp", ['footerModule', 'ngLoaderContainer', 'ngSanitize', 'validation', 'validation.rule', 'ui.utils.masks'])
  .service('faleConoscoService', ['$http', function ($http) {
    this.enviarMensagem = function (contato) {
      return $http.post('/FaleConosco/EnviarMensagem', contato);
    }
    this.carregaDadosParticipante = function () {
      return $http.get('/MinhaConta/PrincipalData');
    }
  }]).controller('faleConoscoController', ['faleConoscoService', 'logger', '$scope', function (service, logger, $scope) {
    $scope.contato = {};
    $scope.listaAssunto = [];
    $scope.loading = false;

    $scope.enviarMensagem = function (contact) {

      if (!contact.Name || !contact.Document || !contact.Email || !(contact.Phone || contact.Cellphone) || !contact.Subject || !contact.Message) {
        showWarningSwal('Aviso', 'Preencha todos os campos corretamente para continuar.');
        return;
      }
      $scope.loading = true;

      service.enviarMensagem(contact)
        .then(function (response) { return response.data; })
        .then(function (result) {
          if (result.Success) {
            $scope.contato = {};
            contact.Document = contact.Document.replace( /\D/g , "");
            $scope.frmContact.$setUntouched();
            $scope.frmContact.$setPristine();
            showSuccessSwal('Info', "Mensagem enviada com sucesso. Aguarde que iremos responder o mais rápido possível.");
          } else {
            if (result.Error)
              showErrorSwal('Ops', result.Error);
            else
              showErrorSwal('Ops', "Não foi possível enviar sua mensagem, por favor, tente novamente.");
          }
          $scope.loading = false;
        })
        .catch(function (resp) {
          $scope.loading = false;
          logger.logResponse(resp);
          showErrorSwal('Ops', "Não foi possível enviar sua mensagem, por favor, tente novamente.");
        });
    };

    $scope.carregaDadosParticipante = function () {
      service
        .carregaDadosParticipante()
        .then(function (response) {
          return response.data;
        })
        .then(function (result) {
          if (result && result.Return) {
            $scope.contato.Name = result.Return.name;
            $scope.contato.Document = result.Return.document;
            $scope.contato.Email = result.Return.email;
            $scope.contato.Cellphone = result.Return.mobilePhone;
          }
        });
    };
  }]);