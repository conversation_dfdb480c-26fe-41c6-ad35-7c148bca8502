$.fn.initProductZoom('.product-details .photo img');
$.fn.initModal("a.modal");
$.fn.initModalZoom("a.zoom, a.details");
$.fn.initFaq(".faq-result ol li a:first-child");
$.fn.initViewType(".viewType");
$.fn.initMediabox(".mediabox");
$.fn.initMobile();
$.fn.initLogin();
$.fn.initScrollbar(".scroll");
$.fn.initTabs(".tabs");
$.fn.initTooltip(".tooltip");
$.fn.initFormTooltip("form input, form select, textarea");
$.fn.initScroll(".scrollTo");
$.fn.initForm("form");
$.fn.initCreditCardBrand("input.creditCard");
$.fn.initChosen("select.autostart");

const showSuccessSwal = function (title, message) {
  return swal({
    title: title,
    text: message,
    icon: "success",
    button: true
  });
};

const showWarningSwal = function (title, message) {
  return swal({
    title: title,
    text: message,
    icon: "warning",
    button: true
  });
};

const showErrorSwal = function (title, message) {
  return swal({
    title: title,
    text: message,
    icon: "error",
    button: true,
    dangerMode: true
  });
};

const showConfirmSwal = function (message) {
  return swal(message, {
    buttons: {
      cancel: 'Não',
      confirm: 'Confirmar'
    },
    defeat: true,
  });
};

const showHtmlSwall = function (_content, _buttonText) {
  return swal({
    content: _content,
    button: _buttonText
  })
}

function pageView(page) {
  if (ga) {
    ga('send', 'pageview', page);
  }
  if (gtag) {
    gtag('event', 'page_view', {
      page_title: document.title,
      page_location: window.location.href,
      page_path: page
    });
  } else if (dataLayer) {
    dataLayer.push({
      event: 'page_view',
      page_title: document.title,
      page_location: window.location.href,
      page_path: page
    });
  }
}

function pageViewOnDocumentReady(page) {
  $(document).ready(function () {
    pageView(page);
  });
}

function sendEvent(eventCategory, eventAction, eventLabel, eventValue) {
  console.debug(eventCategory, eventAction, eventLabel, eventValue);
  if (ga) {
    ga('send', {
      hitType: 'event',
      dr: window.location.host,
      eventCategory: eventCategory,
      eventAction: eventAction,
      eventLabel: eventLabel,
      eventValue: eventValue || 1
    });
  }

  if (gtag) {
    gtag('event', 'custom_event', {
      event_category: eventCategory,
      event_action: eventAction,
      event_label: eventLabel,
      value: eventValue || 1
    });
  } else if (dataLayer) {
    dataLayer.push({
      event: 'custom_event',
      event_category: eventCategory,
      event_action: eventAction,
      event_label: eventLabel,
      value: eventValue || 1
    });
  }
}

function sendCustomEvent(eventName = 'custom_event', params = {}) {
  console.debug(eventName, params);
  if (gtag) {
    gtag('event', eventName, params);
  } else if (dataLayer) {
    params.event = eventName;
    dataLayer.push(params);
  }
}

function hasPath(path) {
  return window.location.href.indexOf(path) > -1;
}
function getParameterByName(name, url) {
  if (!url) url = window.location.href;
  url = unescape(url);
  name = name.replace(/[\[\]]/g, "\\$&");
  var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
    results = regex.exec(url);
  if (!results) return null;
  if (!results[2]) return '';
  return decodeURIComponent(results[2].replace(/\+/g, " "));
}

function getCookieByName(cname) {
  var name = cname + "=";
  var decodedCookie = decodeURIComponent(document.cookie);
  var ca = decodedCookie.split(';');
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) == ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) == 0) {
      return c.substring(name.length, c.length);
    }
  }
  return '';
}

(function (i, s, o, g, r, a, m) {
  i['GoogleAnalyticsObject'] = r; i[r] = i[r] || function () {
    (i[r].q = i[r].q || []).push(arguments)
  }, i[r].l = 1 * new Date(); a = s.createElement(o),
    m = s.getElementsByTagName(o)[0]; a.async = 1; a.src = g; m.parentNode.insertBefore(a, m)
})(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
ga('js', new Date());
ga('create', 'G-JBQ7YTLNVC', 'auto', {
  cookieFlags: 'max-age=7200;secure;samesite=strict',
  cookieDomain: 'auto'
});
if (!(hasPath('/Departamento/Categoria/') || hasPath('/Departamento/Index/') || hasPath('/Departamento/Subcategoria/')
  || hasPath('/Loja/Index/') || hasPath('/comunicacao/detalhes/'))) {
  if (hasPath('/Catalog/Pesquisa')) {
    pageView('/Catalog/Pesquisa');
  } else if (window.location.pathname == '/') {
    pageView('/Home');
  } else {
    ga('send', 'pageview');
  }
}
