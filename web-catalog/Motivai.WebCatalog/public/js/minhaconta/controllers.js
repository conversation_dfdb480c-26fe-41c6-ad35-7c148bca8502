function initEvt(selector) {
  $(selector).on('change', function (evt, params) {
    if (params.selected == 'other') {
      $(".other").css("display", "block");
    } else {
      $(".other").css("display", "none");
    }
  });
}
function adicionaFeedback(btn) {
  $(btn).attr("disabled", "disabled");
  $(btn).addClass("processing");
  $(btn).html("Aguarde");
}
function removeLoaderFromButton(btn, text) {
  $(btn).removeClass("processing");
  $(btn).html(text);
  $(btn).removeAttr("disabled");
}
function criaDataRetrocendoDias(dias) {
  return new Date(new Date() - dias * 86400000);
}
function showInfoMessage(mensagem) {
  showSuccessSwal('Info', mensagem);
}
function showErrorMessage(mensagem) {
  showErrorSwal('Ops', mensagem);
}

var userValidationRecaptcha = null;
function loadReCaptcha() {
  userValidationRecaptcha = null;
  userValidationRecaptcha = grecaptcha.render('userValidationRecaptcha', { 'sitekey': '6Ld1yb8qAAAAABB9QlKzBXTDuMoL-Jg-N8WhPIKy' });
}

angular.module("platformApp")
  .run(['$rootScope', '$location', function ($rootScope, $location) {
    $rootScope.$on('$routeChangeSuccess', function (e, current, pre) {
      $(".active").removeClass("active");
      if ($location.path().indexOf("/pedido") > -1) {
        $("#li-pedidos").addClass("active");
      } else if ($location.path().indexOf("/endereco") > -1 || $location.path().indexOf("/editar-endereco") > -1 || $location.path().indexOf("/novo-endereco") > -1) {
        $("#li-enderecos").addClass("active");
      } else {
        $("#li-" + $location.path().replace("/", "")).addClass("active");
      }
    });
  }])
  .controller('minhaContaCtrl', ['$scope', function ($scope) { }])
  .controller('comprarPontosController', ['pointsPurchaseService', 'EnderecoApi', 'logger', '$scope', '$injector', '$timeout', '$filter', function (service, enderecoApi, logger, $scope, $injector, $timeout, $filter) {
    $scope.dirtyWithError = dirtyWithError;
    $scope.card = {};
    var mtoken = null;
    var token = null;
    $scope.showForm = true;
    $scope.showTable = false;
    $scope.loading = false;
    $scope.valorReais = 'R$ 0,00';
    $scope.formas = [];
    $scope.classeMensagem = '';
    $scope.mensagemFeedback = "";

    const $validationProvider = $injector.get('$validation');
    const currencyFilter = $filter('currency');

    const mostraErro = function (mensagem) {
      $scope.loading = false;
      showErrorSwal('Ops', mensagem);
    };

    function initCreditCardBrand() {
      $timeout(function () {
        $scope.$evalAsync(function () {
          $("input.creditCard").validateCreditCard(function (t) {
            return !t.card_type ? $(this).next().attr("class", "cardBrand") : ($(this).next().attr("class", "cardBrand"), $(this).next().addClass("icons-" + t.card_type.name));
          });
        });
      }, 10);
    }

    $scope.pesquisaCep = function (cep) {
      enderecoApi.fillAddressByCep(cep, $scope.dadosCompra.BillingAddress);
    };

    const processarCompra = function (hashToken) {

      const payload = {
        Cpf: $scope.dadosCompra.Cpf,
        Phone: $scope.dadosCompra.Phone,
        Email: $scope.dadosCompra.Email,
        CardToken: hashToken,
        Ticket: { Token: token },
        Installment: { Token: $scope.dadosCompra.installment },
        BillingAddress: $scope.dadosCompra.BillingAddress,
        LocationInfo: { timezone: moment.tz.guess() }
      };
      var formaEscolhida = $scope.formas.find(f => f.Token == $scope.dadosCompra.installment);
      if (!formaEscolhida) {
        mostraErro('Opção de pagamento inválida.');
        return;
      }
      $scope.pontosCompra.total = formaEscolhida.Count * formaEscolhida.Value;
      $scope.pontosCompra.valorUnit = $scope.pontosCompra.total / $scope.pontosCompra.pontos;
      service.comprarPontos(payload)
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          sendEvent('Comprar Pontos', 'Processado', 'Minha Conta');
          $scope.loading = false;
          if (retorno.Success) {
            $("#userBalanceSpan").html($("#cnP").val() + ' ' + currencyFilter(retorno.Return, '') + ' ' + $("#cnS").val());
            showSuccessSwal('Info', 'Compra efetuada com sucesso, por favor, feche esta janela para prosseguir com o pedido.');
            $scope.showForm = false;
            $scope.showTable = true;
          } else {
            sendEvent('Comprar Pontos', 'Error Processamento', 'Minha Conta');
            mostraErro(retorno.Error);
            logger.log(retorno.Error);
          }
        })
        .catch(function (resp) {
          mostraErro("Ocorreu um erro ao processar o pagamento. Tente novamente! Se o problema persistir, entre em contato com nossa equipe de atendimento!");
          logger.logResponse(resp);
        });
    }

    const comprarPontos = function (frmPointsPurchase) {
      angular.forEach(frmPointsPurchase.$error, function (field) {
        angular.forEach(field, function (errorField) { errorField.$setTouched(); })
      });
      $validationProvider.validate(frmPointsPurchase)
        .then(function () {
          $scope.loading = true;
          // service.enviarMundipagg(mtoken, $scope.card)
          service.enviarPagarme(mtoken, $scope.card)
            .then(function (result) {
              if (result.success) {
                processarCompra(result.token);
              } else if (result.success == false) {
                mostraErro(result.error);
              } else {
                // logger.log(token, token.status);
                mostraErro('Não foi possível inicar a compra de pontos, por favor, tente novamente.');
              }
            })
            .catch(function (resp) {
              sendEvent('Comprar Pontos', 'Error Gateway', 'Minha Conta');
              $scope.card = {};
              mostraErro("Ocorreu um erro ao processar o pagamento. Tente novamente! Se o problema persistir, entre em contato com nossa equipe de atendimento!");
              logger.logResponse(resp);
            });
        })
        .catch(function () {
          mostraErro("Preencha os campos corretamente para prosseguir com a compra.");
        });
    };

    $scope.comprar = function (frmPointsPurchase) {
      sendEvent('Comprar Pontos', 'Comprar', 'Minha Conta');
      comprarPontos(frmPointsPurchase);
    };

    const updateComboFormas = function () {
      $timeout(function () {
        $scope.$evalAsync(function () {
          $("#creditCardInstallments").trigger("chosen:updated");
        });
      }, 100);
    }

    const pesquisarOpcoes = function (value) {
      $("#pointsPrice").val(currencyFilter($scope.pontosCompra.reais, 'R$ '));
      $scope.loading = true;
      service.carregaOpcoesPagamento(value)
        .then(function (response) { return response.data; })
        .then(function (resp) {
          $scope.loading = false;
          if (resp.Success) {
            mtoken = resp.Return.Token;
            token = resp.Return.Ticket;
            $scope.formas = resp.Return.Installments;
            $scope.pontosCompra.reais = resp.Return.Total;
            $("#pointsPrice").val(currencyFilter($scope.pontosCompra.reais, 'R$ '));
            updateComboFormas();
          } else {
            logger.log(resp.Error);
            mostraErro(resp.Error);
          }
        })
        .catch(function (resp) {
          mostraErro("Ocorreu um erro ao carregar as opções de pagamento. Se o problema persistir, entre em contato com nossa equipe de atendimento!");
          logger.logResponse(resp);
        });
    };

    $scope.pesquisarOpcoes = function (value) {
      token = null;
      $scope.formas = [];
      $scope.pontosCompra = { pontos: 0, reais: 0 };
      updateComboFormas();
      var parts = value.split('-');
      if (!parts[0] || !parts[1]) {
        return;
      }
      $scope.pontosCompra = { pontos: parts[0], reais: parts[1] };
      sendEvent('Comprar Pontos', 'Opções Compra', 'Minha Conta');
      pesquisarOpcoes($scope.pontosCompra.pontos);
    };

    $scope.pesquisarOpcoesManual = function (value) {
      if (isNaN(value) || value <= 0) return;
      $scope.pontosCompra = { pontos: value, reais: 0 };
      pesquisarOpcoes(value);
    }

    $scope.initForm = function (cpf, phone, email) {
      $scope.dadosCompra = {
        Cpf: cpf,
        Phone: phone,
        Email: email,
        BillingAddress: {}
      };
      $scope.showForm = true;
      $timeout(function () {
        $scope.$evalAsync(function () {
          $.fn.initChosen('select');
          $.fn.initCreditCardBrand("#creditCardNumber");
        });
      }, 50);
      $(".pointsManual").on('blur', function (event) {
        console.log('', event);
      });
    };
  }])
  .controller('cadastroCtrl', ['MinhaContaApiService', 'SecurityApiService', 'RegistrationMfaValidationFactory', 'logger', '$scope', '$injector', '$timeout', '$q', function (minhacontaApi, securityApiService, mfaValidationFactory, logger, $scope, $injector, $timeout, $q) {
    $scope.estadosCivil = [];
    $scope.operadoras = [];
    $scope.loadingDados = false;
    $scope.dadosCadastro = { IsPessoaFisica: true };
    $scope.dirtyWithError = dirtyWithError;
    const page = "CADASTRO";

    var $validationProvider = $injector.get('$validation');

    $scope.loading = function () {
      return mfaValidationFactory.loading;
    }

    $scope.isMfaValidationStep = function () {
      return mfaValidationFactory.isMfaValidationStep;
    };

    $scope.requireMfaValidationAtRegistration = function () {
      return !mfaValidationFactory.parametrizations || mfaValidationFactory.parametrizations && mfaValidationFactory.parametrizations.requireMfaValidationAtRegistration;
    }

    $scope.disabled = function () {
      return $scope.requireMfaValidationAtRegistration() && !mfaValidationFactory.authenticated;
    }

    function initChosen(selector) {
      $(selector + "_chosen").remove();
      $timeout(function () {
        $scope.$evalAsync(function () {
          $(selector).chosen({
            disable_search_threshold: 10,
            no_results_text: 'Resultado não encontrado',
            placeholder_text_multiple: 'Selecione uma ou mais opções',
            placeholder_text_single: 'Selecione uma opção'
          });
        });
      }, 10);
    }

    const updateIeChosen = function () {
      $('#ieEstado').trigger('chosen:updated');
    };

    const mostraMensagemErroGeral = function (msg) {
      $scope.erroGeral = true;
      $scope.mensagemErroGeral = msg;
    };

    const mostraMensagemErro = function (msg) {
      $scope.classeMensagem = "errors";
      $scope.mensagemFeedback = msg;
    };

    const mostraMensagemSucesso = function (msg) {
      $scope.classeMensagem = "success";
      $scope.mensagemFeedback = msg;
    };

    const showForm = function () {
      $("#registerForm").css("display", "block");
      $.fn.initChosen('select.autostart');
      $.fn.initChosen('#estadocivil');
      $.fn.initChosen('#operadora');
      if (!$scope.IsPessoaFisica) {
        $.fn.initChosen("#ieEstado");
        updateIeChosen();
      }
    };

    const carregaEstadosCivil = function () {
      return minhacontaApi.carregaEstadosCivil()
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          if (retorno.Success) {
            $scope.estadosCivil = retorno.Return;
          } else {
            logger.log(retorno.Error);
          }
        })
        .catch(function (resp) {
          logger.logResponse(resp);
        });
    };

    const carregaOperadoras = function () {
      return minhacontaApi.carregaOperadoras()
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          if (retorno.Success) {
            $scope.operadoras = retorno.Return;
          } else {
            logger.log(retorno.Error);
          }
        })
        .catch(function (resp) {
          logger.logResponse(resp);
        });
    };

    const loadMetadataHeaders = function () {
      minhacontaApi.getMetadataHeaders()
        .then(function (response) { return response.data; })
        .then(function (metadataReturn) {
          if (metadataReturn && metadataReturn.Return && metadataReturn.Return.length) {
            $scope.metadataHeaders = metadataReturn.Return;
          } else {
            $scope.metadataHeaders = [];
          }
        })
        .catch(function (resp) {
          logger.logResponse(resp);
        });
    };

    $scope.pesquisarPessoaPeloDocumento = function () {
      return minhacontaApi.pesquisarPessoaPeloDocumento($scope.dadosCadastro.AccountRepresentative.Document)
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          if (retorno.Success) {
            $scope.dadosCadastro.AccountRepresentative = retorno.Return;
          } else {
            logger.log(retorno.Error);
          }
        })
        .catch(function (resp) {
          logger.logResponse(resp);
        });
    };

    const carregaDados = function () {
      $("#div-container").css("display", "block");
      $scope.loadingDados = true;
      minhacontaApi.carregaDadosCadastro()
        // .then(function(response) { return response.data; }) não precisa por causa do $promise
        .then(function (retorno) {
          $scope.loadingDados = false;
          if (retorno && retorno.Success) {
            $scope.dadosCadastro = retorno.Return;
            $scope.notAllowChangeDocument = $scope.dadosCadastro.Cpf || $scope.dadosCadastro.Cnpj;
            $scope.IsPessoaFisica = $scope.dadosCadastro.Type == 'Fisica';
            $scope.IsPessoaJuridica = $scope.dadosCadastro.Type == 'Juridica';
            $scope.exempt = $scope.dadosCadastro.StateInscriptionExempt;
            $scope.dadosCadastro.BirthDate = moment(new Date($scope.dadosCadastro.BirthDate));
            $timeout(function () {
              $scope.$evalAsync(function () {
                showForm();
              });
            }, 50);
          } else if (retorno && !retorno.Success) {
            mostraMensagemErroGeral(retorno.Error);
            logger.log(retorno.Error);
          } else {
            mostraMensagemErroGeral("Não foi possível carregar os dados do cadastro, por favor, tente novamente.");
          }
        })
        .catch(function (response) {
          mostraMensagemErroGeral("Ocorreu um erro durante o carregamento dos dados, por favor, tente novamente.");
          $scope.loadingDados = false;
          logger.logResponse(response);
        });
    };

    $scope.onExemptChange = function () {
      $scope.exempt = !$scope.exempt;
      $timeout(function () {
        $scope.$evalAsync(function () {
          updateIeChosen();
        });
      }, 50);
    };

    $scope.salvarDados = function (registerForm) {
      $scope.classeMensagem = null;
      $scope.mensagemFeedback = null;
      $validationProvider.validate(registerForm)
        .then(function () {
          const dadosCadastro = $scope.dadosCadastro;
          dadosCadastro.locationInfo = {
            timezone: moment.tz.guess()
          }
          const btn = $("button[type=submit]");
          adicionaFeedback(btn);
          minhacontaApi.salvaDadosCadastro(dadosCadastro)
            .then(function (retorno) {
              if (retorno.Success) {
                mostraMensagemSucesso("Seus dados foram atualizados com sucesso!");
                if (retorno.HasRedirect) {
                  $window.location.href = retorno.Redirect;
                }
              } else {
                mostraMensagemErro(retorno.Error);
              }
              removeLoaderFromButton(btn, "Confirmar dados");
            })
            .catch(function (response) {
              removeLoaderFromButton(btn, "Confirmar dados");
              mostraMensagemErro("Ocorreu um erro ao atualizar os dados, por favor, tente novamente.");
              logger.logResponse(response);
            });
        })
        .catch(function () {
          mostraMensagemErro("Preencha os campos corretamente para prosseguir.");
        });
    };

    const validateAuthenticated = function () {
      if (mfaValidationFactory.authenticated && mfaValidationFactory.page != page) {
        mfaValidationFactory.authenticated = false;
        mfaValidationFactory.page = page;
      }
    }

    const getAuthenticationMfaParameters = function () {
      mfaValidationFactory.loading = true;
      securityApiService.getAuthenticationMfaParameters()
        .then(function (response) { return response.data; })
        .then(function (response) {
          validateAuthenticated();
          mfaValidationFactory.isMfaValidationStep = false;
          mfaValidationFactory.page = page;
          if (!response) {
            showErrorMessage('Ocorreu um erro durante a consulta das configurações da campanha, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          }

          if (response.Success) {
            mfaValidationFactory.parametrizations = response.Return;
          } else {
            showErrorMessage(response.Error);
          }
          mfaValidationFactory.loading = false;
        })
        .catch(function (response) {
          mfaValidationFactory.loading = false;
          showErrorMessage('Ocorreu um erro durante a consulta das configurações da campanha, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          logger.logResponse(response);
        });
    }

    $q.all([
      carregaEstadosCivil(),
      carregaOperadoras()
    ]).then(function (values) {
      carregaDados();
    });

    $scope.init = function (loadMetadata) {
      if (loadMetadata == true) {
        loadMetadataHeaders();
      }
    };

    getAuthenticationMfaParameters();

  }])
  .controller('enderecosCtrl', ['MeusEnderecosApiService', 'SecurityApiService', 'RegistrationMfaValidationFactory', 'MinhaContaApiService', 'logger', '$scope', function (enderecosApi, securityApiService, mfaValidationFactory, minhacontaApi, logger, $scope) {
    $scope.enderecos = [];
    const page = "ENDERECOS";

    $scope.loading = function () {
      return mfaValidationFactory.loading;
    }

    $scope.isMfaValidationStep = function () {
      return mfaValidationFactory.isMfaValidationStep;
    };

    $scope.requireMfaValidationAtRegistration = function () {
      return !mfaValidationFactory.parametrizations || mfaValidationFactory.parametrizations && mfaValidationFactory.parametrizations.requireMfaValidationAtRegistration;
    }

    $scope.disabled = function () {
      return $scope.requireMfaValidationAtRegistration() && !mfaValidationFactory.authenticated;
    }

    const mostraMensagemErroGeral = function (msg) {
      $scope.erroGeral = true;
      $scope.mensagemErroGeral = msg;
    };

    const mostraMensagemErro = function (msg) {
      $scope.classeMensagem = "errors";
      $scope.mensagemFeedback = msg;
    };

    const mostraMensagemSucesso = function (msg) {
      $scope.classeMensagem = "success";
      $scope.mensagemFeedback = msg;
    };

    const validateAuthenticated = function () {
      if (mfaValidationFactory.authenticated && mfaValidationFactory.page != page) {
        mfaValidationFactory.authenticated = false;
        mfaValidationFactory.page = page;
      }
    }

    const carregaEnderecos = function () {
      $scope.loadingEnderecos = true;

      enderecosApi.carregaEnderecos()
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          $scope.loadingEnderecos = false;
          if (retorno.Success) {
            $scope.enderecos = retorno.Return;
          } else if (retorno && retorno.Success == false) {
            mostraMensagemErroGeral(retorno.Error);
            logger.log(retorno.Error);
          } else {
            mostraMensagemErroGeral("Ocorreu um erro ao carregar os endereços de entrega, por favor, tente novamente.");
          }
        })
        .catch(function (response) {
          $scope.loadingEnderecos = false;
          mostraMensagemErroGeral("Ocorreu um erro ao carregar os endereços de entrega, por favor, tente novamente.");
          logger.logResponse(response);
        });
    };

    $scope.apagaEndereco = function (enderecoId) {
      if (confirm("Deseja excluir o endereço?")) {
        enderecosApi.excluiEndereco(enderecoId)
          // .then(function(response) { return response.data; }) não precisa por causa do $promise
          .then(function (retorno) {
            if (retorno.Success) {
              mostraMensagemSucesso("Endereço excluído com sucesso!");
              carregaEnderecos();
            } else {
              mostraMensagemErro(retorno.Error);
              logger.log(retorno.Error);
            }
          }).catch(function (response) {
            mostraMensagemErro("Ocorreu um erro durante a exclusão do endereço, por favor, tente novamente.");
            logger.logResponse(response);
          });
      }
    };

    const getAuthenticationMfaParameters = function () {
      mfaValidationFactory.loading = true;
      securityApiService.getAuthenticationMfaParameters()
        .then(function (response) { return response.data; })
        .then(function (response) {
          validateAuthenticated();
          mfaValidationFactory.isMfaValidationStep = false;
          mfaValidationFactory.page = page;
          if (!response) {
            showErrorMessage('Ocorreu um erro durante a consulta das configurações da campanha, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          }

          if (response.Success) {
            mfaValidationFactory.parametrizations = response.Return;
          } else {
            showErrorMessage(response.Error);
          }
          mfaValidationFactory.loading = false;
        })
        .catch(function (response) {
          mfaValidationFactory.loading = false;
          showErrorMessage('Ocorreu um erro durante a consulta das configurações da campanha, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          logger.logResponse(response);
        });
    }

    carregaEnderecos();
    getAuthenticationMfaParameters();
  }])
  .controller('edicaoEnderecoCtrl', ['MinhaContaApiService', 'MeusEnderecosApiService', 'EnderecoApi', 'logger', '$scope', '$routeParams', '$injector',
    function (minhaContaApiService, enderecosApi, enderecoApi, logger, $scope, $routeParams, $injector) {
      var endereco = {};
      var edicaoEndereco = !!$routeParams.enderecoId;
      var enderecoId = $routeParams.enderecoId;
      var $validationProvider = $injector.get('$validation');
      $scope.desabilitarEdicao = true;

      const mostraMensagemErro = function (msg) {
        $scope.classeMensagem = "errors";
        $scope.mensagemFeedback = msg;
      }

      const mostraMensagemSucesso = function (msg) {
        $scope.classeMensagem = "success";
        $scope.mensagemFeedback = msg;
      }

      const carregaEndereco = function (enderecoId) {
        $scope.loading = true;
        enderecosApi.carregaEndereco(enderecoId)
          // .then(function(response) { return response.data; }) não precisa por causa do $promise
          .then(function (retorno) {
            if (retorno.Success) {
              if (retorno.Return.constructor == Array)
                $scope.endereco = retorno.Return[0];
              else
                $scope.endereco = retorno.Return;
            } else {
              logger.log(retorno.Error);
            }
            $scope.loading = false;
          })
          .catch(function (response) {
            mostraMensagemErro("Ocorreu um erro ao carregar o endereço, por favor, tente novamente.");
            logger.logResponse(response);
            $scope.loading = false;
          });
      };

      const carregaDadosParticipante = function () {
        $scope.loading = true;
        minhaContaApiService.getPrincipalContact()
          .then(function (result) { return result.data; })
          .then(function (response) {
            if (response && response.Return) {
              let phone = '';
              if (response.Return.mobilePhone) {
                phone = response.Return.mobilePhone.replace(/\D+/g, '')
              }
              $scope.endereco.Receiver = {
                Name: response.Return.name,
                Cpf: response.Return.document,
                Email: response.Return.email,
                Cellphone: phone
              };
            }
            $scope.loading = false;
          });
      };

      $scope.salvaEndereco = function (endereco) {
        $validationProvider.validate($scope.enderecoForm)
          .then(function () {
            endereco.locationInfo = {
              timezone: moment.tz.guess()
            }
            var btn = $("button[type=submit]");
            adicionaFeedback(btn);
            (edicaoEndereco ? enderecosApi.atualizaEndereco(endereco) : enderecosApi.cadastraEndereco(endereco))
              // .then(function(response) { return response.data; }) não precisa por causa do $promise
              .then(function (retorno) {
                if (retorno.Success) {
                  mostraMensagemSucesso("Endereço salvo com sucesso!");
                  if (!edicaoEndereco) {
                    enderecoId = retorno.Return;
                    endereco.id = retorno.Return;
                    edicaoEndereco = true;
                  }
                } else {
                  mostraMensagemErro(retorno.Error);
                  logger.log(retorno.Error);
                }
                removeLoaderFromButton(btn, "Confirmar dados");
              })
              .catch(function (resp) {
                removeLoaderFromButton(btn, "Confirmar dados");
                mostraMensagemErro("Ocorreu um erro durante a atualização do endereço, por favor, tente novamente.");
                logger.logResponse(resp);
              });
          })
          .catch(function () {
            mostraMensagemErro("Alguns campos obrigatórios estão inválido!");
          });
      };

      $scope.pesquisaCep = function (cep, endereco) {
        enderecoApi.fillAddressByCep(cep, endereco);
      };

      $scope.init = function () {
        $scope.endereco = {};
        // setTimeout(function(){ $.fn.initChosen("#estado"); }, 250);
        if (edicaoEndereco) {
          carregaEndereco(enderecoId);
        } else {
          carregaDadosParticipante();
        }
      };
    }])
  .controller('resumoCtrl', ['MinhaContaApiService', 'logger', '$scope', '$filter', function (service, logger, $scope, $filter) {
    $scope.resumo = null;
    $scope.acumulos = { ultimos: [] };
    $scope.resgates = { ultimos: [] };

    const carregaResumo = function () {
      $scope.loadingSaldo = true;

      service.consultaSaldoDisponivel()
        .then(function (response) { return response.data; })
        .then(function (consultaResponse) {
          $scope.loadingSaldo = false;
          if (consultaResponse.Success) {
            $scope.resumo = consultaResponse.Return;
            var currencyFilter = $filter('currency');
            var coinPrefix = '', coinSufix = '';
            if ($scope.resumo.CoinName) {
              if ($scope.resumo.CoinName.Prefix)
                coinPrefix = $scope.resumo.CoinName.Prefix;
              if ($scope.resumo.CoinName.Sufix)
                coinSufix = $scope.resumo.CoinName.Sufix;
            }
            $("#userBalanceSpan").html(coinPrefix + ' ' + currencyFilter($scope.resumo.AvailableBalance, '') + ' ' + coinSufix);
          } else {
            logger.log(consultaResponse.Error);
            $scope.resumo = {};
            $scope.resumo.ocorreuErro = true;
            if (consultaResponse.Error)
              $scope.resumo.ErrorMessage = consultaResponse.Error;
            else
              $scope.resumo.descricaoErro = "Ocorreu um erro durante a consulta do saldo disponível, por favor, tente novamente.";
          }
        })
        .catch(function (resp) {
          logger.logResponse(resp);
          $scope.loadingSaldo = false;
          $scope.resumo = {};
          $scope.resumo.descricaoErro = "Ocorreu um erro durante a consulta do saldo disponível, por favor, tente novamente.";
        });
    };

    const carregaUltimosAcumulos = function () {
      $scope.loadingAcumulos = true;

      service.carregaUltimosAcumulos()
        .then(function (response) { return response.data; })
        .then(function (acumulosResp) {
          $scope.loadingAcumulos = false;

          if (acumulosResp.Success) {
            $scope.acumulos.ultimos = acumulosResp.Return;
          } else {
            logger.log(acumulosResp.Error);
            $scope.acumulos.ocorreuErro = true;
            if (acumulosResp.Error)
              $scope.acumulos.ErrorMessage = acumulosResp.Error;
            else
              $scope.acumulos.ErrorMessage = "Ocorreu um erro ao carregar os últimos acúmulos, por favor, tente novamente."
          }
        })
        .catch(function (resp) {
          logger.logResponse(resp);
          $scope.loadingAcumulos = false;
          $scope.acumulos.ocorreuErro = true;
          $scope.acumulos.ErrorMessage = "Ocorreu um erro ao carregar os últimos acúmulos, por favor, tente novamente."
        });
    };

    const carregaUltimosResgates = function () {
      $scope.loadingResgates = true;

      service.carregaUltimosResgates()
        .then(function (response) { return response.data; })
        .then(function (resgatesResp) {
          $scope.loadingResgates = false;

          if (resgatesResp.Success) {
            $scope.resgates.ultimos = resgatesResp.Return;
          } else {
            logger.log(resgatesResp.Error);
            $scope.resgates.ocorreuErro = true;
            if (resgatesResp.Error)
              $scope.resgates.ErrorMessage = resgatesResp.Error;
            else
              $scope.resgates.ErrorMessage = "Ocorreu um erro ao carregar os últimos resgates, por favor, tente novamente."
          }
        })
        .catch(function (resp) {
          logger.logResponse(resp);
          $scope.loadingResgates = false;
          $scope.resgates.ocorreuErro = true;
          $scope.resgates.ErrorMessage = "Ocorreu um erro ao carregar os últimos acúmulos, por favor, tente novamente."
        });
    };

    $scope.init = function () {
      carregaResumo();
      carregaUltimosAcumulos();
      carregaUltimosResgates();
    };
  }])
  .controller('pontosExpirarCtrl', ['MinhaContaApiService', 'logger', '$scope', function (service, logger, $scope) {
    $scope.pontosExpirar = null;
    $scope.totalPontosExpirar = 0;
    $scope.ocorreuErro = false;
    $scope.moeda = '';

    const carregaPontos = function () {
      $scope.loadingPontos = true;
      service.carregaPontosExpirar()
        .then(function (response) { return response.data; })
        .then(function (pontosResp) {
          $scope.loadingPontos = false;
          if (pontosResp.Success) {
            $scope.ocorreuErro = false;
            if (pontosResp.Return) {
              $scope.moeda = pontosResp.Return.CoinName;
              $scope.pontosExpirar = pontosResp.Return.ExpiringPoints;
              $scope.totalPontosExpirar = pontosResp.Return.TotalPoints;
            } else {
              $scope.pontosExpirar = [];
              $scope.totalPontosExpirar = 0;
            }
          } else {
            logger.log(pontosResp.Error);
            $scope.ocorreuErro = true;
            if (pontosResp.Error)
              $scope.ErrorMessage = pontosResp.Error;
            else
              $scope.ErrorMessage = "Ocorreu um erro ao carregar os pontos, por favor, tente novamente."
          }
        })
        .catch(function (resp) {
          logger.logResponse(resp);
          $scope.loadingPontos = false;
          $scope.ocorreuErro = true;
          $scope.ErrorMessage = "Ocorreu um erro ao carregar os pontos, por favor, tente novamente."
        });
    };

    $scope.init = function () {
      carregaPontos();
    };
  }])
  .controller('pontosBloqueadosCtrl', ['MinhaContaApiService', 'logger', '$scope', function (service, logger, $scope) {
    $scope.pontosBloqueados = null;
    $scope.totalPontosBloqueados = 0;
    $scope.ocorreuErro = false;
    $scope.moeda = "";

    const carregaPontos = function () {
      $scope.loadingPontos = true;

      service.carregaPontosBloqueados()
        .then(function (response) { return response.data; })
        .then(function (pontosResp) {
          $scope.loadingPontos = false;

          if (pontosResp.Success) {
            $scope.ocorreuErro = false;
            if (pontosResp.Return) {
              $scope.moeda = pontosResp.Return.CoinName;
              $scope.pontosBloqueados = pontosResp.Return.BlockedPoints;
              $scope.totalPontosBloqueados = pontosResp.Return.TotalPoints;
            } else {
              $scope.pontosBloqueados = [];
              $scope.totalPontosBloqueados = 0;
            }
          } else {
            logger.log(pontosResp.Error);
            $scope.ocorreuErro = true;
            if (pontosResp.Error)
              $scope.ErrorMessage = pontosResp.Error;
            else
              $scope.ErrorMessage = "Ocorreu um erro ao carregar os pontos, por favor, tente novamente."
          }
        })
        .catch(function (resp) {
          logger.logResponse(resp);
          $scope.loadingPontos = false;
          $scope.ocorreuErro = true;
          $scope.ErrorMessage = "Ocorreu um erro ao carregar os pontos, por favor, tente novamente."
        });
    };

    $scope.init = function () {
      carregaPontos();
    };
  }])
  .controller('extratoCtrl', ['MinhaContaApiService', 'logger', '$scope', '$timeout', function (service, logger, $scope, $timeout) {
    $scope.extrato = null;
    $scope.saldoPeriodo = 0;
    $scope.moeda = '';
    // filtros
    $scope.dataInicialRange = null;
    $scope.dataFinalRange = null;
    $scope.filtros = {
      tipoExtrato: null,
      dataInicial: null,
      dataFinal: null
    };

    $scope.headers = [];
    $scope.body = [];
    $scope.headerDetails = [];

    const carregaParametrizacaoMetadata = function () {
      $scope.loadingParametrizacaoMetadata = true;
      $scope.ocorreuErro = false;
      service.carregaParametrizacaoMetadata()
        .then(function (response) {
          return response.data;
        })
        .then(function (parametrizacaoMetadataResp) {
          if (parametrizacaoMetadataResp.Success) {
            if (parametrizacaoMetadataResp.Return) {
              $scope.headerDetails = parametrizacaoMetadataResp.Return.headersDetails;
              $scope.metadataOrientationType = parametrizacaoMetadataResp.Return.displayOrientation || 'HORIZONTAL';
            } else {
              $scope.headerDetails = [];
              $scope.metadataOrientationType = 'HORIZONTAL';
            }
            $scope.loadingParametrizacaoMetadata = false;
          } else {
            $scope.ocorreuErro = true;
            $scope.loadingParametrizacaoMetadata = false;
            logger.log(extratoResp.Error);
            if (extratoResp.Error)
              $scope.ErrorMessage = extratoResp.Error;
            else
              $scope.ErrorMessage = "Ocorreu um erro ao carregar as configurações de detalhes do extrato, por favor, tente novamente."
          }
        }).catch(function (resp) {
          $scope.ocorreuErro = true;
          $scope.loadingParametrizacaoMetadata = false;
          $scope.ErrorMessage = "Ocorreu um erro ao carregar as configurações de detalhes do extrato, por favor, tente novamente."
          logger.logResponse(resp);
        });
    }

    const carregaExtrato = function () {
      $scope.ocorreuErro = false;
      $scope.ErrorMessage = null;
      $scope.loadingExtrato = true;

      service.carregaExtrato($scope.filtros.tipoExtrato, $scope.filtros.dataInicial, $scope.filtros.dataFinal)
        .then(function (response) { return response.data; })
        .then(function (extratoResp) {
          $scope.loadingExtrato = false;
          if (extratoResp.Success) {
            $scope.ocorreuErro = false;
            if (extratoResp.Return) {
              $scope.extrato = extratoResp.Return.Transactions;
              if ($scope.extrato && $scope.extrato.length > 0) {
                carregaParametrizacaoMetadata();
              }
              $scope.saldoPeriodo = extratoResp.Return.TotalAmount;
              $scope.moeda = extratoResp.Return.CoinName;
            } else {
              $scope.extrato = [];
              $scope.saldoPeriodo = 0;
            }
          } else {
            $scope.ocorreuErro = true;
            logger.log(extratoResp.Error);
            if (extratoResp.Error)
              $scope.ErrorMessage = extratoResp.Error;
            else
              $scope.ErrorMessage = "Ocorreu um erro ao carregar o extrato, por favor, tente novamente."
          }
        })
        .catch(function (resp) {
          $scope.loadingExtrato = false;
          $scope.ocorreuErro = true;
          $scope.ErrorMessage = "Ocorreu um erro ao carregar o extrato, por favor, tente novamente."
          logger.logResponse(resp);
        });
    };


    $scope.filtra = function () {
      var opcPeriodo = $("#periodo").val();
      var dias = parseInt(opcPeriodo);
      if (dias && dias > 0) {
        $scope.filtros.dataInicial = criaDataRetrocendoDias(dias);
        $scope.filtros.dataFinal = new Date();
      } else if (opcPeriodo === "other") {
        if ($scope.dataInicialRange)
          $scope.filtros.dataInicial = $scope.dataInicialRange.format('YYYY-MM-DD');
        if ($scope.dataFinalRange)
          $scope.filtros.dataFinal = $scope.dataFinalRange.format('YYYY-MM-DD');
      } else {
        $scope.filtros.dataInicial = null;
        $scope.filtros.dataFinal = null;
        /*
        try {
          $scope.filtros.dataInicial = new Date($("#dataInicial").val() + "T03:00:00.000");
          $scope.filtros.dataFinal = new Date($("#dataFinal").val() + "T03:00:00.000");
        } catch(ex) {
          $scope.ocorreuErro = true;
          $scope.ErrorMessage = "Selecione a data inicial e final do período.";
        }
        */
      }
      if ($scope.filtros.situacao == "")
        $scope.filtros.situacao = null;
      carregaExtrato();
    };

    $scope.showDetails = function (extraData) {

      $scope.headers = [];
      $scope.body = [];

      $scope.extraData = {
        headers: extraData.headers,
        body: extraData.body
      };

      $scope.extraData.body.forEach(b => {
        if (b.property) {
          const header = $scope.headerDetails.find(h => h.property === b.property);
          if (header) {
            $scope.headers.push(header.description);
            $scope.body.push({
              key: header.description,
              value: b.value
            });
          }
        } else {
          $scope.headers.push(b.header);
          $scope.body.push({
            key: b.header,
            value: b.value
          });
        }
      });

      showExtradataDetails();

    };

    const showExtradataDetails = function () {
      $timeout(function () {
        $scope.$evalAsync(function () {
          $.fancybox.open('#modal-extradata');
        });
      }, 50);
    }


    $scope.init = function () {
      $.fn.initChosen("select.autostart")
      initEvt("#periodo");
      carregaExtrato();
    };
  }])
  .controller('pedidosCtrl', ['MinhaContaApiService', '$scope', function (service, $scope) {
    $scope.pedidos = null;
    // filtros
    var dataInicial = null, dataFinal = null, situacao = null;

    const carregaPedidos = function () {
      $scope.loadingPedidos = true;
      $scope.ocorreuErro = false;
      $scope.ErrorMessage = null;
      service.carregaPedidos(dataInicial, dataFinal, situacao)
        .then(function (response) { return response.data; })
        .then(function (pedidosResp) {
          $scope.loadingPedidos = false;
          if (pedidosResp.Success) {
            $scope.ocorreuErro = false;
            if (pedidosResp.Return) {
              $scope.pedidos = pedidosResp.Return;

              for (var i in $scope.pedidos) {
                const pedido = $scope.pedidos[i];
                if (pedido.OrderType == 'CARD_ORDER') {
                  pedido.descricaoTipo = 'Cartão Pré-pago';
                } else if (pedido.OrderType == 'CASHBACK_ORDER') {
                  pedido.descricaoTipo = 'Transferência Cashback';
                } else if (pedido.OrderType == 'BILL_PAYMENT_ORDER') {
                  pedido.descricaoTipo = 'Pague Contas';
                } else if (pedido.OrderType == 'RECHARGE_ORDER') {
                  pedido.descricaoTipo = 'Recarga';
                } else {
                  pedido.descricaoTipo = 'Produtos';
                }
              }
            } else {
              $scope.pedidos = [];
            }
          } else {
            $scope.ocorreuErro = true;
            if (pedidosResp.Error)
              $scope.ErrorMessage = pedidosResp.Error;
            else
              $scope.ErrorMessage = "Ocorreu um erro ao carregar os pedidos, por favor, tente novamente."
          }
        })
        .catch(function (resp) {
          $scope.loadingPedidos = false;
          $scope.ocorreuErro = true;
          $scope.ErrorMessage = "Ocorreu um erro ao carregar os pedidos, por favor, tente novamente."
        });
    };

    $scope.filtra = function () {
      tipoExtrato = $("#tipoTransacao").val();
      var opcPeriodo = $("#periodo").val();

      dataInicial = null;
      dataFinal = null;
      situacao = $("#status").val();

      var dias = parseInt(opcPeriodo);

      if (dias && dias > 0) {
        dataInicial = criaDataRetrocendoDias(dias);
        dataFinal = new Date();
      } else if (opcPeriodo === "other") {
        try {
          dataInicial = moment($scope.dataInicial).format("YYYY-MM-DD");
          dataFinal = moment($scope.dataFinal).format("YYYY-MM-DD");
        } catch (ex) {
          $scope.ocorreuErro = true;
          $scope.ErrorMessage = "Selecione a data inicial e final do período.";
        }
      }
      if (situacao == "")
        situacao = null;
      carregaPedidos();
    };

    $scope.init = function () {
      $.fn.initChosen("select.autostart");
      initEvt("#periodo");

      carregaPedidos();
    };
  }])
  .controller('alterarSenhaCtrl', ['MinhaContaApiService', "SecurityApiService", "RegistrationMfaValidationFactory", 'logger', '$scope', function (service, securityApiService, mfaValidationFactory, logger, $scope) {
    $scope.senha = {};
    const page = "SENHA";

    $scope.loading = function () {
      return mfaValidationFactory.loading;
    }

    $scope.isMfaValidationStep = function () {
      return mfaValidationFactory.isMfaValidationStep;
    };

    $scope.requireMfaValidationAtRegistration = function () {
      return !mfaValidationFactory.parametrizations || mfaValidationFactory.parametrizations && mfaValidationFactory.parametrizations.requireMfaValidationAtRegistration;
    }

    $scope.disabled = function () {
      return $scope.requireMfaValidationAtRegistration() && !mfaValidationFactory.authenticated;
    }

    const validateAuthenticated = function () {
      if (mfaValidationFactory.authenticated && mfaValidationFactory.page != page) {
        mfaValidationFactory.authenticated = false;
        mfaValidationFactory.page = page;
      }
    }

    const redirectIfParticipantMigrated = () => {
      service.carregaParticipanteMigrado()
        .then(function (response) {
          if (response && response.data) {
            location.href = "/home";
          }
        });
    }

    const getAuthenticationMfaParameters = function () {
      mfaValidationFactory.loading = true;
      securityApiService.getAuthenticationMfaParameters()
        .then(function (response) { return response.data; })
        .then(function (response) {
          validateAuthenticated();
          mfaValidationFactory.isMfaValidationStep = false;
          mfaValidationFactory.page = page;
          if (!response) {
            showErrorMessage('Ocorreu um erro durante a consulta das configurações da campanha, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          }

          if (response.Success) {
            mfaValidationFactory.parametrizations = response.Return;
          } else {
            showErrorMessage(response.Error);
          }
          mfaValidationFactory.loading = false;
        })
        .catch(function (response) {
          mfaValidationFactory.loading = false;
          showErrorMessage('Ocorreu um erro durante a consulta das configurações da campanha, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
          logger.logResponse(response);
        });
    }

    $scope.validatePassword = function () {
      var regex = new RegExp("^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])");

      if ($scope.senha.novaSenha && $scope.senha.novaSenha.length < 6) {
        $scope.newPasswordInvalid = true;
        $scope.passwordChangeErrorMessage = "A senha deve conter pelo menos 6 caracteres";
        return;
      }

      if (!regex.test($scope.senha.novaSenha)) {
        $scope.newPasswordInvalid = true;
        $scope.passwordChangeErrorMessage = "A senha deve ter ao menos 1 letra maiúscula, 1 letra minúscula e 1 número";
        return;
      }

      $scope.newPasswordInvalid = false;
      $scope.passwordChangeErrorMessage = null;

      $scope.validarConfirmarSenha();
    };

    $scope.validarConfirmarSenha = function () {
      if ($scope.senha.novaSenha && $scope.senha.confirmaSenha) {
        if ($scope.senha.novaSenha != $scope.senha.confirmaSenha) {
          $scope.passwordConfirmInvalid = true;
          $scope.confirmPasswordErrorMessage = "O confirmar senha deve ser igual à nova senha";
          return;
        }
      }

      $scope.passwordConfirmInvalid = false;
      $scope.confirmPasswordErrorMessage = null;
    };

    $scope.alterarSenha = function (senha) {
      if (senha.novaSenha != senha.confirmaSenha) {
        showErrorSwal('Ops', "Senhas não conferem.");
        return;
      }
      $scope.loadingSenha = true;
      service.alterarSenha(senha)
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          $scope.loadingSenha = false;
          if (retorno.Success) {
            showSuccessSwal('Info', 'Senha atualizada com sucesso.');
          } else {
            showErrorSwal('Ops', retorno.Error);
          }
        })
        .catch(function (resp) {
          $scope.loadingSenha = false;
          showErrorSwal('Ops', 'Ocorreu um erro ao atualizar a senha. Tente novamente. Caso o problema persista entre em contato com nossa equipe de atendimento!');
          logger.logResponse(resp);
        });
    };
    redirectIfParticipantMigrated();
    getAuthenticationMfaParameters();
  }])
  .controller('detalhesPedidoCtrl', ['MeusPedidosApiService', '$scope', '$routeParams', '$timeout', function (service, $scope, $routeParams, $timeout) {
    $scope.pedido = {};
    $scope.eventos = null;
    $scope.sending = false;
    var p = $routeParams.pedido;

    const loadOrder = function () {
      $scope.loading = true;
      $scope.temValesVirtuais = false;

      service.loadOrder('PRODUCT_ORDER', p)
        // .then(function(response) { return response.data; }) não precisa por causa do $promise
        .then(function (pedidoResp) {
          $scope.loading = false;
          if (pedidoResp && pedidoResp.Success) {
            $scope.pedido = pedidoResp.Return;
            if ($scope.pedido && $scope.pedido.VouchersItems && $scope.pedido.VouchersItems.length > 0) {
              $scope.temValesVirtuais = $scope.pedido.VouchersItems.findIndex(v => v.TipoProduto == 'ValeVirtual') > -1;
            }
          } else if (pedidoResp && !pedidoResp.Success) {
            showErrorMessage(pedidoResp.Error);
          } else {
            showErrorMessage("Não foi possível carregar o pedido, por favor, tente novamente.");
          }
        })
        .catch(function (resp) {
          $scope.loading = false;
          showErrorMessage("Ocorreu um erro ao carregar o pedido.");
        });
    };

    const showVoucher = function (voucherItem) {
      if (!voucherItem || !voucherItem.VoucherCode) {
        $scope.voucher = null;
        return;
      }
      $scope.voucher = voucherItem;
      $timeout(function () {
        $scope.$evalAsync(function () {
          $.fancybox.open('#modal-voucher');
        });
      }, 50);
    };

    $scope.showVoucherCode = function (item) {
      showVoucher(item);
    };

    $scope.showTracking = function (item) {
      $scope.previsaoEntrega = item.EstimatedDeliveryDays;
      if (item.EstimatedDeliveryDate) {
        $scope.dataPrevisaoEntrega = moment.utc(item.EstimatedDeliveryDate).local().format();
      } else {
        $scope.dataPrevisaoEntrega = null;
      }
      $scope.codigoRastreio = item.TrackingCode;
      $scope.linkRastreio = item.TrackingLink;
      if (item.TrackingEvents) {
        for (var id in item.TrackingEvents) {
          if (item.TrackingEvents[id].date) {
            item.TrackingEvents[id].date = moment.utc(item.TrackingEvents[id].date).local().format();
          }
        }

        const uniqueEvent = [];
        for (rastreio of item.TrackingEvents) {
          if (!uniqueEvent.find(e => e.code == rastreio.code)) {
            uniqueEvent.push(rastreio);
          }
        }

        $scope.eventos = uniqueEvent;
      } else {
        $scope.eventos = null;
      }
      $timeout(function () {
        $scope.$evalAsync(function () {
          $.fancybox.open('#modal-tracking');
        });
      }, 50);
    }

    $scope.approve = function () {
      if (!confirm('Deseja aprovar o pedido?')) {
        return;
      }
      $scope.loading = true;
      service.approveOrder(p)
        .then(function (response) { return response.data; })
        .then(function (actionResult) {
          $scope.loading = false;
          if (actionResult.Success) {
            showInfoMessage('Pedido aprovado com sucesso.');
            loadOrder();
          } else {
            showErrorMessage(actionResult.Error);
          }
        })
        .catch(function (err) {
          $scope.loading = false;
          showErrorMessage("Ocorreu um erro ao aprovar o pedido, por favor, tente novamente.");
        });
    };

    $scope.refuse = function () {
      if (!confirm('Deseja reprovar o pedido?')) {
        return;
      }
      $scope.loading = true;
      service.refuseOrder(p)
        .then(function (response) { return response.data; })
        .then(function (actionResult) {
          $scope.loading = false;
          if (actionResult.Success) {
            showInfoMessage('Pedido reprovado com sucesso.');
            loadOrder();
          } else {
            showErrorMessage(actionResult.Error);
          }
        })
        .catch(function (err) {
          $scope.loading = false;
          showErrorMessage("Ocorreu um erro ao reprovar o pedido, por favor, tente novamente.");
        });
    };

    $scope.isLink = function (link) {
      try {
        const isValid = new URL(link);
        return isValid.protocol === 'http:' || isValid.protocol === 'https:';
      } catch (err) {
        return false;
      }
    }

    $scope.showDecriptedCode = function (link) {
      $scope.faEyeClass = 'fa fa-eye';
      $scope.voucherLink = link;
    };

    $scope.showEncriptedCode = function (link) {
      $scope.faEyeClass = 'fa fa-eye-slash';
      $scope.voucherLink = "*".repeat(link.length);
    };

    $scope.disabled = function (item, vouchers) {
      if (!item) {
        return true;
      }
      if (item.PartnerName == 'Directshop') {
        return false;
      }
      if (!vouchers || !vouchers.length) {
        return true;
      }

      const voucher = vouchers.find(v => v.SkuCode == item.SkuCode);
      if (!voucher) {
        return true;
      }

      return voucher.Status && voucher.Status != "ACTIVE" && voucher.Status != "REDEEMED";
    }

    $scope.consultVouchersLink = function (item) {
      if (!item || !item.PartnerId || !item.PartnerId.length) {
        showInfoMessage('Nenhum vale virtual no pedido.');
        return;
      }

      $scope.loading = true;
      $scope.vouchersLink = null;
      service.consultVouchersLink(p, item.PartnerId)
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          $scope.loading = false;
          if (retorno.Success) {
            $scope.vouchersPartnerName = item.PartnerName;
            $scope.vouchersLink = retorno.Return;
            $.fancybox.open('#modal-vouchers-link');
          } else {
            showErrorMessage(retorno.Error);
          }
        })
        .catch(function (err) {
          $scope.loading = false;
          showErrorMessage("Ocorreu um erro ao consultar o link dos vales, por favor, tente novamente mais tarde.");
        });
    }

    $scope.init = function () {
      loadOrder();

      $.fn.initScrollbar(".scroll");
      $("#btn-show-tracking").fancybox({
        scrolling: 'no',
        titleShow: false,
        onClosed: function () {
          $("#modal-tracking").hide();
        }
      });

      $("#btn-consult-vouchers-link").fancybox({
        scrolling: 'no',
        titleShow: false,
        onClosed: function () {
          $("#modal-vouchers-link").hide();
        }
      });
    };
  }])
  .controller('detalhesPedidoCartaoCtrl', ['MeusPedidosApiService', '$scope', '$routeParams', function (service, $scope, $routeParams) {
    $scope.cardOrder = {};
    var p = $routeParams.pedido;

    const handleStatus = function (cardOrder) {
      if (cardOrder.resumedStatus == 'Processing') {
        cardOrder.resumedStatus = 'Em Andamento';
      } else if (cardOrder.resumedStatus == 'Canceled') {
        cardOrder.resumedStatus = 'Cancelado';
      } else if (cardOrder.resumedStatus == 'Error') {
        cardOrder.resumedStatus = 'Não Aprovado';
      } else if (cardOrder.resumedStatus == 'Shipping') {
        cardOrder.resumedStatus = 'Em Entrega';
      } else if (cardOrder.resumedStatus == 'Delivered') {
        cardOrder.resumedStatus = 'Entregue';
      } else {
        cardOrder.resumedStatus = 'Recebido';
      }
    };

    const loadOrder = function () {
      $scope.loading = true;
      service.loadOrder('CARD_ORDER', p)
        // .then(function(response) { return response.data; }) não precisa por causa do $promise
        .then(function (response) {
          $scope.loading = false;
          if (response && response.Success) {
            $scope.cardOrder = response.Return;
            if ($scope.cardOrder.createDate) {
              $scope.cardOrder.formattedDate = moment.utc($scope.cardOrder.createDate).local().format('DD/MM/YYYY HH:mm');
            }
            handleStatus($scope.cardOrder);
          } else if (response && !response.Success) {
            showErrorMessage(response.Error);
          } else {
            showErrorMessage('Não foi possível carregar o pedido, por favor, tente novamente.');
          }
        })
        .catch(function (resp) {
          $scope.loading = false;
          showErrorMessage('Ocorreu um erro ao carregar o pedido.');
        });
    };


    $scope.init = function () {
      loadOrder();
    };
  }])
  .controller('detalhesPedidoCashbackCtrl', ['MeusPedidosApiService', '$scope', '$routeParams', function (service, $scope, $routeParams) {
    $scope.order = {};
    var p = $routeParams.pedido;

    const handleStatus = function (order) {
      if (order.status == 'CANCELED') {
        order.resumedStatus = 'Cancelado';
      } else if (order.status == 'ERROR') {
        order.resumedStatus = 'Não Aprovado';
      } else if (order.status == 'TRANSFERRED') {
        order.resumedStatus = 'Transferido';
        order.showTransferReceipt = true;
      } else {
        order.resumedStatus = 'Recebido';
      }

      if (order.bankAccount.type == 'CheckingAccount') {
        order.bankAccount.typeDescription = 'Conta Corrente';
      } else if (order.bankAccount.type == 'SavingsAccount') {
        order.bankAccount.typeDescription = 'Conta Poupança';
      } else {
        order.bankAccount.typeDescription = 'Não Informada';
      }
    };

    const loadOrder = function () {
      $scope.loading = true;
      service.loadOrder('CASHBACK_ORDER', p)
        // .then(function(response) { return response.data; }) não precisa por causa do $promise
        .then(function (response) {
          $scope.loading = false;
          if (response && response.Success) {
            $scope.order = response.Return;
            if ($scope.order.createDate) {
              $scope.order.formattedDate = moment.utc($scope.order.createDate).local().format('DD/MM/YYYY HH:mm');
            }
            if ($scope.order.transferDate) {
              $scope.order.formattedTransferDate = moment.utc($scope.order.transferDate).local().format('DD/MM/YYYY');
            }
            handleStatus($scope.order);
          } else if (response && !response.Success) {
            showErrorMessage(response.Error);
          } else {
            showErrorMessage('Não foi possível carregar o pedido, por favor, tente novamente.');
          }
        })
        .catch(function (resp) {
          $scope.loading = false;
          showErrorMessage('Ocorreu um erro ao carregar o pedido.');
        });
    };

    $scope.init = function () {
      loadOrder();
    };
  }])
  .controller('detalhesPedidoRecargaCtrl', ['MeusPedidosApiService', '$scope', '$routeParams', function (service, $scope, $routeParams) {
    $scope.order = {};
    var p = $routeParams.pedido;

    const handleStatus = function (order) {
      if (order.confirmed) {
        order.resumedStatus = 'Confirmado';
      } else {
        order.resumedStatus = 'Processando';
      }
    };

    $scope.printProof = function () {
      const printPopup = window.open('', 'printPopup', 'width=340,height=500');
      printPopup.document.title = 'Comprovante de Recarga';
      printPopup.document.write('<pre>')
      printPopup.document.write($scope.order.proofPayment);
      printPopup.document.write('</pre>');
      printPopup.document.close();
      printPopup.focus();
      printPopup.print();
      printPopup.close();
    };

    const loadOrder = function () {
      $scope.loading = true;
      service.loadOrder('RECHARGE_ORDER', p)
        .then(function (response) {
          $scope.loading = false;
          if (response && response.Success) {
            $scope.order = response.Return;
            if ($scope.order.systemDate) {
              $scope.order.formattedDate = moment.utc($scope.order.systemDate).local().format('DD/MM/YYYY HH:mm');
            }
            handleStatus($scope.order);
          } else if (response && !response.Success) {
            showErrorMessage(response.Error);
          } else {
            showErrorMessage('Não foi possível carregar o pedido, por favor, tente novamente.');
          }
        })
        .catch(function (resp) {
          $scope.loading = false;
          showErrorMessage('Ocorreu um erro ao carregar o pedido.');
        });
    };

    $scope.init = function () {
      loadOrder();
    };
  }])
  .controller('detalhesPedidoPagueContasCtrl', ['MeusPedidosApiService', '$scope', '$routeParams', function (service, $scope, $routeParams) {
    $scope.order = {};
    var p = $routeParams.pedido;

    const handleStatus = function (order) {
      if (order.confirmed) {
        order.resumedStatus = 'Confirmado';
      } else {
        order.resumedStatus = 'Processando';
      }
    };

    $scope.printProof = function () {
      const printPopup = window.open('', 'printPopup', 'width=340,height=500');
      printPopup.document.title = 'Comprovante de Pagamento';
      printPopup.document.write('<pre>')
      printPopup.document.write($scope.order.proofPayment);
      printPopup.document.write('</pre>');
      printPopup.document.close();
      printPopup.focus();
      printPopup.print();
      printPopup.close();
    };

    const loadOrder = function () {
      $scope.loading = true;
      service.loadOrder('BILL_PAYMENT_ORDER', p)
        .then(function (response) {
          $scope.loading = false;
          if (response && response.Success) {
            $scope.order = response.Return;
            if ($scope.order.systemDate) {
              $scope.order.formattedDate = moment.utc($scope.order.systemDate).local().format('DD/MM/YYYY HH:mm');
            }
            handleStatus($scope.order);
          } else if (response && !response.Success) {
            showErrorMessage(response.Error);
          } else {
            showErrorMessage('Não foi possível carregar o pedido, por favor, tente novamente.');
          }
        })
        .catch(function (resp) {
          $scope.loading = false;
          showErrorMessage('Ocorreu um erro ao carregar o pedido.');
        });
    };

    $scope.init = function () {
      loadOrder();
    };
  }])
  .controller('dadosDuplaAutenticacaoCtrl', ['MinhaContaApiService', 'logger', '$scope', '$timeout', function (service, logger, $scope, $timeout) {
    $scope.counter = 0;
    $scope.tokenSended = false;

    $scope.candEditAuthenticationFormat = false;

    $scope.userToken = {};
    $scope.participantMfaSettings = {};

    const buscarDadosDuplaAutenticacao = function () {
      $scope.loadingMfaSettings = true;
      service.buscarDadosDuplaAutenticacao()
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          if (retorno.Success) {
            $scope.participantMfaSettings = retorno.Return;
            $scope.candEditAuthenticationFormat = $scope.authenticationMfaFormat == 'ALWAYS_SELECT' ? true : false;
          } else {
            showErrorSwal('Ops', retorno.Error);
          }
          $scope.loadingMfaSettings = false;
        })
        .catch(function (resp) {
          $scope.loadingMfaSettings = false;
          showErrorSwal('Ops', 'Ocorreu um erro ao carregar os dados, por favor, tente novamente. Caso o problema persista entre em contato com nossa equipe de atendimento!');
          logger.logResponse(resp);
        });
    };

    $scope.sendToken = function () {
      $scope.loadingMfaSettings = true;
      service
        .enviarMfaToken($scope.participantMfaSettings)
        .then(function (response) {
          return response.data;
        })
        .then(function (retorno) {
          $scope.loadingMfaSettings = false;
          if (retorno.Success && retorno.Return) {
            showSuccessSwal(
              "Info",
              "Código de segurança enviado com sucesso"
            );
            $scope.tokenSended = retorno.Return;
            $scope.startCountdown(60);
          } else {
            $scope.loadingMfaSettings = false;
            logger.log(retorno.Error);
            showErrorSwal("Ops", retorno.Error);
            $scope.tokenSended = false;
          }
        })
        .catch(function (retorno) {
          $scope.tokenSended = false;
          logger.logResponse(retorno);
          showErrorSwal(
            "Ops",
            "Ocorreu um erro ao enviar o código de segurança, por favor, tente novamente."
          );
        });
    };

    $scope.validateToken = function () {
      if ($scope.userToken.token === undefined) {
        showErrorSwal("Ops", 'Informe o código de segurança');
        return;
      }
      $scope.userToken.email = $scope.participantMfaSettings.Email;
      $scope.userToken.mobilePhone = $scope.participantMfaSettings.MobilePhone;
      $scope.userToken.authenticationMfaFormat = $scope.participantMfaSettings.AuthenticationMfaFormat;
      $scope.loadingMfaSettings = true;
      service
        .validarTokenDuplaAutenticacao($scope.userToken)
        .then(function (response) {
          return response.data;
        })
        .then(function (retorno) {
          $scope.loadingMfaSettings = false;
          if (retorno.Success) {
            showSuccessSwal(
              "Info",
              "Código de segurança validado com sucesso"
            );
            $scope.counter = 0;
            $scope.tokenSended = false;
          } else {
            showErrorSwal("Ops", retorno.Error);
          }
        })
        .catch(function (retorno) {
          $scope.loadingMfaSettings = false;
          logger.logResponse(retorno);
          showErrorSwal(
            "Ops",
            "Ocorreu um erro ao validar o código de segurança, por favor, tente novamente."
          );
        });
    };

    $scope.startCountdown = function (seconds) {
      // Using timeout.
      $scope.counter = seconds;
      $scope.Timeout = function () {
        $scope.counter--;
        timeout = $timeout($scope.Timeout, 1000);
        if ($scope.counter == 0) {
          $timeout.cancel(timeout);
        }
      };
      var timeout = $timeout($scope.Timeout, 1000);
    };

    $scope.init = function (authenticationMfaFormat) {
      $("#mfaSettings").css("display", "block");
      $.fn.initChosen('select.autostart');
      $.fn.initChosen('#authFormat');
      $("#authFormat").trigger("chosen:updated");
      $scope.authenticationMfaFormat = authenticationMfaFormat;
      buscarDadosDuplaAutenticacao();
    }
  }])
  .controller("RegistrationMfaValidationCtrl", [
    "RegistrationMfaValidationApiService",
    "RegistrationMfaValidationFactory",
    "logger",
    "$scope",
    function (service, factory, logger, $scope) {
      $scope.actualStep = "BASIC_INFO";
      $scope.steps = {
        BASIC_INFO: "BASIC_INFO",
        TOKEN_OPTION: "TOKEN_OPTION",
        TOKEN_CONFIRMATION: "TOKEN_CONFIRMATION",
        USER_CONFIRMED: "USER_CONFIRMED"
      };
      $scope.sendType = {
        ALL: "ALL",
        EMAIL: "EMAIL",
        SMS: "SMS"
      };
      $scope.user = {};
      $scope.alreadySentToken = false;
      $scope.parametrizations = {
        requireMfaValidationAtRegistration: false,
        registrationMfaValidationMethod: null
      };

      $scope.init = function () {
        factory.authenticated = false;
        $scope.actualStep = "BASIC_INFO";
        getAuthenticationMfaParameters();
      };

      $scope.loading = function () {
        return factory.loading;
      }

      const loadUserInfo = function () {
        factory.loading = true;
        service.getPrincipalContact()
          .then(function (response) { return response.data; })
          .then(function (response) {
            factory.loading = false;
            if (response) {
              if (response.Success) {
                $scope.user = response.Return;
              } else {
                showErrorMessage(response.Error);
              }
            } else {
              showErrorMessage('Ocorreu um erro durante a consulta do usuário, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
            }
          })
          .catch(function (response) {
            factory.loading = false;
            showErrorMessage('Ocorreu um erro durante a consulta do usuário, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
            logger.logResponse(response);
          });
      }

      $scope.cancelAuth = function () {
        factory.isMfaValidationStep = false;
      }

      $scope.sendAuthorizationCode = function () {
        if (!$scope.user.sendMethod) {
          showErrorMessage('Selecione a forma de envio do código de segurança.');
          return;
        }

        factory.loading = true;
        service.sendAuthorizationCode($scope.user.sendMethod)
          .then(function (response) { return response.data; })
          .then(function (response) {
            factory.loading = false;
            if (response) {
              if (response.Success) {
                $scope.actualStep = $scope.steps.TOKEN_CONFIRMATION;
              } else {
                showErrorMessage(response.Error);
                $scope.alreadySentToken = true;
              }
            } else {
              showErrorMessage('Ocorreu um erro durante o envio do código de autenticação, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
              $scope.alreadySentToken = true;
            }
          })
          .catch(function (response) {
            factory.loading = false;
            showErrorMessage('Ocorreu um erro durante o envio do código de autenticação, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
            logger.logResponse(response);
          });
      };

      $scope.validateAuthorizationCode = function () {
        if (!$scope.user.code) {
          showErrorMessage('Preencha o código de segurança.');
          return;
        }

        factory.loading = true;
        service.validateAuthorizationCode($scope.user.code)
          .then(function (response) { return response.data; })
          .then(function (response) {
            factory.loading = false;
            if (response) {
              if (response.Success) {
                $scope.actualStep = $scope.steps.USER_CONFIRMED;
                factory.isMfaValidationStep = false;
                factory.authenticated = true;
                history.back();
              } else {
                showErrorMessage(response.Error);
              }
            } else {
              showErrorMessage('Ocorreu um erro durante a confirmação do código de autenticação, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
            }
          })
          .catch(function (response) {
            factory.loading = false;
            showErrorMessage('Ocorreu um erro durante a confirmação do código de autenticação, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
            logger.logResponse(response);
          });
      };

      $scope.isRecaptchaStep = function () {
        return $scope.actualStep == $scope.steps.BASIC_INFO;
      };

      $scope.isTokenOptionStep = function () {
        return $scope.actualStep == $scope.steps.TOKEN_OPTION;
      };

      $scope.isTokenConfirmationStep = function () {
        return $scope.actualStep == $scope.steps.TOKEN_CONFIRMATION;
      };

      $scope.isUserAuthenticated = function () {
        return $scope.isFinalStep();
      }

      $scope.requireMfaValidationAtRegistration = function () {
        return $scope.parametrizations && $scope.parametrizations.requireMfaValidationAtRegistration;
      }

      $scope.isFinalStep = function () {
        return $scope.actualStep == $scope.steps.USER_CONFIRMED;
      };

      $scope.canChoose = function () {
        return $scope.parametrizations.registrationMfaValidationMethod == $scope.sendType.ALL || !$scope.parametrizations.registrationMfaValidationMethod;
      }

      $scope.canSendEmail = function () {
        return $scope.parametrizations.registrationMfaValidationMethod == $scope.sendType.EMAIL || $scope.canChoose();
      }

      $scope.canSendSMS = function () {
        return $scope.parametrizations.registrationMfaValidationMethod == $scope.sendType.SMS || $scope.canChoose();
      }

      $scope.initUserValidation = function () {
        prepCaptcha();
      }

      const prepCaptcha = function () {
        factory.isMfaValidationStep = true;
        if (userValidationRecaptcha === null)
          $.getScript("https://www.google.com/recaptcha/api.js?onload=loadReCaptcha");
        else {
          $('#userValidationRecaptcha').html('');
          $.getScript("https://www.google.com/recaptcha/api.js?onload=loadReCaptcha");
        }
      }

      $scope.backToTokenOption = function () {
        initializeUserValidation();
        $scope.actualStep = $scope.steps.TOKEN_OPTION;
      }

      const initializeUserValidation = function () {
        $scope.actualStep = $scope.steps.BASIC_INFO;
        $scope.pinRequest = {
          pin: null,
          confirmedPin: null
        };
      }

      $scope.validateReCaptcha = function () {
        if (!isReCaptchaValid()) return;
        $scope.actualStep = $scope.steps.TOKEN_OPTION;
      }

      const isReCaptchaValid = function () {
        const token = grecaptcha.getResponse(userValidationRecaptcha);
        if (token.length > 0) {
          return true;
        } else {
          showErrorMessage("Preencha o reCaptcha para prosseguir.");
          return false;
        }
      };

      const getAuthenticationMfaParameters = function () {
        factory.loading = true;
        service.getAuthenticationMfaParameters()
          .then(function (response) { return response.data; })
          .then(function (response) {
            factory.authenticated = false;
            factory.loading = false;
            if (response) {
              if (response.Success) {
                $scope.parametrizations = response.Return;
                if ($scope.requireMfaValidationAtRegistration()) {
                  $scope.initUserValidation();
                  loadUserInfo();
                }
              } else {
                showErrorMessage(response.Error);
              }
            } else {
              showErrorMessage('Ocorreu um erro durante a consulta das configurações da campanha, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
            }
          })
          .catch(function (response) {
            factory.loading = false;
            showErrorMessage('Ocorreu um erro durante a consulta das configurações da campanha, por favor, tente novamente. Se o problema persistir, entre em contato com nossa equipe de atendimento.');
            logger.logResponse(response);
          });
      }
    },
  ]);
