angular.module("platformApp", ['footerModule', 'platformComponents', 'ngRoute', 'ngResource', 'validation', 'validation.rule',
	'ui.utils.masks', 'brasil.filters', 'ngLoaderContainer', 'moment-picker', 'EnderecoModule'])
	.factory('httpRequestInterceptor', ['$q', '$window', httpRequestInterceptor])
	.config(['$httpProvider', function($httpProvider) {
		$httpProvider.interceptors.push('httpRequestInterceptor');
	}])
	.factory('MinhaContaEndpoint', ['$resource', function($resource) {
		return $resource("/MinhaConta/DadosCadastro/:id", {id: "@id"}, {
			carrega: { method: "GET" },
			recusa: { methd: "PUT" },
			atualiza: { method: "POST", data: {}, isArray: false }
		});
	}])
	.factory('MeusEnderecosEndpoint', ['$resource', function($resource) {
		return $resource("/MinhaConta/Enderecos/:id", { id: "@id" }, {
			carrega: { url: "/MinhaConta/GetEndereco/:id", method: "GET" },
			cadastra: { url: "/MinhaConta/CadastrarEndereco", method: "POST", data: {}, isArray: false},
			atualiza: { url: "/MinhaConta/AtualizarEndereco", method: "PUT", data: {}, isArray: false},
			exclui: { url: "/MinhaConta/ExcluirEndereco/:id", method: "DELETE", data: {}, isArray: false },
		});
	}])
	.factory('MeusPedidosEndpoint', ['$resource', function($resource) {
		return $resource("/MinhaConta/GetPedido/:p", {p: "@p"}, {
			carrega: { method: "GET", data: {}, isArray: false }
		});
	}])
	.factory('RegistrationMfaValidationFactory', function () {
		return {
			isMfaValidationStep: false,
			authenticated: false,
			parametrizations: null,
			page: null,
			loading: false
		}
	})
	.service("SecurityApiService", ["$http", function ($http) {
		this.getAuthenticationMfaParameters = function () {
		  return $http.get('/settings/security/authentication-mfa');
		}
	}])
	.service('MinhaContaApiService', ['$http', 'MinhaContaEndpoint', function($http, minhacontaEndpoint) {
		this.carregaOperadoras = function() {
			return $http.get("/MinhaConta/Operadoras");
		};

		this.pesquisarPessoaPeloDocumento = function(document) {
			return $http.get("/MinhaConta/PesquisarPessoaPeloDocumento", { params:{ document } });
		};

		this.carregaEstadosCivil = function() {
			return $http.get("/MinhaConta/EstadosCivil");
		};

		this.carregaParticipanteMigrado = () => {
			return $http.get("/MinhaConta/GetParticipantMigrated");
		}

		this.getMetadataHeaders = function() {
			return $http.get("/MinhaConta/MetadataHeaders");
		};

		this.carregaDadosCadastro = function() {
			return minhacontaEndpoint.carrega().$promise;
		};

		this.salvaDadosCadastro = function(dadosCadastro) {
			return minhacontaEndpoint.atualiza({id: dadosCadastro.Id}, dadosCadastro).$promise;
		};

		this.consultaSaldoDisponivel = function() {
			return $http.get("/MinhaConta/GetResumo");
		};

		this.carregaUltimosAcumulos = function() {
			return $http.get("/MinhaConta/GetUltimosAcumulos");
		};

		this.carregaUltimosResgates = function() {
			return $http.get("/MinhaConta/GetUltimosResgates");
		};

		this.carregaPontosExpirar = function() {
			return $http.get("/MinhaConta/GetPontosExpirar");
		};

		this.carregaPontosBloqueados = function() {
			return $http.get("/MinhaConta/GetPontosBloqueados");
		};

		this.carregaExtrato = function(t, di, df) {
			return $http.get("/MinhaConta/GetExtrato", {params: {t: t, di: di, df: df}});
		};

		this.carregaParametrizacaoMetadata = function() {
			return $http.get("/MinhaConta/GetParametrizacaoMetadata");
		}

		this.carregaPedidos = function(di, df, s) {
			return $http.get("/MinhaConta/GetPedidos", {params: {di: di, df: df, s: s}});
		};

		this.alterarSenha = function (senha) {
			return $http.post('/MinhaConta/AlterarSenha', { 'senhaAntiga': senha.senhaAntiga, 'novaSenha': senha.novaSenha, 'confirmaSenha': senha.confirmaSenha });
			};

		this.getComprarPontos = function () {
			return $http.get('/MinhaConta/GetComprarPontos');
		};

		this.comprarPontos = function (comprar) {
			return $http.post('/MinhaConta/ComprarPontos', JSON.stringify(comprar));
		};
		this.buscarDadosDuplaAutenticacao = function () {
			return $http.get('/MinhaConta/BuscarDadosDuplaAutenticacao');
		};

		this.enviarMfaToken = function (dados) {
			return $http.post("/MinhaConta/EnviarMfaToken", dados);
		};

		this.validarTokenDuplaAutenticacao = function(dados) {
			return $http.post('/MinhaConta/ValidarTokenDuplaAutenticacao', dados);
		}

		this.getPrincipalContact = function () {
			return $http.get('/MinhaConta/PrincipalData');
		}
	}])
	.service('MeusEnderecosApiService', ['MeusEnderecosEndpoint', '$http', function(enderecosEndpoint, $http) {
		this.carregaEnderecos = function() {
			return $http.get("/MinhaConta/EnderecosEntrega");
		};

		this.carregaEndereco = function(idEndereco) {
			return enderecosEndpoint.carrega({id: idEndereco}).$promise;
		};

		this.cadastraEndereco = function(endereco) {
			return enderecosEndpoint.cadastra({}, endereco).$promise;
		};

		this.atualizaEndereco = function(endereco) {
			return enderecosEndpoint.atualiza({}, endereco).$promise;
		};

		this.excluiEndereco = function(id) {
			return enderecosEndpoint.exclui({id: id}).$promise;
		};
	}])
	.service('MeusPedidosApiService', ['MeusPedidosEndpoint', '$http', function (endpoint, $http) {
		const config = { headers : { 'Content-Type': 'application/json' } };
		this.loadOrder = function(type, p) {
			return endpoint.carrega({ p: p, type: type }).$promise;
		};
		this.approveOrder = function(p) {
			return $http.post('/minhaconta/aprovapedido/' + p, {}, config);
		};
		this.refuseOrder = function(p) {
			return $http.post('/minhaconta/recusapedido/' + p, {}, config);
		};
		this.consultVouchersLink = function(p, itemGrouperId) {
			return $http.get('/MinhaConta/ConsultLinkVouchers', {params: {id: p, partnerId: itemGrouperId}});
		};
	}])
	.service('pointsPurchaseService', ['$http', function($http) {
		const config = { headers : { 'Content-Type': 'application/json' } };
		this.enviarPagarme = function(appId, card) {
			var expTokens = card.expiration.split('/');
			var payload = {
				card_number: card.number,
				card_holder_name: card.holder,
				card_expiration_date: expTokens[0] + expTokens[1],
				card_cvv: card.cvv
			};
			return pagarme.client.connect({ encryption_key: appId })
				.then(function(client) {
					return client.security.encrypt(payload);
				})
				.then(function(token) {
					return {
						success: true,
						token: token
					};
				})
				.catch(function(err) {
					return {
						success: false,
						error: err
					};
				});
		};
		this.enviarMundipagg = function(appId, card) {
			var expTokens = card.expiration.split('/');
			var payload = {
				type: "card",
				card: {
					number: card.number,
					holder_name: card.holder,
					exp_month: expTokens[0],
					exp_year: expTokens[1],
					cvv: card.cvv
				}
			};
			return $http.post("https://api.mundipagg.com/core/v1/tokens?appId="+appId, JSON.stringify(payload), config);
		};
		this.carregaEnderecos = function() {
			return $http.get("/MinhaConta/EnderecosEntrega");
		};
		this.carregaOpcoesPagamento = function(pontos) {
			return $http.get("/ComprarPontos/OpcoesPagamento?pontos=" + pontos);
		};
		this.comprarPontos = function(data) {
			return $http.post("/ComprarPontos/CompraCarrinho", data, config);
		};
		this.getPrincipalContact = function () {
			return $http.get('/MinhaConta/PrincipalData');
		}
		this.sendAuthorizationCode = function (sendMethod) {
			return $http.post('/auth/authentication/code', { "sendMethod": sendMethod } , 30000);
		}
	}])
	.service("RegistrationMfaValidationApiService", ["$http", function ($http) {
		this.getPrincipalContact = function () {
		  return $http.get('/MinhaConta/PrincipalData');
		}

		this.sendAuthorizationCode = function (sendMethod) {
		  return $http.post('/auth/authentication/code', { "sendMethod": sendMethod } , 30000);
		}

		this.validateAuthorizationCode = function (code) {
		  return $http.put('/auth/authentication/code', { "code": code }, 30000);
		}

		this.getAuthenticationMfaParameters = function () {
		  return $http.get('/settings/security/authentication-mfa');
		}
	}])
