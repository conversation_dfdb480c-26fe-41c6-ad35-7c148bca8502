angular.module("popupEnderecoApp", ['platformComponents', 'ngResource', 'validation', 'validation.rule', 'ui.utils.masks', 'brasil.filters', 'EnderecoModule'])
.factory('MeusEnderecosEndpoint', ['$resource', function($resource) {
	return $resource("/MinhaConta/Enderecos/:id", {id: "@@id" }, {
		carrega: { method: "GET" },
		carrega: { url: "/MinhaConta/GetEndereco/:id", method: "GET" },
		carregaDadosParticipante: { url: "/MinhaConta/PrincipalData", method: "GET" },
		cadastra: { url: "/MinhaConta/CadastrarEndereco", method: "POST", data: {}, isArray: false},
		atualiza: { url: "/MinhaConta/AtualizarEndereco", method: "PUT", data: {}, isArray: false},
	});
}])
.service('MinhaContaService', ['MeusEnderecosEndpoint', function(endpoint) {
	this.cadastraEndereco = function(endereco) {
		return endpoint.cadastra({}, endereco).$promise;
	};
	this.carregaEndereco = function(idEndereco) {
		return endpoint.carrega({id: idEndereco}).$promise;
	};
	this.atualizaEndereco = function(endereco) {
		return endpoint.atualiza({}, endereco).$promise;
	};
	this.carregaDadosParticipante = function () {
		return endpoint.carregaDadosParticipante({}).$promise;
	};
}])
.controller('EnderecoController', ['MinhaContaService', 'EnderecoApi', '$injector', '$scope',
		function(service, enderecoApi, $injector, $scope) {
	const enderecoId = getParameterByName('address');
	const isEditing = enderecoId && enderecoId.length;
	$scope.endereco = { Receiver: {} };

	$scope.desabilitarEdicao = true;
	const $validationProvider = $injector.get('$validation');

	$scope.pageTitle = isEditing ? 'Editar o endereço' : 'Cadastrar novo endereço';
	$scope.buttonText = isEditing ? 'Atualizar' : 'Cadastrar';
	$scope.disableButton = false;
	$scope.showButton = true;

	var callerFieldCep = parent.jQuery('#shippingZipcode');
	if (callerFieldCep && callerFieldCep.length) {
		callerFieldCep = callerFieldCep[0];
	} else {
		callerFieldCep = null;
	}

	const adicionaFeedback = function() {
		const btn = $("button[type=submit]");
		$(btn).attr("disabled", "disabled");
		$(btn).addClass("processing");
		$(btn).html("Aguarde");
	};

	const removeLoaderFromButton = function(text) {
		const btn = $("button[type=submit]");
		$(btn).removeClass("processing");
		$(btn).html(text);
		$(btn).removeAttr("disabled");
	};

	const mostraMensagemErro = function(msg) {
		$scope.classeMensagem = "errors";
		$scope.mensagemFeedback = msg;
	};

	const mostraMensagemSucesso = function(msg) {
		$scope.classeMensagem = "success";
		$scope.mensagemFeedback = msg;
	};

	const closePopup = function() {
		frames.top.$.fancybox.close(true);
	};

	const carregaEndereco = function(enderecoId) {
		$scope.loading = true;
		service.carregaEndereco(enderecoId)
			// .then(function(response) { return response.data; }) não precisa por causa do $promise
			.then(function(retorno) {
				if(retorno.Success) {
					if(retorno.Return.constructor == Array)
						$scope.endereco = retorno.Return[0];
					else
						$scope.endereco = retorno.Return;
					enderecoApi.fillAddressByCep($scope.endereco.Cep, $scope.endereco);
				} else {
					logger.log(retorno.Error);
				}
				$scope.loading = false;
			}).catch(function(response) {
				mostraMensagemErro("Ocorreu um erro ao carregar o endereço, por favor, tente novamente.");
				logger.logResponse(response);
				$scope.loading = false;
			});
	};

	$scope.searchAddressByCep = function(cep) {
		enderecoApi.fillAddressByCep(cep, $scope.endereco);
	};

	const saveAddress = function() {
		service.cadastraEndereco($scope.endereco)
			.then(function(saveResult) {
				$scope.cadastrando = false;
				removeLoaderFromButton(null, "Cadastrar");
				if(saveResult.Success) {
					$scope.disableButton = false;
					mostraMensagemSucesso("Endereço cadastrado com sucesso.");
					frames.top.parent.hasNewAddress = true;
					frames.top.createdAddressId = saveResult.Return;
					// closePopup();
				} else {
					mostraMensagemErro(saveResult.Error);
				}
			})
			.catch(function(response) {
				$scope.cadastrando = false;
				removeLoaderFromButton(null, "Cadastrar");
				mostraMensagemErro("Ocorreu um erro durante o cadastro do endereço, por favor, tente novamente.");
			});
	};

	const updateAddress = function() {
		service.atualizaEndereco($scope.endereco)
			// .then(function(response) { return response.data; }) não precisa por causa do $promise
			.then(function(saveResult) {
				$scope.cadastrando = false;
				removeLoaderFromButton(null, "Atualizar");
				if(saveResult.Success) {
					mostraMensagemSucesso("Endereço atualizado com sucesso.");
				} else {
					mostraMensagemErro(saveResult.Error);
				}
			})
			.catch(function(response) {
				$scope.cadastrando = false;
				removeLoaderFromButton(null, "Cadastrar");
				mostraMensagemErro("Ocorreu um erro durante o cadastro do endereço, por favor, tente novamente.");
			});
	};

	$scope.cadastraEndereco = function(s) {
		$scope.cadastrando = true;
		adicionaFeedback();

		$scope.mensagemFeedback = '';
		$validationProvider.validate($scope.enderecoForm)
			.then(function() {
				if (isEditing) {
					updateAddress();
				} else {
					saveAddress();
				}
			})
			.catch(function() {
				removeLoaderFromButton(null, "Cadastrar");
				mostraMensagemErro("Preencha todos os campos corretamente para prosseguir.");
			});
	};

	const carregaDadosParticipante = function() {
		$scope.loading = true;
		service.carregaDadosParticipante()
			.then(function (response) {
				if (response && response.Return) {
					$scope.endereco.Receiver.Name = response.Return.name;
					$scope.endereco.Receiver.Cpf = response.Return.document;
					$scope.endereco.Receiver.Email = response.Return.email;
					if (response.Return.mobilePhone) {
						const phone = response.Return.mobilePhone.replace(/\D+/g, '')
						$scope.endereco.Receiver.Cellphone = phone;
					}
				}
				$scope.loading = false;
			});
	};

	$scope.init = function() {
		carregaDadosParticipante();
		if (isEditing) {
			carregaEndereco(enderecoId);
		}
	};
}]);