angular.module("platformApp", ['footerModule', 'platformComponents', 'validation', 'validation.rule', 'ui.utils.masks', 'brasil.filters', 'ngLoaderContainer', 'EnderecoModule'])
  .factory('httpRequestInterceptor', ['$q', '$window', httpRequestInterceptor])
  .config(['$httpProvider', function ($httpProvider) {
    $httpProvider.interceptors.push('httpRequestInterceptor');
  }])
  .service('pointsPurchaseService', ['$http', function ($http) {
    const config = { headers: { 'Content-Type': 'application/json' } };
    this.enviarPagarme = function (appId, card) {
      var expTokens = card.expiration.split('/');
      var payload = {
        card_number: card.number,
        card_holder_name: card.holder,
        card_expiration_date: expTokens[0] + expTokens[1],
        card_cvv: card.cvv
      };
      return pagarme.client.connect({ encryption_key: appId })
        .then(function (client) {
          return client.security.encrypt(payload);
        })
        .then(function (token) {
          return {
            success: true,
            token: token
          };
        })
        .catch(function (err) {
          return {
            success: false,
            error: err
          };
        });
    };
    this.enviarPagto = function (appId, card) {
      var expTokens = card.expiration.split('/');
      var payload = {
        type: "card",
        card: {
          number: card.number,
          holder_name: card.holder,
          exp_month: expTokens[0],
          exp_year: expTokens[1],
          cvv: card.cvv
        }
      };
      return $http.post("https://api.mundipagg.com/core/v1/tokens?appId=" + appId, JSON.stringify(payload), config);
    };
    this.carregaEnderecos = function () {
      return $http.get("/MinhaConta/EnderecosEntrega");
    };
    this.comprarPontos = function (data) {
      return $http.post("/ComprarPontos/CompraCarrinho", data, config);
    };
  }])
  .controller('compraCtl', ['pointsPurchaseService', 'EnderecoApi', 'logger', '$scope', '$injector', '$timeout', function (service, enderecoApi, logger, $scope, $injector, $timeout) {
    $scope.dirtyWithError = dirtyWithError;
    $scope.card = {};
    $scope.showForm = true;
    $scope.loading = false;
    $scope.classeMensagem = '';
    $scope.mensagemFeedback = "";

    var $validationProvider = $injector.get('$validation');

    const mostraErro = function (mensagem) {
      $scope.loading = false;
      showErrorSwal('Ops', mensagem);
    };

    function initCreditCardBrand() {
      $timeout(function () {
        $scope.$evalAsync(function () {
          $("input.creditCard").validateCreditCard(function (t) {
            return !t.card_type ? $(this).next().attr("class", "cardBrand") : ($(this).next().attr("class", "cardBrand"), $(this).next().addClass("icons-" + t.card_type.name));
          });
        });
      }, 10);
    }

    $scope.pesquisaCep = function (cep) {
      enderecoApi.fillAddressByCep(cep, $scope.dadosCompra.BillingAddress);
    };

    const processarCompra = function (hashToken) {
      const payload = {
        Cpf: $scope.dadosCompra.Cpf,
        Phone: $scope.dadosCompra.Phone,
        Email: $scope.dadosCompra.Email,
        CardToken: hashToken,
        Ticket: { Token: $("#t").val() },
        Installment: { Token: $scope.dadosCompra.installment },
        BillingAddress: $scope.dadosCompra.BillingAddress,
        LocationInfo: {
          Timezone: moment.tz.guess()
        }
      };
      service.comprarPontos(payload)
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          if (retorno.Success) {
            try {
              frames.top.hasCompletedBalancePurchase = true;
            } catch (err) {
              console.error(err);
            }
            $scope.classeMensagem = 'validation-summary-success';
            showSuccessSwal('Info', 'Compra efetuada com sucesso, por favor, feche esta janela para prosseguir com o pedido.');
            setTimeout(function () {
              frames.top.$.fancybox.close(true);
            }, 2000);
          } else {
            mostraErro(retorno.Error);
            logger.log(retorno.Error);
          }
          $scope.loading = false;
        })
        .catch(function (response) {
          mostraErro("Ocorreu um erro ao processar o pagamento. Tente novamente! Se o problema persistir, entre em contato com nossa equipe de atendimento!");
          logger.log(response.data, response.status);
        });
    }

    const comprarPontos = function (frmPointsPurchase) {
      angular.forEach(frmPointsPurchase.$error, function (field) {
        angular.forEach(field, function (errorField) { errorField.$setTouched(); })
      });
      $validationProvider.validate(frmPointsPurchase)
        .then(function () {
          $scope.loading = true;
          service.enviarPagarme($("#aid").val(), $scope.card)
            .then(function (result) {
              if (result.success) {
                processarCompra(result.token);
              } else if (result.success == false) {
                mostraErro(result.error);
              } else {
                mostraErro('Não foi possível inicar a compra de pontos, por favor, tente novamente.');
              }
            })
            .catch(function (resp) {
              sendEvent('Comprar Pontos', 'Error Gateway', 'Popup');
              $scope.card = {};
              mostraErro("Ocorreu um erro ao processar o pagamento. Tente novamente! Se o problema persistir, entre em contato com nossa equipe de atendimento!");
              logger.logResponse(resp);
            });
        })
        .catch(function () {
          mostraErro("Preencha os campos corretamente para prosseguir com a compra.");
        });
    };

    $scope.comprar = function (frmPointsPurchase) {
      sendEvent('Comprar Pontos', 'Comprar', 'Popup');
      comprarPontos(frmPointsPurchase);
    };

    $scope.initForm = function (cpf, phone, email) {
      $scope.dadosCompra = {
        Cpf: cpf,
        Phone: phone,
        Email: email,
        BillingAddress: {}
      };
      $timeout(function () {
        $scope.$evalAsync(function () {
          $.fn.initChosen('#creditCardInstallments');
          $.fn.initCreditCardBrand("#creditCardNumber");
        });
      }, 50);
    };
  }]);