function updateQuantity(id) {
  var txtQtde = document.getElementById(id);
  var scope = angular.element(txtQtde).scope();
  scope.$apply(function() {
    scope.updateQuantity(id, txtQtde.value);
  });
}

function addLoaderToButton() {
  const btnResgatar = $("#btn-buy");
  btnResgatar.attr("disabled", "disabled");
  btnResgatar.html("Aguarde");
  btnResgatar.addClass("processing");
}

$('#divCart').css('visibility', 'visible');

window.canSubmit = false;

function onFormSubmit() {
  return window.canSubmit === true;
}

angular.module("platformApp", ['footerModule', 'ngLoaderContainer', 'ngResource', 'ui.utils.masks'])
.factory('httpRequestInterceptor', ['$q', '$window', httpRequestInterceptor])
.config(['$httpProvider', function($httpProvider) {
  $httpProvider.interceptors.push('httpRequestInterceptor');
}])
.factory('CarrinhoEndpoint', ['$resource', function($resource) {
	return $resource("/Carrinho", {}, {
		addItem: { url: "/Carrinho/Add", method: "POST", data: {}, isArray: false },
		removeItem: { url: "/Carrinho/Remove/:id", method: "DELETE", data: {}, isArray: false},
		updateQuantity: { url: "/Carrinho/AtualizaQtde/:id", method: "POST", data: {}, isArray: false},
    recalculate: { url: "/Carrinho/RecalcularFreteCarrinho/:id", method: "POST", data: {}, isArray: false},
    applyDiscountCoupon: { url: "/Carrinho/Desconto", method: "POST", data: {}, isArray: false }
	});
}])
.service('CarrinhoService', ['CarrinhoEndpoint', '$http', function(endpoint, $http) {
	this.getShippingAddresses = function() {
		return $http.get("/MinhaConta/EnderecosEntrega?resumed=true");
	};
	this.loadShippingCart = function() {
		return $http.get("/Carrinho/GetItensCarrinho");
	};
	this.removeItem = function(p) {
		return endpoint.removeItem({id: p}, {}).$promise;
	};
	this.updateQuantity = function(p, q) {
		return endpoint.updateQuantity({id: p}, {q: q}).$promise;
	};
	this.calculateShipping = function(cep) {
		return endpoint.recalculate({id: cep}, {}).$promise;
  };
  this.applyDiscountCoupon = function(discountCoupon) {
    return endpoint.applyDiscountCoupon({coupon: discountCoupon}).$promise;
  }
}])
.controller('cartCtrl', ['CarrinhoService', 'logger', '$scope', '$timeout', function(carrinhoService, logger, $scope, $timeout) {
  $scope.requiresTokenToRedeem = false;

  $scope.showErrorMessage = false;
  $scope.errorMessage = "";
  $scope.discountCoupon = {};
  $scope.enderecosEntrega = [];
  $scope.loadingCart = false;
  $scope.shoppingCart = { DiscountAmount:0, ProductsShippingCost:0, VouchersShippingCost:0, Produtos:[], ProductsTotal:0, VouchersTotal:0, Vales:[], Total:0 };

  $scope.hasProducts = function(carrinho) {
    return !!carrinho && carrinho.ProductsItems && carrinho.ProductsItems.length > 0;
  };

  $scope.hasVouchers = function(carrinho) {
    return !!carrinho && carrinho.VouchersItems && carrinho.VouchersItems.length > 0;
  };

  $scope.isEmpty = function(carrinho) {
    return !!carrinho && !($scope.hasProducts(carrinho) || $scope.hasVouchers(carrinho));
  };

  const showError = function(showPopup, detalhes) {
    if (!detalhes || !detalhes.length) {
      $scope.showErrorMessage = false;
      $scope.errorMessage = null;
      return;
    }
    $scope.showErrorMessage = true;
    $scope.errorMessage = detalhes;
    if (showPopup) {
      showErrorSwal('Ops', detalhes);
    }
  }

  const restartShippingZipcode = function() {
    if($scope.loadingShippingAddress || $scope.loadingCart) return;
    if($scope.shippingZipcode && $scope.shippingZipcode.length > 0) {
      $("#shippingZipcode").val($scope.shippingZipcode);
    }
    $timeout(function() {
      $scope.$evalAsync(function() {
        $("#shippingZipcode").chosen('destroy');
        $("#shippingZipcode_chosen").remove();
        $.fn.initChosen("#shippingZipcode");
      });
    }, 100);
  };

  const startViewEvents = function() {
    $timeout(function() {
      $scope.$evalAsync(function() {
        $.fn.initBlazy('.cart-checkout .photo img', 'blazy-src');
        $('.quantityController .more, .quantityController .less').on('click', function() {
          var newVal;
          $button = $(this);
          qtyField = $button.closest('.field').find('input');
          oldValue = qtyField.val();
          if($button.text() == '+') {
            newVal = parseFloat(oldValue) + 1;
          } else {
            newVal = parseFloat(oldValue) - 1;
          }
          qtyField.val(newVal);
          updateQuantity(qtyField.attr("id"));
        });
        $.fn.initChosen("#shippingZipcode");
        // $("#shippingZipcode").val($scope.shippingZipcode);
      });
    }, 100);
  }

  const loadViewFromCart = function(shoppingCart) {
    $scope.shoppingCart = shoppingCart;
    if($scope.shoppingCart.ShippingAddress) {
      $scope.shippingZipcode = $scope.shoppingCart.ShippingAddress.Id;
      $scope.cepSelecionado = $scope.shippingZipcode;
    }
    showError($scope.shoppingCart.OccurredError, $scope.shoppingCart.ErrorMessage);
    if ($scope.shoppingCart.Notification && $scope.shoppingCart.Notification.length) {
      showWarningSwal('Aviso', $scope.shoppingCart.Notification);
    }
    if($scope.shoppingCart.OccurredError) {
      if ($scope.isEmpty($scope.shoppingCart)) {
        $scope.ocorreuErroCarrinho = true;
        $scope.errorMessageCarrinho = $scope.shoppingCart.ErrorMessage;
      }
    } else {
      $scope.showErrorMessage = false;
      $scope.errorMessage = null;
      $scope.ocorreuErroCarrinho = false;
      $scope.errorMessageCarrinho = null;
    }
    startViewEvents();
  };

  const loadShippingAddresses = function(recreateCombo) {
    $scope.hasNoAddress = false;
    $scope.loadingShippingAddress = true;
    carrinhoService.getShippingAddresses()
      .then(function(response) { return response.data; })
			.then(function(addressesResponse) {
        $scope.loadingShippingAddress = false;
        if (addressesResponse.Success) {
          $scope.enderecosEntrega = addressesResponse.Return;
          if (!$scope.enderecosEntrega || !$scope.enderecosEntrega.length) {
            $scope.hasNoAddress = true;
          }
        } else {
          showError(true, addressesResponse.Error);
        }
        restartShippingZipcode(recreateCombo);
      })
      .catch(function(resp) {
        $scope.loadingShippingAddress = false;
        $scope.enderecosEntrega = [];
        showError(true, "Ocorreu um erro ao carregar os endereços de entrega, por favor, tente novamente.");
        restartShippingZipcode(recreateCombo);
        logger.logResponse(resp);
      });
  };

  const calculateShipping = function(cepDestino) {
    $scope.loadingCart = true;
    carrinhoService.calculateShipping(cepDestino)
      // .then(function(response) { return response.data; }) não precisa por causa do $promise
      .then(function(calcResult) {
        if(calcResult.Success) {
          loadViewFromCart(calcResult.Return);
        } else {
          showError(true, calcResult.Error);
          startViewEvents();
          logger.log(calcResult.Error);
        }
        $scope.loadingCart = false;
      })
      .catch(function(resp) {
        $scope.loadingCart = false;
        showError(true, "Ocorreu um erro ao efetuar o cálculo de frente, por favor, tente novamente.");
        startViewEvents();
        logger.logResponse(resp);
      });
  };

  const loadShippingCart = function() {
    $scope.loadingCart = true;
    carrinhoService.loadShippingCart()
      .then(function(response) { return response.data; })
			.then(function(cartResult) {
        $scope.loadingCart = false;
        if(cartResult.Success) {
          loadViewFromCart(cartResult.Return);
        } else {
          $scope.ocorreuErroCarrinho = true;
          $scope.errorMessageCarrinho = cartResult.Error;
          startViewEvents();
          logger.log(cartResult.Error);
        }
      })
      .catch(function(resp) {
        $scope.loadingCart = false;
        showError(true, "Ocorreu um erro ao carregar o seu carrinho, por favor, tente novamente.");
        startViewEvents();
        logger.logResponse(resp);
      });
  };

  $scope.removeItem = function(item) {
    $scope.loadingCart = true;
    carrinhoService.removeItem(item.Id)
      // .then(function(response) { return response.data; }) não precisa por causa do $promise
      .then(function(result) {
        $scope.loadingCart = false;
        if(result.Success) {
          loadViewFromCart(result.Return);
        } else {
          showError(true, result.Error);
          startViewEvents();
        }
      })
      .catch(function(resp) {
        $scope.loadingCart = false;
        showError(true, "Ocorreu um erro ao atualizar o seu carrinho, por favor, atualize a página.");
        startViewEvents();
      });
  };

  $scope.updateQuantity = function(p, qtde) {
    var parsedQtde = -1;
    try {
      parsedQtde = parseInt(qtde);
    } catch(ex) {
      parsedQtde = 1;
    }
    if(parsedQtde < 1) {
      if (confirm('Quantidade inferior a 1, deseja remover o item?')) {
        $scope.removeItem({ Id: p});
      } else {
        $('#' + p).val(1);
      }
      return;
    }

    if($scope.shoppingCart) {
      let item = null;

      if ($scope.shoppingCart.ProductsItems) {
        item = $scope.shoppingCart.ProductsItems.find(i => i.Id === p);
      }

      if (!item && $scope.shoppingCart.VouchersItems) {
        item = $scope.shoppingCart.VouchersItems.find(i => i.Id === p);
      }

      if (!!item && item.CartItemLimitEnabled && item.CartItemLimit !== null && parsedQtde > parseInt(item.CartItemLimit)) {
        parsedQtde = parseInt(item.CartItemLimit);
        $('#' + p).val(parsedQtde);

        showError(true, `Você pode adicionar no máximo ${item.CartItemLimit.toString()} quantidade deste produto no seu carrinho.`);
        return;
      }
    }

    $scope.loadingCart = true;
    carrinhoService.updateQuantity(p, parsedQtde)
      .then(function(updateResult) {
        $scope.loadingCart = false;
        if(updateResult.Success) {
          loadViewFromCart(updateResult.Return);
        } else {
          showError(true, updateResult.Error);
          startViewEvents();
        }
      })
      .catch(function(resp) {
        $scope.loadingCart = false;
        showError(stat, "Ocorreu um erro ao atualizar o seu carrinho, por favor, atualize a página.");
        startViewEvents();
      });
  };

  $scope.onCepChange = function() {
    $scope.shippingZipcode = $("#shippingZipcode").val();
     if ($scope.shippingZipcode == "other") {
        $scope.showAddressPopup();
        $scope.shippingZipcode = null;
        $scope.cepSelecionado = null;
     } else {
      calculateShipping($scope.shippingZipcode);
     }
  };

  $scope.showAddressPopup = function() {
    $.fancybox({
        maxWidth: 720,
        maxHeight: 480,
        padding: 0,
        href: "/Carrinho/NovoEndereco",
        type: 'iframe',
        beforeShow: function() {
          this.maxWidth = 720;
          this.maxHeight = 480;
          this.width = $(".fancybox-iframe").contents().find("html").width();
          this.height = $(".fancybox-iframe").contents().find("html").height();
        },
        afterClose: function() {
          const createdAddressId = window.createdAddressId;
          loadShippingAddresses(false);
          if (!createdAddressId) {
            return;
          }
          $scope.shippingZipcode = createdAddressId;
          $timeout(function() {
            $scope.$evalAsync(function() {
              calculateShipping(createdAddressId);
            });
          }, 250);
        }
      });
  };

  $scope.editSelectedShippingAddress = function() {
    if (!$scope.shoppingCart.ShippingAddress || !$scope.shoppingCart.ShippingAddress.Id) {
      return;
    }

    $.fancybox({
      maxWidth: 720,
      maxHeight: 480,
      padding: 0,
      href: "/Carrinho/NovoEndereco?address=" + $scope.shoppingCart.ShippingAddress.Id,
      type: 'iframe',
      beforeShow: function() {
        this.maxWidth = 720;
        this.maxHeight = 480;
        this.width = $(".fancybox-iframe").contents().find("html").width();
        this.height = $(".fancybox-iframe").contents().find("html").height();
      },
      afterClose : function() {
        return;
      }
    });
  };

  const showPointsPurchasePopup = function() {
    sendEvent('Comprar Pontos', 'Click', 'Completar Pontos');
    $.fancybox({
      maxWidth: 720,
      maxHeight: 480,
      padding: 0,
      href: "/ComprarPontos/Carrinho",
      type: 'iframe',
      beforeShow: function() {
        this.maxWidth = 720;
        this.maxHeight = 480;
        this.width = $(".fancybox-iframe").contents().find("html").width();
        this.height = $(".fancybox-iframe").contents().find("html").height();
      },
      afterClose : function() {
        if (window.hasCompletedBalancePurchase) {
          $scope.startPurchase();
        } else {
          sendEvent('Comprar Pontos', 'Cancelado', 'Completar Pontos');
        }
        return;
      }
    });
  };

  $scope.showPointsPurchasePopup = function() {
    showPointsPurchasePopup();
  };

  $scope.applyDiscountCoupon = function() {
    if(!$scope.discountCoupon.coupon || $scope.discountCoupon.coupon == "") {
      $scope.discountCoupon = {};
      showError(true, "Cupom de desconto inválido.");
      return;
    }
    $scope.loadingCart = true;
    carrinhoService.applyDiscountCoupon($scope.discountCoupon.coupon)
      // .then(function(response) { return response.data; }) não precisa por causa do $promise
      .then(function(discountResult) {
        $scope.loadingCart = false;
        if(discountResult.Success) {
          loadViewFromCart(discountResult.Return);
        } else {
          showError(true, discountResult.Error);
          startViewEvents();
        }
      })
      .catch(function(resp) {
        $scope.loadingCart = false;
        showError(stat, "Ocorreu um erro ao aplicar o cupom, por favor, atualize a página.");
        startViewEvents();
      });
  };

  const recordCartButtonClick = function() {
    sendEvent('Carrinho', 'Click', 'Finalizar');
  };

  const showSecurityTokenPopup = function() {
    $.fancybox({
      maxWidth: 720,
      maxHeight: 340,
      padding: 0,
      href: '/Carrinho/TokenPedido',
      type: 'iframe',
      beforeShow: function() {
        this.maxWidth = 720;
        this.maxHeight = 340;
        this.width = $(".fancybox-iframe").contents().find("html").width();
        this.height = $(".fancybox-iframe").contents().find("html").height();
      },
      afterClose: function() {
        if (canSubmit()) {
          submitForm();
        } else {
          showError(true, 'É necessário confirmar o código de segurança para prosseguir com o pedido.');
        }
      }
    });
  };

  const canSubmit = function() {
    return $scope.requiresTokenToRedeem === false || window.canSubmit;
  };

  const submitForm = function() {
    addLoaderToButton();
    recordCartButtonClick();

    const frmCart = $('#frmCart');
    frmCart.attr('action', '/Pedido/IniciarResgate');
    frmCart.submit();
  };

  $scope.startPurchase = function() {
    if (canSubmit()) {
      submitForm();
    } else {
      showSecurityTokenPopup();
    }
  };

  $scope.init = function(requiresTokenToRedeem) {
    loadShippingAddresses(true);
    loadShippingCart();
    $scope.requiresTokenToRedeem = requiresTokenToRedeem === true;
    window.canSubmit = requiresTokenToRedeem === false;
  }
}]);
