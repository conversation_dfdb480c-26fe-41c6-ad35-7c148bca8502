angular.module("platformApp", ['footerModule', 'ngLoaderContainer', 'ngSanitize'])
  .service('institutionalService', ['$http', function($http) {
    this.loadShippingPolicy = function() {
      return $http.get("/Politica/CarregaPoliticaEntrega");
    }
  }])
  .service('pointsPurchaseService', ['$http', function($http) {
    this.precisaCompletarSaldo = function() {
      return $http.get("/ComprarPontos/PrecisaComplementarSaldo");
    };
  }])
  .controller('orderCtrl', ['institutionalService', 'pointsPurchaseService', '$sce', '$scope', function(catalogService, pointsPurchaseService, $sce, $scope) {
    $scope.loadingPolicy = false;
    $scope.canSubmit = false;
    $scope.accepted = false;

    const loadShippingPolicy = function() {
      $scope.loadingPolicy = true;
      catalogService.loadShippingPolicy()
        .then(function(response) { return response.data; })
        .then(function(retorno) {
          $scope.loadingPolicy = false;
          if (retorno.Success) {
            $scope.shippingPolicy = $sce.trustAsHtml(retorno.Return.content);
            $scope.contentId = retorno.Return.contentId;
            $scope.version = retorno.Return.version;
          }
          $.fn.initScrollbar(".scroll");
        })
        .catch(function(err) {
          $scope.loadingPolicy = false;
          console.log(err);
        })
    };

    const removeMainButtonLoader = function() {
      removeLoaderFromButton('#btn-buy', 'Finalizar Compra');
    };

    $scope.onAcceptanceToggle = function() {
      $scope.accepted = !$scope.accepted;
      $scope.canSubmit = $scope.accepted;
    };

    const validateReceiver = function() {
      if (!$('#cpf').val()) {
        showWarningSwal('Preencha o CPF do recebedor.');
        return false;
      }
      if (!$('#nome').val()) {
        showWarningSwal('Preencha o nome do recebedor.');
        return false;
      }

      if (!$('#celular').val()) {
        showWarningSwal('Preencha o celular do recebedor.');
        return false;
      }
      if (!$('#email').val()) {
        showWarningSwal('Preencha o e-mail do recebedor.');
        return false;
      }
      return true;
    };

    $scope.confirmOrder = function() {
      if (!$scope.accepted) {
        showWarningSwal('É necessário aceitar os termos do regulamento.');
        removeMainButtonLoader();
        return false;
      }

      if (!validateReceiver()) {
        removeMainButtonLoader();
        return false;
      }

      sendEvent('Processo Compra', 'Click', 'Finalizar');
      if ($scope.sendingOrder) {
        return;
      }
      $scope.sendingOrder = true;
      addLoaderToButton('#btn-buy');
      $('#cartForm').submit();
    };

    const showPointsPurchasePopop = function() {
      sendEvent('Comprar Pontos', 'Click', 'Completar Pontos');
      $.fancybox({
        maxWidth: 720,
        maxHeight: 480,
        padding: 0,
        href: "/ComprarPontos/Carrinho",
        type: 'iframe',
        beforeShow: function() {
          this.maxWidth = 720;
          this.maxHeight = 480;
          this.width = $(".fancybox-iframe").contents().find("html").width();
          this.height = $(".fancybox-iframe").contents().find("html").height();
        },
        afterClose : function() {
          if (window.hasCompletedBalancePurchase) {
            $scope.confirmOrder();
          } else {
            sendEvent('Comprar Pontos', 'Cancelado', 'Completar Pontos');
            removeMainButtonLoader();
          }
          return;
        }
      });
    };

    $scope.showPointsPurchasePopup = function() {
      addLoaderToButton('#btn-buy');
      // verifica se precisa completar o saldo
      pointsPurchaseService.precisaCompletarSaldo()
        .then(function(result) { return result.data; })
        .then(result => {
          if (result.success) {
            if (result.return) {
              showPointsPurchasePopop();
            } else {
              $scope.confirmOrder();
            }
          } else {
            showPointsPurchasePopop();
          }
        })
        .catch(function(error) {
          showErrorSwal('Ocorreu um problema durante a validação do carrinho, por favor, atualize a página.');
          removeMainButtonLoader();
        });
    };

    $scope.init = function() {
      sendEvent('Processo Compra', 'View', 'Finalizar');

      $("#timezoneoffset").val((new Date()).getTimezoneOffset());
      try {
        $("#timezone").val(Intl.DateTimeFormat().resolvedOptions().timeZone);
      } catch (ex) { }

      loadShippingPolicy();
    };
  }]);

