angular.module("platformApp", ['footerModule', 'ngLoaderContainer', 'ngResource', 'ui.utils.masks'])
.factory('httpRequestInterceptor', ['$q', '$window', httpRequestInterceptor])
.config(['$httpProvider', function($httpProvider) {
  $httpProvider.interceptors.push('httpRequestInterceptor');
}])
.service('OrderService', ['$http', function($http) {
	const config = { headers : { 'Content-Type': 'application/json' } };
	this.getOrder = function(token) {
		return $http.get("/pedidofabricante/" + token);
	};
	this.price = function(data) {
		return $http.put("/pedidofabricante/precificar", data, config);
	};
}])
.controller('orderCtrl', ['OrderService', 'logger', '$scope', '$injector', '$timeout', '$filter', function (service, logger, $scope, $injector, $timeout, $filter) {
	$scope.loading = false;
	$scope.sending = false;
	$scope.messageClass = false;
	$scope.errorMessage = null;

	const clearMessage = function() {
		$scope.errorMessage = null;
	};

	const showErrorMessage = function(message) {
		$scope.messageClass = 'validation-summary-errors';
		$scope.errorMessage = message;
	};

	const showInfoMessage = function(message) {
		$scope.messageClass = 'validation-summary-success';
		$scope.errorMessage = message;
	};

	const loadFactoryOrder = function(token) {
		if (!token) return;
		$scope.loading = true;
		service.getOrder(token)
			.then(function(response) { return response.data; })
			.then(function(orderResult) {
				$scope.loading = false;
				if (orderResult.Success) {
					$scope.factoryOrder = orderResult.Return;
				} else {
					showErrorMessage(orderResult.Error);
				}
			})
			.catch(function(err) {
				$scope.loading = false;
				showErrorMessage('Não foi possível carregar o pedido do fabricante, por favor, tente novamente.');
			});
	};

	const priceItems = function() {
		if (!$scope.factoryOrder || !$scope.factoryOrder.CanBePriced) {
			showWarningSwal('Aviso', 'Pedido não está disponível para precificar.');
			return;
		}

		const itemsToPrice = $scope.factoryOrder.Items ? $scope.factoryOrder.Items.filter(function(i) { return i.CanBePriced }) : null;
		if (!itemsToPrice || !itemsToPrice.length) {
			showWarningSwal('Aviso', 'Não foi encontrado nenhum item para precificar.');
			return;
		}
		$scope.sending = true;
		const payload = {
			orderId: $scope.factoryOrder.OrderId,
			itemGrouperId: $scope.factoryOrder.ItemGrouperId,
			items: itemsToPrice.map(function(item) {
				return {
					productId: item.ProductId,
					skuId: item.SkuId,
					quantity: item.Quantity,
					unitPrice: item.UnitPrice
				};
			})
		};
		service.price(payload)
			.then(function(response) { return response.data; })
			.then(function(priceResult) {
				$scope.sending = false;
				if (priceResult.Success) {
					showInfoMessage('Itens foram precificados com sucesso.');
				} else {
					showErrorMessage(priceResult.Error);
				}
			})
			.catch(function(err) {
				$scope.sending = false;
				showErrorMessage('Não foi possível precificar os itens do pedido, por favor, tente novamente.');
			});
	};

	$scope.onFormSubmit = function() {
		clearMessage();
		priceItems();
	};

	$scope.init = function() {
		const token = $('#token').val();
		loadFactoryOrder(token);
	};
}]);