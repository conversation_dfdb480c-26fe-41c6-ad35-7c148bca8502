﻿angular.module("platformApp", ['footerModule', 'platformComponents', 'ngLoaderContainer', 'ngResource', 'ui.utils.masks'])
  .factory('httpRequestInterceptor', ['$q', '$window', httpRequestInterceptor])
  .config(['$httpProvider', function ($httpProvider) {
    $httpProvider.interceptors.push('httpRequestInterceptor');
  }])
  .factory('DepartamentoEndpoint', ['$resource', function ($resource) {
    return $resource("/Departamento/CarregaProdutos", {}, {
      pesquisa: { method: "POST", data: {}, isArray: false }
    });
  }])
  .service('DepartmentService', ['DepartamentoEndpoint', '$http', function (departamentoEndpoint, $http) {
    this.carregaMediaBox = function (id) {
      return $http.get("/Departamento/CarregaMediaBox/" + id);
    };

    this.searchProducts = function (id, c, sb, p, fb, cr, v, srt, pd, pa, f, t) {
      return departamentoEndpoint.pesquisa({ d: [id], c: c, sb: sb, p: p, f: fb, cr: cr, v: v, srt: srt, pd: pd, pa: pa, fr: f, tk: t }).$promise;
    };

    this.getFeaturedProducts = function (id) {
      return $http.get("/Departamento/ProdutosMaisResgatados/" + id);
    };

    this.loadProductsOffers = function (id) {
      return $http.get("/Departamento/ProdutosOfertas/" + id);
    };

    this.loadSpecialShop = function () {
      return $http.get("/Catalog/randomSpecialShop");
    };
  }]);

function getParameterByName(name, url) {
  if (!url) url = window.location.href;
  name = name.replace(/[\[\]]/g, "\\$&");
  var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
    results = regex.exec(url);
  if (!results) return null;
  if (!results[2]) return '';
  return decodeURIComponent(results[2].replace(/\+/g, " "));
}
