angular.module("platformApp")
.controller('categoryCtrl', ['DepartmentService', 'logger', '$scope', '$timeout', function(departmentService, logger, $scope, $timeout) {
	var departmentId = 0;
	var categorias = [];

	$scope.mostBuyedProducts = [];
	$scope.offersProducts = [];

	// paginação
	$scope.total = 0;
	$scope.fromItem = 0;
	$scope.pageSize = 40;
	$scope.currentPage = 1;
	$scope.firstPage = 1;
	$scope.lastPage = 5;
	$scope.showPrevious = false;
	$scope.showNext = false;
	$scope.pageLinks = [];

  // busca
  $scope.query = "";
  var selectedPartners = [];
  var selectedSubcategories = [];
  var selectedManufacturers = [];
  var selectedColors = [];
  var selectedVoltages = [];

  $scope.subcategories = [];
  $scope.partners = [];
  $scope.products = [];
  $scope.manufacturers = null;
  $scope.colors = null;
  $scope.voltages = null;
  $scope.hasProducts = true;
  var ptMin = null;
  var ptMax = null;

  $scope.loadingProds = false;
  $scope.loadingMostBuyedProducts = false;
  $scope.loadingProdsVistos = false;
  $scope.loadingOffers = false;

  const removeItem = function(arr, it) {
  	if(arr && arr.length > 0) {
  		var i = arr.indexOf(it);
  		if(i >= 0)
  			arr.splice(i, 1);
  	}
  };

  const updatePagination = function(totalRegistros) {
  	$scope.total = totalRegistros;

	  // calculo da primeira pagina
  	if($scope.currentPage > 1) {
  		$scope.showPrevious = true;
  		if($scope.currentPage <= 3)
  			$scope.firstPage = $scope.currentPage - ($scope.currentPage - 1);
  		else
  			$scope.firstPage = $scope.currentPage - 2;
  	} else {
  		$scope.showPrevious = false;
			$scope.firstPage = 1;
		}

		// calculo da ultima pagina
  	if($scope.total > $scope.currentPage * $scope.pageSize) {
  		$scope.showNext = true;
	  	if($scope.total > ($scope.currentPage + 1) * $scope.pageSize)
	  		$scope.lastPage = $scope.currentPage + 2;
  		else
	  		$scope.lastPage = $scope.currentPage + 1;
	  } else {
  		$scope.showNext = false;
	  	$scope.lastPage = $scope.currentPage;
	  }

	  var leftItems = $scope.lastPage - $scope.firstPage + 1;

	  if(leftItems < 5) {
	  	if($scope.showNext) {
	  		$scope.lastPage += 5 - leftItems;
	  		var lastPageAvailable = Math.ceil($scope.total / $scope.pageSize);
	  		if($scope.lastPage > lastPageAvailable) {
	  			leftItems = $scope.lastPage - lastPageAvailable;
  				$scope.lastPage = lastPageAvailable;
  				if($scope.firstPage > 1) {
  					$scope.firstPage -= leftItems;
  				}
	  		}
	  	} else {
	  		$scope.firstPage -= 5 - leftItems;
	  	}
  		if($scope.firstPage < 1)
  			$scope.firstPage = 1;
	  }

  	$scope.pageLinks = [];
  	for(var i = $scope.firstPage; i <= $scope.lastPage; i++) {
  		$scope.pageLinks.push(i);
  	}
  };

  const getItemStartFromPage = function(page) {
  	if(page > 1)
  		return (page - 1) * $scope.pageSize;
  	else
  		return 0;
  };

  $scope.goToPage = function(page) {
  	$scope.currentPage = page;
  	$scope.fromItem = getItemStartFromPage(page);
  	searchProducts();
  };

	const searchProducts = function() {
		$scope.loadingProds = true;

		departmentService.searchProducts(departmentId, categorias, selectedSubcategories, selectedPartners,
				selectedManufacturers, selectedColors, selectedVoltages, $scope.sortBy, ptMin, ptMax, $scope.fromItem, $scope.pageSize)
			// .then(function(response) { return response.data; }) não precisa por causa do $promise
			.then(function(retornoPesquisa) {
				if(retornoPesquisa.Success && !!retornoPesquisa.Return && retornoPesquisa.Return) {
					if(selectedPartners.length == 0) {
					  $scope.partners = retornoPesquisa.Return.Parters;
					}
					if(selectedSubcategories.length == 0) {
				  	$scope.subcategories = retornoPesquisa.Return.Subcategories;
					}
					if(selectedManufacturers.length == 0) {
				  		$scope.manufacturers = retornoPesquisa.Return.Manufacturers;
					}
					if(selectedColors.length == 0) {
				  		$scope.colors = retornoPesquisa.Return.Colors;
					}
					if(selectedVoltages.length == 0) {
				  		$scope.voltages = retornoPesquisa.Return.Voltages;
					}

				  $scope.products = retornoPesquisa.Return.Result;
				  $scope.hasProducts = $scope.products && $scope.products.length > 0;

				  if($scope.hasProducts == true) {
				  	updatePagination(retornoPesquisa.Return.Total)
					  $timeout(function() {
							$scope.$evalAsync(function() {
								$.fn.initBlazy('.list-products .photo img', 'blazy-src');
								$.fn.initBlazy('.ranking img', 'blazy-src');
							});
						});
					}
				} else {
					$scope.products = [];
					$scope.hasProducts = false;
					logger.log(retornoPesquisa.Error);
				}
				$scope.loadingProds = false;
			})
			.catch(function(resp) {
				$scope.loadingProds = false;
				logger.logResponse(resp);
			});
	};

  const loadSpecialShop = function() {
  	departmentService.loadSpecialShop()
			.then(function(response) { return response.data; })
			.then(function(lojaResponse) {
				if(lojaResponse.Success) {
					$scope.lojaEspecial = lojaResponse.Return;
					$timeout(function() {
						$scope.$evalAsync(function() {
							$.fn.initBlazy('.loja-especial img', 'blazy-src');
						});
					}, 50);
				} else {
					$scope.lojaEspecial = null;
					logger.lhog(lojaResponse.Error);
				}
			})
			.catch(function(response) {
				$scope.lojaEspecial = null;
				logger.logResponse(response);
			});
  };

  const applyFilters = function() {
  	$scope.fromItem = 0;
  	searchProducts();
  };

  $scope.filtrarPontos = function(pontosMin, pontosMax) {
    ptMin = pontosMin;
    ptMax = pontosMax;
    applyFilters();
  };

	function toggleSideFilter(currentFilters, item, filterName) {
		if(item.isSelected == true) {
  		removeItem(currentFilters, item.Description);
  		item.isSelected = false;
  	} else {
  		currentFilters.push(item.Description);
  		item.isSelected = true;
			if (filterName && item.Description)
				sendEvent('Catálogo', 'Filtro - ' + filterName, item.Description);
  	}
	  applyFilters();
	}

  $scope.toggleSubcategory = function(cat) {
		toggleSideFilter(selectedSubcategories, cat, 'Subcategoria');
  };

  $scope.togglePartner = function(parceiro) {
		toggleSideFilter(selectedPartners, parceiro, 'Parceiro');
  };

  $scope.toggleManufacturer = function(fabricante) {
		toggleSideFilter(selectedManufacturers, fabricante, 'Fabricante');
  };

  $scope.toggleColor = function(cor) {
		toggleSideFilter(selectedColors, cor, 'Cor');
  };

  $scope.toggleVoltage = function(voltagem) {
		toggleSideFilter(selectedVoltages, voltagem, 'Voltagem');
  };

  $scope.changePageSize = function(pageSize) {
  	$scope.pageSize = pageSize;
  	applyFilters();
	};

	$scope.changeSortFilter = function() {
		applyFilters();
	};

	$scope.initCategoria = function(id, cat, subcat) {
		departmentId = id;
		categorias.push(cat);
		if (subcat && subcat.length > 0) {
			$scope.subcategories.push({isSelected: true, Description: subcat});
			selectedSubcategories.push(subcat);
		}

		var sc = getParameterByName('sc');
		if(sc && sc.length > 0) {
			selectedSubcategories.push(sc);
			$scope.subcategories.push({Descricao: sc, isSelected: true});
		}

		searchProducts();
		loadSpecialShop();
	}
}]);

function mudaPageSize() {
	const scope = angular.element(document.getElementById('pageItens')).scope();
	scope.$apply(function() {
		scope.changePageSize(document.getElementById('pageItens').value);
	});
}
