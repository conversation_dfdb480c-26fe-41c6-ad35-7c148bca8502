function searchProducts() {
  var scope = angular.element(document.getElementById('filtroPontos')).scope();
  scope.$apply(function () {
    scope.searchOnClick(document.getElementById('headerQueryInput').value);
  });
}

function mudaPageSize() {
  var scope = angular.element(document.getElementById('filtroPontos')).scope();
  scope.$apply(function () {
    scope.changePageSize(document.getElementById('pageItens').value);
  });
}
angular.module("platformApp")
  .controller('specialStoreCtrl', ['catalogService', 'logger', '$scope', '$timeout', function (catalogService, logger, $scope, $timeout) {
    $scope.lojas = [];
    $scope.showSpecialStores = false;
    $scope.loadingSpecialStores = false;
    $scope.showSpecialShopAsFeaturedInHomePage = false;

    const homeLayoutSettings = function () {
      catalogService.homeLayoutSettings()
        .then(function (response) {
          $scope.showSpecialShopAsFeaturedInHomePage = response.data.Return;
        })
        .catch(function (err, status) {
          logger.log(err, status);
        });
    }

    const loadSpecialStores = function () {
      $scope.showSpecialStores = true;
      $scope.loadingSpecialStores = true;
      catalogService.loadSpecialStores()
        .then(function (response) { return response.data; })
        .then(function (jsonResponse) {
          if (jsonResponse.Success) {
            const specialshops = jsonResponse.Return;
            $scope.loadingSpecialStores = false;
            if (!specialshops || specialshops.length == 0) {
              $scope.showSpecialStores = false;
              return;
            }

            const specialShopsInGrid = specialshops
              .filter(specialShop => specialShop.ViewParametrization != null
                && specialShop.ViewParametrization.showAsFeatured
                && specialShop.ViewParametrization.presentationType === 'Grid'
              );
            for (const specialShop of specialShopsInGrid) {
              if (!specialShop.ViewParametrization || !specialShop.ViewParametrization.showAsFeatured) {
                specialShop.LinkBannerImageUrl = `${specialShop.LinkBannerImageUrl}/450/450/crop`;
                specialShop.styleClass = 'col-md-4 col-sm-4 col-xs-12 grid-item';
                continue;
              }
              let width, size;
              switch (specialShop.ViewParametrization.gridItemSize) {
                case 100:
                  width = '1135';
                  size = '12';
                  break;
                case 75:
                  width = '842';
                  size = '9';
                  break;
                case 66:
                  width = '745';
                  size = '8';
                  break;
                case 50:
                  width = '552';
                  size = '6';
                case 33:
                  width = '356';
                  size = '4';
                  break;
                default:
                  width = '303';
                  size = '3';
                  break;
              }
              specialShop.LinkBannerImageUrl = `${specialShop.LinkBannerImageUrl}/${width}/180/crop`;
              specialShop.styleClass = `col-md-${size} col-sm-${size} col-xs-12 grid-item`;

            }
            specialShopsInGrid.sort((a, b) => a.ViewParametrization.gridItemPosition - b.ViewParametrization.gridItemPosition);
            $scope.specialShopsInGrid = specialShopsInGrid;
            $scope.showSpecialStoresGrid = specialShopsInGrid.length > 0;

            $scope.specialShopInCards = specialshops
              .filter(specialShop => specialShop.ViewParametrization != null
                && specialShop.ViewParametrization.showAsFeatured
                && specialShop.ViewParametrization.presentationType === 'Card'
              )
              .sort((a, b) => a.ViewParametrization.gridItemPosition - b.ViewParametrization.gridItemPosition);
            $scope.showSpecialStoresCards = $scope.specialShopInCards.length > 0;

            $scope.specialShop = specialshops.filter(specialShop => !specialShop.ViewParametrization || !specialShop.ViewParametrization.showAsFeatured);
            $scope.hasAnyDefaultSpecialStore = $scope.specialShop.length > 0;

            $timeout(function () {
              if ($scope.showSpecialStoresGrid) {
                $scope.$evalAsync(function () {
                  startBLazyLoading(".special-shop-feature-grid .grid-item img");
                });
              }
              if ($scope.showSpecialStoresCards) {
                $scope.$evalAsync(function () {
                  startBLazyLoading(".special-shop-feature-card .card-item img");
                });
              }
              if ($scope.hasAnyDefaultSpecialStore) {
                $scope.$evalAsync(function () {
                  startBLazyLoading("article.list-banners.lojas .slider img");
                  $.fn.initSlider(".list-banners");
                });
              }
            }, 500);
          } else {
            logger.log(jsonResponse.Error);
            $scope.showSpecialStores = false;
            $scope.loadingSpecialStores = false;
          }
        })
        .catch(function (err, status) {
          logger.log(err, status);
          $scope.showSpecialStores = false;
          $scope.loadingSpecialStores = false;
        });
    };

    homeLayoutSettings();
    loadSpecialStores();
  }])
  .controller('homeProductsCtrl', ['$scope', '$timeout', 'logger', 'catalogService', function ($scope, $timeout, logger, catalogService) {
    $scope.products = [];
    $scope.loadingProds = false;
    $scope.hasProducts = false;

    const getFeaturedProducts = function () {
      $scope.loadingProds = true;
      catalogService.getFeaturedProducts()
        .then(function(response) { return response.data; })
        .then(function(response) {
          $scope.loadingProds = false;
          if (response.Success) {
            $scope.products = response.Return;
            $scope.hasProducts = !$scope.products || !$scope.products.length;
            $timeout(function () {
              $scope.$evalAsync(function () {
                $.fn.initBlazy("article.list-products.mais-desejados .photo img", 'blazy-src');
                $.fn.initBlazy('.ranking img', 'blazy-src');
              });
            }, 50);
          } else {
            $scope.hasProducts = false;
            $scope.loadingProds = false;
            logger.log(response.Error);
          }
        })
        .catch(function (resp, status) {
          $scope.loadingProds = false;
          logger.log(resp, status);
        });
    };

    getFeaturedProducts();
  }])
  .controller('vouchersCtrl', ['$scope', '$timeout', 'logger', 'catalogService', function ($scope, $timeout, logger, catalogService) {
    $scope.products = [];
    $scope.showVouchers = false;
    $scope.loadingVouchers = false;

    var getFeaturedVouchers = function () {
      $scope.showVouchers = true;
      $scope.loadingVouchers = true;
      catalogService.getFeaturedVouchers()
        .then(function (response) { return response.data; })
        .then(function (response) {
          if (response.Success) {
            $scope.loadingVouchers = false;
            $scope.products = response.Return;
            if (!$scope.products || !$scope.products.length) {
              $scope.showVouchers = false;
              return;
            }
            $timeout(function () {
              $scope.$evalAsync(function () {
                $.fn.initBlazy('.list-products.vouchers .photo img', 'blazy-src');
                $.fn.initBlazy('.ranking img', 'blazy-src');
                $.fn.initSlider('.vouchers');
              });
            }, 50);
          } else {
            logger.log(response.Error);
            $scope.loadingVouchers = false;
            $scope.showVouchers = false;
          }
        })
        .catch(function (resp, status) {
          logger.log(resp, status);
          $scope.loadingVouchers = false;
          $scope.showVouchers = false;
        });
    };

    getFeaturedVouchers();
  }])
  .controller('homeCtrl', ['catalogService', 'logger', '$scope', '$timeout', function (catalogService, logger, $scope, $timeout) {
    $scope.mediaBoxHome = [];
    $scope.mediaboxProducts = [];
    $scope.showSpecialShopAsFeaturedInHomePage = false;

    const homeLayoutSettings = function () {
      catalogService.homeLayoutSettings()
        .then(function (response) {
          $scope.showSpecialShopAsFeaturedInHomePage = response.data.Return;
        })
        .catch(function (err, status) {
          logger.log(err, status);
        });
    }

    const initMediabox = function () {
      $scope.$evalAsync(function () {
        for (var i in $scope.mediaBoxHome) {
          var mediaBox = $scope.mediaBoxHome[i];
          $scope.mediaBoxHome[i].Show = true;
          if (mediaBox.Communication && mediaBox.Location == 'Modal') {
            mediaBox.Show = false;
            $('#cname').val(mediaBox.Id);
            if (!mediaBox.AlwaysShowModal && getCookieByName(mediaBox.Id) == '1') {
              continue;
            } else {
              $('#modal-title').text(mediaBox.Name);
              $('#modal-text').html(mediaBox.Message);
              $.fancybox.open('#modal-communication', {
                afterClose: function () {
                  document.cookie = $('#cname').val() + '=1';
                  sendEvent('Comunicações', 'Visualização', mediaBox.Name);
                }
              });
            }
          }
        }
        $scope.mediaBoxHome = $scope.mediaBoxHome.filter(function (m) { return m.Show == true; });

        $timeout(function () {
          $.fn.initBlazy('.mediabox-products .photo img', 'blazy-src');

          $('.mediabox').nivoSlider({
            controlNav: true,
            controlNavThumbs: false,
            prevText: 'Anterior',
            nextText: 'Próximo',
            pauseTime: 10000,
            afterLoad: () => {
              $('.nivoSlider .nivo-caption').attr('class', 'nivo-caption animate');
              if ($('aside.sidebar').length & $(window).width() > 900) {
                $getBCHeight = $('.breadcrumbs').outerHeight() + $('.breadcrumbs').offset().top
                $getSBTop = $('aside.sidebar').offset().top - 32
                $('aside.sidebar').css('margin-top', $getBCHeight - $getSBTop);
              }
            },
            beforeChange: () => { $('.nivoSlider .nivo-caption').attr('class', 'nivo-caption animateOut'); },
            afterChange: () => { $('.nivoSlider .nivo-caption').attr('class', 'nivo-caption animate'); }
          });
        }, 50);
      });
    };

    const carregaMediaBoxHome = function () {
      catalogService.carregaMediaBoxHome()
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          if (retorno.Success) {
            if (retorno.Return && retorno.Return.length > 0) {
              retorno.Return.forEach(mb => {
                if (mb.ContentType == 'BANNER' || mb.ContentType == 'PRODUCT' && mb.FeaturedProduct) {
                  $scope.mediaBoxHome.push(mb);
                  if (mb.ContentType == 'PRODUCT') {
                    mb.Link = '/Produto/Detalhes/' + mb.FeaturedProduct.Url;
                    $scope.mediaboxProducts.push(mb.FeaturedProduct);
                  }
                }
              });
              initMediabox();
            }
          } else {
            logger.log(retorno.Error);
          }
        })
        .catch(function (response) {
          $scope.loadingProds = false;
          logger.log(response.data, response.status);
        });
    };

    carregaMediaBoxHome();
    homeLayoutSettings();
  }])
  .controller('searchCtrl', ['catalogService', 'logger', '$scope', '$timeout', '$sanitize', function (catalogService, logger, $scope, $timeout, $sanitize) {
    var query = getParameterByName('q');
    query = $sanitize(query);

    // paginação
    $scope.total = 0;
    $scope.fromItem = 0;
    $scope.pageSize = 40;
    $scope.currentPage = 1;
    $scope.firstPage = 1;
    $scope.lastPage = 5;
    $scope.showPrevious = false;
    $scope.showNext = false;
    $scope.pageLinks = [];

    $scope.loadingProds = false;

    // busca
    $scope.query = query || "";
    var selectedPartners = [];
    var selectedDepartaments = [];
    var selectedCategories = [];
    var selectedSubcategories = [];
    var selectedManufacturers = [];
    var selectedColors = [];
    var selectedVoltages = [];
    var ptMin = null;
    var ptMax = null;

    $scope.lojaEspecial = null;
    $scope.departments = [];
    $scope.categorias = [];
    $scope.subcategories = [];
    $scope.partners = [];
    $scope.products = [];
    $scope.manufacturers = null;
    $scope.colors = null;
    $scope.voltages = null;
    $scope.hasProducts = true;

    const removeItem = function (arr, it) {
      if (arr && arr.length > 0) {
        var i = arr.indexOf(it);
        if (i >= 0)
          arr.splice(i, 1);
      }
    };

    const updatePagination = function (totalRegistros) {
      $scope.total = totalRegistros;

      // calculo da primeira pagina
      if ($scope.currentPage > 1) {
        $scope.showPrevious = true;
        if ($scope.currentPage <= 3)
          $scope.firstPage = $scope.currentPage - ($scope.currentPage - 1);
        else
          $scope.firstPage = $scope.currentPage - 2;
      } else {
        $scope.showPrevious = false;
        $scope.firstPage = 1;
      }

      // calculo da ultima pagina
      if ($scope.total > $scope.currentPage * $scope.pageSize) {
        $scope.showNext = true;
        if ($scope.total > ($scope.currentPage + 1) * $scope.pageSize)
          $scope.lastPage = $scope.currentPage + 2;
        else
          $scope.lastPage = $scope.currentPage + 1;
      } else {
        $scope.showNext = false;
        $scope.lastPage = $scope.currentPage;
      }

      var leftItems = $scope.lastPage - $scope.firstPage + 1;

      if (leftItems < 5) {
        if ($scope.showNext) {
          $scope.lastPage += 5 - leftItems;
          var lastPageAvailable = Math.ceil($scope.total / $scope.pageSize);
          if ($scope.lastPage > lastPageAvailable) {
            leftItems = $scope.lastPage - lastPageAvailable;
            $scope.lastPage = lastPageAvailable;
            if ($scope.firstPage > 1) {
              $scope.firstPage -= leftItems;
            }
          }
        } else {
          $scope.firstPage -= 5 - leftItems;
        }
        if ($scope.firstPage < 1)
          $scope.firstPage = 1;
      }

      $scope.pageLinks = [];
      for (var i = $scope.firstPage; i <= $scope.lastPage; i++) {
        $scope.pageLinks.push(i);
      }
    };

    const getItemStartFromPage = function (page) {
      if (page > 1)
        return (page - 1) * $scope.pageSize;
      else
        return 0;
    };

    const loadSpecialShop = function () {
      catalogService.loadSpecialShop()
        .then(function (response) { return response.data; })
        .then(function (lojaResponse) {
          if (lojaResponse.Success) {
            $scope.lojaEspecial = lojaResponse.Return;
            $timeout(function () {
              $scope.$evalAsync(function () {
                startBLazyLoading(".loja-especial img");
              });
            }, 10);
          } else {
            logger.log(lojaResponse.Error);
          }
        })
        .catch(function (response) {
          $scope.lojaEspecial = null;
          logger.log(response.data, response.status);
        });
    };

    const searchProducts = function (query, from, take) {
      $scope.loadingProds = true;
      sendCustomEvent('pesquisa_catalogo', {
        query: query,
        parceiros: selectedPartners,
        departmentos: selectedDepartaments,
        categorias: selectedCategories,
        subcategorias: selectedSubcategories,
        fabricantes: selectedManufacturers,
        cores: selectedColors,
        voltagens: selectedVoltages,
        pontos_min: ptMin,
        pontos_max: ptMax,
        sort: $scope.sortBy,
        from: from,
        take: take
      });
      catalogService.searchProducts(query, selectedPartners, selectedDepartaments, selectedCategories, selectedSubcategories,
        selectedManufacturers, selectedColors, selectedVoltages, $scope.sortBy, ptMin, ptMax, from, take)
        .then(function (retornoPesquisa) {
          $scope.loadingProds = false;

          if (retornoPesquisa.Success == true && !!retornoPesquisa.Return) {
            if (selectedPartners.length == 0) {
              $scope.partners = retornoPesquisa.Return.Partners;
            }
            if (selectedDepartaments.length == 0) {
              $scope.departments = retornoPesquisa.Return.Departments;
            }
            if (selectedCategories.length == 0) {
              $scope.categorias = retornoPesquisa.Return.Categories;
            }
            if (selectedSubcategories.length == 0) {
              $scope.subcategories = retornoPesquisa.Return.Subcategories;
            }
            if (selectedManufacturers.length == 0) {
              $scope.manufacturers = retornoPesquisa.Return.Manufacturers;
            }
            if (selectedColors.length == 0) {
              $scope.colors = retornoPesquisa.Return.Colors;
            }
            if (selectedVoltages.length == 0) {
              $scope.voltages = retornoPesquisa.Return.Voltages;
            }

            $scope.products = retornoPesquisa.Return.Result;
            $scope.hasProducts = $scope.products && $scope.products.length > 0;

            if ($scope.hasProducts == true) {
              updatePagination(retornoPesquisa.Return.Total)
              $timeout(function () {
                $scope.$evalAsync(function () {
                  $.fn.initBlazy('article.list-products .products .photo img', 'blazy-src');
                  $.fn.initBlazy('.ranking img', 'blazy-src');
                });
              });
            }
          } else {
            $scope.hasProducts = false;
            $scope.products = [];
            if (retornoPesquisa.Error)
              logger.log(retornoPesquisa.Error);
          }
        })
        .catch(function (response) {
          $scope.loadingProds = false;
          logger.logResponse(response);
        });
    };

    const applyFilters = function () {
      $scope.fromItem = 0;
      $scope.currentPage = 1;
      searchProducts($scope.query, $scope.fromItem, $scope.pageSize);
    };

    function toggleSideFilter(currentFilters, item, filterName) {
      if (item.isSelected == true) {
        removeItem(currentFilters, item.Description);
        item.isSelected = false;
      } else {
        currentFilters.push(item.Description);
        item.isSelected = true;
        if (filterName && item.Description)
          sendEvent('Catálogo', 'Filtro - ' + filterName, item.Description);
      }
      applyFilters();
    }

    $scope.goToPage = function (page) {
      $scope.currentPage = page;
      $scope.fromItem = getItemStartFromPage(page);
      $scope.searchProducts($scope.query, $scope.fromItem, $scope.pageSize);
    };

    $scope.changePageSize = function (pageSize) {
      $scope.pageSize = pageSize;
      $scope.searchProducts($scope.query, $scope.fromItem, $scope.pageSize);
    };

    $scope.searchProducts = function (query) {
      query = $sanitize(query);
      $scope.query = query;
      $("#breadcrumbQuery").html(query);
      sendEvent('Catálogo', 'Pesquisa', query);
      searchProducts(query, $scope.fromItem, $scope.pageSize);
    };

    $scope.searchOnClick = function (query) {
      $scope.fromItem = 0;
      $scope.currentPage = 1;
      $scope.searchProducts(query);
    };

    $scope.toggleDepartment = function (dep) {
      toggleSideFilter(selectedDepartaments, dep, 'Departamento');
    };

    $scope.toggleCategory = function (cat) {
      toggleSideFilter(selectedCategories, cat, 'Categoria');
    };

    $scope.toggleSubcategory = function (cat) {
      toggleSideFilter(selectedSubcategories, cat, 'Subcategoria');
    };

    $scope.togglePartner = function (parceiro) {
      toggleSideFilter(selectedPartners, parceiro, 'Parceiro');
    };

    $scope.toggleManufacturer = function (fabricante) {
      toggleSideFilter(selectedManufacturers, fabricante, 'Fabricante');
    };

    $scope.toggleColor = function (cor) {
      toggleSideFilter(selectedColors, cor, 'Cor');
    };

    $scope.toggleVoltage = function (voltagem) {
      toggleSideFilter(selectedVoltages, voltagem, 'Voltagem');
    };

    $scope.changeSortFilter = function () {
      applyFilters();
    };

    $scope.filtrarPontos = function (pontosMin, pontosMax) {
      ptMin = pontosMin;
      ptMax = pontosMax;
      applyFilters();
    };

    const init = function () {
      loadSpecialShop();
      var form = document.getElementById('headerSearchForm');
      form.removeAttribute("method");
      form.removeAttribute("action");
      document.getElementById('headerSearchButton').setAttribute('onclick', "searchProducts();return false;");
    };

    init();
    $scope.searchProducts(query);
  }]);
