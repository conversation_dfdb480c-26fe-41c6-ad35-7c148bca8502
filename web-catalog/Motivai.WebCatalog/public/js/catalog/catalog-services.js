angular.module("platformApp", ['ngSanitize', 'footerModule', 'platformComponents', 'ngLoaderContainer', 'ngResource', 'ui.utils.masks'])
.factory('httpRequestInterceptor', ['$q', '$window', httpRequestInterceptor])
.config(['$httpProvider', function($httpProvider) {
  $httpProvider.interceptors.push('httpRequestInterceptor');
}])
.factory('CatalogoEndpoint', ['$resource', function($resource) {
	return $resource("/catalog/search", {}, {
		pesquisa: { method: "POST", data: {}, isArray: false}
	});
}])
.service('catalogService', ['CatalogoEndpoint', '$http', function(catalogoEndpoint, $http) {
  this.loadSpecialStores = function() {
  	return $http.get("/catalog/specialShops");
  };

  this.getFeaturedProducts = function() {
  	return $http.get("/catalog/featureHomeProducts");
  };

  this.getFeaturedVouchers = function() {
  	return $http.get("/catalog/featuredVouchers");
  };

  this.carregaMediaBoxHome = function () {
    return $http.get("/catalog/featuredMediaboxes");
  };

  this.searchProducts = function(query, p, d, c, s, f, cr, v, srt, pd, pa, fr, t) {
		return catalogoEndpoint.pesquisa({}, { q: query, p: p, d: d, c: c, sb: s, f: f, cr: cr, v: v, srt: srt, pd: pd, pa: pa, fr: fr, tk: t}).$promise;
  };

  this.loadSpecialShop = function() {
    return $http.get("/catalog/randomSpecialShop");
  };

  this.homeLayoutSettings = function() {
    return $http.get("/catalog/homeLayoutSettings");
  }
}]);
