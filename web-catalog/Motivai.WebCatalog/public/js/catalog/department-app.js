angular.module("platformApp")
  .controller('departmentBannersCtrl', ['DepartmentService', 'logger', '$scope', '$timeout', function (departmentService, logger, $scope, $timeout) {
    var departmentId = 0;

    $scope.mediaBoxes = [];
    $scope.mediaboxProducts = [];

    const initMediabox = function () {
      $timeout(function () {
        $scope.$evalAsync(function () {
          $.fn.initBlazy('.mediabox-products .photo img', 'blazy-src');

          $('.mediabox').nivoSlider({
            controlNav: true,
            controlNavThumbs: false,
            prevText: 'Anterior',
            nextText: 'Próximo',
            pauseTime: 10000,
            afterLoad: () => {
              $('.nivoSlider .nivo-caption').attr('class', 'nivo-caption animate');
              if ($('aside.sidebar').length & $(window).width() > 900) {
                $getBCHeight = $('.breadcrumbs').outerHeight() + $('.breadcrumbs').offset().top
                $getSBTop = $('aside.sidebar').offset().top - 32
                $('aside.sidebar').css('margin-top', $getBCHeight - $getSBTop);
              }
            },
            beforeChange: () => { $('.nivoSlider .nivo-caption').attr('class', 'nivo-caption animateOut'); },
            afterChange: () => { $('.nivoSlider .nivo-caption').attr('class', 'nivo-caption animate'); }
          });

          if ($scope.mediaBoxes.length > 0) {
            $("aside.sidebar").removeAttr("style");
          }
        });
      }, 50);
    };

    const loadMediaboxes = function () {
      $("aside.sidebar").css("margin-top", "0em");

      departmentService.carregaMediaBox(departmentId)
        .then(function (response) { return response.data; })
        .then(function (retorno) {
          if (retorno.Success) {
            if (retorno.Return && retorno.Return.length > 0) {
              retorno.Return.forEach(mb => {
                if (mb.ContentType == 'BANNER' || mb.ContentType == 'PRODUCT' && mb.FeaturedProduct) {
                  $scope.mediaBoxes.push(mb);
                  if (mb.ContentType == 'PRODUCT') {
                    mb.Link = '/Produto/Detalhes/' + mb.FeaturedProduct.Url;
                    $scope.mediaboxProducts.push(mb.FeaturedProduct);
                  }
                }
              });
              initMediabox();
            }
          } else {
            logger.log(retorno.Error);
          }
        })
        .catch(function (response) {
          logger.log(response.data, response.status);
        });
    };

    $scope.init = function (id) {
      departmentId = id;

      loadMediaboxes();
    }
  }])
  .controller('departmentCtrl', ['DepartmentService', 'logger', '$scope', '$timeout', function (departmentService, logger, $scope, $timeout) {
    var departmentId = 0;
    $scope.lojaEspecial = null;
    $scope.hasBuyedProducts = true;
    $scope.mostBuyedProducts = [];
    $scope.temMaisVistos = true;
    $scope.hasOffersProducts = true;
    $scope.offersProducts = [];

    // paginação
    $scope.total = 0;
    $scope.fromItem = 0;
    $scope.pageSize = 20;
    $scope.hasMoreItems = true;

    // busca
    $scope.query = "";
    var selectedPartners = [];
    var selectedCategories = [];
    var selectedSubcategories = [];
    var selectedManufacturers = [];
    var selectedColors = [];
    var selectedVoltages = [];

    $scope.categorias = [];
    $scope.subcategories = [];
    $scope.partners = [];
    $scope.products = [];
    $scope.manufacturers = null;
    $scope.colors = null;
    $scope.voltages = null;
    $scope.hasProducts = true;
    var ptMin = null;
    var ptMax = null;

    $scope.loadingProds = false;
    $scope.loadingMostBuyedProducts = false;
    $scope.loadingProdsVistos = false;
    $scope.loadingOffers = false;
    $scope.fetching = false;

    const removeItem = function (arr, it) {
      if (arr && arr.length > 0) {
        var i = arr.indexOf(it);
        if (i >= 0)
          arr.splice(i, 1);
      }
    };

    const searchProducts = function () {
      if (!$scope.fetching) {
        $scope.loadingProds = true;
      }

      departmentService.searchProducts(departmentId, selectedCategories, selectedSubcategories, selectedPartners, selectedManufacturers, selectedColors,
        selectedVoltages, null, ptMin, ptMax, $scope.fromItem, $scope.pageSize)
        // .then(function(response) { return response.data; }) não precisa por causa do $promise
        .then(function (retornoPesquisa) {
          $scope.loadingProds = false;
          $scope.fetching = false;
          if (retornoPesquisa.Success && !!retornoPesquisa.Return && retornoPesquisa.Return) {
            if (selectedPartners.length == 0) {
              $scope.partners = retornoPesquisa.Return.Partners;
            }
            if (selectedCategories.length == 0) {
              $scope.categorias = retornoPesquisa.Return.Categories;
            }
            if (selectedSubcategories.length == 0) {
              $scope.subcategories = retornoPesquisa.Return.Subcategories;
            }
            if (selectedManufacturers.length == 0) {
              $scope.manufacturers = retornoPesquisa.Return.Manufacturers;
            }
            if (selectedColors.length == 0) {
              $scope.colors = retornoPesquisa.Return.Colors;
            }
            if (selectedVoltages.length == 0) {
              $scope.voltages = retornoPesquisa.Return.Voltages;
            }

            $scope.total = retornoPesquisa.Return.Total;
            if ($scope.fromItem == 0) {
              $scope.products = retornoPesquisa.Return.Result;
            } else {
              $scope.products = $scope.products.concat(retornoPesquisa.Return.Result);
            }
            $scope.hasProducts = $scope.products && $scope.products.length > 0;
            // Atualiza a quantidade de produto carregados
            $scope.fromItem = $scope.fromItem + $scope.pageSize;
            $scope.hasMoreItems = $scope.total > $scope.fromItem;

            if ($scope.hasProducts == true) {
              $timeout(function () {
                $scope.$evalAsync(function () {
                  $.fn.initBlazy('.list-products.vitrine .photo img', 'blazy-src');
                  $.fn.initBlazy('.ranking img', 'blazy-src');
                });
              }, 50);
            }
          } else {
            $scope.products = [];
            $scope.hasProducts = false;
            $scope.hasMoreItems = false;
            logger.log(retornoPesquisa.Error);
          }
        })
        .catch(function (resp) {
          $scope.products = [];
          $scope.hasProducts = false;
          $scope.hasMoreItems = false;
          logger.logResponse(resp);
        });
    };

    const getFeaturedProducts = function () {
      $scope.loadingMostBuyedProducts = true;
      $scope.loadingProdsVistos = true;

      departmentService.getFeaturedProducts(departmentId)
        .then(function (response) { return response.data; })
        .then(function (response) {
          $scope.loadingMostBuyedProducts = false;
          $scope.loadingProdsVistos = false;
          if (response.Success) {
            $scope.mostBuyedProducts = response.Return;

            if ($scope.mostBuyedProducts && $scope.mostBuyedProducts.length > 0) {
              $timeout(function () {
                $scope.$evalAsync(function () {
                  $.fn.initBlazy('.list-products.mais-resgatados .photo img', 'blazy-src');
                  $.fn.initBlazy('.ranking img', 'blazy-src');
                  $.fn.initSlider('.mais-resgatados');
                });
              }, 50);
            } else {
              $scope.hasBuyedProducts = false;
            }
          } else {
            $scope.hasBuyedProducts = false;
            $scope.loadingMostBuyedProducts = false;
            $scope.loadingProdsVistos = false;
            logger.log(response.Error);
          }
        })
        .catch(function (response) {
          $scope.hasBuyedProducts = false;
          $scope.loadingMostBuyedProducts = false;
          $scope.loadingProdsVistos = false;
          logger.logResponse(response);
        });
    };

    const loadProductsOffers = function () {
      $scope.loadingOffers = true;

      departmentService.loadProductsOffers(departmentId)
        .then(function (response) { return response.data; })
        .then(function (response) {
          $scope.loadingOffers = false;
          if (response.Success) {
            $scope.offersProducts = response.Return;

            if ($scope.offersProducts && $scope.offersProducts.length > 0) {
              $timeout(function () {
                $scope.$evalAsync(function () {
                  $.fn.initBlazy('.list-products.ofertas .photo img', 'blazy-src');
                  $.fn.initBlazy('.ranking img', 'blazy-src');
                  $.fn.initSlider('.ofertas');
                });
              }, 10);
            } else {
              $scope.hasOffersProducts = false;
            }
          } else {
            $scope.hasOffersProducts = false;
            logger.log(response.Error);
          }
        })
        .catch(function (response) {
          $scope.loadingOffers = false;
          $scope.hasOffersProducts = false;
          logger.logResponse(response);
        });
    };

    const applyFilters = function () {
      $scope.fromItem = 0;
      searchProducts();
    };

    $scope.filtrarPontos = function (pontosMin, pontosMax) {
      ptMin = pontosMin;
      ptMax = pontosMax;
      applyFilters();
    };

    const loadSpecialShops = function () {
      departmentService.loadSpecialShop()
        .then(function (response) { return response.data; })
        .then(function (lojaResponse) {
          if (lojaResponse.Success) {
            $scope.lojaEspecial = lojaResponse.Return;
            $timeout(function () {
              $scope.$evalAsync(function () {
                startBLazyLoading(".loja-especial img");
              });
            }, 10);
          } else {
            $scope.lojaEspecial = null;
            logger.log(lojaResponse.Error);
          }
        })
        .catch(function (resp) {
          $scope.lojaEspecial = null;
          logger.logResponse(resp);
        });
    };

    $scope.fetchMoreItens = function () {
      $scope.fetching = true;
      searchProducts();
    };


    function toggleSideFilter(currentFilters, item, filterName) {
      if (item.isSelected == true) {
        removeItem(currentFilters, item.Description);
        item.isSelected = false;
      } else {
        currentFilters.push(item.Description);
        item.isSelected = true;
        if (filterName && item.Description)
          sendEvent('Catálogo', 'Filtro - ' + filterName, item.Description);
      }
      applyFilters();
    }

    $scope.toggleCategory = function (cat) {
      toggleSideFilter(selectedCategories, cat, 'Categoria');
    };

    $scope.toggleSubcategory = function (cat) {
      toggleSideFilter(selectedSubcategories, cat, 'Subcategoria');
    };

    $scope.togglePartner = function (parceiro) {
      toggleSideFilter(selectedPartners, parceiro, 'Parceiro');
    };

    $scope.toggleManufacturer = function (fabricante) {
      toggleSideFilter(selectedManufacturers, fabricante, 'Fabricante');
    };

    $scope.toggleColor = function (cor) {
      toggleSideFilter(selectedColors, cor, 'Cor');
    };

    $scope.toggleVoltage = function (voltagem) {
      toggleSideFilter(selectedVoltages, voltagem, 'Voltagem');
    };

    $scope.init = function (id) {
      departmentId = id;
      loadSpecialShops();
      searchProducts();
      getFeaturedProducts();
      loadProductsOffers();
    };
  }]);
