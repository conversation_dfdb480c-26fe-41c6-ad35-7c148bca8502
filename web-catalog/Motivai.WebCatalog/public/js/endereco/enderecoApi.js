angular.module("EnderecoModule", []).service('EnderecoApi', ['$http', '$q', function ($http, $q) {
	const _this = this;
	this.consultaCep = function(cep) {
		return $http.get("/addresses/query/" + cep);
	};
	this.fillAddressByCep = function(cep, address) {
		if(!cep) return $q.reject('CEP inválido');
		cep = cep.replace("-", "");

		address.cepSearchError = false;
		address.cepSearchMessage = 'Pesquisando endereço pelo CEP...';
		address.Changed = false

		return _this.consultaCep(cep)
			.then(function(response) { return response.data; })
			.then(function(queryResult) {
				if (queryResult.Success) {
					if (queryResult.Return) {
						if (queryResult.Return.Street) {
							address.Changed = address.Street != queryResult.Return.Street;
							address.Street = queryResult.Return.Street;
						}

						if (queryResult.Return.Neighborhood) {
							address.Changed = address.Changed || address.Neighborhood != queryResult.Return.Neighborhood;
							address.Neighborhood = queryResult.Return.Neighborhood;
						}

						address.Changed = address.Changed || address.City != queryResult.Return.City;
						address.City = queryResult.Return.City;

						address.State = queryResult.Return.State;
						address.InitialState = queryResult.Return.Uf;
					} else {
						address.Street = '';
						address.Neighborhood = '';
						address.City = '';
						address.State = '';
						address.InitialState = '';
					}

					if (queryResult.Return && queryResult.Return.Cep && queryResult.Return.Street) {
						address.cepSearchError = false;
						address.cepSearchMessage = null;
						address.FilledManually = false;
						if (address.Changed) {
							address.cepSearchError = true;
							address.cepSearchMessage = 'Endereço atualizado com os dados do Correios.';
						}
					} else {
						address.cepSearchError = true;
						address.FilledManually = true;
						if (address.State && address.City) {
							address.cepSearchMessage = 'Preencha a rua e o bairro manualmente';
						} else {
							address.cepSearchMessage = "Endereço não encontrado pelo CEP informado, preencha manualmente os campos.";
						}
					}
				} else {
					address.cepSearchError = true;
					address.FilledManually = true;
					address.Cep = null;
					address.cepSearchMessage = "Ocorreu um erro ao carregar o endereço pelo CEP, por favor, tente novamente.";
				}
				return $q.resolve(address);
			})
			.catch(function(resp) {
				address.cepSearchError = true;
				address.FilledManually = true;
				address.cepSearchMessage = "Ocorreu um erro ao carregar o endereço pelo CEP, por favor, tente novamente.";
				return address;
			});
	};
}]);