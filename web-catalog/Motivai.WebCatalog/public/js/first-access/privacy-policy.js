angular.module("platformApp", ['footerModule', 'platformComponents', 'validation', 'validation.rule', 'ui.utils.masks', 'brasil.filters',
	'moment-picker', 'ngLoaderContainer', 'EnderecoModule'])
	.service("PrivacyPolicyService", [
		'$http',
		function ($http) {
			this.carregaDadosPrivacyPolicy = function () {
				return $http.get("/PrimeiroAcesso/GetPrivacyPolicy");
			};
			this.registrarAceitePolitica = function (acceptance) {
				return $http.post("/PrimeiroAcesso/Privacidade", acceptance)
			};
		}])
	.controller('PrivacyPolicyCtrl', ['PrivacyPolicyService', 'logger', '$scope', '$timeout', '$sce',
		function (service, logger, $scope, $timeout, $sce) {
			$scope.privacyPolicy = {
				content: '',
				contentId: '',
				version: '',
				essentialsCookies: false,
				analyticsCookies: false,
				accepted: false,
				loaded: false,
				canSubmit: false
			};

			const carregaDadosPrivacyPolicy = function () {
				service.carregaDadosPrivacyPolicy()
					.then(function (response) { return response.data; })
					.then(function (data) {
						if (data.Success) {
							handlePrivacyPolicyFields(data.Return);
						} else {
							showErrorSwal('Ops', retorno.Error);
							logger.log(data.Error);
						}
					}).catch(function (response) {
						logger.logResponse(response);
						showErrorSwal('Ops', 'Ocorreu um erro ao carregar os dados da política de privacidade, por favor, tente novamente');
					})
			}

			const handlePrivacyPolicyFields = function (data) {
				$scope.privacyPolicy.content = $sce.trustAsHtml(data.Content);
				$scope.privacyPolicy.cookieAcceptanceContent = $sce.trustAsHtml(data.CookieAcceptanceContent);
				$scope.privacyPolicy.contentId = data.ContentId;
				$scope.privacyPolicy.version = data.Version;
				$scope.privacyPolicy.loaded = true;
				$scope.privacyPolicy.skipFirstAccessRegistrationData = data.SkipFirstAccessRegistrationData;
				try {
					$scope.privacyPolicy.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
				} catch (error) {
					console.log(error);
				}

				$timeout(function() {
					$.fn.initScrollbar(".scroll");
				}, 500);
			}

			$scope.verifyAcceptance = function () {
				if ($scope.privacyPolicy.essentialsCookies && $scope.privacyPolicy.accepted) {
					$scope.privacyPolicy.canSubmit = true;
				} else {
					$scope.privacyPolicy.canSubmit = false;
				}
			}

			$scope.init = function () {
				carregaDadosPrivacyPolicy();
			};

			$scope.registrarAceitePolitica = function () {
				addLoaderToButton();
				service.registrarAceitePolitica($scope.privacyPolicy)
					.then(function (response) { return response.data; })
					.then(function (retorno) {
						if (retorno.Success) {
							location.href = retorno.Redirect;
						} else {
							logger.logResponse(retorno);
							removeLoaderFromButton();
							showErrorSwal("Ops", retorno.Error);
						}
					}).catch(function (response) {
						removeLoaderFromButton();
						$scope.loading = false;
						showErrorSwal("Ops", "Não foi possível registrar o aceite das políticas de privacidade, por gentileza, tente novamente");
					});
			}

			$scope.showCookiesDetails = function () {
				var content = document.createElement("div");
				content.innerHTML += "<h2>Essenciais</h2>"
				content.innerHTML += "<p>"
				content.innerHTML += "Esses cookies são essenciais para permitir o acesso do usuário em nosso site e fornecer acesso a recursos exclusivos para participantes e outras áreas seguras do site. <br>"
				content.innerHTML += "Esses cookies não coletam informações sobre você que poderiam ser usadas para fins de marketing e não lembram de onde você esteve na internet. <br>"
				content.innerHTML += "Esta categoria de cookies não pode ser desativada."
				content.innerHTML += "</p>";

				content.innerHTML += "<h2>Analytics</h2>"
				content.innerHTML += "<p>"
				content.innerHTML += "Utilizamos cookies do Google Analytics para coletar informações sobre como os visitantes navegam em nosso site. <br>"
				content.innerHTML += "Esses cookies coletam informações para nos mostrar como o nosso site está sendo utilizado por seus usuários, para fins de melhoria no serviço prestado. <br>"
				content.innerHTML += "Anonimizamos endereços IP nos serviços mencionados, e os dados anonimizados são transmitidos e armazenados pelos respectivos serviços em servidores nos Estados Unidos. <br>"
				content.innerHTML += "Os fornecedores dos serviços também podem transferir essas informações para terceiros, quando exigido por lei, ou onde esses terceiros processem as informações em nome dos mesmos.<br>"
				content.innerHTML += "O servidor não associará seu endereço IP a nenhum outro dado mantido pelo provedor de serviço.<br>"
				content.innerHTML += "</p>";

				showHtmlSwall(
					content,
					"Ok, entendi!"
				);
			}
		}])