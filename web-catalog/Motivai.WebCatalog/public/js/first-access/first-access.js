
function initChosen(selector) {
	$(selector).chosen({
		disable_search_threshold: 10,
		no_results_text: 'Resultado não encontrado',
		placeholder_text_multiple: 'Selecione uma ou mais opções',
		placeholder_text_single: 'Selecione uma opção'
	});
	$(selector).trigger("chosen:updated");
}

angular.module("platformApp", ['footerModule', 'platformComponents', 'validation', 'validation.rule', 'ui.utils.masks', 'brasil.filters',
'moment-picker', 'ngLoaderContainer', 'EnderecoModule'])
.service('MinhaContaApiService', ['$http', function($http) {

	this.carregaOperadoras = function() {
		return $http.get("/MinhaConta/Operadoras");
	};

	this.pesquisarPessoaPeloDocumento = function(document) {
		return $http.get("/PrimeiroAcesso/PesquisarPessoaPeloDocumento", { params:{ document } });
	};

	this.carregaEstadosCivil = function() {
		return $http.get("/MinhaConta/EstadosCivil");
	};

	this.carregaDadosCadastro = function() {
		return $http.get("/PrimeiroAcesso/Dados");
	};

	this.completarDadosCadastro = function(dados) {
		return $http.post("/PrimeiroAcesso/Completar", dados);
	};
	this.carregaConfiguracoesPrimeiroAcesso = function() {
		return $http.get("/PrimeiroAcesso/Configuracoes");
	};

	this.registrarCartaoDireto = (dadosCartao) => {
		return $http.get("/PrimeiroAcesso/TokenizacaoInfo")
			.then(tokenInfo => {
				if (!dadosCartao.nome_impressao || !dadosCartao.numero_cartao || !dadosCartao.cvv || !dadosCartao.expiration) {
					throw new Error('Preenchimento dos dados do Cartão obrigatório para conclusão do cadastro.')
				}
				if (tokenInfo.data.Error)
					throw new Error(tokenInfo.data.Error)

				const expiration = dadosCartao.expiration.split('', 5);
				dadosCartao.mes_validade = expiration[0]+expiration[1];
				dadosCartao.ano_validade = `20${expiration[3]+expiration[4]}`;
				const { clientId, token, endpoint, affiliationId } = tokenInfo.data.Return;

				const config = { 'client_id': clientId, 'access_token': token, 'Content-Type': 'application/json' };
				return $http({
						method: 'POST',
						url: `${endpoint}/cartoes`,
						headers: config,
						data: dadosCartao
					})
					.then(responseToken => {
						const cardToken = responseToken.data.cartao_token_id;
						return $http.post(
								`${endpoint}/transacoes/pagamentos`,
								{ valor: 0, id_filiacao: affiliationId, cartao_token_id: cardToken },
								{ headers: config }
							)
							.catch((reason) => {
								if (reason.data.codigo_retorno == "53" || reason.data.codigo_retorno == "67") {
									return $http.post("/PrimeiroAcesso/Cartao", {
										firstDigits: dadosCartao.numero_cartao.substring(0, 4),
										lastDigits: dadosCartao.numero_cartao.substring(dadosCartao.numero_cartao.length - 4),
										externalCardToken: cardToken
									})
								}
								// switch (reason.data.codigo_retorno) {
								// 	case '95':
								// 		throw new Error('CVV inválido, por favor, verifique os dados do cartão.');
								// 	default:
								// }
								throw new Error('Dados inválidos, por favor, verifique os dados do cartão (Erro ' + reason.data.codigo_retorno + ').');
							}).catch(function(resp) {
								throw new Error('Dados inválidos, por favor, verifique os dados do cartão. ' + resp);
							});
					});
			})
			.then(function(resp) {
				throw new Error("Não foi possível cadastrar o cartão. Se o problema persistir entre em contato com o Administrador. " + resp);
			});
	}

}])
.controller('PrimeiroAcessoCtrl', ['MinhaContaApiService', 'EnderecoApi', 'logger', '$scope', '$timeout', '$injector',
function (service, enderecoApi, logger, $scope, $timeout, $injector) {
	$scope.estadosCivil = [];
	$scope.operadoras = [];
	$scope.notAllowChangeDocument = true;
	$scope.dadosCadastro = { IsPessoaFisica: true, HomeAddress: { EnableEdit: true }, CommercialAddress: { EnableEdit: true }, Contact: {}, fillDocument: false };
	$scope.dadosCartao = {name: '', number: '', expiration: '',cvv: ''};
	$scope.selectedPersonType = '';
	$scope.firstAccessSettings = {};
	$scope.canViewDocumentField = false;
	$scope.startBirthDate = moment().subtract(30, 'years').format("DD/MM/YYYY");
	const $validationProvider = $injector.get('$validation');

	const delayInitChosen = function(selector) {
		$timeout(function() {
			$.fn.initChosen(selector);
		}, 250);
	};

	const initPfForm = function() {
		delayInitChosen('#estadocivil');
		delayInitChosen('#sexo');
	};

	const initPjForm = function() {
		delayInitChosen("#ieEstado");
	};

	const carregaEstadosCivil = function() {
		service.carregaEstadosCivil()
			.then(function(response) { return response.data; })
			.then(function(retorno) {
				if(retorno.Success) {
					$scope.estadosCivil = retorno.Return;
					delayInitChosen('#estadocivil');
				} else {
					logger.log(retorno.Error);
				}
			})
			.catch(function(resp) {
				logger.logResponse(resp);
			});
	};

	const carregaOperadoras = function() {
		service.carregaOperadoras()
			.then(function(response) { return response.data; })
			.then(function(retorno) {
				if(retorno.Success) {
					$scope.operadoras = retorno.Return;
					delayInitChosen('#operadora');
				} else {
					logger.log(retorno.Error);
				}
			})
			.catch(function(resp) {
				logger.logResponse(resp);
			});
	};

	$scope.pesquisarPessoaPeloDocumento = function() {
		service.pesquisarPessoaPeloDocumento($scope.dadosCadastro.AccountRepresentative.Document)
			.then(function(response) { return response.data; })
			.then(function(retorno) {
				if (retorno.Success) {
					$scope.dadosCadastro.AccountRepresentative = retorno.Return;
				} else {
					logger.log(retorno.Error);
				}
			})
			.catch(function(resp) {
				logger.logResponse(resp);
			});
	};

	$scope.toggleUserDocument = function(selectedPersonType) {
		switch(selectedPersonType) {
			case 'cpf':
				$scope.dadosCadastro.IsPessoaJuridica = false;
				$scope.dadosCadastro.IsPessoaFisica = true;
				initPfForm();
				break;
			case 'cnpj':
				$scope.dadosCadastro.IsPessoaJuridica = true;
				$scope.dadosCadastro.IsPessoaFisica = false;
				initPjForm();
				break;
			default:
				$scope.dadosCadastro.IsPessoaJuridica = false;
				$scope.dadosCadastro.IsPessoaFisica = false;
		}
		console.log($scope.dadosCadastro);
	}

	const carregaDados = function() {
		service.carregaDadosCadastro()
			.then(function(response) { return response.data; })
			.then(function(data) {
				if (data.Success) {
					delayInitChosen('#sexo');
					if (!data.Return) return;
					$scope.dadosCadastro = data.Return;
					if ($scope.dadosCadastro.FillDocument) {
						carregaConfiguracoes();
					}
					if ($scope.dadosCadastro.Cpf || $scope.dadosCadastro.Cnpj) {
						$scope.notAllowChangeDocument = true;
					} else {
						$scope.notAllowChangeDocument = false;
					}
					if ($scope.dadosCadastro.IsPessoaJuridica) {
						$timeout(function() {
							$scope.$evalAsync(function() {
								$.fn.initChosen("#ieEstado");
								$scope.exempt = $scope.dadosCadastro.StateInscriptionExempt;
							});
						}, 100);
					}
					if ($scope.dadosCadastro.HomeAddress) {
						$scope.pesquisaCep($scope.dadosCadastro.HomeAddress);
					}
				}	else {
					logger.log(data.Error);
				}
			})
			.catch(function(resp) {
				logger.logResponse(resp);
			});
	}

	$scope.pesquisaCep = function(address) {
		enderecoApi.fillAddressByCep(address.Cep, address);
	};

	const handleDocumentField = function() {
		if ($scope.dadosCadastro && $scope.dadosCadastro.FillDocument && $scope.firstAccessSettings && $scope.firstAccessSettings.EnableDocumentFieldAtFirstAccess) {
			$scope.canViewDocumentField = true;
			delayInitChosen('#selectedPersonType');
		} else {
			$scope.canViewDocumentField = false;
		}
	};

	const carregaConfiguracoes = function() {
		service.carregaConfiguracoesPrimeiroAcesso()
			.then(function(response) { return response.data; })
			.then(function(retorno) {
				if (retorno.Success) {
					$scope.firstAccessSettings = retorno.Return;
					handleDocumentField();
				} else {
					logger.log(retorno.Error);
				}
			})
			.catch(function(resp) {
				logger.logResponse(resp);
			});
	};

	$scope.completar = function(dadosCadastro) {
		addLoaderToButton();
		$scope.classeMensagem = null;
		$scope.mensagemFeedback = null;
		$validationProvider.validate($scope.registerForm)
		.then(function() {
			service.completarDadosCadastro(dadosCadastro)
				.then(function(response) { return response.data; })
				.then(function(retorno) {
					if(retorno.Success) {
						showSuccessSwal('Info', 'Seus dados foram atualizados com sucesso!');
						if (retorno.Redirect) {
							location.href = retorno.Redirect;
						}
						removeLoaderFromButton();
					} else {
						logger.log(retorno.Error);
						showErrorSwal('Ops', retorno.Error);
						removeLoaderFromButton();
					}
				})
				.catch(function(response) {
					logger.logResponse(response);
					showErrorSwal('Ops', 'Ocorreu um erro ao atualizar os dados, por favor, tente novamente.');
					removeLoaderFromButton();
				});
		})
		.catch(function() {
			showErrorSwal('Ops', 'Preencha os campos corretamente para prosseguir.');
			removeLoaderFromButton();
		});
	};

	$scope.registrarCartaoDireto = (dadosCartao) => {
		addLoaderToButton();
		service.registrarCartaoDireto(dadosCartao)
			.then((response) => {return response.data})
			.then((retorno) => {
				if (retorno.Success) {
					showSuccessSwal('Info', 'Seu cartão foi cadastrado com sucesso!');
					if (retorno.Redirect) {
						location.href = retorno.Redirect;
					}
					removeLoaderFromButton();
				} else {
					logger.log(retorno.Error);
					showErrorSwal('Ops', retorno.Error);
					removeLoaderFromButton();
				}
			})
			.catch((response) => {
				logger.logResponse(response);
				showErrorSwal('Ops', response.toString().replace('Error:', ''));
				removeLoaderFromButton();
			});
	}

	$scope.init = function() {
		carregaEstadosCivil();
		carregaOperadoras();
		carregaDados();

	};
}]);


