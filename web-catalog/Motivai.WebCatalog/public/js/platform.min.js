function pageView(page){ga&&ga("send","pageview",page);gtag?gtag("event","page_view",{page_title:document.title,page_location:window.location.href,page_path:page}):dataLayer&&dataLayer.push({event:"page_view",page_title:document.title,page_location:window.location.href,page_path:page})}function pageViewOnDocumentReady(page){$(document).ready(function(){pageView(page)})}function sendEvent(eventCategory,eventAction,eventLabel,eventValue){console.debug(eventCategory,eventAction,eventLabel,eventValue);ga&&ga("send",{hitType:"event",dr:window.location.host,eventCategory:eventCategory,eventAction:eventAction,eventLabel:eventLabel,eventValue:eventValue||1});gtag?gtag("event","custom_event",{event_category:eventCategory,event_action:eventAction,event_label:eventLabel,value:eventValue||1}):dataLayer&&dataLayer.push({event:"custom_event",event_category:eventCategory,event_action:eventAction,event_label:eventLabel,value:eventValue||1})}function sendCustomEvent(eventName="custom_event",params={}){console.debug(eventName,params);gtag?gtag("event",eventName,params):dataLayer&&(params.event=eventName,dataLayer.push(params))}function hasPath(path){return window.location.href.indexOf(path)>-1}function getParameterByName(name,url){url||(url=window.location.href);url=unescape(url);name=name.replace(/[\[\]]/g,"\\$&");var regex=new RegExp("[?&]"+name+"(=([^&#]*)|&|#|$)"),results=regex.exec(url);return results?results[2]?decodeURIComponent(results[2].replace(/\+/g," ")):"":null}function getCookieByName(cname){for(var c,name=cname+"=",decodedCookie=decodeURIComponent(document.cookie),ca=decodedCookie.split(";"),i=0;i<ca.length;i++){for(c=ca[i];c.charAt(0)==" ";)c=c.substring(1);if(c.indexOf(name)==0)return c.substring(name.length,c.length)}return""}$.fn.initProductZoom(".product-details .photo img");$.fn.initModal("a.modal");$.fn.initModalZoom("a.zoom, a.details");$.fn.initFaq(".faq-result ol li a:first-child");$.fn.initViewType(".viewType");$.fn.initMediabox(".mediabox");$.fn.initMobile();$.fn.initLogin();$.fn.initScrollbar(".scroll");$.fn.initTabs(".tabs");$.fn.initTooltip(".tooltip");$.fn.initFormTooltip("form input, form select, textarea");$.fn.initScroll(".scrollTo");$.fn.initForm("form");$.fn.initCreditCardBrand("input.creditCard");$.fn.initChosen("select.autostart");const showSuccessSwal=function(title,message){return swal({title:title,text:message,icon:"success",button:!0})},showWarningSwal=function(title,message){return swal({title:title,text:message,icon:"warning",button:!0})},showErrorSwal=function(title,message){return swal({title:title,text:message,icon:"error",button:!0,dangerMode:!0})},showConfirmSwal=function(message){return swal(message,{buttons:{cancel:"Não",confirm:"Confirmar"},defeat:!0})},showHtmlSwall=function(_content,_buttonText){return swal({content:_content,button:_buttonText})};(function(i,s,o,g,r,a,m){i.GoogleAnalyticsObject=r;i[r]=i[r]||function(){(i[r].q=i[r].q||[]).push(arguments)};i[r].l=1*new Date;a=s.createElement(o);m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)})(window,document,"script","https://www.google-analytics.com/analytics.js","ga");ga("js",new Date);ga("create","G-JBQ7YTLNVC","auto",{cookieFlags:"max-age=7200;secure;samesite=strict",cookieDomain:"auto"});hasPath("/Departamento/Categoria/")||hasPath("/Departamento/Index/")||hasPath("/Departamento/Subcategoria/")||hasPath("/Loja/Index/")||hasPath("/comunicacao/detalhes/")||(hasPath("/Catalog/Pesquisa")?pageView("/Catalog/Pesquisa"):window.location.pathname=="/"?pageView("/Home"):ga("send","pageview"));