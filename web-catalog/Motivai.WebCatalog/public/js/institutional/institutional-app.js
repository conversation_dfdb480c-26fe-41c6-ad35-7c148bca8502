﻿angular.module("platformApp", ['footerModule', 'ngLoaderContainer', 'ngSanitize'])
  .service('institutionalService', ['$http', function($http) {
    this.loadPrivacyPolicy = function() {
      return $http.get("/Politica/CarregaPoliticaPrivacidade");
    }
    this.loadShippingPolicy = function() {
      return $http.get("/Politica/CarregaPoliticaEntrega");
    }
    this.loadRegulament = function() {
      return $http.get("/Regulamento/CarregaRegulamento");
    }
  }])
  .controller('institutionalCtrl', ['institutionalService', '$sce', '$scope', function(catalogService, $sce, $scope) {
    $scope.loadPrivacyPolicy = function() {
      $scope.loadingPolicy = true;
      catalogService.loadPrivacyPolicy()
        .then(function(response) { return response.data; })
        .then(function(privacyResponse) {
          $scope.loadingPolicy = false;
          if (privacyResponse.Success) {
            $scope.privacyPolicy = $sce.trustAsHtml(privacyResponse.Return.content);
          }
        })
        .catch(function(err) {
          $scope.loadingPolicy = false;
          console.log(err);
        })
    };

    $scope.loadShippingPolicy = function() {
      $scope.loadingPolicy = true;
      catalogService.loadShippingPolicy()
        .then(function(response) { return response.data; })
        .then(function(retorno) {
          $scope.loadingPolicy = false;
          if (retorno.Success) {
            $scope.shippingPolicy = $sce.trustAsHtml(retorno.Return.content);
          }
          $.fn.initScrollbar(".scroll");
        })
        .catch(function(err) {
          $scope.loadingPolicy = false;
          console.log(err);
        })
    };

    $scope.loadRegulament = function() {
      $scope.loadingRegulament = true;
      catalogService.loadRegulament()
        .then(function(response) { return response.data; })
        .then(function(retorno) {
          $scope.loadingRegulament = false;
          if (retorno.Success)
            $scope.regulament = $sce.trustAsHtml(retorno.Return);
        })
        .catch(function(err) {
          $scope.loadingRegulament = false;
          console.log(err);
        });
    };
  }]);
