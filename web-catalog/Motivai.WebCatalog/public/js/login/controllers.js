function bloqueia(selector, text) {
  var btn = $(selector);
  btn.attr("disabled", "disabled");
  btn.addClass("processing");
  btn.text(text);
}

function desbloqueia(selector, text) {
  var btn = $(selector);
  btn.removeAttr("disabled");
  btn.removeClass("processing");
  btn.text(text);
}

var rememberPasswordRecaptcha = null;
var loginRecaptcha = null;
function initReCaptcha() {
  if ($('#loginRpa').val() != "1") {
    rememberPasswordRecaptcha = grecaptcha.render('rememberPasswordRecaptcha', { 'sitekey': '6Ld1yb8qAAAAABB9QlKzBXTDuMoL-Jg-N8WhPIKy' });
    loginRecaptcha = grecaptcha.render('loginRecaptcha', { 'sitekey': '6Ld1yb8qAAAAABB9QlKzBXTDuMoL-Jg-N8WhPIKy' });
  }
}
angular.module("platformLogin", ['footerModule'])
  .service('LoginAPIService', ['$http', function($http) {
    this.validaUsuario = function(user) {
      return $http.post("/login/logar", user);
    };
    this.validaUsuarioRpa = function(user) {
      return $http.post("/login/rpaLogar", user);
    };
    this.relembraSenha = function(user) {
      return $http.post("/login/relembrarsenha", user);
    };
    this.startRecover = function(data) {
      return $http.post("/login/recuperacaosenha", data);
    };
    this.sendToken = function(data) {
      return $http.post("/login/enviatoken", data);
    };
    this.validateToken = function(data) {
      return $http.post("/login/validatoken", data);
    };
    this.changePassword = function(data) {
      return $http.post("/login/alterasenha", data);
    };
  }])
  .controller('loginCtrl', ['LoginAPIService', 'logger', '$scope', '$window', function(loginAPI, logger, $scope, $window) {
    $scope.requiredFieldMessage = 'Usuário e senha são obrigatórios.';
    $scope.authenticate = function(usuario) {
      $scope.error = false;
      $scope.mensagemLogin = null;
      if (usuario.login.$invalid || usuario.password.$invalid)
        return;

      usuario.timezone = moment.tz.guess();
      usuario.recaptchaToken = validaReCaptcha();

      var request = $('#loginRpa').val() == "1" ? loginAPI.validaUsuarioRpa(usuario) : loginAPI.validaUsuario(usuario);

      if (usuario.recaptchaToken || $('#loginRpa').val() == "1") {
        bloqueia("#btn-login", "Entrando");
        request.then(function(response) { return response.data; })
          .then(function(data) {
            if (data.Success) {
              if (data.Redirect) {
                $window.location.href = data.Redirect;
              } else {
                desbloqueia("#btn-login", "Entrar");
              }
            } else {
              $scope.error = true;
              $scope.mensagemLogin = data.Error;
              resetRecaptcha();
              desbloqueia("#btn-login", "Entrar");
            }
          })
          .catch(function(response) {
            $scope.error = true;
            $scope.mensagemLogin = "Não foi possível efetuar validação do usuário, por favor, tente novamente.";
            logger.logResponse(response);
            resetRecaptcha();
            desbloqueia("#btn-login", "Entrar");
          });
      }
    };

    const validaReCaptcha = function() {
      if ($('#loginRpa').val() == "1")
        return null;

      try {
        const token = grecaptcha.getResponse(loginRecaptcha);
        if (token.length > 0) {
          return token;
        } else {
          throw Error("Preencha o reCaptcha para prosseguir.");
        }
      } catch (err) {
        desbloqueia("#btn-login", "Entrar");
        $scope.error = true;
        $scope.mensagemLogin = err.message;
      }
    };

    const resetRecaptcha = function() {
      if ($('#loginRpa').val() == "1")
        return;
      grecaptcha.reset(loginRecaptcha);
    };
  }])
  .controller('passwordRecoverCtrl', ['LoginAPIService', '$scope', function(loginAPI, $scope) {
    $scope.usuario = {};
    $scope.instrucoesEnviadas = false;
    $scope.primeiroPasso = true;
    $scope.segundoPasso = false;
    $scope.terceiroPasso = false;
    $scope.quartoPasso = false;
    $scope.passosFinalizados = false;

    const setPasso = function(passo) {
      $scope.primeiroPasso = passo === 1;
      $scope.segundoPasso = passo === 2;
      $scope.terceiroPasso = passo === 3;
      $scope.quartoPasso = passo === 4;
      $scope.passosFinalizados = passo === 5;
    };

    $scope.relembraSenha = function(usuario) {
      if (usuario.login.$invalid || usuario.email.$invalid)
        return;
      bloqueia("#btn-continue", "Aguarde");
      loginAPI.relembraSenha(usuario)
        .then(function(response) { return response.data; })
        .then(function(data) {
          desbloqueia("#btn-continue", "Continuar");
          if (data.Success) {
            $scope.instrucoesEnviadas = true;
            if (data.Redirect) {
              $window.location.href = data.Redirect;
            }
          } else {
            $scope.rememberPasswordErrorMessage = data.Error;
          }
        })
        .catch(function(response) {
          desbloqueia("#btn-continue", "Continuar");
          $scope.rememberPasswordErrorMessage = "Não foi possível efetuar operação, por favor, tente novamente.";
        });
    };

    $scope.startRecover = function(dados) {
      if (dados.login.$invalid || dados.email.$invalid)
        return;
      setPasso(1);
      $scope.rememberPasswordErrorMessage = null;

      dados.recaptchaToken = validateRecaptcha();
      if (dados.recaptchaToken) {
        bloqueia("#btn-continue", "Aguarde");
        loginAPI.startRecover(dados)
          .then(function(response) { return response.data; })
          .then(function(data) {
            if (data && data.Success) {
              setPasso(2);
              $scope.email = data.Return.email;
              $scope.canUseEmail = data.Return.canUseEmail;
              $scope.telefone = data.Return.telefone;
              $scope.canUseSms = data.Return.canUseSms;
            } else {
              $scope.rememberPasswordErrorMessage = data ? data.Error : "Usuário não encontrado.";
              grecaptcha.reset(rememberPasswordRecaptcha);
            }
            desbloqueia("#btn-continue", "Continuar");
          })
          .catch(function(response) {
            $scope.rememberPasswordErrorMessage = "Não foi possível efetuar operação, por favor, tente novamente.";
            grecaptcha.reset(rememberPasswordRecaptcha);
            desbloqueia("#btn-continue", "Continuar");
          });
      }
    };

    $scope.sendToken = function(opc) {
      if (!opc) {
        $scope.mensagemEnvio = "Selecione uma forma de envio do token de segurança.";
        return;
      }
      setPasso(2);
      $scope.mensagemEnvio = null;
      bloqueia("#btn-send", "Aguarde");
      loginAPI.sendToken({ formaEnvio: opc })
        .then(function(response) { return response.data; })
        .then(function(data) {
          desbloqueia("#btn-send", "Continuar");
          if (data && data.Success) {
            setPasso(3);
          } else {
            $scope.mensagemEnvio = data ? data.Error : "Não foi possível enviar o token, por favor, tente novamente.";
          }
        })
        .catch(function(response) {
          desbloqueia("#btn-send", "Continuar");
          $scope.mensagemEnvio = "Não foi possível efetuar operação, por favor, tente novamente.";
        });
    };

    $scope.validateToken = function(t) {
      if (!t) {
        $scope.mensagemValidacaoToken = "Código de segurança inválido.";
        return;
      }
      setPasso(3);
      $scope.mensagemEnvio = null;
      bloqueia("#btnValidar", "Aguarde");
      loginAPI.validateToken({ token: t })
        .then(function(response) { return response.data; })
        .then(function(data) {
          desbloqueia("#btnValidar", "Continuar");
          if (data && data.Success && data.Return) {
            setPasso(4);
          } else {
            $scope.mensagemValidacaoToken = data ? data.Error : "Não foi possível validar o código de segurança, por favor, tente novamente.";
          }
        })
        .catch(function(response) {
          desbloqueia("#btnValidar", "Continuar");
          $scope.mensagemValidacaoToken = "Não foi possível efetuar operação, por favor, tente novamente.";
        });
    };

    $scope.validatePassword = function() {
      var regex = new RegExp("^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])");

      if ($scope.senha.novaSenha && $scope.senha.novaSenha.length < 6) {
        $scope.newPasswordInvalid = true;
        $scope.passwordChangeErrorMessage = "A senha deve conter pelo menos 6 caracteres";
        return;
      }

      if (!regex.test($scope.senha.novaSenha)) {
        $scope.newPasswordInvalid = true;
        $scope.passwordChangeErrorMessage = "A senha deve ter ao menos 1 letra maiúscula, 1 letra minúscula e 1 número";
        return;
      }

      $scope.newPasswordInvalid = false;
      $scope.passwordChangeErrorMessage = null;

      $scope.validarConfirmarSenha();
    };

    $scope.validarConfirmarSenha = function() {
      if ($scope.senha.novaSenha && $scope.senha.confirmacaoSenha) {
        if ($scope.senha.novaSenha != $scope.senha.confirmacaoSenha) {
          $scope.passwordConfirmInvalid = true;
          $scope.confirmPasswordErrorMessage = "O confirmar senha deve ser igual à nova senha";
          return;
        }
      }

      $scope.passwordConfirmInvalid = false;
      $scope.confirmPasswordErrorMessage = null;
    };

    $scope.changePassword = function(model) {
      if (!model || !model.novaSenha || !model.confirmacaoSenha) {
        $scope.mensagemAlteracaoSenha = "Preencha a senha e a confirmação de senha.";
        return;
      } else if (model.novaSenha != model.confirmacaoSenha) {
        $scope.mensagemAlteracaoSenha = "A nova senha e a confirmação de senha diferem.";
        return;
      }
      setPasso(4);
      $scope.mensagemAlteracaoSenha = null;
      bloqueia("#btn-change", "Aguarde");
      loginAPI.changePassword(model)
        .then(function(response) { return response.data; })
        .then(function(data) {
          desbloqueia("#btn-change", "Alterar");
          if (data && data.Success && data.Return) {
            setPasso(5);
          } else {
            $scope.mensagemAlteracaoSenha = data ? data.Error : "Não foi possível alterar a senha, por favor, tente novamente.";
          }
        })
        .catch(function(response) {
          desbloqueia("#btn-change", "Alterar");
          $scope.mensagemAlteracaoSenha = "Não foi possível efetuar operação, por favor, tente novamente.";
        });
    };

    const validateRecaptcha = function() {
      try {
        const token = grecaptcha.getResponse(rememberPasswordRecaptcha);
        if (token.length > 0) {
          return token;
        } else {
          throw Error("Preencha o reCaptcha para prosseguir.");
        }
      } catch (err) {
        desbloqueia("#btn-continue", "Continuar");
        $scope.error = true;
        $scope.rememberPasswordErrorMessage = err.message;
      }
    };
  }]);
