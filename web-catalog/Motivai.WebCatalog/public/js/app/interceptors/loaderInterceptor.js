﻿angular.module("gpLoading", []).factory("loadingInterceptor", ['$q', '$rootScope', '$timeout', function ($q, $rootScope, $timeout) {
  return {
    request: function (config) {
      $rootScope.loading = true;
      return config;
    },
    requestError: function (rejection) {
      $rootScope.loading = false;
      return $q.reject(rejection);
    },
    response: function (config) {
      $rootScope.loading = false;
      return config;
    },
    responseError: function (rejection) {
      $rootScope.loading = false;
      return $q.reject(rejection);
    }
  };
}]);