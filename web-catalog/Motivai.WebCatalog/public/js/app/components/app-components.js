angular.module("platformComponents", [])
  .component('selectState', {
    bindings: {
      selectId: '@',
      required: '<',
      disabled: '<',
      state: '<',
      onUpdate: '&'
    },
    controller: function () {
      const ctrl = this;
      ctrl.$postLink = function () {
        setTimeout(function () { $.fn.initChosen('#' + ctrl.selectId); }, 250);
      };
      ctrl.$onChanges = function (bindings) {
        if (bindings.state || bindings.required || bindings.disabled) {
          $('#' + ctrl.selectId).trigger("chosen:updated");
        }
      };
      ctrl.onChange = function () {
        ctrl.onUpdate({ $value: ctrl.state });
      };
    },
    template: '<select id="{{$ctrl.selectId}}" name="{{$ctrl.selectId}}" ng-model="$ctrl.state" ng-change="$ctrl.onChange()" ng-required="$ctrl.required" ng-disabled="$ctrl.disabled"><option value="Acre">Acre</option><option value="Alagoas">Alagoas</option><option value="Amapá">Amapá</option><option value="Amazonas">Amazonas</option><option value="Bahia">Bahia</option><option value="Ceará">Ceará</option><option value="Distrito Federal">Distrito Federal</option><option value="Espírito Santo">Espírito Santo</option><option value="Goiás">Goiás</option><option value="Maranhão">Maranhão</option><option value="Mato Grosso">Mato Grosso</option><option value="Mato Grosso do Sul">Mato Grosso do Sul</option><option value="Minas Gerais">Minas Gerais</option><option value="Paraná">Paraná</option><option value="Paraíba">Paraíba</option><option value="Pará">Pará</option><option value="Pernambuco">Pernambuco</option><option value="Piauí">Piauí</option><option value="Rio de Janeiro">Rio de Janeiro</option><option value="Rio Grande do Norte">Rio Grande do Norte</option><option value="Rio Grande do Sul">Rio Grande do Sul</option><option value="Rondônia">Rondônia</option><option value="Roraima">Roraima</option><option value="Santa Catarina">Santa Catarina</option><option value="Sergipe">Sergipe</option><option value="São Paulo">São Paulo</option><option value="Tocantins">Tocantins</option></select>'
  })
  .directive('mediabox', [function () {
    return {
      restrict: 'EA',
      replace: true,
      transclude: false,
      scope: { items: "<", classes: "@" },
      controller: function ($scope) {
        const ctrl = this;
        $scope.formatDataSrcSet = function (item) {
          return item.ImageUrl + '/1680 1920w 3x, ' + item.ImageUrl + '/1680 1680w 3x, ' + item.MediumImageUrl + '/1280 1280w 3x, ' + item.SmallImageUrl + '/599 599w 3x';
        };
        ctrl.initMediabox = function () {
          $('.mediabox img[data-srcset]').srcset();
          $('.mediabox').nivoSlider({
            controlNav: true,
            controlNavThumbs: false,
            prevText: 'Anterior',
            nextText: 'Próximo',
            pauseTime: 10000,
            afterLoad: function () { $('.nivoSlider .nivo-caption').attr('class', 'nivo-caption animate') },
            beforeChange: function () { $('.nivoSlider .nivo-caption').attr('class', 'nivo-caption animateOut') },
            afterChange: function () { $('.nivoSlider .nivo-caption').attr('class', 'nivo-caption animate') }
          });
        };
        ctrl.$postLink = function () {
          setTimeout(function () {
            console.log('Items 1', ctrl.items);
            ctrl.initMediabox();
          }, 100);
        };
        ctrl.$onChanges = function () {
          setTimeout(function () {
            console.log('Items 2', ctrl.items);
          }, 100);
        };
      },
      template: '<article class="mediabox {{classes}}"><a ng-repeat="item in items | orderBy:\'Position\'" target="_blank" href="{{::item.Link}}"><img data-srcset="{{formatDataSrcSet(item)}}"></a></article>'
    };
  }])
  .component('selectChosen', {
    restrict: 'E',
    bindings: {
      selectId: '@',
      placeholder: '@',
      required: '<',
      disabled: '<',
      value: '<',
      options: '<',
      onUpdate: '@'
    },
    controller: function () {
      const ctrl = this;
      ctrl.$postLink = function () {
        setTimeout(function () {
          $.fn.initChosen('#' + ctrl.selectId);
        }, 100);
      };
      ctrl.$onChanges = function (bindings) {
        if (!ctrl.options) {
          ctrl.options = [];
        }
        if (bindings.value || bindings.required || bindings.disabled) {
          $('#' + ctrl.selectId).trigger("chosen:updated");
        }
      };
      ctrl.onChange = function () {
        ctrl.onUpdate({ $value: ctrl.value });
      };
    },
    template: '<select id="{{$ctrl.selectId}}" name="{{$ctrl.selectId}}" ng-model="$ctrl.value" ng-change="$ctrl.onChange()" ng-required="$ctrl.required" ng-disabled="$ctrl.disabled" placeholder="{{$ctrl.placeholder}}"><option value="" selected disabled="disabled">{{$ctrl.placeholder}}</option><option ng-repeat="opt in $ctrl.options track by $index" value="{{opt.value}}">{{opt.text}}</option></select>'
  })
  .directive('productShowcase', [function () {
    return {
      restrict: 'EA',
      replace: true,
      transclude: false,
      scope: { product: "=", hideButton: '@' },
      template: `
        <li>
          <div class="dealbadge-top" ng-if="::product.ShowPriceWithPurchaseCost">
            <p>Necessário comprar <span ng-bind="::((product.Price - product.AvailableBalance) | currency:'')"></span> pontos</p>
          </div>
          <div class="medals ranking">
            <ul ng-if="::product.Rankings"><li ng-repeat="rank in product.Rankings"><img src="/assets/img/img-loader.gif" blazy-src="{{::(rank.Icon + '/45/45')}}" title="{{::rank.Name}}"></li></ul>
          </div>
          <div class="photo">
            <a href="/Produto/Detalhes/{{::product.Url}}" title="{{::product.Name}}">
              <img src="/assets/img/img-loader.gif" blazy-src="{{::product.ImageUrl}}">
            </a>
          </div>
          <div class="tags">
            <div class="sale" ng-if="product.Promotional">Promoção</div>
          </div>
          <h2><a href="/Produto/Detalhes/{{::product.Url}}" title="{{::product.Name}}">{{::product.Name}}</a></h2>
          <p class="price" ng-if="::!product.Available"><strong><span>Produto esgotado</span></strong></p>
          <p class="price" ng-if="::product.Available">
            <span ng-if="::!(product.DynamicPrice || product.ShowPriceWithPurchaseCost)">
              <span ng-if="::product.Promotional"><span class="promo-d"><span ng-bind="::product.CoinName.Prefix"></span> <span ng-bind="::(product.PriceFrom | currency:'')">0</span> <span ng-bind="::product.CoinName.Sufix">pontos</span></span></span>
              <span class="promo-por"><strong ng-bind="::product.CoinName.Prefix"></strong> <strong ng-bind="::(product.Price | currency:'')">0</strong> <strong ng-bind="::product.CoinName.Sufix">pontos</strong></span>
            </span>
            <span ng-if="::product.ShowPriceWithPurchaseCost">
              <span ng-if="::product.Promotional"><span class="promo-d"><span ng-bind="::product.CoinName.Prefix"></span> <span ng-bind="::(product.PriceFrom | currency:'')">0</span> <span ng-bind="::product.CoinName.Sufix">pontos</span></span></span>
              <span class="promo-por"><strong ng-bind="::product.CoinName.Prefix"></strong> <strong ng-bind="::(product.AvailableBalance | currency:'')">0</strong> <strong ng-bind="::product.CoinName.Sufix">pontos</strong></span>
              <span class="points-purchase">+ <strong ng-bind="::(product.PointsNeededPurchaseCost | currency:'R$ ')">0</strong></span>
            </span>
            <strong ng-if="::product.DynamicPrice" ng-bind="::product.DynamicPriceDescription"><span>Produto sob encomenda</span></strong>
          </p>
        </li>
      `
    };
  }]);
