﻿angular.module("ngLoaderContainer", [])
  .directive('loaderContainer', [function () {
    return {
      restrict: 'EA',
      replace: true,
      transclude: true,
      scope: { isLoading: "=", divStyle: "@", imgStyle: "@" },
      template: '<div><img class="loader" style="{{::imgStyle}}" ng-show="isLoading" ng-src="/assets/img/preloader.gif" /><div ng-transclude ng-show="!isLoading" class="{{::divClass}}"></div></div>',
      link: function (scope, element, attrs, ctrl, transcludeFn) {
        scope.divClass = attrs.class;
        if (element && element.lenth) {
          element[0].removeClass(attrs.class);
        }
        var divStyle = "";
        if (!scope.divStyle) {
          divStyle = "width: 100%; margin: 0 auto;";
        }
        attrs.$set("class", "");
        attrs.$set("style", divStyle)
      }
    };
  }]);