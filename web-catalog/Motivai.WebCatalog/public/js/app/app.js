angular.module("footerModule", [])
  .service('logger', ['$http', function ($http) {
    this.log = function(err, status) {
      console.log('Error: ', status, err);
      if (ga && ga instanceof Function) {
        ga('send', 'exception', { exDescription: status + ' - ' + err });
      }
    };
    this.logResponse = function(response) {
      if (ga && ga instanceof Function) {
        try {
          ga('send', 'exception', { exDescription: response.status + ' - ' + response.data });
        } catch (err) {}
      } else if (err) {
        console.error('Error: ', response.status, response.data);
      }
    };
  }])
  .service('rodapeService', ['$http', function ($http) {
    this.carregaRodape = function () {
      return $http.get('/Catalog/footer');
    }
  }])
  .controller('footerCtrl', ['rodapeService', 'logger', '$scope', function (rodapeService, logger, $scope) {
    $scope.rodape = {};
    const carregaRodape = function () {
      rodapeService.carregaRodape()
        .then(function(response) { return response.data; })
        .then(function (retorno) {
          if (retorno.Success) {
            $scope.rodape = retorno.Return;
          } else {
            logger.log(retorno.Error);
          }
        })
        .catch(function(response) {
          logger.logResponse(response);
        });
    }

    carregaRodape();
  }]);
function dirtyWithError(field, errorType = null) {
  return field && (field.$touched || field.$dirty) && (errorType && field.$error[errorType] || field.$error && Object.keys(field.$error).length);
}
function httpRequestInterceptor($q, $window) {
  return {
    request: function(config) {
      if (config.url.indexOf('Login') >= 0 || config.url.indexOf('login') >= 0) {
        return $q.reject({ status: 401 });
      }
      return config;
    },
    response: function(response) {
      if(response.status == 401 || response.status == 403
        || (response.data && response.data.toString().indexOf('ng-app="platformLogin"') > 0)) {
        $window.location.href = '/Login';
        return $q.reject(response);
      } else {
        return response;
      }
    },
    responseError: function(response) {
      console.log('Failed: ', response);
      if(response.status == 401 || response.status == 403) {
        $window.location.href = '/Login';
        return $q.reject(response);
      } else if(response.status == 404) {
        return $q.reject('A página solicitada não foi encontrada.');
      }
      return response;
    }
  };
}

function startOrUpdateChosen(elementId) {
  if ($('#' + elementId + '_chosen').length) {
    $('#' + elementId).trigger('chosen:updated');
  } else {
    $.fn.initChosen('#' + elementId);
  }
}

function initChosen(selector, placeholder = 'Selecione') {
  return $(selector).chosen({
    disable_search: true,
    width: '100%',
    disable_search_threshold: 10,
    no_results_text: 'Resultado não encontrado',
    placeholder_text_multiple: placeholder || 'Selecione uma ou mais opções',
    placeholder_text_single: placeholder || 'Selecione uma opção'
  })
}

function startBLazyLoading(selector) {
  return new Blazy({
    src: 'blazy-src',
    selector: selector,
    loadInvisible: !0,
    success: function (t) {
      setTimeout(function () { var i; i = $(t).parent(); i = i.removeClass("loading"); }, 200);$.Deferred().resolve()
    },
    error: function (t, o) {
      "missing" !== o && "invalid" !== o || $(t).attr("src", "/assets/img/imgError.png");$.Deferred().resolve()
    }
  });
};
function startNivoSlider(selector) {
  // $(selector + ' img[data-srcset]').srcset();
  $(selector).nivoSlider({
    controlNav: true,
    controlNavThumbs: false,
    prevText: 'Anterior',
    nextText: 'Próximo',
    pauseTime: 10000,
    afterLoad: function() { $('.nivoSlider .nivo-caption').attr('class', 'nivo-caption animate') },
    beforeChange: function() { $('.nivoSlider .nivo-caption').attr('class','nivo-caption animateOut') },
    afterChange: function() { $('.nivoSlider .nivo-caption').attr('class','nivo-caption animate') }
  });
}

function addLoaderToButton(elementSelector = null, text = null) {
  if (!elementSelector) {
    elementSelector = "button[type=submit]";
  }
  if (!text) {
    text = 'Aguarde';
  }
  const btnFeedback = $(elementSelector);
  btnFeedback.attr("disabled", "disabled");
  btnFeedback.html(text);
  btnFeedback.addClass("processing");
}

function removeLoaderFromButton(elementSelector = null, text = null) {
  if (!elementSelector) {
    elementSelector = "button[type=submit]";
  }
  if (!text) {
    text = 'Continuar';
  }
  const btnFeedback = $(elementSelector);
  btnFeedback.removeAttr("disabled");
  btnFeedback.html(text);
  btnFeedback.removeClass("processing");
}

var qtyOfVerifications = 0;
function waitForElement(selector, callback) {
  if (document.querySelector(selector)) {
    callback();
  } else {
    qtyOfVerifications++;
    if (qtyOfVerifications > 2) {
      callback();
      return;
    }
    setTimeout(() => waitForElement(selector, callback), 100);
  }
}
