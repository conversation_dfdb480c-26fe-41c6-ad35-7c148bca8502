﻿angular.module("platformApp", ['footerModule', 'ngLoaderContainer', 'ngSanitize'])
.service('faqService', ['$http', function($http) {
  this.findAllFaq = function() {
    return $http.get('FAQ/CarregarFaq');
  }
  this.findFaqByTerm = function(term) {
    return $http({
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      url: '/FAQ/BuscarFaqPeloTermo',
      data: "term=" + term
    });
  }
}])
.controller("faqController", ['faqService', '$sce', '$scope', '$timeout', function(catalogService, $sce, $scope, $timeout) {
  $scope.faqs = [];
  $scope.faq = { term: '' };

  const findAllFaq = function() {
    $scope.loadingFaq = true;
    catalogService.findAllFaq()
      .then(function(response) { return response.data; })
			.then(function(retorno) {
        $scope.loadingFaq = false;
        if (retorno.Success) {
          $scope.faqs = retorno.Return;
          $timeout(function() {
            $scope.$evalAsync(function() {
              var t = startBLazyLoading("article.faq-result ol li");
              $('article.faq-result ol li a').click(function(obj) {
                $(this).closest("ol").find(".active .answer").slideToggle("fast");
                $(this).closest("ol").find(".active").removeClass("active");
                $(this).parent().addClass("active");
                $(this).next().slideToggle("fast");
              });
            });
          });

        }
      })
      .catch(function(err) {
        console.log(err);
      });
  }

  $scope.findFaqByTerm = function() {
    $scope.loadingFaq = true;
    catalogService.findFaqByTerm($scope.faq.term)
      .then(function(response) { return response.data; })
			.then(function(faqResponse) {
        $scope.loadingFaq = false;
        if (faqResponse.Success) {
          $scope.faqs = faqResponse.Return;
          $timeout(function() {
            $scope.$evalAsync(function() {
              // $.fn.initBlazy("article.faq-result ol li");
              $('article.faq-result ol li a').click(function(obj) {
                $(this).closest("ol").find(".active .answer").slideToggle("fast");
                $(this).closest("ol").find(".active").removeClass("active");
                $(this).parent().addClass("active");
                $(this).next().slideToggle("fast");
              });
            });
          });
        }
      })
      .catch(function(err) {
        console.log(err);
      })
  }

  $scope.init = function() {
    findAllFaq();
  };
}]);