function startBLazyLoading(selector) {
  return new Blazy({
    src: 'blazy-src',
    selector: selector,
    loadInvisible: !0,
    success: function(t) {
      setTimeout(function() { var i; i = $(t).parent(); i = i.removeClass("loading"); }, 200); $.Deferred().resolve()
    },
    error: function(t, o) {
      "missing" !== o && "invalid" !== o || $(t).attr("src", "/assets/img/imgError.png"); $.Deferred().resolve()
    }
  });
};

const initSlider = function(clazz) {
  window.bLazy = startBLazyLoading("article.list-products." + clazz + " .slider img");
  initBxSlider("article.list-products." + clazz + " .slider ul");
}

function initBxSlider(selector) {
  $(selector).bxSlider({
    auto: !0,
    slideWidth: 240,
    slideMargin: 25,
    minSlides: $.fn.getGridSize(),
    maxSlides: $.fn.getGridSize(),
    prevText: "Anterior",
    nextText: "Proximo",
    onSlideAfter: function(t) {
      return $.when(window.bLazy).then(function() {
        window.bLazy.load($(t).parent().find(".photo img"));
      })
    }
  });
}

angular.module("platformApp", ['footerModule', 'platformComponents', 'ngLoaderContainer', 'ngResource', 'ui.utils.masks'])
  .factory('CarrinhoEndpoint', ['$resource', function($resource) {
    return $resource("/Carrinho", {}, {
      addItem: { url: "/Carrinho/Add", method: "POST", data: {}, isArray: false }
    });
  }])
  .factory('ProdutoEndpoint', ['$resource', function($resource) {
    return $resource("/Produto/ConsultaDisponibilidade", {}, {
      consultaDisponibilidade: { method: "POST", data: {}, isArray: false },
      registraInteresse: { url: "/Produto/RegistrarAvisoDisponibilidade", method: "POST", data: {}, isArray: false },
      pesquisaSkus: { url: "/Produto/GetAtributos", method: "POST", data: {}, isArray: false }
    });
  }])
  .service('CarrinhoService', ['CarrinhoEndpoint', '$http', function(endpoint, $http) {
    this.calculaFrete = function(c, id, md, qt) {
      return $http.post("/Carrinho/CalculaFreteItem", { c: c, id: id, md: md, q: qt });
    };
    this.addItemCarrinho = function(p, q) {
      return endpoint.addItem({}, { p: p, q: q }).$promise;
    };
  }])
  .service('ProdutoService', ['ProdutoEndpoint', '$http', function(endpoint, $http) {
    const nullIfEmpty = function(val) {
      return val && val.length ? val : null;
    }
    this.carregaMarketplace = function(id) {
      return $http.get("/Produto/Marketplace/" + id);
    };
    this.searchProductsSimilares = function(id) {
      return $http.get("/Produto/ProdutosSimilares/" + id);
    };
    this.consultaDisponibilidade = function(id, md, qtde, priceDefinedByParticipant) {
      return endpoint.consultaDisponibilidade({}, { id: id, md: md, qt: qtde, priceDefinedByParticipant: priceDefinedByParticipant }).$promise;
    };
    this.registraInteresse = function(p) {
      return endpoint.registraInteresse({}, { p: p }).$promise;
    };
    this.carregaEnderecos = function() {
      return $http.get("/MinhaConta/EnderecosEntrega?resumed=true");
    };
    this.pesquisaSkus = function(i, m, v, t, c) {
      return endpoint.pesquisaSkus({}, {
        i: i,
        m: nullIfEmpty(m),
        v: nullIfEmpty(v),
        t: nullIfEmpty(t),
        c: nullIfEmpty(c)
      }).$promise;
    };
  }])
  .controller('productCtrl', ['ProdutoService', 'CarrinhoService', 'logger', '$scope', '$timeout', '$location', '$filter',
    function(produtoService, carrinhoService, logger, $scope, $timeout, $location, $filter) {
      var produto = null;
      var ean = null;
      var primeiroFiltro = null;

      $scope.loadingDetails = false;
      $scope.showProductPriceDetails = true;
      $scope.showProductPriceFrom = true;

      $scope.quantidade = 1;
      $scope.marketplace = [];
      $scope.loadingMarketplace = false;
      $scope.marketplaceFound = true;
      $scope.loadingSimilares = false;
      $scope.produtosSimilares = [];
      $scope.enderecosEntrega = null
      // Cep
      $scope.mostraDivCalculo = false;
      $scope.novoCep = false;
      $scope.cepAlternativo = "";
      $scope.calculoFrete = { valorFrete: 0, tempoFrete: 0 }
      $scope.priceDefinedByParticipant = null;

      const setErro = function(detalhes) {
        $scope.loadingDetails = false;
        showErrorSwal('Ops', detalhes);
      }

      const carregaMarketplace = function() {
        $scope.loadingMarketplace = true;
        produtoService.carregaMarketplace(ean)
          .then(function(response) { return response.data; })
          .then(function(marketResp) {
            $scope.loadingMarketplace = false;
            if (marketResp.Success) {
              $scope.marketplace = marketResp.Return.filter(function(it) {
                if (it.ElasticId != produto) return it;
              });
              if (!$scope.marketplace || $scope.marketplace.length == 0) {
                $scope.marketplaceFound = false;
              }
            } else {
              $scope.marketplaceFound = false;
              logger.log(marketResp.Error);
            }
          })
          .catch(function(resp) {
            $scope.loadingMarketplace = false;
            $scope.marketplace = null;
            logger.logResponse(resp);
          });
      };

      const searchProductsSimilares = function(cat) {
        $scope.loadingSimilares = true;
        $scope.hasProduct = true;
        produtoService.searchProductsSimilares(cat)
          .then(function(response) { return response.data; })
          .then(function(similares) {
            $scope.loadingSimilares = false;
            if (similares.Success) {
              $scope.produtosSimilares = similares.Return;
              $scope.hasProduct = $scope.produtosSimilares && $scope.produtosSimilares.length > 0;
              $timeout(function() {
                $scope.$evalAsync(function() {
                  $.fn.initBlazy('.list-products.similares .photo img', 'blazy-src');
                  $.fn.initSlider('.similares');
                });
              }, 10);
            } else {
              $scope.hasProduct = false;
              logger.log(similares.Error);
            }
          })
          .catch(function(resp) {
            $scope.loadingSimilares = false;
            $scope.hasProduct = false;
            logger.logResponse(resp);
          });
      };

      const carregaEnderecosEntrega = function() {
        produtoService.carregaEnderecos()
          .then(function(response) { return response.data; })
          .then(function(enderecos) {
            if (enderecos.Success) {
              if (enderecos.Return)
                $scope.enderecosEntrega = enderecos.Return;
              else {
                $scope.enderecosEntrega = [];
              }
              $timeout(function() {
                $scope.$evalAsync(function() {
                  $.fn.initChosen('#shippingZipcode');
                  // $('#shippingZipcode').trigger("chosen:updated");
                });
              }, 50);
            } else {
              logger.log(enderecos.Error);
              $scope.enderecosEntrega = [];
            }
          })
          .catch(function(resp) {
            logger.logResponse(resp);
            $scope.enderecosEntrega = [];
          });
      };

      const calculaFrete = function(cepDestino) {
        $scope.isCalculatingShipping = true;
        const id = $("#id").val();
        const skuCode = $("#md").val();
        const qt = $("#quantidade").val() || 1;

        sendEvent('Produto', 'Calculo Frete', 'SKU ' + skuCode, 1);
        sendCustomEvent('calculo_frete', {
          id: id,
          sku: skuCode,
          modelo: $("#model").val(),
          voltagem: $("#voltagem").val(),
          cor: $("#cor").val(),
          tamanho: $("#tamanho").val(),
          quantidade: $('#quantidade').val(),
          marca: $("#spn-brand").text(),
          departamento: $('#department').text(),
          categoria: $('#category').text(),
          subcategoria: $('#subcategory').text(),
          quantidade: qt,
        });

        carrinhoService.calculaFrete(cepDestino, id, skuCode, qt)
          .then(function(response) { return response.data; })
          .then(function(calcResult) {
            $scope.isCalculatingShipping = false;
            var valorFrete = 0;
            var tempoFrete = "";
            if (calcResult.Success) {
              $('.shippingCalc .result').addClass('active');
              $scope.mostraDivCalculo = true;
              valorFrete = calcResult.Return.ShippingCost;
              tempoFrete = calcResult.Return.EstimatedDelivery;
              $scope.manufacturers = calcResult.Return.Factories;
              $('.shippingCalc .fabricante').css('display', 'block');
              $timeout(function() {
                $scope.$evalAsync(function() {
                  $.fn.initChosen("#fabricante");
                  $('#fabricante').trigger("chosen:updated");
                });
              }, 50);
            } else {
              $('.shippingCalc .fabricante').css('display', 'none');
              $('.shippingCalc .result').removeClass('active');
              setErro(calcResult.Error);
              logger.log(calcResult.Error);
            }
            $scope.calculoFrete.valorFrete = valorFrete;
            $scope.calculoFrete.tempoFrete = tempoFrete;
            $scope.calcResult = true;
          })
          .catch(function(resp) {
            $scope.isCalculatingShipping = false;
            setErro('Não foi possível calcular o carrinho, por favor, tente novamente.');
            logger.logResponse(resp);
          });
      };

      const updateCombo = function(selector, dataArray, selectedItem, onChange) {
        $(selector).off();
        $(selector).chosen('destroy');
        var optionHtml = '';
        if (dataArray) {
          optionHtml += '<option value="">Limpar Filtro</option>';
          for (var i in dataArray) {
            item = dataArray[i];
            if (selectedItem == item || dataArray.length == 1)
              optionHtml += '<option value="' + item + '" selected>' + item + '</option>';
            else
              optionHtml += '<option value="' + item + '">' + item + '</option>';
          }
        }
        $(selector).html(optionHtml);
        setTimeout(() => {
          if (onChange && typeof(onChange) === 'function') {
            initChosen(selector).change(onChange);
          } else {
            initChosen(selector);
          }
        }, 100);
      };

      const updateSkuAttributes = function(skus) {
        if (!skus) return;
        if (skus.Return.Voltages && primeiroFiltro != 'V') {
          if (skus.Return.Voltages.length == 1) {
            $scope.voltagem = skus.Return.Voltages[0];
          }
          updateCombo("#voltagem", skus.Return.Voltages, $scope.voltagem, $scope.voltagemOnChange);
        }
        if (skus.Return.Colors && primeiroFiltro != 'C') {
          if (skus.Return.Colors.length == 1) {
            $scope.cor = skus.Return.Colors[0];
          }
          updateCombo("#cor", skus.Return.Colors, $scope.cor, $scope.corOnChange);
        }
        if (skus.Return.Sizes && primeiroFiltro != 'T') {
          if (skus.Return.Sizes.length == 1) {
            $scope.tamanho = skus.Return.Sizes[0];
          }
          updateCombo("#tamanho", skus.Return.Sizes, $scope.tamanho, $scope.tamanhoOnChange);
        }
        if (skus.Return.Models && primeiroFiltro != 'M') {
          if (skus.Return.Models.length == 1) {
            $scope.modelo = skus.Return.Models[0];
          }
          updateCombo("#modelo", skus.Return.Models, $scope.modelo, $scope.corOnChange);
        }
      };

      const showProductPriceSection = function() {
        $scope.showProductPriceDetails = true;
        $('#fieldsetPrices').css('display', 'block');
        $('#fieldsetShipping').css('display', 'block');
        $('#pDisponivel').css('display', 'none');
      };

      const showProductUnavailableSection = function() {
        $scope.showProductPriceDetails = false;
        $('#fieldsetPrices').css('display', 'none');
        $('#fieldsetShipping').css('display', 'none');
        $('#pDisponivel').css('display', 'block');
      };

      const pesquisaSku = function() {
        blockRedeemButton();
        $scope.loadingDetails = true;
        showProductPriceSection();

        produtoService.pesquisaSkus(produto, $scope.modelo, $scope.voltagem, $scope.tamanho, $scope.cor)
          .then(function(skus) {
            if (skus.Success) {
              if (skus.Return) {
                updateSkuAttributes(skus);

                if (skus.Return.SkuCode && skus.Return.SkuCode.length) {
                  $("#md").val(skus.Return.SkuCode);
                  $("#spn-sku").html(skus.Return.SkuCode);

                  $scope.customAttributes = skus.Return.CustomAttributes;
                  if ($scope.customAttributes && $scope.customAttributes.length > 0) {
                    $("#btn-show-modal").css("visibility", "visible");
                    $scope.hasCustomAttributes = true;
                  } else {
                    $scope.hasCustomAttributes = false;
                    $("#btn-show-modal").css("visibility", "hidden");
                  }

                  $scope.dynamicPriceProductParametrization = {
                    dynamicPrice: skus.Return.DynamicPrice,
                    dynamicPricingSetter: skus.Return.DynamicPricingSetter,
                    dynamicPriceMinimumValue: skus.Return.DynamicPriceMinimumValue,
                    dynamicPriceMaximumValue: skus.Return.DynamicPriceMaximumValue,
                    isDynamicPriceDefinedByAdmin: skus.Return.DynamicPrice && skus.Return.DynamicPricingSetter != "PARTICIPANT",
                    isDynamicPriceDefinedByParticipant: skus.Return.DynamicPrice && skus.Return.DynamicPricingSetter == "PARTICIPANT",
                    hasDynamicPriceRange: skus.Return.DynamicPriceMinimumValue != null || skus.Return.DynamicPriceMaximumValue
                  };
                  $scope.showDynamicPriceRange = $scope.dynamicPriceProductParametrization.dynamicPrice && $scope.dynamicPriceProductParametrization.hasDynamicPriceRange;

                  $scope.showProductPriceDetails = !$scope.dynamicPriceProductParametrization.dynamicPrice;

                  if ($scope.dynamicPriceProductParametrization.isDynamicPriceDefinedByParticipant) {
                    $scope.loadingDetails = false;
                    $(".button.button-primary.btn-buy").css("margin-top", "0");
                  } else {
                    $scope.consultaDisponibilidade(skus.Return.SkuCode);
                  }
                } else {
                  $scope.loadingDetails = false;
                  $scope.hasCustomAttributes = false;
                  showProductPriceSection();
                }
              } else {
                $scope.loadingDetails = false;
                $scope.hasCustomAttributes = false;
                showProductUnavailableSection();
              }
            } else if (!skus.Success && skus.Error) {
              logger.log(skus.Error);
              $scope.loadingDetails = false;
              setErro(skus.Error);
            }
            unblockRedeemButton();
          })
          .catch(function(response) {
            $scope.loadingDetails = false;
            setErro('Não foi possível carregar o SKU, por favor, tente novamente.');
            logger.logResponse(response);
            unblockRedeemButton();
          });
      };

      $scope.consultaDisponibilidade = function(sku) {
        if (!sku) {
          $scope.loadingDetails = false;
          return;
        }

        if ($scope.dynamicPriceProductParametrization.isDynamicPriceDefinedByParticipant) {
          if ($scope.priceDefinedByParticipant < $scope.dynamicPriceProductParametrization.dynamicPriceMinimumValue) {
            return setErro('Valor mínimo para resgate do produto é ' + $scope.dynamicPriceProductParametrization.dynamicPriceMinimumValue);
          }

          if ($scope.priceDefinedByParticipant > $scope.dynamicPriceProductParametrization.dynamicPriceMaximumValue) {
            return setErro('Valor máximo para resgate do produto é ' + $scope.dynamicPriceProductParametrization.dynamicPriceMaximumValue);
          }
        }

        $scope.loadingDetails = true;
        produtoService.consultaDisponibilidade($("#id").val(), sku, 1, $scope.priceDefinedByParticipant)
          .then(function(dispRet) {
            $scope.loadingDetails = false;
            if (dispRet.Success) {
              if (dispRet.Return.Available === true) {
                showProductPriceSection();
                $('.points-purchase').css('display', 'none');

                if ($scope.dynamicPriceProductParametrization.dynamicPrice) {
                  $scope.showProductPriceDetails = false;
                  return;
                }

                if (dispRet.Return.Price) {
                  $scope.showProductPriceDetails = true;
                  $scope.productPrice = dispRet.Return.Price;
                  $scope.showDynamicPriceRange = $scope.dynamicPriceProductParametrization.dynamicPrice && dispRet.Return.DynamicPrice;
                  $("span#preco").css("display", "inline-block").html($filter('currency')(dispRet.Return.Price, ''));

                  $scope.showProductPriceFrom = dispRet.Return.PriceFrom && dispRet.Return.PriceFrom > dispRet.Return.Price;
                  if ($scope.showProductPriceFrom) {
                    $("span#precoDe").html($filter('currency')(dispRet.Return.PriceFrom, ''));
                    $(".button.button-primary.btn-buy").css("margin-top", "0.5em");
                  } else {
                    $(".button.button-primary.btn-buy").css("margin-top", "0");
                  }
                }
              } else {
                showProductUnavailableSection();
              }
            } else if (dispRet.Error) {
              setErro(dispRet.Error);
              logger.log(dispRet.Error);
            }
          })
          .catch(function(response) {
            $scope.loadingDetails = false;
            setErro('Não foi possível consultar disponibilidade do produto, por favor, atualize a página.');
            logger.logResponse(response);
          });
      };

      $scope.consultaDisponibilidadePrecoParticipante = function() {
        $scope.priceDefinedByParticipant = $('#priceDefinedByParticipant').val();
        $scope.consultaDisponibilidade($("#md").val());
      };

      $scope.registraInteresse = function() {
        $scope.loadingAviso = true;
        sendEvent('Produto', 'Registro Interesse', 'SKU ' + $("#md").val(), 1);
        produtoService.registraInteresse($("#p").val())
          .then(function(dispRet) {
            $scope.loadingAviso = false;
            if (dispRet.Success) {
              $scope.registroAviso = "Você será notificado assim que houver disponibilidade do produto.";
            } else {
              $scope.erroRegistroAviso = dispRet.Error;
              logger.log(dispRet.Error);
            }
          })
          .catch(function(response) {
            $scope.erroRegistroAviso = "Não foi possível completar a operação, por favor, tente novamente.";
            logger.logResponse(response);
          });
      };

      $scope.onShippingAddressChange = function() {
        if ($scope.shippingZipcode === 'other') {
          $.fancybox({
            maxWidth: 720,
            maxHeight: 480,
            padding: 0,
            href: '/Carrinho/NovoEndereco',
            type: 'iframe',
            beforeShow: function() {
              this.maxWidth = 720;
              this.maxHeight = 480;
              this.width = $(".fancybox-iframe").contents().find("html").width();
              this.height = $(".fancybox-iframe").contents().find("html").height();
            },
            afterClose: function() {
              var cepField = $('#shippingZipcode');
              if (cepField && cepField.length) {
                cepField = cepField[0];
                $scope.shippingZipcode = cepField.value;
                $('#shippingZipcode').trigger("chosen:updated");
              }
            }
          });
          $scope.shippingZipcode = null;
        }
      };

      $scope.onCepChange = function() {
        const selectedCep = $('#shippingZipcode').val();
        if (!selectedCep) {
          showWarningSwal('Selecione um endereço para calcular o frete.');
          return;
        }
        $scope.isCalculatingShipping = false;
        if (selectedCep == -1) {
          $scope.cepAlternativo = "";
          $("#cepAlternativo").val($scope.cepAlternativo);
          $scope.resultadoCalculo = false;
          $scope.mostraDivCalculo = true;
          $scope.novoCep = true;
          $(".shippingCalc .other").addClass("active");
          $('.shippingCalc .result').removeClass('active');
        } else {
          $scope.mostraDivCalculo = true;
          $scope.novoCep = false;
          $scope.resultadoCalculo = false;
          $(".shippingCalc .other").removeClass("active");
          $('.shippingCalc .result').removeClass('active');
          calculaFrete(selectedCep);
        }
      };

      $scope.calculaFrete = function() {
        $scope.cepAlternativo = $("#cepAlternativo").val();
        calculaFrete($scope.cepAlternativo);
      };

      const limpaFiltros = function() {
        $scope.tamanho = null;
        $scope.modelo = null;
        $scope.cor = null;
        $scope.voltagem = null;
      }

      $scope.tamanhoOnChange = function() {
        if (primeiroFiltro === 'T') {
          limpaFiltros();
        }
        $scope.tamanho = $("#tamanho").val();
        if (!primeiroFiltro && $scope.tamanho) {
          primeiroFiltro = 'T';
        }
        pesquisaSku();
      };

      $scope.modelOnChange = function() {
        if (primeiroFiltro === 'M') {
          limpaFiltros();
        }
        $scope.modelo = $("#modelo").val();
        if (!primeiroFiltro && $scope.modelo) {
          primeiroFiltro = 'M';
        }
        pesquisaSku();
      };

      $scope.corOnChange = function() {
        if (primeiroFiltro === 'C') {
          limpaFiltros();
        }
        $scope.cor = $("#cor").val();
        if (!primeiroFiltro && $scope.cor) {
          primeiroFiltro = 'C';
        }
        pesquisaSku();
      };

      $scope.voltagemOnChange = function() {
        if (primeiroFiltro === 'V') {
          limpaFiltros();
        }
        $scope.voltagem = $("#voltagem").val();
        if (!primeiroFiltro && $scope.voltagem) {
          primeiroFiltro = 'V';
        }
        pesquisaSku();
      };

      $scope.addItemCarrinho = function() {
        carrinhoService.addItemCarrinho()
          .then(function(retornoCarrinho) {
            if (retornoCarrinho.Success) {
              $location.path('/Carrinho/Index');
            } else if (retornoCarrinho.Error) {
              logger.log(retornoCarrinho.Error);
            }
          })
          .catch(function(resp) {
            $scope.isCalculatingShipping = false;
            logger.logResponse(resp);
          });
      };

      $scope.initSearch = function(prod, codEan, cat, carEnd, isDynamicPriceDefinedByAdmin, isDynamicPriceDefinedByParticipant) {
        const skuCode = $("#md").val();
        sendEvent('Produto', 'View', 'SKU ' + skuCode, 1);
        sendCustomEvent('produto_visualizacao', {
          id: $("#id").val(),
          sku: skuCode,
          ean: codEan,
          marca: $("#spn-brand").text(),
          modelo: $("#model").val(),
          cor: $("#cor").val(),
          voltagem: $("#voltagem").val(),
          tamanho: $("#tamanho").val(),
          departamento: $('#department').text(),
          categoria: $('#category').text(),
          subcategoria: $('#subcategory').text(),
        });
        $.fn.initChosen("select.autostart");
        produto = prod;
        ean = codEan;

        if (isDynamicPriceDefinedByAdmin || isDynamicPriceDefinedByParticipant) {
          $scope.showProductPriceDetails = false;
          $scope.dynamicPriceProductParametrization = {
            dynamicPrice: true,
            isDynamicPriceDefinedByAdmin: isDynamicPriceDefinedByAdmin,
            isDynamicPriceDefinedByParticipant: isDynamicPriceDefinedByParticipant
          }
          $scope.modelo = $("#model").val();
          pesquisaSku();
        }

        if (ean) {
          carregaMarketplace();
        }
        searchProductsSimilares(cat);
        if (carEnd) {
          carregaEnderecosEntrega();
        }
      };
    }]);

function consultaDisponibilidade() {
  var txtQtde = document.getElementById('quantidade');
  var scope = angular.element(txtQtde).scope();
  scope.$apply(function() {
    scope.consultaDisponibilidade(txtQtde.value);
  });
}

function verifyIfAnyRequiredEmpty(elemQuery) {
  var requiredAttrs = $(elemQuery);
  var isValid = false;
  if (requiredAttrs && requiredAttrs.length > 0) {
    requiredAttrs.each(function(idx) {
      if (idx >= 0 && requiredAttrs[idx]) {
        if ((requiredAttrs[idx].type == 'checkbox' && !requiredAttrs[idx].checked)
          || (!requiredAttrs[idx].value || requiredAttrs[idx].value.trim().length == 0)) {
          isValid = true;
        }
      }
    });
  }
  return isValid;
}

function sendWithCustomAttributes() {
  if (verifyIfAnyRequiredEmpty('.required-attr')) {
    $("#prod-attrs-msg").html('Preencha os campos obrigatórios para prosseguir.').css("display", "block");
    return;
  } else {
    $("#prod-attrs-msg").css("display", "none").html('');
  }
  $("#btn-attr-confirm").addClass('processing').text('Aguarde');
  $('#frm-prod').submit();
}

function verifyRequired() {
  if (verifyIfAnyRequiredEmpty('#cor')) {
    showWarningSwal('Selecione a cor desejada do produto.');
    return false;
  }
  if (verifyIfAnyRequiredEmpty('#tamanho')) {
    showWarningSwal('Selecione o tamanho desejado do produto.');
    return false;
  }
  if (verifyIfAnyRequiredEmpty('#voltagem')) {
    showWarningSwal('Selecione a voltagem desejada do produto.');
    return false;
  }
  if (verifyIfAnyRequiredEmpty('#modelo')) {
    showWarningSwal('Selecione o modelo desejado do produto.');
    return false;
  }
  if (verifyIfAnyRequiredEmpty('#quantidade')) {
    showWarningSwal('Preencha a quantidade desejada do produto.');
    return false;
  }

  if (verifyIfAnyRequiredEmpty('.required')) {
    showWarningSwal('Aviso', 'Selecione o(s) atributo(s) desejado do produto para continuar.');
    return false;
  }
  return true;
}

function blockRedeemButton() {
  $(".btn-buy").attr("disabled", "disabled").addClass("processing");
}

function unblockRedeemButton() {
  $(".btn-buy").removeAttr("disabled").removeClass("processing");
}

function onAddToCart() {
  addLoaderToButton();
  const skuCode = $("#md").val();
  sendEvent('Produto', 'Add', 'SKU ' + skuCode, 1, $('#priceDefinedByParticipant').val());
  sendCustomEvent('carrinho_adicionar', {
    id: $("#id").val(),
    sku: skuCode,
    modelo: $("#model").val(),
    voltagem: $("#voltagem").val(),
    cor: $("#cor").val(),
    tamanho: $("#tamanho").val(),
    quantidade: $('#quantidade').val(),
    marca: $("#spn-brand").text(),
    departamento: $('#department').text(),
    categoria: $('#category').text(),
    subcategoria: $('#subcategory').text()
  });
}

$(document).ready(function() {
  $("#btn-show-modal").fancybox({
    scrolling: 'no',
    titleShow: false,
    beforeShow: function() {
      $("#btn-attr-confirm").removeClass('processing').text('Confirmar e Adicionar no Carrinho');
    },
    onClosed: function() {
      $("#btn-attr-confirm").removeClass('processing').text('Confirmar e Adicionar no Carrinho');
      $("#modal-custom-attrs").hide();
    }
  });
  // $("#btn-attr-confirm").on('click', sendWithCustomAttributes);
});
