img.loader {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: 120px;
}

fieldset .field span {
  display: inline-block;
}

fieldset .field input,
fieldset .field select,
fieldset .field textarea
{
  width: 100%;
}

.chosen-drop .chosen-results { max-height: 500px }


/* .produto-detalhes .fancybox-inner { padding-top: 1.7em; } */
.product-details .gallery .cycle-carousel-wrap { max-width: 64px; }
.product-details .gallery .controlNav .prev, .product-details .gallery .controlNav .next { padding: 0.15em 0.3em; }

.product-details .div-loading div { text-align: left; }
.product-details .div-loading img { width: 150px; }
.product-details .label-for-radio { cursor: pointer; }
.product-details .aviso-produto-esgotado { width: 30px; }
.product-details .img-ranking { display: inline-block; width: 50px !important; height: 50px !important; }
.product-details .shippingCalc .fabricante { padding-top: 1em; margin-bottom: 0px !important; display: none; }
.product-details .shippingCalc .fabricante select { width: 221px; }
.product-details .custom-attrs { min-width: 700px; height: 100%; overflow-y: auto; }
.product-details .custom-attrs h1 { font-size: 1.2em; }
.product-details .custom-attrs .btn-attr-confirm { float: right; margin-bottom: 1em; }
.product-details .custom-attrs #prod-attrs-msg { display: none; }

.primeiro-acesso #page {
  margin-bottom: 2em;
}

.CustomValidationError, .validation-invalid {
  color: #F83C3C;
}

.info.header-info {
  text-align: center;
  max-height: 3em;
  padding: 0.70em 1em;
}

.info.header-info span {
  font-size: 24px;
}

/* Customização do Swal - transferir para o tema */
.swal-button-container .swal-button.swal-button--confirm {
  background-color: #0058ff;
}
.breadcrumbs+#mediabox .nivoSlider .nivo-prevNav {
  left: 1%;
  margin-left: 0;
}
.breadcrumbs+#mediabox .nivoSlider .nivo-nextNav {
  right: 1%;
  margin-right: 0;
}
.swal-title {
  font-size: 20px;
}
.fluid {
  box-sizing: border-box;
  width: 100%;
}
.display-block {
	display: block !important;
}
.button.inline {
  -webkit-border-radius: .25em;
  border-radius: .25em;
}
.button.inline:after {
  content: '';
  margin: 0px;
}
.button.btn-fluid {
  position: absolute;
  right: 0px;
}

.bottom-m1 {
  margin-bottom: 1em;
}
.pull-left {
  float: left !important;
}
.right-p1 {
  padding-right: 1em;
}
.top-p29 {
  padding-top: 29px;
}
.top-p85 {
  padding-top: 8.5px;
}

.discount-coupon {
  text-transform: uppercase;
}

.list-address {
  padding: 1.5em;
}
.list-address.cart-new-address {
  text-align: center;
  padding: 0 1.5em 1em 1.5em !important;
}
.list-address.cart-new-address .button {
  width: 100%;
}
.list-address .link-edit-address {
  position: relative;
  display: block;
}

.cart-products table tbody tr td .photo img {
  width: 100%;
}
p.price span span.promo-d span,
p.price span span.promo-por,
p.price strong span {
  display: inline-block;
}

.order-confirmation .register .recipient,
.order-confirmation .cart-total {
  margin-bottom: 2em;
}

p.price span.promo-d span {
    text-decoration: line-through;
}

article.list-products ul.products .photo img {
  width: 500px;
}
.load-more {
  margin-bottom: 2em;
}
.listing {
  list-style: disc;
}
.break {
  display: block;
}
.inline {
  display: inline-block !important;
}
.conteudo-pedido-finalizado {
  margin-top: 1em;
  margin-bottom: 1em;
}

form fieldset .field.field-metadata:nth-child(2) {
  width: 48.93617% !important;
  margin-right: 0px !important;
  clear: none !important;
}
.field.state-inscription-exempt {
  width: 100% !important;
  padding-top: 1em;
  height: 2em;
}

.field.state-inscription-exempt label {
  font-size: 100%;
}

.ng-touched.ng-invalid,
.ng-dirty.ng-invalid,
.ng-invalid-cpf,
.ng-invalid-telefone,
.ng-invalid-br-phone-number,
.ng-invalid-cep,
.ng-invalid-endereco,
.chosen-required .chosen-container {
  border-color: #d05151 !important;
  border-width: 2px !important;
}
.platform-chosen .chosen-container { width: 100% !important; margin-bottom: 0px !important; }
.no-border, .no-border td { border: 0px; }
.recovery-note {
  font-size: 14px;
  line-height: 1.5em;
}

.variable ul li,
.variable ul li img {
  width: 100%;
  height: 45px;
  border: none !important;
}

.armored_website {
  width: 140px !important;
}

.extra-service-actions {
  height: 50px;
}

.lojaespecial-header {
  position: relative;
  clear: both;
  overflow: hidden;
}

.banner.loja-especial img {
  width: 100%;
}

td.td-vouchers-links {
  padding: 0px;
  padding-top: 2em;
}

.td-vouchers-links table {
  margin-bottom: 0px;
}

article.lojaespecial {
  position: relative;
  width: 100%;
  height: auto;
  overflow: hidden;
  margin-bottom: 1em;
}

.lojaespecial img {
  width: 1905px;
  max-height: 300px;
}

/* === Footer - Inicio === */
footer .social-network i{
  display:inline-block;
  height:35px;
  width:35px;
  color:transparent;
  background-size:contain
}
footer .social-network i:before{
  content:"0"
}
@media only screen and (max-width: 599px){
  footer .social-network i{
    display:block;
  }
}
footer .social-network a:hover{text-decoration:none;
  opacity:0.8
}
footer .logo-facebook{
  background:url("../assets/img/social/facebook.png?12391239") no-repeat
}
footer .logo-instagram{
  background:url("../assets/img/social/instagram.png?1248749867") no-repeat
}
footer .logo-twitter{
  background:url("../assets/img/social/twitter.png?23908468gt0") no-repeat
}
footer .logo-youtube{
  background:url("../assets/img/social/youtube.png?232112848") no-repeat
}
footer .logo-snapchat{
  background:url("../assets/img/social/snapchat.png?2156136777") no-repeat
}
/* === Footer - fim === */

/* prepaid card style - start */
.card {
  height: 235px;
  max-width: 375px;
  border-radius: 16px;
  background-image: url("../assets/img/cards/card-all-bg-md-dark.png");
  background-color: #251843;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center center;
  color: #fff;
  margin: 35px auto;
  padding: 10px;
  -webkit-box-shadow: 2px 2px 20px #707070;
  box-shadow: 2px 2px 20px #707070;
}

.card-balance {
  font-weight: 700;
  font-size: 20px;
}

.card-number {
  font-size: 28px;
  padding: 139px 20px 10px 20px;
}

.card-number p {
  margin-bottom: 0px;
  text-align: left;
}

.card-status {
  float: right;
  padding: 5px;
  background: rgba(0, 0, 0, 0.65);
  width: 124px;
  border-radius: 21px;
  margin-top: 6px;
  height: 30px;
  min-width: 100px;
  font-size: 12px;
  line-height: 21px;
}

.dot {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  display: inline-block;
}

.balance-text {
  text-align: left;
  font-size: 12px;
  margin: 0;
  line-height: 10px;
}

.green-background-color {
  background-color: rgba(61, 234, 34, 0.671);
}

.red-background-color {
  background-color: rgba(243, 53, 15, 0.671);
}

.status {
  height: 10px;
  margin-left: 5px;
}

.card-select {
  display: block !important;
  visibility: visible;
  position: absolute;
}

.user-validation-template {
  margin-top: 20px;
}

.top-block {
  display: inline-block;
  margin-bottom: 10px;
  height: 38px;
  padding: 0px 0px 0px 20px;
  float: right;
}

.bottom-block {
  padding: 0px 0px 0px 20px;
}

h3.card-name {
  float: right;
  font-size: 16px;
  font-style: normal;
  font-weight: lighter;
  color: #fff;
}

.user-validation-form-buttons .back {
  border: 1px solid;
  padding: 10px 12px;
  border-radius: 2em;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 14px;
  margin: 1.9em 0.5em 1em 0.5em;
  margin-left: 0;
}

.prepaid-card-menu button {
  margin-left: 5px;
}

@media only screen and (max-width: 1023px) {
  .card {
    height: 235px;
    max-width: 375px;
    background-image: url("../assets/img/cards/card-all-bg-md-dark.png");
    background-size: cover;
  }
}

@media only screen and (max-width: 415px) {
  .card {
    height: 178px;
    max-width: 300px;
    background-image: url("/assets/img/cards/card-all-bg-md-dark.png");
    background-size: cover;
  }
  .top-block {
    padding: 4px;
  }
  .card-number {
    font-size: 18px;
    padding: 85px 10px 4px 10px;
  }
  .card-balance {
    font-size: 16px;
  }
  .card-status {
    margin-top: 10px;
    height: 22px;
    font-size: 8px;
    line-height: 10px;
    width: 0px;
  }
  .bottom-block {
    padding: 0px 0px 0px 10px;
  }
  .prepaid-card-menu button {
    margin-left: 5px;
    width: 100%;
  }
}

@media only screen and (max-width: 320px) {
  .card {
    height: 178px;
    max-width: 300px;
    background-image: url("/assets/img/cards/card-all-bg-md-dark.png");
    background-size: cover;
  }
  .top-block {
    padding: 4px;
  }
  .card-number {
    font-size: 12px;
    padding: 85px 10px 4px 10px;
  }
  .card-balance {
    font-size: 13px;
  }
  .card-status {
    margin-top: 10px;
    height: 22px;
    font-size: 8px;
    line-height: 10px;
    width: 0px;
  }
  .bottom-block {
    padding: 0px 0px 0px 10px;
  }
  .prepaid-card-menu button {
    margin-left: 5px;
    width: 100%;
  }
}

@media only screen and (max-width: 280px) {
  .card {
    height: 178px;
    max-width: 300px;
    background-image: url("/assets/img/cards/card-all-bg-md-dark.png");
    background-size: cover;
  }
  .top-block {
    padding: 4px;
  }
  .card-number {
    font-size: 10px;
    padding: 85px 10px 4px 10px;
  }
  .card-balance {
    font-size: 10px;
  }
  .card-status {
    margin-top: 10px;
    height: 22px;
    font-size: 8px;
    line-height: 10px;
    width: 100px;
  }
  .bottom-block {
    padding: 0px 0px 0px 10px;
  }
  .prepaid-card-menu button {
    margin-left: 5px;
    width: 100%;
  }
}
/* prepaid card style - end */

/* === lojas especiais destacadas - start */
.vitrine-mktplace {
  width: 90%;
  margin: 0 auto;
}
.vitrine-mktplace .mktp-center-content {
  display: flex!important;
  justify-content: center!important;
  height: 100%;
}


.vitrine-mktplace .mktp-blc-banner, .special-shop-feature-card .mktp-blc-banner img {
  border: 1px solid #E5E5E5;
  box-sizing: border-box;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  border-radius: 12px;
  padding: 0;
  margin: 8px 0;
  /* height: 180px; */
  height: auto;
  width: 100%;
}

.mktp-group-card h1,
.mktp-group-card h2,
.mktp-group-card h3 {
  padding: 40px 0 20px 0;
  font-size: 160%;
}
.special-shop-feature-card .container {
  margin: 0 auto;
  padding: 4em 4.5em 4em 4.5em !important;
}

@media (max-width: 1200px) {
  .vitrine-mktplace .mktp-blc-banner, .special-shop-feature-card .mktp-blc-banner img {
    height: auto;
  }
}

@media (max-width: 960px) {
  .vitrine-mktplace {
      width: 100%;
      display: flex!important;
      justify-content: center!important;
      height: 100%;
      padding: 12px;
  }
  .mktp-group-card-m {
      padding: 15px;
      margin-left: 5px;
      margin-right: 5px;
  }
  .mktp-group-card h1,
  .mktp-group-card h2,
  .mktp-group-card h3 {
      padding: 16px 0 0px 0;
      font-size: 200%;
  }
}
@media (max-width: 600px) {
  .vitrine-mktplace {
      padding: 28px;
  }
  .mktp-group-card h1,
  .mktp-group-card h2,
  .mktp-group-card h3 {
      padding: 16px 0 0px 0;
      font-size: 180%;
  }
}
/* === lojas especiais destacadas - end */
