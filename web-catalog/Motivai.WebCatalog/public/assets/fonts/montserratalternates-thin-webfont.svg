<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="montserrat_alternatesthin" horiz-adv-x="1374" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="512" />
<glyph unicode="&#xfb01;" horiz-adv-x="1296" d="M236 0v1266q0 119 67.5 189.5t188.5 70.5q59 0 115 -19.5t91 -56.5l-28 -29q-69 66 -176 66t-162.5 -59.5t-55.5 -172.5v-196h359v-39h-359v-1020h-40zM987 1405q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5zM1020 0v1059h41 v-1059h-41z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1161" d="M47 1020v39h205v108q0 180 79 269.5t224 89.5t224 -89.5t79 -269.5v-1108q0 -231 205 -231q59 0 104 18l7 -36q-54 -21 -109 -21q-120 0 -184 70t-64 190v1118q0 320 -262 320t-262 -320v-108h358v-39h-358v-1020h-41v1020h-205z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="512" />
<glyph unicode=" "  horiz-adv-x="512" />
<glyph unicode="&#x09;" horiz-adv-x="512" />
<glyph unicode="&#xa0;" horiz-adv-x="512" />
<glyph unicode="!" horiz-adv-x="495" d="M195 47q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5zM225 1434h45l-8 -1049h-29z" />
<glyph unicode="&#x22;" horiz-adv-x="684" d="M166 1434h45l-4 -512h-37zM473 1434h45l-4 -512h-37z" />
<glyph unicode="#" horiz-adv-x="1396" d="M72 430v39h309l61 496h-307v39h311l54 430h41l-54 -430h492l53 430h41l-53 -430h303v-39h-307l-62 -496h306v-39h-310l-53 -430h-41l53 430h-491l-53 -430h-41l53 430h-305zM422 469h491l62 496h-492z" />
<glyph unicode="$" horiz-adv-x="1230" d="M119 197l28 26q67 -79 189 -132.5t262 -57.5v669q-57 14 -94.5 24.5t-88 29t-83.5 37t-68.5 47t-56 61.5t-34 78.5t-13.5 99.5q0 146 110.5 248.5t327.5 112.5v239h39v-239h2q115 0 226.5 -35.5t189.5 -95.5l-23 -33q-84 61 -187.5 93t-203.5 32h-4v-668q192 -47 272 -84 q167 -77 196 -225q7 -34 7 -72q0 -152 -120.5 -253.5t-354.5 -104.5v-240h-39v240q-149 4 -279 60t-200 143zM201 1077q0 -46 11.5 -85.5t30 -68.5t50 -54.5t61.5 -42.5t75 -34t81 -27.5t88 -23.5v658q-194 -9 -295.5 -98t-101.5 -224zM637 33q214 3 324 92.5t110 226.5 q0 49 -13.5 90t-33 70t-56 55t-67 42t-83 33.5t-86.5 26t-95 23.5v-659z" />
<glyph unicode="%" horiz-adv-x="1638" d="M102 1055q0 173 85.5 279t220.5 106t220 -106t85 -279t-85 -279t-220 -106t-220.5 106t-85.5 279zM143 1055q0 -159 73 -252.5t192 -93.5q118 0 191 93.5t73 252.5t-73 252.5t-191 93.5q-119 0 -192 -93.5t-73 -252.5zM307 0l979 1434h45l-979 -1434h-45zM926 379 q0 173 85 279t220 106t220 -106t85 -279t-85 -279t-220 -106t-220 106t-85 279zM967 379q0 -159 73 -252.5t191 -93.5t191 93.5t73 252.5t-73 252.5t-191 93.5t-191 -93.5t-73 -252.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1298" d="M115 344q0 121 86 225.5t301 233.5l-17 16q-106 116 -143 188t-37 156q0 125 83.5 201t225.5 76q133 0 211 -68.5t78 -187.5q0 -103 -73.5 -187t-268.5 -202l469 -512q75 120 113 311l35 -8q-40 -199 -121 -330l227 -250l-31 -26l-221 241q-164 -227 -473 -227 q-196 0 -320 96.5t-124 253.5zM156 344q0 -142 112 -228.5t291 -86.5q294 0 449 219l-480 526q-214 -130 -293 -223t-79 -207zM346 1163q0 -75 36 -142.5t140 -182.5l15 -15q189 114 258 189t69 172q0 100 -66 158.5t-184 58.5q-126 0 -197 -65t-71 -173z" />
<glyph unicode="'" horiz-adv-x="376" d="M166 1434h45l-4 -512h-37z" />
<glyph unicode="(" horiz-adv-x="636" d="M252 561q0 283 59 529.5t168 429.5h39q-225 -378 -225 -959q0 -580 225 -958h-39q-110 188 -168.5 432.5t-58.5 525.5z" />
<glyph unicode=")" horiz-adv-x="636" d="M119 -397q225 378 225 958q0 581 -225 959h39q109 -183 168 -429.5t59 -529.5q0 -281 -58.5 -525.5t-168.5 -432.5h-39z" />
<glyph unicode="*" horiz-adv-x="733" d="M55 1014l275 157l-275 158l19 33l274 -158v316h37v-316l274 158l19 -33l-272 -158l272 -157l-19 -33l-274 158v-316h-37v316l-274 -158z" />
<glyph unicode="+" horiz-adv-x="1146" d="M154 696v39h399v391h41v-391h399v-39h-399v-389h-41v389h-399z" />
<glyph unicode="," horiz-adv-x="372" d="M127 -287l59 281q-23 1 -38 16.5t-15 36.5q0 22 15 37.5t38 15.5q24 0 39 -16.5t15 -36.5q0 -7 -9 -59l-67 -275h-37z" />
<glyph unicode="-" horiz-adv-x="778" d="M133 522v39h512v-39h-512z" />
<glyph unicode="." horiz-adv-x="372" d="M133 47q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="/" horiz-adv-x="614" d="M-41 -205l676 1929h41l-676 -1929h-41z" />
<glyph unicode="0" horiz-adv-x="1335" d="M121 717q0 327 151 525t396 198t395.5 -198t150.5 -525t-150.5 -525t-395.5 -198t-396 198t-151 525zM162 717q0 -313 139 -498.5t367 -185.5t367 185.5t139 498.5t-139 498.5t-367 185.5t-367 -185.5t-139 -498.5z" />
<glyph unicode="1" horiz-adv-x="700" d="M20 1395v39h408v-1434h-39v1395h-369z" />
<glyph unicode="2" horiz-adv-x="1136" d="M57 1255q165 185 471 185q203 0 322 -98.5t119 -268.5q0 -107 -49.5 -206t-182.5 -234l-585 -594h895v-39h-959v31l621 628q129 130 174 221.5t45 190.5q0 153 -104 241.5t-296 88.5q-287 0 -442 -174z" />
<glyph unicode="3" horiz-adv-x="1122" d="M39 197l27 26q63 -83 187 -136.5t277 -53.5q210 0 321.5 96t111.5 264q0 169 -115 265t-344 96h-74v30l465 611h-811v39h866v-31l-465 -610h17q247 0 374.5 -108t127.5 -292q0 -182 -124 -290.5t-350 -108.5q-159 0 -292 56t-199 147z" />
<glyph unicode="4" horiz-adv-x="1318" d="M94 426v31l807 977h49l-800 -969h774v375h41v-375h319v-39h-319v-426h-41v426h-830z" />
<glyph unicode="5" horiz-adv-x="1122" d="M66 197l26 26q62 -83 186.5 -136.5t274.5 -53.5q214 0 325 100.5t111 267.5q0 91 -28.5 157.5t-93 116t-174.5 74.5t-267 25h-270l75 660h727v-39h-692l-67 -582h235q162 0 278 -28t185.5 -83t101 -129t31.5 -172q0 -181 -123.5 -294t-353.5 -113q-156 0 -289 56.5 t-198 146.5z" />
<glyph unicode="6" horiz-adv-x="1212" d="M121 717q0 352 164.5 537.5t445.5 185.5q193 0 305 -68l-20 -35q-102 64 -285 64q-265 0 -417 -174t-152 -506q0 -148 28 -256q27 170 159 268t317 98q215 0 343 -113t128 -304q0 -189 -128 -304.5t-329 -115.5q-274 0 -416.5 191.5t-142.5 531.5zM225 410 q0 -69 29.5 -135t84.5 -120.5t144 -88t197 -33.5q185 0 300.5 105t115.5 276q0 173 -116 276t-314 103q-194 0 -317.5 -110.5t-123.5 -272.5z" />
<glyph unicode="7" horiz-adv-x="1167" d="M74 1106v328h995v-31l-641 -1403h-47l639 1395h-905v-289h-41z" />
<glyph unicode="8" horiz-adv-x="1277" d="M113 385q0 145 95.5 240.5t270.5 124.5q-153 29 -234 115t-81 216q0 165 128 262t345 97q218 0 347.5 -97t129.5 -262q0 -129 -83 -215.5t-236 -115.5q177 -29 273.5 -124.5t96.5 -240.5q0 -182 -140 -286.5t-388 -104.5t-386 104t-138 287zM154 383q0 -163 127.5 -256.5 t355.5 -93.5q229 0 358 94t129 256q0 160 -129.5 253t-357.5 93t-355.5 -93t-127.5 -253zM205 1083q0 -146 114 -230.5t318 -84.5t320 84.5t116 228.5q0 147 -118 233.5t-318 86.5q-199 0 -315.5 -86t-116.5 -232z" />
<glyph unicode="9" horiz-adv-x="1212" d="M76 1020q0 189 127.5 304.5t328.5 115.5q275 0 417.5 -191.5t142.5 -531.5q0 -352 -164.5 -537.5t-446.5 -185.5q-195 0 -305 67l21 35q101 -63 284 -63q265 0 417.5 174t152.5 506q0 145 -29 256q-27 -170 -158.5 -268.5t-316.5 -98.5q-215 0 -343 113.5t-128 304.5z M117 1020q0 -173 116 -276t314 -103q194 0 317 110.5t123 272.5q0 69 -29.5 135t-84.5 120.5t-144 88t-197 33.5q-185 0 -300 -105t-115 -276z" />
<glyph unicode=":" horiz-adv-x="372" d="M133 47q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5zM133 1012q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -38t-38 -16t-37.5 16t-15.5 38z" />
<glyph unicode=";" horiz-adv-x="372" d="M127 -287l59 281q-23 1 -38 16.5t-15 36.5q0 22 15 37.5t38 15.5q24 0 39 -16.5t15 -36.5q0 -7 -9 -59l-67 -275h-37zM133 1012q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -38t-38 -16t-37.5 16t-15.5 38z" />
<glyph unicode="&#x3c;" horiz-adv-x="1146" d="M154 694v45l839 351v-46l-792 -327l792 -328v-45z" />
<glyph unicode="=" horiz-adv-x="1146" d="M154 473v39h839v-39h-839zM154 922v39h839v-39h-839z" />
<glyph unicode="&#x3e;" horiz-adv-x="1146" d="M154 344v45l792 328l-792 327v46l839 -351v-45z" />
<glyph unicode="?" horiz-adv-x="1134" d="M41 1255q165 185 471 185q201 0 320.5 -94.5t119.5 -247.5q0 -74 -22 -136t-57 -106t-77.5 -83.5t-85.5 -78.5t-78 -81t-57 -100t-22 -128h-41q0 76 22 138.5t57 106.5t77.5 83t85.5 77.5t78 79.5t57 99t22 127q0 136 -104.5 220.5t-294.5 84.5q-287 0 -442 -174zM479 47 q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="@" horiz-adv-x="2113" d="M121 520q0 204 70 376t193.5 291t297.5 186t377 67q270 0 482 -112.5t332 -320.5t120 -477q0 -253 -79 -394.5t-206 -141.5q-85 0 -136.5 50.5t-51.5 137.5v142q-54 -152 -181 -241t-292 -89q-220 0 -367.5 151.5t-147.5 384.5t147.5 384t367.5 151q165 0 292 -89 t181 -239v322h41v-856q0 -88 40 -129t107 -41q107 0 175.5 132t68.5 365q0 194 -66.5 357t-184 275.5t-283 175.5t-359.5 63q-261 0 -465 -110t-318 -311t-114 -460q0 -257 113 -458t315.5 -312.5t460.5 -111.5q96 0 201 23t186 67l16 -37q-86 -47 -194.5 -70.5 t-208.5 -23.5q-270 0 -482 116.5t-330 327t-118 479.5zM573 530q0 -218 135 -357.5t339 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-339 -139t-135 -357z" />
<glyph unicode="A" horiz-adv-x="1646" d="M260 0v834q0 298 150 452t413 154t413 -154t150 -452v-834h-40v473h-1045v-473h-41zM301 512h1045v326q0 278 -137 420.5t-386 142.5t-385.5 -142.5t-136.5 -420.5v-326z" />
<glyph unicode="B" horiz-adv-x="1531" d="M272 0v1434h568q226 0 353.5 -93t127.5 -270q0 -134 -75 -219t-208 -117q361 -50 361 -360q0 -183 -130 -279t-388 -96h-609zM313 39h576q469 0 469 336q0 334 -469 334h-576v-670zM313 748h535q206 0 319 82.5t113 240.5t-113 241t-319 83h-535v-647z" />
<glyph unicode="C" horiz-adv-x="1456" d="M121 717q0 204 95.5 369.5t264 259.5t375.5 94q144 0 270.5 -46.5t216.5 -136.5l-24 -28q-180 172 -463 172q-146 0 -275 -53t-220.5 -143.5t-145 -217.5t-53.5 -270t53.5 -270t145 -217.5t220.5 -143.5t275 -53q281 0 463 174l24 -29q-91 -91 -217 -137.5t-270 -46.5 q-207 0 -375.5 94t-264 259.5t-95.5 369.5z" />
<glyph unicode="D" horiz-adv-x="1691" d="M272 0v1434h543q167 0 308.5 -54t239.5 -149t153 -227.5t55 -286.5t-55 -286.5t-153 -227.5t-239.5 -149t-308.5 -54h-543zM313 39h498q161 0 296.5 51t228 141t143.5 215t51 271t-51 271t-143.5 215t-228 141t-296.5 51h-498v-1356z" />
<glyph unicode="E" horiz-adv-x="1257" d="M133 377q0 145 91.5 235t246.5 117q-132 32 -204 120t-72 218q0 162 130.5 267.5t368.5 105.5q127 0 244.5 -32.5t200.5 -90.5l-21 -33q-186 117 -422 117q-222 0 -341 -93.5t-119 -242.5q0 -151 103 -234t286 -83h336v-39h-334q-207 0 -330 -83.5t-123 -246.5 q0 -159 125 -252.5t367 -93.5q165 0 299.5 48t212.5 134l30 -27q-82 -92 -224 -143t-320 -51q-256 0 -393.5 105.5t-137.5 277.5z" />
<glyph unicode="F" horiz-adv-x="1218" d="M260 0v987q0 209 126.5 331t375.5 122q262 0 420 -125l-27 -33q-150 119 -393 119q-232 0 -346.5 -109t-114.5 -311v-274h743v-39h-743v-668h-41z" />
<glyph unicode="G" horiz-adv-x="1585" d="M121 717q0 153 56.5 287t154 230t235.5 151t295 55q311 0 494 -183l-25 -28q-180 172 -469 172q-197 0 -358 -90t-251.5 -246.5t-90.5 -347.5q0 -156 57 -287.5t151.5 -216.5t215 -132.5t250.5 -47.5q133 0 259 49.5t222 148.5v471h39v-702h-39v174q-97 -90 -222.5 -135 t-258.5 -45q-108 0 -211.5 30t-194.5 91t-160 146t-109 203t-40 253z" />
<glyph unicode="H" horiz-adv-x="1671" d="M272 0v1434h41v-686h1045v686h41v-1434h-41v709h-1045v-709h-41z" />
<glyph unicode="I" horiz-adv-x="864" d="M96 0v39h316v1356h-316v39h672v-39h-315v-1356h315v-39h-672z" />
<glyph unicode="J" horiz-adv-x="976" d="M8 -8l29 24q59 -91 144 -139.5t186 -48.5q157 0 233 91t76 278v1198h-537v39h578v-1241q0 -404 -352 -404q-109 0 -204.5 54t-152.5 149z" />
<glyph unicode="K" horiz-adv-x="1419" d="M272 0v1434h41v-998l971 998h55l-639 -658l686 -776h-55l-661 748l-357 -367v-381h-41z" />
<glyph unicode="L" horiz-adv-x="1187" d="M272 0v1434h41v-1395h854v-39h-895z" />
<glyph unicode="M" horiz-adv-x="2506" d="M272 0v1434h41v-306q107 312 463 312q190 0 316 -91t170 -268q50 182 173 270.5t308 88.5q236 0 370 -137t134 -398v-905h-41v903q0 247 -120.5 372.5t-344.5 125.5t-343.5 -132.5t-119.5 -406.5v-862h-41v903q0 247 -119.5 372.5t-343.5 125.5t-342.5 -132.5 t-118.5 -406.5v-862h-41z" />
<glyph unicode="N" horiz-adv-x="1638" d="M272 0v1434h41v-342q55 173 187 260.5t327 87.5q258 0 404.5 -149t146.5 -427v-864h-41v862q0 264 -133 401.5t-379 137.5q-248 0 -380 -145t-132 -435v-821h-41z" />
<glyph unicode="O" horiz-adv-x="1712" d="M121 717q0 204 95.5 369.5t264 259.5t375.5 94t375.5 -94t264 -259.5t95.5 -369.5t-95.5 -369.5t-264 -259.5t-375.5 -94t-375.5 94t-264 259.5t-95.5 369.5zM162 717q0 -193 90.5 -349.5t249.5 -245.5t354 -89t354 89t249.5 245.5t90.5 349.5t-90.5 349.5t-249.5 245.5 t-354 89t-354 -89t-249.5 -245.5t-90.5 -349.5z" />
<glyph unicode="P" horiz-adv-x="1454" d="M272 0v1434h486q265 0 414 -124.5t149 -346.5t-149 -346.5t-414 -124.5h-445v-492h-41zM313 530h449q250 0 384 113t134 320t-133.5 319.5t-384.5 112.5h-449v-865z" />
<glyph unicode="Q" horiz-adv-x="1712" d="M121 717q0 204 95.5 369.5t264 259.5t375.5 94t375.5 -94t264 -259.5t95.5 -369.5q0 -201 -92.5 -365t-256 -258.5t-365.5 -99.5v-270h-41v270q-152 4 -284.5 60.5t-227 151.5t-149 227.5t-54.5 283.5zM162 717q0 -141 51.5 -266t140.5 -215.5t214.5 -144.5t267.5 -58 v387h41v-387q190 5 344 95.5t241.5 245.5t87.5 343q0 143 -53.5 270t-145 217.5t-220.5 143.5t-275 53t-275 -53t-220.5 -143.5t-145 -217.5t-53.5 -270z" />
<glyph unicode="R" horiz-adv-x="1466" d="M272 0v1434h486q265 0 414 -124.5t149 -346.5q0 -172 -92 -288t-260 -159l372 -516h-51l-364 506q-71 -14 -168 -14h-445v-492h-41zM313 530h449q250 0 384 113t134 320t-133.5 319.5t-384.5 112.5h-449v-865z" />
<glyph unicode="S" horiz-adv-x="1230" d="M119 197l28 26q70 -82 199 -136t275 -54q222 0 336 89.5t114 229.5q0 69 -25.5 123t-69 87t-102 59.5t-124 42t-135 34.5t-135 35.5t-124 46.5t-102 67t-69 96.5t-25.5 135.5q0 73 28.5 136.5t84.5 114.5t150 80.5t216 29.5q115 0 226.5 -35.5t189.5 -95.5l-23 -33 q-84 61 -187.5 93t-203.5 32q-216 0 -328 -91t-112 -233q0 -77 31 -134.5t84 -92.5t121 -60.5t144 -43.5t151.5 -37t143.5 -46.5t121 -65.5t84 -100t31 -145q0 -73 -29.5 -136.5t-87 -113.5t-153.5 -79t-219 -29q-155 0 -293 56.5t-211 146.5z" />
<glyph unicode="T" horiz-adv-x="1122" d="M8 1292q253 148 551 148q302 0 555 -148l-20 -32q-224 134 -512 141v-1401h-41v1401q-277 -3 -512 -141z" />
<glyph unicode="U" horiz-adv-x="1630" d="M260 569v865h41v-863q0 -263 132 -400.5t376 -137.5q246 0 377 144.5t131 434.5v822h41v-1434h-41v342q-55 -172 -186.5 -260t-323.5 -88q-256 0 -401.5 149t-145.5 426z" />
<glyph unicode="V" horiz-adv-x="1372" d="M20 1434h48l618 -1379l619 1379h47l-645 -1434h-41z" />
<glyph unicode="W" horiz-adv-x="2478" d="M260 547v887h41v-885q0 -261 119.5 -388.5t341.5 -127.5q220 0 338.5 127.5t118.5 388.5v885h41v-885q0 -261 117.5 -388.5t338.5 -127.5t341 127.5t120 388.5v885h41v-887q0 -274 -133.5 -413.5t-368.5 -139.5q-195 0 -318 96t-159 289q-36 -192 -160 -288.5t-317 -96.5 q-237 0 -369.5 139t-132.5 414z" />
<glyph unicode="X" horiz-adv-x="1271" d="M51 0l559 741l-518 693h51l494 -662l493 662h50l-518 -693l559 -741h-52l-534 711l-535 -711h-49z" />
<glyph unicode="Y" horiz-adv-x="1615" d="M250 946v488h41v-486q0 -263 132.5 -400.5t379.5 -137.5q249 0 380.5 144.5t131.5 434.5v445h41v-1039q0 -299 -145.5 -452.5t-407.5 -153.5q-341 0 -524 238l30 28q175 -227 496 -227q247 0 378.5 143t131.5 426v322q-55 -173 -187 -260.5t-327 -87.5q-259 0 -405 148.5 t-146 426.5z" />
<glyph unicode="Z" horiz-adv-x="1329" d="M113 0v31l516 678h-381v39h409l494 647h-1038v39h1095v-31l-497 -655h356v-39h-387l-510 -670h1069v-39h-1126z" />
<glyph unicode="[" horiz-adv-x="589" d="M272 -397v1917h267v-39h-226v-1839h226v-39h-267z" />
<glyph unicode="\" horiz-adv-x="614" d="M-61 1724h41l675 -1929h-41z" />
<glyph unicode="]" horiz-adv-x="589" d="M51 -358h225v1839h-225v39h266v-1917h-266v39z" />
<glyph unicode="^" horiz-adv-x="1150" d="M203 297l350 840h45l350 -840h-45l-328 793l-327 -793h-45z" />
<glyph unicode="_" horiz-adv-x="1024" d="M0 0h1024v-39h-1024v39z" />
<glyph unicode="`" horiz-adv-x="1228" d="M338 1468h82l287 -225h-62z" />
<glyph unicode="a" d="M111 530q0 233 147 384t367 151q165 0 292 -89t181 -239v322h41v-1059h-41v324q-54 -152 -181 -241t-292 -89q-220 0 -367 151.5t-147 384.5zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357z" />
<glyph unicode="b" d="M236 0v1520h40v-783q54 150 181.5 239t292.5 89q220 0 367 -151t147 -384t-147 -384.5t-367 -151.5q-165 0 -292.5 89.5t-181.5 240.5v-324h-40zM276 530q0 -218 135 -357.5t339 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-339 -139t-135 -357z" />
<glyph unicode="c" horiz-adv-x="1116" d="M111 530q0 235 146.5 385t373.5 150q116 0 215.5 -43.5t165.5 -128.5l-29 -31q-60 81 -151.5 122.5t-200.5 41.5q-208 0 -343.5 -139t-135.5 -357q0 -220 135.5 -358.5t343.5 -138.5q109 0 200.5 41.5t151.5 122.5l29 -31q-66 -85 -165.5 -128.5t-215.5 -43.5 q-227 0 -373.5 150.5t-146.5 385.5z" />
<glyph unicode="d" d="M111 530q0 233 147 384t367 151q165 0 292 -89t181 -239v783h41v-1520h-41v324q-54 -152 -181 -241t-292 -89q-220 0 -367 151.5t-147 384.5zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357z" />
<glyph unicode="e" horiz-adv-x="1204" d="M111 530q0 235 141 385t362 150q100 0 187.5 -38t149 -102.5t99 -150.5t44.5 -182l-930 -182q37 -171 166 -274t311 -103q111 0 206.5 41.5t156.5 122.5l28 -29q-69 -85 -171.5 -129.5t-219.5 -44.5q-230 0 -380 151t-150 385zM150 537q0 -47 6 -91l895 177 q-13 107 -68 197.5t-152 148t-217 57.5q-203 0 -333.5 -138.5t-130.5 -350.5z" />
<glyph unicode="f" horiz-adv-x="620" d="M236 0v1266q0 119 67.5 189.5t188.5 70.5q59 0 115 -19.5t91 -56.5l-28 -29q-69 66 -176 66t-162.5 -59.5t-55.5 -172.5v-196h359v-39h-359v-1020h-40z" />
<glyph unicode="g" horiz-adv-x="1386" d="M111 551q0 224 148 369t372 145q168 0 296 -85.5t183 -229.5v309h41v-965q0 -255 -120.5 -376t-368.5 -121q-145 0 -269.5 46.5t-199.5 129.5l28 28q171 -166 441 -166q230 0 339 109.5t109 343.5v266q-55 -146 -182.5 -232.5t-296.5 -86.5q-110 0 -206.5 38.5 t-165.5 106t-108.5 164t-39.5 207.5zM152 551q0 -210 135.5 -343.5t343.5 -133.5t343.5 133.5t135.5 343.5q0 209 -135.5 342t-343.5 133t-343.5 -133t-135.5 -342z" />
<glyph unicode="h" horiz-adv-x="1368" d="M236 0v1520h40v-744q48 135 163.5 212t281.5 77q198 0 311 -114t113 -326v-625h-41v625q0 195 -99.5 298t-281.5 103q-207 0 -327 -124t-120 -327v-575h-40z" />
<glyph unicode="i" horiz-adv-x="512" d="M203 1405q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5zM236 0v1059h40v-1059h-40z" />
<glyph unicode="j" horiz-adv-x="522" d="M-176 -328l29 29q69 -66 182 -66q103 0 157 59.5t54 172.5v1192h41v-1202q0 -119 -65.5 -189.5t-184.5 -70.5q-141 0 -213 75zM213 1405q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="k" horiz-adv-x="1167" d="M236 0v1520h40v-1143l748 682h59l-460 -422l507 -637h-53l-487 612l-314 -284v-328h-40z" />
<glyph unicode="l" horiz-adv-x="579" d="M236 254v1266h40v-1256q0 -231 205 -231q60 0 105 18l6 -37q-52 -20 -109 -20q-120 0 -183.5 70t-63.5 190z" />
<glyph unicode="m" horiz-adv-x="2187" d="M236 0v1059h40v-281q46 134 159 210.5t274 76.5q163 0 267 -80.5t134 -234.5q41 144 156.5 229.5t285.5 85.5q193 0 302.5 -114t109.5 -326v-625h-41v625q0 195 -97 298t-272 103q-200 0 -317 -124t-117 -327v-575h-41v625q0 195 -96.5 298t-271.5 103 q-200 0 -317.5 -124t-117.5 -327v-575h-40z" />
<glyph unicode="n" horiz-adv-x="1368" d="M236 0v1059h40v-283q48 135 163.5 212t281.5 77q198 0 311 -114t113 -326v-625h-41v625q0 195 -99.5 298t-281.5 103q-207 0 -327 -124t-120 -327v-575h-40z" />
<glyph unicode="o" horiz-adv-x="1249" d="M111 530q0 234 146 384.5t368 150.5t368 -150.5t146 -384.5t-146 -385t-368 -151t-368 151t-146 385zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357z" />
<glyph unicode="p" d="M236 -397v1456h40v-324q54 151 181.5 240.5t292.5 89.5q220 0 367 -152t147 -385t-147 -383.5t-367 -150.5q-165 0 -292.5 89t-181.5 239v-719h-40zM276 528q0 -218 135 -356.5t339 -138.5t338.5 138.5t134.5 356.5t-134.5 358t-338.5 140t-339 -140t-135 -358z" />
<glyph unicode="q" d="M111 528q0 233 147 385t367 152q165 0 292 -89t181 -241v324h41v-1456h-41v719q-54 -150 -181 -239t-292 -89q-220 0 -367 150.5t-147 383.5zM152 528q0 -218 134.5 -356.5t338.5 -138.5t338.5 138.5t134.5 356.5t-134.5 358t-338.5 140t-338.5 -140t-134.5 -358z" />
<glyph unicode="r" horiz-adv-x="784" d="M236 0v1059h40v-275q45 135 151.5 208t264.5 73v-41h-12q-190 0 -297 -121.5t-107 -333.5v-569h-40z" />
<glyph unicode="s" horiz-adv-x="948" d="M66 139l24 33q61 -60 164.5 -99.5t224.5 -39.5q181 0 266.5 62.5t85.5 172.5q0 72 -40 118.5t-104 69t-141 37.5t-154.5 32.5t-141.5 44.5t-104 83.5t-40 139.5q0 115 95 193.5t280 78.5q98 0 194 -30t156 -79l-24 -32q-61 50 -147.5 76t-178.5 26q-167 0 -249 -64 t-82 -169q0 -74 40 -122.5t103.5 -72t140.5 -39t154 -32.5t140.5 -43.5t103.5 -80.5t40 -135q0 -125 -100.5 -199.5t-292.5 -74.5q-127 0 -239.5 40.5t-173.5 104.5z" />
<glyph unicode="t" horiz-adv-x="765" d="M223 254v1036h41v-231h359v-39h-359v-756q0 -113 55 -172t162 -59q108 0 176 65l29 -28q-35 -37 -91 -56.5t-116 -19.5q-123 0 -189.5 70t-66.5 190z" />
<glyph unicode="u" horiz-adv-x="1359" d="M223 434v625h41v-625q0 -195 102 -298t289 -103q198 0 313 123.5t115 326.5v576h41v-1059h-41v281q-47 -134 -157 -210.5t-269 -76.5q-204 0 -319 114t-115 326z" />
<glyph unicode="v" horiz-adv-x="1040" d="M12 1059h47l461 -1012l461 1012h47l-487 -1059h-41z" />
<glyph unicode="w" horiz-adv-x="2134" d="M223 475v584h41v-584q0 -226 96 -334t295 -108q198 0 295 109.5t97 342.5v574h40v-574q0 -233 96.5 -342.5t295.5 -109.5q198 0 294.5 108t96.5 334v584h41v-584q0 -481 -432 -481q-169 0 -272 77t-140 244q-37 -167 -140 -244t-272 -77q-216 0 -324 118.5t-108 362.5z " />
<glyph unicode="x" horiz-adv-x="1019" d="M57 0l426 549l-401 510h51l375 -481l375 481h49l-400 -510l431 -549h-54l-399 518l-401 -518h-52z" />
<glyph unicode="y" horiz-adv-x="1359" d="M223 475v584h41v-584q0 -195 102 -298t289 -103q198 0 313 123.5t115 326.5v535h41v-965q0 -255 -108.5 -376t-331.5 -121q-133 0 -248 46.5t-180 129.5l29 28q71 -80 173.5 -123t223.5 -43q206 0 303.5 109.5t97.5 343.5v234q-47 -134 -157 -210.5t-269 -76.5 q-204 0 -319 114t-115 326z" />
<glyph unicode="z" horiz-adv-x="1026" d="M104 0v31l367 485h-274v39h303l350 465h-735v39h792v-31l-356 -473h274v-39h-305l-358 -477h756v-39h-814z" />
<glyph unicode="{" horiz-adv-x="614" d="M133 543v39h72q92 0 92 106v623q0 209 215 209h51v-39h-65q-160 0 -160 -170v-623q0 -56 -15.5 -84.5t-50.5 -42.5q35 -14 50.5 -42.5t15.5 -84.5v-622q0 -170 160 -170h65v-39h-51q-215 0 -215 209v622q0 109 -92 109h-72z" />
<glyph unicode="|" horiz-adv-x="585" d="M272 -397v1917h41v-1917h-41z" />
<glyph unicode="}" horiz-adv-x="614" d="M51 -358h66q159 0 159 170v622q0 56 15.5 84.5t50.5 42.5q-35 14 -50.5 42.5t-15.5 84.5v623q0 170 -159 170h-66v39h51q215 0 215 -209v-623q0 -106 93 -106h71v-39h-71q-93 0 -93 -109v-622q0 -209 -215 -209h-51v39z" />
<glyph unicode="~" horiz-adv-x="1146" d="M141 592q6 119 67 187.5t157 68.5q54 0 101 -23t82.5 -56t69.5 -66.5t76 -56.5t88 -23q84 0 133 59t54 160h37q-6 -119 -67 -187.5t-157 -68.5q-54 0 -101 23t-82.5 56t-69.5 66.5t-76 56.5t-88 23q-84 0 -133 -59t-54 -160h-37z" />
<glyph unicode="&#xa1;" horiz-adv-x="495" d="M195 1012q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -38t-37.5 -16t-37.5 16t-15.5 38zM225 -315l8 989h29l8 -989h-45z" />
<glyph unicode="&#xa2;" horiz-adv-x="1116" d="M111 530q0 227 137 375.5t352 159.5v240h39v-240q113 -2 210.5 -45.5t162.5 -126.5l-29 -31q-58 80 -148 121t-196 43v-993q106 2 196 43t148 121l29 -31q-65 -83 -162.5 -126.5t-210.5 -45.5v-240h-39v240q-215 11 -352 159.5t-137 376.5zM152 530q0 -212 125.5 -349 t322.5 -148v993q-197 -11 -322.5 -148t-125.5 -348z" />
<glyph unicode="&#xa3;" horiz-adv-x="1265" d="M61 0v39h246v659h-246v37h246v252q0 209 126.5 331t375.5 122q262 0 420 -125l-27 -33q-150 119 -393 119q-232 0 -346.5 -109t-114.5 -311v-246h613v-37h-613v-659h860v-39h-1147z" />
<glyph unicode="&#xa4;" horiz-adv-x="1433" d="M80 41l223 225q-75 75 -117.5 174t-42.5 207q0 110 42.5 210t117.5 177l-223 226l29 28l225 -225q166 143 383 143q222 0 385 -141l223 223l29 -28l-221 -224q77 -77 120 -177t43 -212q0 -109 -43 -209t-120 -174l221 -223l-29 -29l-223 224q-165 -142 -385 -142 q-216 0 -383 144l-225 -226zM184 647q0 -138 71.5 -256t194.5 -187t267 -69q146 0 270 69t196 187t72 256q0 140 -72 259.5t-196 189t-270 69.5q-144 0 -267 -69.5t-194.5 -189t-71.5 -259.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1372" d="M20 1434h48l618 -791l619 791h47l-617 -793h463v-37h-491v-246h491v-36h-491v-322h-41v322h-492v36h492v246h-492v37h463z" />
<glyph unicode="&#xa6;" horiz-adv-x="585" d="M272 319h41v-716h-41v716zM272 803v717h41v-717h-41z" />
<glyph unicode="&#xa7;" horiz-adv-x="964" d="M66 -66l24 33q60 -59 161 -99t208 -40q159 0 245.5 63t86.5 187q0 75 -38.5 125.5t-100 77t-135.5 45.5t-148.5 40.5t-136 51.5t-100 88.5t-38.5 142.5q0 96 57.5 166.5t149.5 99.5q-158 74 -158 236q0 139 104 214t292 75q98 0 193 -29.5t157 -79.5l-25 -33 q-62 50 -148 76.5t-177 26.5q-172 0 -263.5 -62.5t-91.5 -187.5q0 -75 38.5 -125.5t100 -77t135.5 -45.5t148.5 -40.5t136 -51.5t100 -88.5t38.5 -142.5q0 -97 -57 -168t-150 -101q157 -73 157 -237q0 -135 -100 -210t-272 -75q-113 0 -223 41t-170 104zM133 649 q0 -57 22.5 -101t56 -70t87 -48t100.5 -34t112 -29.5t107 -32.5q99 20 161.5 84t62.5 162q0 57 -22.5 101t-56.5 70t-87.5 48t-100 33.5t-111.5 29t-106 31.5q-99 -20 -162 -83t-63 -161z" />
<glyph unicode="&#xa8;" horiz-adv-x="1228" d="M393 1366q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5zM729 1366q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1687" d="M121 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM160 717q0 -188 89.5 -345t246.5 -248t346 -91 q142 0 269 54t218.5 145.5t145 219t53.5 269.5t-52.5 268t-142.5 216.5t-216.5 143t-270.5 52.5q-143 0 -270.5 -53.5t-218.5 -145t-144 -218t-53 -267.5zM438 717q0 190 119 311t301 121q94 0 174 -35.5t135 -103.5l-30 -31q-98 131 -281 131q-164 0 -270.5 -109.5 t-106.5 -283.5t106.5 -283.5t270.5 -109.5q183 0 281 131l30 -31q-55 -68 -135 -103.5t-174 -35.5q-182 0 -301 121t-119 311z" />
<glyph unicode="&#xaa;" horiz-adv-x="806" d="M100 1063q0 76 59.5 123t188.5 47h250v63q0 191 -211 191q-149 0 -246 -84l-20 29q51 42 121 67t145 25q122 0 187 -58t65 -170v-407h-39v133q-31 -64 -98 -100.5t-160 -36.5q-114 0 -178 48.5t-64 129.5zM139 1065q0 -68 55 -107.5t154 -39.5q183 0 250 153v131h-250 q-108 0 -158.5 -36.5t-50.5 -100.5z" />
<glyph unicode="&#xab;" horiz-adv-x="866" d="M119 530l301 379h47l-299 -379l299 -378h-47zM426 530l301 379h47l-299 -379l299 -378h-47z" />
<glyph unicode="&#xac;" horiz-adv-x="1146" d="M154 696v39h839v-409h-41v370h-798z" />
<glyph unicode="&#xad;" horiz-adv-x="778" d="M133 522v39h512v-39h-512z" />
<glyph unicode="&#xae;" horiz-adv-x="1687" d="M121 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM160 717q0 -188 89.5 -345t246.5 -248t346 -91 q142 0 269 54t218.5 145.5t145 219t53.5 269.5t-52.5 268t-142.5 216.5t-216.5 143t-270.5 52.5q-143 0 -270.5 -53.5t-218.5 -145t-144 -218t-53 -267.5zM580 291v852h307q144 0 228.5 -74t84.5 -199q0 -99 -55 -167t-152 -93l207 -319h-49l-201 311q-40 -4 -63 -4h-266 v-307h-41zM618 637h265q131 0 203.5 61.5t72.5 171.5t-73 172t-203 62h-265v-467z" />
<glyph unicode="&#xaf;" horiz-adv-x="1228" d="M336 1335v41h557v-41h-557z" />
<glyph unicode="&#xb0;" horiz-adv-x="860" d="M123 1133q0 129 89.5 218t217.5 89t217.5 -89t89.5 -218t-89.5 -218.5t-217.5 -89.5t-217.5 89.5t-89.5 218.5zM162 1133q0 -113 77.5 -192t190.5 -79t190.5 79t77.5 192t-77.5 191.5t-190.5 78.5t-190.5 -78.5t-77.5 -191.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="1146" d="M154 0v39h839v-39h-839zM154 696v39h399v391h41v-391h399v-39h-399v-389h-41v389h-399z" />
<glyph unicode="&#xb2;" horiz-adv-x="880" d="M82 1413q105 111 309 111q156 0 236 -61t80 -156q0 -65 -34.5 -123t-127.5 -142l-375 -335h584v-37h-648v28l410 367q88 79 120 131.5t32 108.5q0 80 -69.5 131t-207.5 51q-187 0 -282 -98z" />
<glyph unicode="&#xb3;" horiz-adv-x="880" d="M84 782l20 31q41 -49 124 -80t188 -31q141 0 212.5 54t71.5 149t-72 149t-214 54h-66v31l303 344h-559v37h613v-29l-308 -348h19q156 0 239.5 -64.5t83.5 -173.5t-83.5 -174t-239.5 -65q-112 0 -201 31.5t-131 84.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="1228" d="M522 1243l287 225h82l-307 -225h-62z" />
<glyph unicode="&#xb5;" horiz-adv-x="1372" d="M236 -397v1456h40v-625q0 -195 102 -298t290 -103q198 0 313 123.5t115 326.5v576h41v-1059h-41v281q-49 -138 -160 -212.5t-266 -74.5q-144 0 -245.5 58t-148.5 171v-620h-40z" />
<glyph unicode="&#xb6;" horiz-adv-x="1228" d="M51 1190q0 153 111.5 241.5t298.5 88.5h495v-1725h-41v1686h-413v-1686h-41v1063q-186 0 -298 90t-112 242z" />
<glyph unicode="&#xb7;" horiz-adv-x="454" d="M174 541q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -38t-38 -16t-37.5 16t-15.5 38z" />
<glyph unicode="&#xb8;" horiz-adv-x="1228" d="M430 -381l23 31q50 -47 127 -47q65 0 106 32.5t41 90.5q0 55 -38.5 88.5t-106.5 33.5h-33l45 162h39l-35 -125q77 -5 123.5 -47.5t46.5 -111.5q0 -73 -52.5 -117.5t-133.5 -44.5q-89 0 -152 55z" />
<glyph unicode="&#xb9;" horiz-adv-x="880" d="M184 670v37h273v776h-258v37h297v-813h245v-37h-557z" />
<glyph unicode="&#xba;" horiz-adv-x="833" d="M82 1204q0 139 94.5 229.5t239.5 90.5t239.5 -90.5t94.5 -229.5t-94.5 -229t-239.5 -90t-239.5 90t-94.5 229zM121 1204q0 -125 84 -203.5t211 -78.5t211 78.5t84 203.5t-84 204t-211 79t-211 -79t-84 -204z" />
<glyph unicode="&#xbb;" horiz-adv-x="866" d="M92 152l299 378l-299 379h47l301 -379l-301 -378h-47zM399 152l299 378l-299 379h47l302 -379l-302 -378h-47z" />
<glyph unicode="&#xbc;" horiz-adv-x="2088" d="M184 584v37h273v776h-258v37h297v-813h245v-37h-557zM532 0l979 1434h45l-978 -1434h-46zM1282 246v28l442 576h48l-439 -567h436v215h39v-215h207v-37h-207v-246h-39v246h-487z" />
<glyph unicode="&#xbd;" horiz-adv-x="2088" d="M184 584v37h273v776h-258v37h297v-813h245v-37h-557zM532 0l979 1434h45l-978 -1434h-46zM1290 743q105 111 309 111q156 0 236 -61t80 -156q0 -65 -34.5 -122.5t-127.5 -141.5l-375 -336h584v-37h-647v29l409 366q88 79 120 131.5t32 108.5q0 80 -69.5 131t-207.5 51 q-187 0 -282 -98z" />
<glyph unicode="&#xbe;" horiz-adv-x="2088" d="M84 696l20 31q41 -49 124 -80t188 -31q141 0 212.5 54t71.5 149t-72 149t-214 54h-66v31l303 344h-559v37h613v-29l-308 -348h19q156 0 239.5 -64.5t83.5 -173.5t-83.5 -174t-239.5 -65q-112 0 -201 31.5t-131 84.5zM532 0l979 1434h45l-978 -1434h-46zM1282 246v28 l442 576h48l-439 -567h436v215h39v-215h207v-37h-207v-246h-39v246h-487z" />
<glyph unicode="&#xbf;" horiz-adv-x="1134" d="M182 2q0 70 22 128.5t57.5 99.5t78 78.5t85 74t78 76t57.5 94.5t22 121h41q0 -71 -22 -130t-57.5 -100.5t-78 -78.5t-85 -73t-78 -75t-57.5 -93.5t-22 -119.5q0 -124 106 -205.5t294 -81.5q287 0 442 174l29 -28q-165 -185 -471 -185q-199 0 -320 91.5t-121 232.5z M549 1012q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -38t-37.5 -16t-37.5 16t-15.5 38z" />
<glyph unicode="&#xc0;" horiz-adv-x="1646" d="M260 0v834q0 298 150 452t413 154t413 -154t150 -452v-834h-40v473h-1045v-473h-41zM301 512h1045v326q0 278 -137 420.5t-386 142.5t-385.5 -142.5t-136.5 -420.5v-326zM547 1776h82l286 -226h-61z" />
<glyph unicode="&#xc1;" horiz-adv-x="1646" d="M260 0v834q0 298 150 452t413 154t413 -154t150 -452v-834h-40v473h-1045v-473h-41zM301 512h1045v326q0 278 -137 420.5t-386 142.5t-385.5 -142.5t-136.5 -420.5v-326zM731 1550l287 226h82l-307 -226h-62z" />
<glyph unicode="&#xc2;" horiz-adv-x="1646" d="M260 0v834q0 298 150 452t413 154t413 -154t150 -452v-834h-40v473h-1045v-473h-41zM301 512h1045v326q0 278 -137 420.5t-386 142.5t-385.5 -142.5t-136.5 -420.5v-326zM541 1550l250 226h65l250 -226h-55l-228 191l-227 -191h-55z" />
<glyph unicode="&#xc3;" horiz-adv-x="1646" d="M260 0v834q0 298 150 452t413 154t413 -154t150 -452v-834h-40v473h-1045v-473h-41zM301 512h1045v326q0 278 -137 420.5t-386 142.5t-385.5 -142.5t-136.5 -420.5v-326zM545 1579q3 80 43 128t106 48q39 0 76.5 -22t62 -48.5t57 -48.5t62.5 -22q49 0 78.5 36t32.5 97h39 q-3 -77 -44 -124.5t-106 -47.5q-39 0 -76.5 22t-62 48.5t-57 48.5t-62.5 22q-49 0 -78 -36.5t-32 -100.5h-39z" />
<glyph unicode="&#xc4;" horiz-adv-x="1646" d="M260 0v834q0 298 150 452t413 154t413 -154t150 -452v-834h-40v473h-1045v-473h-41zM301 512h1045v326q0 278 -137 420.5t-386 142.5t-385.5 -142.5t-136.5 -420.5v-326zM602 1673q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5 t-15.5 37.5zM938 1673q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1646" d="M260 0v834q0 298 150 452t413 154t413 -154t150 -452v-834h-40v473h-1045v-473h-41zM301 512h1045v326q0 278 -137 420.5t-386 142.5t-385.5 -142.5t-136.5 -420.5v-326zM633 1782q0 78 54 134t134 56q81 0 136 -56t55 -134q0 -76 -54.5 -130.5t-136.5 -54.5t-135 54 t-53 131zM674 1782q0 -62 41 -104t106 -42t107.5 42.5t42.5 103.5q0 62 -43 106.5t-107 44.5t-105.5 -43.5t-41.5 -107.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2168" d="M236 0v829q0 287 142 446t400 159h1274v-39h-883v-647h791v-39h-791v-670h914v-39h-955v494h-852v-494h-40zM276 532h852v863h-350q-241 0 -371.5 -147.5t-130.5 -418.5v-297z" />
<glyph unicode="&#xc7;" horiz-adv-x="1456" d="M121 717q0 204 95.5 369.5t264 259.5t375.5 94q144 0 270.5 -46.5t216.5 -136.5l-24 -28q-180 172 -463 172q-146 0 -275 -53t-220.5 -143.5t-145 -217.5t-53.5 -270t53.5 -270t145 -217.5t220.5 -143.5t275 -53q281 0 463 174l24 -29q-91 -91 -217 -137.5t-270 -46.5h-6 l-31 -109q77 -5 123.5 -47.5t46.5 -111.5q0 -73 -52.5 -117.5t-133.5 -44.5q-89 0 -152 55l23 31q50 -47 127 -47q65 0 106 32.5t41 90.5q0 55 -38.5 88.5t-106.5 33.5h-33l41 148q-147 7 -275 65t-219 153t-143.5 225.5t-52.5 277.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1257" d="M133 377q0 145 91.5 235t246.5 117q-132 32 -204 120t-72 218q0 162 130.5 267.5t368.5 105.5q127 0 244.5 -32.5t200.5 -90.5l-21 -33q-186 117 -422 117q-222 0 -341 -93.5t-119 -242.5q0 -151 103 -234t286 -83h336v-39h-334q-207 0 -330 -83.5t-123 -246.5 q0 -159 125 -252.5t367 -93.5q165 0 299.5 48t212.5 134l30 -27q-82 -92 -224 -143t-320 -51q-256 0 -393.5 105.5t-137.5 277.5zM412 1776h82l286 -226h-61z" />
<glyph unicode="&#xc9;" horiz-adv-x="1257" d="M133 377q0 145 91.5 235t246.5 117q-132 32 -204 120t-72 218q0 162 130.5 267.5t368.5 105.5q127 0 244.5 -32.5t200.5 -90.5l-21 -33q-186 117 -422 117q-222 0 -341 -93.5t-119 -242.5q0 -151 103 -234t286 -83h336v-39h-334q-207 0 -330 -83.5t-123 -246.5 q0 -159 125 -252.5t367 -93.5q165 0 299.5 48t212.5 134l30 -27q-82 -92 -224 -143t-320 -51q-256 0 -393.5 105.5t-137.5 277.5zM596 1550l287 226h82l-308 -226h-61z" />
<glyph unicode="&#xca;" horiz-adv-x="1257" d="M133 377q0 145 91.5 235t246.5 117q-132 32 -204 120t-72 218q0 162 130.5 267.5t368.5 105.5q127 0 244.5 -32.5t200.5 -90.5l-21 -33q-186 117 -422 117q-222 0 -341 -93.5t-119 -242.5q0 -151 103 -234t286 -83h336v-39h-334q-207 0 -330 -83.5t-123 -246.5 q0 -159 125 -252.5t367 -93.5q165 0 299.5 48t212.5 134l30 -27q-82 -92 -224 -143t-320 -51q-256 0 -393.5 105.5t-137.5 277.5zM406 1550l249 226h66l250 -226h-56l-227 191l-227 -191h-55z" />
<glyph unicode="&#xcb;" horiz-adv-x="1257" d="M133 377q0 145 91.5 235t246.5 117q-132 32 -204 120t-72 218q0 162 130.5 267.5t368.5 105.5q127 0 244.5 -32.5t200.5 -90.5l-21 -33q-186 117 -422 117q-222 0 -341 -93.5t-119 -242.5q0 -151 103 -234t286 -83h336v-39h-334q-207 0 -330 -83.5t-123 -246.5 q0 -159 125 -252.5t367 -93.5q165 0 299.5 48t212.5 134l30 -27q-82 -92 -224 -143t-320 -51q-256 0 -393.5 105.5t-137.5 277.5zM467 1673q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5zM803 1673q0 22 15.5 37.5 t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="864" d="M96 0v39h316v1356h-316v39h672v-39h-315v-1356h315v-39h-672zM156 1776h82l286 -226h-61z" />
<glyph unicode="&#xcd;" horiz-adv-x="864" d="M96 0v39h316v1356h-316v39h672v-39h-315v-1356h315v-39h-672zM340 1550l287 226h82l-308 -226h-61z" />
<glyph unicode="&#xce;" horiz-adv-x="864" d="M96 0v39h316v1356h-316v39h672v-39h-315v-1356h315v-39h-672zM150 1550l249 226h66l250 -226h-56l-227 191l-227 -191h-55z" />
<glyph unicode="&#xcf;" horiz-adv-x="864" d="M96 0v39h316v1356h-316v39h672v-39h-315v-1356h315v-39h-672zM211 1673q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5zM547 1673q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5 t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1691" d="M41 709v39h231v686h543q167 0 308.5 -54t239.5 -149t153 -227.5t55 -286.5t-55 -286.5t-153 -227.5t-239.5 -149t-308.5 -54h-543v709h-231zM313 39h498q161 0 296.5 51t228 141t143.5 215t51 271t-51 271t-143.5 215t-228 141t-296.5 51h-498v-647h465v-39h-465v-670z " />
<glyph unicode="&#xd1;" horiz-adv-x="1638" d="M272 0v1434h41v-342q55 173 187 260.5t327 87.5q258 0 404.5 -149t146.5 -427v-864h-41v862q0 264 -133 401.5t-379 137.5q-248 0 -380 -145t-132 -435v-821h-41zM547 1579q3 80 43 128t106 48q39 0 76.5 -22t62 -48.5t57 -48.5t62.5 -22q49 0 78.5 36t32.5 97h39 q-3 -77 -44 -124.5t-106 -47.5q-39 0 -76.5 22t-62 48.5t-57 48.5t-62.5 22q-49 0 -78 -36.5t-32 -100.5h-39z" />
<glyph unicode="&#xd2;" horiz-adv-x="1712" d="M121 717q0 204 95.5 369.5t264 259.5t375.5 94t375.5 -94t264 -259.5t95.5 -369.5t-95.5 -369.5t-264 -259.5t-375.5 -94t-375.5 94t-264 259.5t-95.5 369.5zM162 717q0 -193 90.5 -349.5t249.5 -245.5t354 -89t354 89t249.5 245.5t90.5 349.5t-90.5 349.5t-249.5 245.5 t-354 89t-354 -89t-249.5 -245.5t-90.5 -349.5zM580 1776h82l286 -226h-61z" />
<glyph unicode="&#xd3;" horiz-adv-x="1712" d="M121 717q0 204 95.5 369.5t264 259.5t375.5 94t375.5 -94t264 -259.5t95.5 -369.5t-95.5 -369.5t-264 -259.5t-375.5 -94t-375.5 94t-264 259.5t-95.5 369.5zM162 717q0 -193 90.5 -349.5t249.5 -245.5t354 -89t354 89t249.5 245.5t90.5 349.5t-90.5 349.5t-249.5 245.5 t-354 89t-354 -89t-249.5 -245.5t-90.5 -349.5zM764 1550l287 226h82l-308 -226h-61z" />
<glyph unicode="&#xd4;" horiz-adv-x="1712" d="M121 717q0 204 95.5 369.5t264 259.5t375.5 94t375.5 -94t264 -259.5t95.5 -369.5t-95.5 -369.5t-264 -259.5t-375.5 -94t-375.5 94t-264 259.5t-95.5 369.5zM162 717q0 -193 90.5 -349.5t249.5 -245.5t354 -89t354 89t249.5 245.5t90.5 349.5t-90.5 349.5t-249.5 245.5 t-354 89t-354 -89t-249.5 -245.5t-90.5 -349.5zM573 1550l250 226h66l250 -226h-56l-227 191l-227 -191h-56z" />
<glyph unicode="&#xd5;" horiz-adv-x="1712" d="M121 717q0 204 95.5 369.5t264 259.5t375.5 94t375.5 -94t264 -259.5t95.5 -369.5t-95.5 -369.5t-264 -259.5t-375.5 -94t-375.5 94t-264 259.5t-95.5 369.5zM162 717q0 -193 90.5 -349.5t249.5 -245.5t354 -89t354 89t249.5 245.5t90.5 349.5t-90.5 349.5t-249.5 245.5 t-354 89t-354 -89t-249.5 -245.5t-90.5 -349.5zM578 1579q3 80 43 128t106 48q39 0 76.5 -22t62 -48.5t57 -48.5t62.5 -22q49 0 78.5 36t32.5 97h39q-3 -77 -44 -124.5t-106 -47.5q-39 0 -76.5 22t-62 48.5t-57 48.5t-62.5 22q-49 0 -78.5 -37t-32.5 -100h-38z" />
<glyph unicode="&#xd6;" horiz-adv-x="1712" d="M121 717q0 204 95.5 369.5t264 259.5t375.5 94t375.5 -94t264 -259.5t95.5 -369.5t-95.5 -369.5t-264 -259.5t-375.5 -94t-375.5 94t-264 259.5t-95.5 369.5zM162 717q0 -193 90.5 -349.5t249.5 -245.5t354 -89t354 89t249.5 245.5t90.5 349.5t-90.5 349.5t-249.5 245.5 t-354 89t-354 -89t-249.5 -245.5t-90.5 -349.5zM635 1673q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5zM971 1673q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5z " />
<glyph unicode="&#xd7;" horiz-adv-x="1146" d="M262 434l283 283l-283 282l29 29l282 -283l283 283l29 -29l-283 -282l283 -283l-29 -28l-283 282l-282 -282z" />
<glyph unicode="&#xd8;" horiz-adv-x="1712" d="M121 717q0 204 95.5 369.5t264 259.5t375.5 94q236 0 418 -121l188 258h47l-204 -279q134 -98 210 -249.5t76 -331.5q0 -204 -95.5 -369.5t-264 -259.5t-375.5 -94q-236 0 -418 121l-188 -258h-47l205 278q-134 98 -210.5 250t-76.5 332zM162 717q0 -169 71.5 -312 t196.5 -237l819 1118q-173 115 -393 115q-195 0 -354 -89t-249.5 -245.5t-90.5 -349.5zM463 147q171 -114 393 -114q195 0 354 89t249.5 245.5t90.5 349.5q0 169 -71.5 312t-196.5 237z" />
<glyph unicode="&#xd9;" horiz-adv-x="1630" d="M260 569v865h41v-863q0 -263 132 -400.5t376 -137.5q246 0 377 144.5t131 434.5v822h41v-1434h-41v342q-55 -172 -186.5 -260t-323.5 -88q-256 0 -401.5 149t-145.5 426zM528 1776h82l287 -226h-61z" />
<glyph unicode="&#xda;" horiz-adv-x="1630" d="M260 569v865h41v-863q0 -263 132 -400.5t376 -137.5q246 0 377 144.5t131 434.5v822h41v-1434h-41v342q-55 -172 -186.5 -260t-323.5 -88q-256 0 -401.5 149t-145.5 426zM713 1550l286 226h82l-307 -226h-61z" />
<glyph unicode="&#xdb;" horiz-adv-x="1630" d="M260 569v865h41v-863q0 -263 132 -400.5t376 -137.5q246 0 377 144.5t131 434.5v822h41v-1434h-41v342q-55 -172 -186.5 -260t-323.5 -88q-256 0 -401.5 149t-145.5 426zM522 1550l250 226h66l249 -226h-55l-227 191l-227 -191h-56z" />
<glyph unicode="&#xdc;" horiz-adv-x="1630" d="M260 569v865h41v-863q0 -263 132 -400.5t376 -137.5q246 0 377 144.5t131 434.5v822h41v-1434h-41v342q-55 -172 -186.5 -260t-323.5 -88q-256 0 -401.5 149t-145.5 426zM584 1673q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5 t-15.5 37.5zM920 1673q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1615" d="M250 946v488h41v-486q0 -263 132.5 -400.5t379.5 -137.5q249 0 380.5 144.5t131.5 434.5v445h41v-1039q0 -299 -145.5 -452.5t-407.5 -153.5q-341 0 -524 238l30 28q175 -227 496 -227q247 0 378.5 143t131.5 426v322q-55 -173 -187 -260.5t-327 -87.5q-259 0 -405 148.5 t-146 426.5zM711 1550l286 226h82l-307 -226h-61z" />
<glyph unicode="&#xde;" horiz-adv-x="1454" d="M272 0v1434h41v-205h445q265 0 414 -124.5t149 -346.5q0 -224 -149 -348.5t-414 -124.5h-445v-285h-41zM313 324h449q251 0 384.5 113t133.5 321q0 207 -133.5 319.5t-384.5 112.5h-449v-866z" />
<glyph unicode="&#xdf;" horiz-adv-x="1335" d="M236 0v1040q0 232 122.5 359t327.5 127q192 0 307 -102t115 -261q0 -139 -79 -236t-202 -134q184 -19 291 -123t107 -273q0 -183 -132.5 -293t-334.5 -110q-122 0 -217 31l12 38q91 -30 205 -30q187 0 306.5 97.5t119.5 266.5q0 170 -117 266.5t-307 96.5h-123v39 q201 0 315.5 98t114.5 266q0 144 -102 234t-277 90q-190 0 -301 -114.5t-111 -334.5v-1038h-40z" />
<glyph unicode="&#xe0;" d="M111 530q0 233 147 384t367 151q165 0 292 -89t181 -239v322h41v-1059h-41v324q-54 -152 -181 -241t-292 -89q-220 0 -367 151.5t-147 384.5zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357z M373 1468h82l286 -225h-61z" />
<glyph unicode="&#xe1;" d="M111 530q0 233 147 384t367 151q165 0 292 -89t181 -239v322h41v-1059h-41v324q-54 -152 -181 -241t-292 -89q-220 0 -367 151.5t-147 384.5zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357z M557 1243l287 225h82l-308 -225h-61z" />
<glyph unicode="&#xe2;" d="M111 530q0 233 147 384t367 151q165 0 292 -89t181 -239v322h41v-1059h-41v324q-54 -152 -181 -241t-292 -89q-220 0 -367 151.5t-147 384.5zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357z M367 1243l249 225h66l250 -225h-55l-228 191l-227 -191h-55z" />
<glyph unicode="&#xe3;" d="M111 530q0 233 147 384t367 151q165 0 292 -89t181 -239v322h41v-1059h-41v324q-54 -152 -181 -241t-292 -89q-220 0 -367 151.5t-147 384.5zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357z M371 1272q3 80 43 128t106 48q39 0 76.5 -22t62 -48.5t57 -48.5t62.5 -22q49 0 78.5 36t32.5 97h39q-3 -77 -44 -124.5t-106 -47.5q-39 0 -76.5 22t-62 48.5t-57 48.5t-62.5 22q-49 0 -78 -36.5t-32 -100.5h-39z" />
<glyph unicode="&#xe4;" d="M111 530q0 233 147 384t367 151q165 0 292 -89t181 -239v322h41v-1059h-41v324q-54 -152 -181 -241t-292 -89q-220 0 -367 151.5t-147 384.5zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357z M428 1366q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5zM764 1366q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#xe5;" d="M111 530q0 233 147 384t367 151q165 0 292 -89t181 -239v322h41v-1059h-41v324q-54 -152 -181 -241t-292 -89q-220 0 -367 151.5t-147 384.5zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357z M459 1407q0 78 54 134t134 56q81 0 136 -56t55 -134q0 -76 -54.5 -130t-136.5 -54t-135 53.5t-53 130.5zM500 1407q0 -62 41 -103.5t106 -41.5t107.5 42t42.5 103q0 62 -43 107t-107 45t-105.5 -44t-41.5 -108z" />
<glyph unicode="&#xe6;" horiz-adv-x="2011" d="M133 291q0 59 19.5 108t62 89t118 62.5t177.5 22.5h401v119q0 165 -85.5 249.5t-250.5 84.5q-115 0 -214.5 -39.5t-170.5 -107.5l-28 28q76 73 184.5 115.5t226.5 42.5q343 0 377 -320q55 146 181.5 233t293.5 87q100 0 187 -38t147.5 -102.5t97.5 -150.5t44 -182 l-936 -184q38 -170 170.5 -272.5t316.5 -102.5q226 0 356 164l29 -29q-145 -174 -385 -174q-169 0 -305.5 86t-200.5 248q-31 -167 -150.5 -250.5t-287.5 -83.5q-176 0 -275.5 81t-99.5 216zM174 293q0 -121 88.5 -190.5t245.5 -69.5q183 0 293 100t110 281v121h-403 q-172 0 -253 -65.5t-81 -176.5zM952 530q0 -44 6 -84l900 177q-10 80 -45 152t-89 128.5t-132 89.5t-167 33q-208 0 -340.5 -138t-132.5 -349v-9z" />
<glyph unicode="&#xe7;" horiz-adv-x="1116" d="M111 530q0 235 146.5 385t373.5 150q116 0 215.5 -43.5t165.5 -128.5l-29 -31q-60 81 -151.5 122.5t-200.5 41.5q-208 0 -343.5 -139t-135.5 -357q0 -220 135.5 -358.5t343.5 -138.5q109 0 200.5 41.5t151.5 122.5l29 -31q-66 -85 -165.5 -128.5t-215.5 -43.5h-21 l-30 -109q77 -5 123.5 -47.5t46.5 -111.5q0 -72 -53 -117t-134 -45q-88 0 -151 55l22 31q50 -47 127 -47q65 0 106.5 32.5t41.5 90.5q0 55 -39 88.5t-107 33.5h-33l41 148q-204 22 -332 168.5t-128 365.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1204" d="M111 530q0 235 141 385t362 150q100 0 187.5 -38t149 -102.5t99 -150.5t44.5 -182l-930 -182q37 -171 166 -274t311 -103q111 0 206.5 41.5t156.5 122.5l28 -29q-69 -85 -171.5 -129.5t-219.5 -44.5q-230 0 -380 151t-150 385zM150 537q0 -47 6 -91l895 177 q-13 107 -68 197.5t-152 148t-217 57.5q-203 0 -333.5 -138.5t-130.5 -350.5zM317 1468h82l287 -225h-61z" />
<glyph unicode="&#xe9;" horiz-adv-x="1204" d="M111 530q0 235 141 385t362 150q100 0 187.5 -38t149 -102.5t99 -150.5t44.5 -182l-930 -182q37 -171 166 -274t311 -103q111 0 206.5 41.5t156.5 122.5l28 -29q-69 -85 -171.5 -129.5t-219.5 -44.5q-230 0 -380 151t-150 385zM150 537q0 -47 6 -91l895 177 q-13 107 -68 197.5t-152 148t-217 57.5q-203 0 -333.5 -138.5t-130.5 -350.5zM502 1243l286 225h82l-307 -225h-61z" />
<glyph unicode="&#xea;" horiz-adv-x="1204" d="M111 530q0 235 141 385t362 150q100 0 187.5 -38t149 -102.5t99 -150.5t44.5 -182l-930 -182q37 -171 166 -274t311 -103q111 0 206.5 41.5t156.5 122.5l28 -29q-69 -85 -171.5 -129.5t-219.5 -44.5q-230 0 -380 151t-150 385zM150 537q0 -47 6 -91l895 177 q-13 107 -68 197.5t-152 148t-217 57.5q-203 0 -333.5 -138.5t-130.5 -350.5zM311 1243l250 225h66l250 -225h-56l-227 191l-227 -191h-56z" />
<glyph unicode="&#xeb;" horiz-adv-x="1204" d="M111 530q0 235 141 385t362 150q100 0 187.5 -38t149 -102.5t99 -150.5t44.5 -182l-930 -182q37 -171 166 -274t311 -103q111 0 206.5 41.5t156.5 122.5l28 -29q-69 -85 -171.5 -129.5t-219.5 -44.5q-230 0 -380 151t-150 385zM150 537q0 -47 6 -91l895 177 q-13 107 -68 197.5t-152 148t-217 57.5q-203 0 -333.5 -138.5t-130.5 -350.5zM373 1366q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5zM709 1366q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5 t-37.5 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#xec;" horiz-adv-x="512" d="M-20 1468h81l287 -225h-61zM236 0v1059h40v-1059h-40z" />
<glyph unicode="&#xed;" horiz-adv-x="512" d="M164 1243l287 225h81l-307 -225h-61zM236 0v1059h40v-1059h-40z" />
<glyph unicode="&#xee;" horiz-adv-x="512" d="M25 1243l200 225h62l200 -225h-55l-176 193l-176 -193h-55zM236 0v1059h40v-1059h-40z" />
<glyph unicode="&#xef;" horiz-adv-x="512" d="M90 1366q0 22 14.5 37.5t36.5 15.5t37 -15.5t15 -37.5t-15 -37.5t-37 -15.5t-36.5 15.5t-14.5 37.5zM236 0v1059h40v-1059h-40zM319 1366q0 22 15 37.5t37 15.5t36.5 -15.5t14.5 -37.5t-14.5 -37.5t-36.5 -15.5t-37 15.5t-15 37.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1265" d="M111 438q0 201 133 322t358 121q194 0 328 -107.5t157 -296.5q27 106 27 244q0 211 -68 361t-194 231l-549 -246l-16 39l520 233q-121 62 -281 62q-163 0 -307 -43l-10 39q142 43 317 43q186 0 328 -80l180 80l17 -39l-156 -70q125 -88 192.5 -243t67.5 -371 q0 -220 -63 -381t-192 -251.5t-314 -90.5q-211 0 -343 122.5t-132 321.5zM152 438q0 -180 120 -292.5t314 -112.5q109 0 199.5 35.5t147.5 93.5t87.5 129.5t30.5 146.5q0 170 -127 287t-322 117q-209 0 -329.5 -110.5t-120.5 -293.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1368" d="M236 0v1059h40v-283q48 135 163.5 212t281.5 77q198 0 311 -114t113 -326v-625h-41v625q0 195 -99.5 298t-281.5 103q-207 0 -327 -124t-120 -327v-575h-40zM408 1272q3 80 43 128t106 48q39 0 76.5 -22t62 -48.5t57 -48.5t62.5 -22q49 0 78.5 36t32.5 97h39 q-3 -77 -44 -124.5t-106 -47.5q-39 0 -76.5 22t-62 48.5t-57 48.5t-62.5 22q-49 0 -78.5 -37t-32.5 -100h-38z" />
<glyph unicode="&#xf2;" horiz-adv-x="1249" d="M111 530q0 234 146 384.5t368 150.5t368 -150.5t146 -384.5t-146 -385t-368 -151t-368 151t-146 385zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357zM348 1468h82l287 -225h-62z" />
<glyph unicode="&#xf3;" horiz-adv-x="1249" d="M111 530q0 234 146 384.5t368 150.5t368 -150.5t146 -384.5t-146 -385t-368 -151t-368 151t-146 385zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357zM532 1243l287 225h82l-307 -225h-62z" />
<glyph unicode="&#xf4;" horiz-adv-x="1249" d="M111 530q0 234 146 384.5t368 150.5t368 -150.5t146 -384.5t-146 -385t-368 -151t-368 151t-146 385zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357zM342 1243l250 225h65l250 -225h-55l-227 191 l-228 -191h-55z" />
<glyph unicode="&#xf5;" horiz-adv-x="1249" d="M111 530q0 234 146 384.5t368 150.5t368 -150.5t146 -384.5t-146 -385t-368 -151t-368 151t-146 385zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357zM346 1272q3 80 43.5 128t106.5 48 q39 0 76.5 -22t62 -48.5t57 -48.5t62.5 -22q49 0 78 36t32 97h39q-3 -77 -43.5 -124.5t-105.5 -47.5q-39 0 -76.5 22t-62 48.5t-57 48.5t-62.5 22q-49 0 -78.5 -37t-32.5 -100h-39z" />
<glyph unicode="&#xf6;" horiz-adv-x="1249" d="M111 530q0 234 146 384.5t368 150.5t368 -150.5t146 -384.5t-146 -385t-368 -151t-368 151t-146 385zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357zM403 1366q0 22 16 37.5t38 15.5t37.5 -15.5 t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-38 15.5t-16 37.5zM739 1366q0 22 16 37.5t38 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-38 15.5t-16 37.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1146" d="M154 696v39h839v-39h-839zM520 348q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5zM520 1085q0 22 15.5 38t37.5 16t38 -16t16 -38t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1249" d="M111 530q0 234 146 384.5t368 150.5q160 0 284 -82l148 219h47l-164 -241q94 -72 146.5 -183.5t52.5 -247.5q0 -234 -146 -385t-368 -151q-161 0 -285 82l-150 -219h-47l164 241q-93 73 -144.5 184.5t-51.5 247.5zM152 530q0 -126 47 -229t131 -170l557 819 q-115 76 -262 76q-204 0 -338.5 -139t-134.5 -357zM362 109q115 -76 263 -76q204 0 338.5 139.5t134.5 357.5q0 126 -47.5 229t-132.5 169z" />
<glyph unicode="&#xf9;" horiz-adv-x="1359" d="M223 434v625h41v-625q0 -195 102 -298t289 -103q198 0 313 123.5t115 326.5v576h41v-1059h-41v281q-47 -134 -157 -210.5t-269 -76.5q-204 0 -319 114t-115 326zM397 1468h82l287 -225h-61z" />
<glyph unicode="&#xfa;" horiz-adv-x="1359" d="M223 434v625h41v-625q0 -195 102 -298t289 -103q198 0 313 123.5t115 326.5v576h41v-1059h-41v281q-47 -134 -157 -210.5t-269 -76.5q-204 0 -319 114t-115 326zM582 1243l286 225h82l-307 -225h-61z" />
<glyph unicode="&#xfb;" horiz-adv-x="1359" d="M223 434v625h41v-625q0 -195 102 -298t289 -103q198 0 313 123.5t115 326.5v576h41v-1059h-41v281q-47 -134 -157 -210.5t-269 -76.5q-204 0 -319 114t-115 326zM391 1243l250 225h66l249 -225h-55l-227 191l-228 -191h-55z" />
<glyph unicode="&#xfc;" horiz-adv-x="1359" d="M223 434v625h41v-625q0 -195 102 -298t289 -103q198 0 313 123.5t115 326.5v576h41v-1059h-41v281q-47 -134 -157 -210.5t-269 -76.5q-204 0 -319 114t-115 326zM453 1366q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5 t-15.5 37.5zM788 1366q0 22 16 37.5t38 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-38 15.5t-16 37.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1359" d="M223 475v584h41v-584q0 -195 102 -298t289 -103q198 0 313 123.5t115 326.5v535h41v-965q0 -255 -108.5 -376t-331.5 -121q-133 0 -248 46.5t-180 129.5l29 28q71 -80 173.5 -123t223.5 -43q206 0 303.5 109.5t97.5 343.5v234q-47 -134 -157 -210.5t-269 -76.5 q-204 0 -319 114t-115 326zM582 1243l286 225h82l-307 -225h-61z" />
<glyph unicode="&#xfe;" d="M236 -397v1917h40v-785q54 151 181.5 240.5t292.5 89.5q220 0 367 -152t147 -385t-147 -383.5t-367 -150.5q-165 0 -292.5 89t-181.5 239v-719h-40zM276 528q0 -218 135 -356.5t339 -138.5t338.5 138.5t134.5 356.5t-134.5 358t-338.5 140t-339 -140t-135 -358z" />
<glyph unicode="&#xff;" horiz-adv-x="1359" d="M223 475v584h41v-584q0 -195 102 -298t289 -103q198 0 313 123.5t115 326.5v535h41v-965q0 -255 -108.5 -376t-331.5 -121q-133 0 -248 46.5t-180 129.5l29 28q71 -80 173.5 -123t223.5 -43q206 0 303.5 109.5t97.5 343.5v234q-47 -134 -157 -210.5t-269 -76.5 q-204 0 -319 114t-115 326zM453 1366q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5zM788 1366q0 22 16 37.5t38 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-38 15.5t-16 37.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2277" d="M121 715q0 154 55 287t153 228t240 149.5t308 54.5h1228v-39h-882v-647h790v-39h-790v-670h913v-39h-1259q-166 0 -307.5 54t-240 148t-153.5 226.5t-55 286.5zM162 715q0 -195 88 -348t251.5 -240.5t375.5 -87.5h305v1356h-305q-212 0 -375.5 -88.5t-251.5 -242.5 t-88 -349z" />
<glyph unicode="&#x153;" horiz-adv-x="2226" d="M111 530q0 234 146 384.5t368 150.5q183 0 317.5 -106.5t175.5 -282.5q42 177 177 283t325 106q104 0 194.5 -38t154 -102.5t102 -150.5t45.5 -182l-963 -184q38 -169 173 -272t321 -103q116 0 214 41.5t162 122.5l29 -29q-71 -85 -177 -129.5t-228 -44.5 q-197 0 -341 108t-188 287q-41 -179 -175 -287t-318 -108q-222 0 -368 151t-146 385zM152 530q0 -218 134.5 -357.5t338.5 -139.5t338.5 139.5t134.5 357.5t-134.5 357t-338.5 139t-338.5 -139t-134.5 -357zM1139 530q0 -44 6 -84l928 177q-14 107 -71.5 197.5t-158 148 t-223.5 57.5q-211 0 -346 -138t-135 -349v-9z" />
<glyph unicode="&#x178;" horiz-adv-x="1615" d="M250 946v488h41v-486q0 -263 132.5 -400.5t379.5 -137.5q249 0 380.5 144.5t131.5 434.5v445h41v-1039q0 -299 -145.5 -452.5t-407.5 -153.5q-341 0 -524 238l30 28q175 -227 496 -227q247 0 378.5 143t131.5 426v322q-55 -173 -187 -260.5t-327 -87.5q-259 0 -405 148.5 t-146 426.5zM582 1673q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5zM918 1673q0 22 15.5 37.5t37.5 15.5t37.5 -15.5t15.5 -37.5t-15.5 -37.5t-37.5 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1228" d="M332 1243l250 225h65l250 -225h-55l-228 191l-227 -191h-55z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1228" d="M336 1272q3 80 43 128t106 48q39 0 76.5 -22t62 -48.5t57 -48.5t62.5 -22q49 0 78.5 36t32.5 97h39q-3 -77 -44 -124.5t-106 -47.5q-39 0 -76.5 22t-62 48.5t-57 48.5t-62.5 22q-49 0 -78 -36.5t-32 -100.5h-39z" />
<glyph unicode="&#x2000;" horiz-adv-x="986" />
<glyph unicode="&#x2001;" horiz-adv-x="1972" />
<glyph unicode="&#x2002;" horiz-adv-x="986" />
<glyph unicode="&#x2003;" horiz-adv-x="1972" />
<glyph unicode="&#x2004;" horiz-adv-x="657" />
<glyph unicode="&#x2005;" horiz-adv-x="493" />
<glyph unicode="&#x2006;" horiz-adv-x="328" />
<glyph unicode="&#x2007;" horiz-adv-x="328" />
<glyph unicode="&#x2008;" horiz-adv-x="246" />
<glyph unicode="&#x2009;" horiz-adv-x="394" />
<glyph unicode="&#x200a;" horiz-adv-x="109" />
<glyph unicode="&#x2010;" horiz-adv-x="778" d="M133 522v39h512v-39h-512z" />
<glyph unicode="&#x2011;" horiz-adv-x="778" d="M133 522v39h512v-39h-512z" />
<glyph unicode="&#x2012;" horiz-adv-x="778" d="M133 522v39h512v-39h-512z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 522v39h1024v-39h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 522v39h2048v-39h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="372" d="M133 1186q0 13 8 59l68 275h37l-60 -281q23 -1 38.5 -17t15.5 -36q0 -22 -15.5 -37.5t-38.5 -15.5t-38 16.5t-15 36.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="372" d="M127 1139l59 280q-23 1 -38 17t-15 37q0 22 15 37.5t38 15.5q24 0 39 -16.5t15 -36.5q0 -8 -9 -60l-67 -274h-37z" />
<glyph unicode="&#x201a;" horiz-adv-x="372" d="M127 -287l59 281q-23 1 -38 16.5t-15 36.5q0 22 15 37.5t38 15.5q24 0 39 -16.5t15 -36.5q0 -7 -9 -59l-67 -275h-37z" />
<glyph unicode="&#x201c;" horiz-adv-x="638" d="M133 1186q0 13 8 59l68 275h37l-60 -281q23 -1 38.5 -17t15.5 -36q0 -22 -15.5 -37.5t-38.5 -15.5t-38 16.5t-15 36.5zM399 1186q0 7 9 59l67 275h37l-59 -281q23 -1 38 -16.5t15 -36.5q0 -22 -15 -37.5t-38 -15.5q-24 0 -39 16.5t-15 36.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="638" d="M127 1139l59 280q-23 1 -38 17t-15 37q0 22 15 37.5t38 15.5q24 0 39 -16.5t15 -36.5q0 -8 -9 -60l-67 -274h-37zM393 1139l60 280q-23 1 -38.5 17t-15.5 37q0 22 15.5 37.5t38.5 15.5t38 -16.5t15 -36.5q0 -14 -8 -60l-68 -274h-37z" />
<glyph unicode="&#x201e;" horiz-adv-x="638" d="M127 -287l59 281q-23 1 -38 16.5t-15 36.5q0 22 15 37.5t38 15.5q24 0 39 -16.5t15 -36.5q0 -7 -9 -59l-67 -275h-37zM393 -287l60 281q-23 1 -38.5 16.5t-15.5 36.5q0 22 15.5 37.5t38.5 15.5t38 -16.5t15 -36.5q0 -13 -8 -59l-68 -275h-37z" />
<glyph unicode="&#x2022;" horiz-adv-x="528" d="M174 551q0 37 26.5 63.5t63.5 26.5t63.5 -26.5t26.5 -63.5t-26.5 -63.5t-63.5 -26.5t-63.5 26.5t-26.5 63.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1138" d="M133 47q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5zM516 47q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5t-38 -15.5t-37.5 15.5t-15.5 37.5zM899 47q0 22 15.5 37.5t37.5 15.5t38 -15.5t16 -37.5t-16 -37.5 t-38 -15.5t-37.5 15.5t-15.5 37.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="394" />
<glyph unicode="&#x2039;" horiz-adv-x="559" d="M119 530l301 379h47l-299 -379l299 -378h-47z" />
<glyph unicode="&#x203a;" horiz-adv-x="559" d="M92 152l299 378l-299 379h47l301 -379l-301 -378h-47z" />
<glyph unicode="&#x205f;" horiz-adv-x="493" />
<glyph unicode="&#x20ac;" horiz-adv-x="1622" d="M61 557v37h236q-10 65 -10 123t10 123h-236v37h242q53 251 250 407t469 156q144 0 270.5 -46.5t216.5 -136.5l-24 -28q-180 172 -463 172q-251 0 -437 -146.5t-239 -377.5h809v-37h-817q-10 -65 -10 -123t10 -123h817v-37h-809q53 -231 239 -377.5t437 -146.5 q281 0 463 174l24 -29q-91 -91 -217 -137.5t-270 -46.5q-272 0 -469 156t-250 407h-242z" />
<glyph unicode="&#x2122;" horiz-adv-x="2060" d="M8 1397v37h717v-37h-338v-813h-41v813h-338zM868 584v850h35l426 -682l424 682h35v-850h-39v774l-410 -658h-22l-410 652v-768h-39z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1054" d="M0 0v1055h1055v-1055h-1055z" />
<hkern u1="&#x2a;" u2="&#xee;" k="-164" />
<hkern u1="&#x2f;" u2="&#xf0;" k="102" />
<hkern u1="F" u2="&#xee;" k="-41" />
<hkern u1="T" u2="&#xef;" k="-41" />
<hkern u1="T" u2="&#xee;" k="-82" />
<hkern u1="V" u2="&#xef;" k="-10" />
<hkern u1="V" u2="&#xee;" k="-51" />
<hkern u1="V" u2="&#xec;" k="-31" />
<hkern u1="V" u2="i" k="51" />
<hkern u1="f" u2="&#xef;" k="-123" />
<hkern u1="f" u2="&#xee;" k="-82" />
<hkern u1="f" u2="&#xec;" k="-143" />
<hkern u1="q" u2="j" k="-102" />
<hkern u1="&#xa3;" u2="&#xee;" k="-41" />
<hkern u1="&#xaa;" u2="&#xee;" k="-164" />
<hkern u1="&#xba;" u2="&#xee;" k="-164" />
<hkern u1="&#xee;" u2="&#xba;" k="-164" />
<hkern u1="&#xee;" u2="&#xaa;" k="-164" />
<hkern u1="&#xee;" u2="&#x3f;" k="-82" />
<hkern u1="&#xee;" u2="&#x2a;" k="-164" />
<hkern u1="&#x2018;" u2="&#xec;" k="-61" />
<hkern u1="&#x201c;" u2="&#xec;" k="-61" />
<hkern g1="ampersand" 	g2="ampersand" 	k="20" />
<hkern g1="ampersand" 	g2="backslash" 	k="164" />
<hkern g1="ampersand" 	g2="colon,semicolon" 	k="10" />
<hkern g1="ampersand" 	g2="degree" 	k="61" />
<hkern g1="ampersand" 	g2="exclam" 	k="20" />
<hkern g1="ampersand" 	g2="exclamdown" 	k="20" />
<hkern g1="ampersand" 	g2="one" 	k="61" />
<hkern g1="ampersand" 	g2="paragraph" 	k="61" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="ampersand" 	g2="question" 	k="174" />
<hkern g1="ampersand" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="ampersand" 	g2="seven" 	k="41" />
<hkern g1="ampersand" 	g2="slash" 	k="-102" />
<hkern g1="ampersand" 	g2="trademark" 	k="61" />
<hkern g1="ampersand" 	g2="underscore" 	k="-123" />
<hkern g1="currency" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="currency" 	g2="seven" 	k="41" />
<hkern g1="currency" 	g2="underscore" 	k="20" />
<hkern g1="currency" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="currency" 	g2="questiondown" 	k="20" />
<hkern g1="currency" 	g2="two" 	k="20" />
<hkern g1="degree" 	g2="backslash" 	k="-102" />
<hkern g1="degree" 	g2="one" 	k="-82" />
<hkern g1="degree" 	g2="percent" 	k="-41" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="123" />
<hkern g1="degree" 	g2="question" 	k="-41" />
<hkern g1="degree" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="degree" 	g2="seven" 	k="-102" />
<hkern g1="degree" 	g2="slash" 	k="123" />
<hkern g1="degree" 	g2="questiondown" 	k="143" />
<hkern g1="degree" 	g2="two" 	k="-61" />
<hkern g1="degree" 	g2="five" 	k="-20" />
<hkern g1="degree" 	g2="four" 	k="82" />
<hkern g1="degree" 	g2="nine" 	k="-82" />
<hkern g1="degree" 	g2="three" 	k="-31" />
<hkern g1="degree" 	g2="zero,six" 	k="-10" />
<hkern g1="percent" 	g2="backslash" 	k="82" />
<hkern g1="percent" 	g2="degree" 	k="61" />
<hkern g1="percent" 	g2="one" 	k="41" />
<hkern g1="percent" 	g2="percent" 	k="236" />
<hkern g1="percent" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="percent" 	g2="question" 	k="123" />
<hkern g1="percent" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="percent" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="percent" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="percent" 	g2="seven" 	k="41" />
<hkern g1="percent" 	g2="slash" 	k="-2" />
<hkern g1="percent" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="percent" 	g2="two" 	k="-20" />
<hkern g1="percent" 	g2="five" 	k="-41" />
<hkern g1="percent" 	g2="four" 	k="-72" />
<hkern g1="percent" 	g2="three" 	k="-20" />
<hkern g1="percent" 	g2="parenright" 	k="41" />
<hkern g1="percent" 	g2="eight" 	k="-41" />
<hkern g1="section" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="section" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="section" 	g2="seven" 	k="-20" />
<hkern g1="section" 	g2="slash" 	k="-61" />
<hkern g1="section" 	g2="underscore" 	k="-20" />
<hkern g1="section" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="section" 	g2="four" 	k="-41" />
<hkern g1="section" 	g2="eight" 	k="-20" />
<hkern g1="trademark" 	g2="backslash" 	k="-82" />
<hkern g1="trademark" 	g2="seven" 	k="-20" />
<hkern g1="trademark" 	g2="slash" 	k="102" />
<hkern g1="trademark" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="trademark" 	g2="questiondown" 	k="41" />
<hkern g1="trademark" 	g2="nine" 	k="-61" />
<hkern g1="yen" 	g2="backslash" 	k="-102" />
<hkern g1="yen" 	g2="colon,semicolon" 	k="82" />
<hkern g1="yen" 	g2="exclamdown" 	k="41" />
<hkern g1="yen" 	g2="one" 	k="-61" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="yen" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="yen" 	g2="seven" 	k="-61" />
<hkern g1="yen" 	g2="slash" 	k="61" />
<hkern g1="yen" 	g2="underscore" 	k="20" />
<hkern g1="yen" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="yen" 	g2="questiondown" 	k="82" />
<hkern g1="yen" 	g2="four" 	k="20" />
<hkern g1="yen" 	g2="three" 	k="-20" />
<hkern g1="yen" 	g2="zero,six" 	k="20" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="backslash" 	g2="ampersand" 	k="-41" />
<hkern g1="backslash" 	g2="backslash" 	k="123" />
<hkern g1="backslash" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="backslash" 	g2="colon,semicolon" 	k="-82" />
<hkern g1="backslash" 	g2="degree" 	k="123" />
<hkern g1="backslash" 	g2="exclamdown" 	k="-82" />
<hkern g1="backslash" 	g2="four" 	k="20" />
<hkern g1="backslash" 	g2="guillemotright,guilsinglright" 	k="-31" />
<hkern g1="backslash" 	g2="one" 	k="41" />
<hkern g1="backslash" 	g2="paragraph" 	k="61" />
<hkern g1="backslash" 	g2="parenright" 	k="-61" />
<hkern g1="backslash" 	g2="percent" 	k="20" />
<hkern g1="backslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-225" />
<hkern g1="backslash" 	g2="question" 	k="123" />
<hkern g1="backslash" 	g2="questiondown" 	k="-61" />
<hkern g1="backslash" 	g2="quoteleft,quotedblleft" 	k="123" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="123" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="backslash" 	g2="section" 	k="-61" />
<hkern g1="backslash" 	g2="seven" 	k="102" />
<hkern g1="backslash" 	g2="slash" 	k="-41" />
<hkern g1="backslash" 	g2="three" 	k="-20" />
<hkern g1="backslash" 	g2="trademark" 	k="123" />
<hkern g1="backslash" 	g2="two" 	k="-20" />
<hkern g1="backslash" 	g2="underscore" 	k="-287" />
<hkern g1="backslash" 	g2="zero,six" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="backslash" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="seven" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="slash" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="trademark" 	k="-82" />
<hkern g1="bracketleft,braceleft" 	g2="underscore" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="27" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="31" />
<hkern g1="colon,semicolon" 	g2="question" 	k="20" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="slash" 	k="-82" />
<hkern g1="colon,semicolon" 	g2="underscore" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="yen" 	k="82" />
<hkern g1="exclam" 	g2="one" 	k="-20" />
<hkern g1="exclam" 	g2="seven" 	k="-20" />
<hkern g1="exclamdown" 	g2="backslash" 	k="113" />
<hkern g1="exclamdown" 	g2="one" 	k="41" />
<hkern g1="exclamdown" 	g2="slash" 	k="-82" />
<hkern g1="exclamdown" 	g2="yen" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="123" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="question" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="questiondown" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="slash" 	k="-31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="underscore" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="five" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="backslash" 	k="133" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="four" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="one" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="paragraph" 	k="184" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="percent" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="question" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="section" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="seven" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="three" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="two" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="zero,six" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="yen" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="currency,Euro" 	k="-20" />
<hkern g1="parenleft" 	g2="backslash" 	k="-61" />
<hkern g1="parenleft" 	g2="four" 	k="41" />
<hkern g1="parenleft" 	g2="one" 	k="-20" />
<hkern g1="parenleft" 	g2="slash" 	k="-61" />
<hkern g1="parenleft" 	g2="trademark" 	k="-41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="205" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="degree" 	k="123" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="paragraph" 	k="225" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="123" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="questiondown" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="113" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="slash" 	k="-225" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="two" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="underscore" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero,six" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="currency,Euro" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="-20" />
<hkern g1="question" 	g2="ampersand" 	k="61" />
<hkern g1="question" 	g2="degree" 	k="-20" />
<hkern g1="question" 	g2="exclamdown" 	k="20" />
<hkern g1="question" 	g2="four" 	k="102" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="question" 	g2="question" 	k="82" />
<hkern g1="question" 	g2="questiondown" 	k="236" />
<hkern g1="question" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="question" 	g2="slash" 	k="61" />
<hkern g1="question" 	g2="three" 	k="31" />
<hkern g1="question" 	g2="underscore" 	k="51" />
<hkern g1="question" 	g2="yen" 	k="31" />
<hkern g1="question" 	g2="eight" 	k="20" />
<hkern g1="question" 	g2="bracketleft" 	k="51" />
<hkern g1="question" 	g2="exclam" 	k="20" />
<hkern g1="question" 	g2="nine" 	k="-20" />
<hkern g1="questiondown" 	g2="ampersand" 	k="41" />
<hkern g1="questiondown" 	g2="backslash" 	k="215" />
<hkern g1="questiondown" 	g2="degree" 	k="102" />
<hkern g1="questiondown" 	g2="four" 	k="82" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="questiondown" 	g2="one" 	k="82" />
<hkern g1="questiondown" 	g2="paragraph" 	k="174" />
<hkern g1="questiondown" 	g2="percent" 	k="123" />
<hkern g1="questiondown" 	g2="question" 	k="266" />
<hkern g1="questiondown" 	g2="questiondown" 	k="82" />
<hkern g1="questiondown" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="questiondown" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="questiondown" 	g2="seven" 	k="82" />
<hkern g1="questiondown" 	g2="slash" 	k="-82" />
<hkern g1="questiondown" 	g2="trademark" 	k="61" />
<hkern g1="questiondown" 	g2="underscore" 	k="-102" />
<hkern g1="questiondown" 	g2="zero,six" 	k="82" />
<hkern g1="questiondown" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="82" />
<hkern g1="questiondown" 	g2="yen" 	k="102" />
<hkern g1="questiondown" 	g2="currency,Euro" 	k="61" />
<hkern g1="questiondown" 	g2="eight" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="ampersand" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="backslash" 	k="-123" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="paragraph" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="percent" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="question" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="section" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-102" />
<hkern g1="quoteleft,quotedblleft" 	g2="slash" 	k="123" />
<hkern g1="quoteleft,quotedblleft" 	g2="three" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="trademark" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="two" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="nine" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="backslash" 	k="-123" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="113" />
<hkern g1="quoteright,quotedblright" 	g2="one" 	k="-102" />
<hkern g1="quoteright,quotedblright" 	g2="paragraph" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="percent" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="question" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="questiondown" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="section" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="123" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="trademark" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="backslash" 	k="-123" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="degree" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-102" />
<hkern g1="quotedbl,quotesingle" 	g2="paragraph" 	k="-123" />
<hkern g1="quotedbl,quotesingle" 	g2="percent" 	k="-61" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="quotedbl,quotesingle" 	g2="question" 	k="-82" />
<hkern g1="quotedbl,quotesingle" 	g2="questiondown" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="quotedbl,quotesingle" 	k="-92" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-123" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="trademark" 	k="-82" />
<hkern g1="quotedbl,quotesingle" 	g2="two" 	k="-61" />
<hkern g1="quotedbl,quotesingle" 	g2="underscore" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="zero,six" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="yen" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="five" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="currency,Euro" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="nine" 	k="-102" />
<hkern g1="slash" 	g2="ampersand" 	k="82" />
<hkern g1="slash" 	g2="backslash" 	k="-82" />
<hkern g1="slash" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="31" />
<hkern g1="slash" 	g2="degree" 	k="-102" />
<hkern g1="slash" 	g2="exclamdown" 	k="133" />
<hkern g1="slash" 	g2="four" 	k="143" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="123" />
<hkern g1="slash" 	g2="one" 	k="-61" />
<hkern g1="slash" 	g2="parenright" 	k="-61" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="205" />
<hkern g1="slash" 	g2="questiondown" 	k="154" />
<hkern g1="slash" 	g2="quoteleft,quotedblleft" 	k="-123" />
<hkern g1="slash" 	g2="quoteright,quotedblright" 	k="-123" />
<hkern g1="slash" 	g2="quotedbl,quotesingle" 	k="-123" />
<hkern g1="slash" 	g2="section" 	k="41" />
<hkern g1="slash" 	g2="seven" 	k="-41" />
<hkern g1="slash" 	g2="slash" 	k="123" />
<hkern g1="slash" 	g2="trademark" 	k="-102" />
<hkern g1="slash" 	g2="underscore" 	k="266" />
<hkern g1="slash" 	g2="zero,six" 	k="41" />
<hkern g1="slash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="133" />
<hkern g1="slash" 	g2="yen" 	k="-82" />
<hkern g1="slash" 	g2="eight" 	k="41" />
<hkern g1="slash" 	g2="sterling" 	k="20" />
<hkern g1="underscore" 	g2="backslash" 	k="266" />
<hkern g1="underscore" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="underscore" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="underscore" 	g2="four" 	k="61" />
<hkern g1="underscore" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="underscore" 	g2="one" 	k="20" />
<hkern g1="underscore" 	g2="paragraph" 	k="195" />
<hkern g1="underscore" 	g2="percent" 	k="61" />
<hkern g1="underscore" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="underscore" 	g2="question" 	k="10" />
<hkern g1="underscore" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="underscore" 	g2="section" 	k="-61" />
<hkern g1="underscore" 	g2="slash" 	k="-287" />
<hkern g1="underscore" 	g2="three" 	k="-61" />
<hkern g1="underscore" 	g2="trademark" 	k="102" />
<hkern g1="underscore" 	g2="two" 	k="-82" />
<hkern g1="underscore" 	g2="zero,six" 	k="41" />
<hkern g1="underscore" 	g2="yen" 	k="20" />
<hkern g1="underscore" 	g2="five" 	k="-20" />
<hkern g1="underscore" 	g2="currency,Euro" 	k="20" />
<hkern g1="underscore" 	g2="nine" 	k="-20" />
<hkern g1="underscore" 	g2="sterling" 	k="-41" />
<hkern g1="eight" 	g2="AE" 	k="10" />
<hkern g1="eight" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="10" />
<hkern g1="eight" 	g2="J" 	k="72" />
<hkern g1="eight" 	g2="T" 	k="10" />
<hkern g1="eight" 	g2="V" 	k="31" />
<hkern g1="eight" 	g2="X" 	k="41" />
<hkern g1="eight" 	g2="Z" 	k="10" />
<hkern g1="eight" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="eight" 	g2="backslash" 	k="61" />
<hkern g1="eight" 	g2="j" 	k="16" />
<hkern g1="eight" 	g2="percent" 	k="20" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="eight" 	g2="question" 	k="20" />
<hkern g1="eight" 	g2="questiondown" 	k="41" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="eight" 	g2="section" 	k="-20" />
<hkern g1="eight" 	g2="v" 	k="-20" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="five" 	g2="J" 	k="82" />
<hkern g1="five" 	g2="V" 	k="10" />
<hkern g1="five" 	g2="X" 	k="20" />
<hkern g1="five" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="five" 	g2="backslash" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="20" />
<hkern g1="five" 	g2="v" 	k="10" />
<hkern g1="five" 	g2="degree" 	k="41" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="seven" 	k="31" />
<hkern g1="five" 	g2="three" 	k="10" />
<hkern g1="five" 	g2="two" 	k="10" />
<hkern g1="five" 	g2="x" 	k="10" />
<hkern g1="four" 	g2="J" 	k="20" />
<hkern g1="four" 	g2="T" 	k="102" />
<hkern g1="four" 	g2="V" 	k="102" />
<hkern g1="four" 	g2="X" 	k="10" />
<hkern g1="four" 	g2="Z" 	k="20" />
<hkern g1="four" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="percent" 	k="61" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="four" 	g2="question" 	k="102" />
<hkern g1="four" 	g2="questiondown" 	k="-20" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="four" 	g2="section" 	k="-31" />
<hkern g1="four" 	g2="v" 	k="20" />
<hkern g1="four" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="degree" 	k="61" />
<hkern g1="four" 	g2="five" 	k="10" />
<hkern g1="four" 	g2="seven" 	k="113" />
<hkern g1="four" 	g2="three" 	k="41" />
<hkern g1="four" 	g2="two" 	k="10" />
<hkern g1="four" 	g2="x" 	k="20" />
<hkern g1="four" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="four" 	g2="dollar,S" 	k="20" />
<hkern g1="four" 	g2="ae" 	k="-61" />
<hkern g1="four" 	g2="slash" 	k="-61" />
<hkern g1="four" 	g2="underscore" 	k="-41" />
<hkern g1="four" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="-41" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="four" 	g2="ampersand" 	k="-41" />
<hkern g1="four" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="four" 	g2="currency,Euro" 	k="-20" />
<hkern g1="four" 	g2="eight" 	k="-20" />
<hkern g1="four" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="four" 	g2="sterling" 	k="-20" />
<hkern g1="four" 	g2="nine" 	k="20" />
<hkern g1="four" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="four" 	g2="one" 	k="51" />
<hkern g1="four" 	g2="paragraph" 	k="20" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="four" 	g2="trademark" 	k="41" />
<hkern g1="seven" 	g2="AE" 	k="143" />
<hkern g1="seven" 	g2="J" 	k="41" />
<hkern g1="seven" 	g2="T" 	k="-20" />
<hkern g1="seven" 	g2="V" 	k="-10" />
<hkern g1="seven" 	g2="X" 	k="10" />
<hkern g1="seven" 	g2="Z" 	k="20" />
<hkern g1="seven" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="seven" 	g2="backslash" 	k="-20" />
<hkern g1="seven" 	g2="j" 	k="41" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="seven" 	g2="question" 	k="-31" />
<hkern g1="seven" 	g2="questiondown" 	k="143" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-61" />
<hkern g1="seven" 	g2="section" 	k="20" />
<hkern g1="seven" 	g2="v" 	k="20" />
<hkern g1="seven" 	g2="yen" 	k="-20" />
<hkern g1="seven" 	g2="five" 	k="41" />
<hkern g1="seven" 	g2="three" 	k="20" />
<hkern g1="seven" 	g2="x" 	k="61" />
<hkern g1="seven" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="seven" 	g2="dollar,S" 	k="20" />
<hkern g1="seven" 	g2="ae" 	k="82" />
<hkern g1="seven" 	g2="slash" 	k="92" />
<hkern g1="seven" 	g2="underscore" 	k="61" />
<hkern g1="seven" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="seven" 	g2="ampersand" 	k="82" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="41" />
<hkern g1="seven" 	g2="currency,Euro" 	k="41" />
<hkern g1="seven" 	g2="eight" 	k="41" />
<hkern g1="seven" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="102" />
<hkern g1="seven" 	g2="sterling" 	k="31" />
<hkern g1="seven" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="seven" 	g2="one" 	k="-20" />
<hkern g1="seven" 	g2="paragraph" 	k="-61" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="seven" 	g2="trademark" 	k="-82" />
<hkern g1="seven" 	g2="exclamdown" 	k="61" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="seven" 	g2="s" 	k="61" />
<hkern g1="seven" 	g2="z" 	k="72" />
<hkern g1="seven" 	g2="zero,six" 	k="41" />
<hkern g1="seven" 	g2="uniFB02" 	k="41" />
<hkern g1="seven" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="seven" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="seven" 	g2="four" 	k="133" />
<hkern g1="seven" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="61" />
<hkern g1="seven" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="61" />
<hkern g1="six" 	g2="AE" 	k="-20" />
<hkern g1="six" 	g2="J" 	k="51" />
<hkern g1="six" 	g2="T" 	k="41" />
<hkern g1="six" 	g2="V" 	k="20" />
<hkern g1="six" 	g2="X" 	k="20" />
<hkern g1="six" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="six" 	g2="j" 	k="16" />
<hkern g1="six" 	g2="percent" 	k="41" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="six" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="six" 	g2="section" 	k="-41" />
<hkern g1="six" 	g2="v" 	k="10" />
<hkern g1="six" 	g2="degree" 	k="51" />
<hkern g1="six" 	g2="seven" 	k="20" />
<hkern g1="six" 	g2="x" 	k="31" />
<hkern g1="six" 	g2="slash" 	k="-20" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="six" 	g2="ampersand" 	k="-20" />
<hkern g1="six" 	g2="currency,Euro" 	k="-20" />
<hkern g1="six" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="six" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="three" 	g2="AE" 	k="-10" />
<hkern g1="three" 	g2="J" 	k="51" />
<hkern g1="three" 	g2="V" 	k="20" />
<hkern g1="three" 	g2="X" 	k="20" />
<hkern g1="three" 	g2="j" 	k="10" />
<hkern g1="three" 	g2="percent" 	k="20" />
<hkern g1="three" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="three" 	g2="v" 	k="10" />
<hkern g1="three" 	g2="degree" 	k="31" />
<hkern g1="three" 	g2="five" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="31" />
<hkern g1="three" 	g2="three" 	k="10" />
<hkern g1="three" 	g2="two" 	k="10" />
<hkern g1="three" 	g2="x" 	k="20" />
<hkern g1="two" 	g2="V" 	k="20" />
<hkern g1="two" 	g2="backslash" 	k="20" />
<hkern g1="two" 	g2="percent" 	k="-20" />
<hkern g1="two" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="two" 	g2="v" 	k="-10" />
<hkern g1="two" 	g2="seven" 	k="10" />
<hkern g1="two" 	g2="x" 	k="20" />
<hkern g1="two" 	g2="slash" 	k="-20" />
<hkern g1="two" 	g2="underscore" 	k="-61" />
<hkern g1="two" 	g2="ampersand" 	k="20" />
<hkern g1="two" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="two" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="two" 	g2="z" 	k="20" />
<hkern g1="two" 	g2="zero,six" 	k="10" />
<hkern g1="two" 	g2="four" 	k="61" />
<hkern g1="two" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="10" />
<hkern g1="zero,nine" 	g2="AE" 	k="10" />
<hkern g1="zero,nine" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="zero,nine" 	g2="J" 	k="102" />
<hkern g1="zero,nine" 	g2="T" 	k="31" />
<hkern g1="zero,nine" 	g2="V" 	k="51" />
<hkern g1="zero,nine" 	g2="X" 	k="61" />
<hkern g1="zero,nine" 	g2="Z" 	k="51" />
<hkern g1="zero,nine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="zero,nine" 	g2="backslash" 	k="41" />
<hkern g1="zero,nine" 	g2="j" 	k="16" />
<hkern g1="zero,nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="zero,nine" 	g2="question" 	k="41" />
<hkern g1="zero,nine" 	g2="questiondown" 	k="61" />
<hkern g1="zero,nine" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="zero,nine" 	g2="v" 	k="-20" />
<hkern g1="zero,nine" 	g2="yen" 	k="20" />
<hkern g1="zero,nine" 	g2="degree" 	k="-10" />
<hkern g1="zero,nine" 	g2="seven" 	k="20" />
<hkern g1="zero,nine" 	g2="three" 	k="20" />
<hkern g1="zero,nine" 	g2="x" 	k="20" />
<hkern g1="zero,nine" 	g2="slash" 	k="41" />
<hkern g1="zero,nine" 	g2="underscore" 	k="41" />
<hkern g1="zero,nine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="zero,nine" 	g2="uniFB02" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="20" />
<hkern g1="B" 	g2="J" 	k="61" />
<hkern g1="B" 	g2="T" 	k="10" />
<hkern g1="B" 	g2="V" 	k="10" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="B" 	g2="underscore" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="J" 	k="61" />
<hkern g1="C,Ccedilla,Euro" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="V" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="X" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="C,Ccedilla,Euro" 	g2="backslash" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="colon,semicolon" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="degree" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="eight" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="exclamdown" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="four" 	k="82" />
<hkern g1="C,Ccedilla,Euro" 	g2="nine" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="C,Ccedilla,Euro" 	g2="one" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="paragraph" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-31" />
<hkern g1="C,Ccedilla,Euro" 	g2="questiondown" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="C,Ccedilla,Euro" 	g2="s" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="three" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="underscore" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="x" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="zero,six" 	k="51" />
<hkern g1="C,Ccedilla,Euro" 	g2="AE" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="dollar,S" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="ae" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="ampersand" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="section" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="two" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="z" 	k="41" />
<hkern g1="AE,OE" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="AE,OE" 	g2="V" 	k="20" />
<hkern g1="AE,OE" 	g2="X" 	k="20" />
<hkern g1="AE,OE" 	g2="colon,semicolon" 	k="20" />
<hkern g1="AE,OE" 	g2="eight" 	k="20" />
<hkern g1="AE,OE" 	g2="exclamdown" 	k="20" />
<hkern g1="AE,OE" 	g2="four" 	k="102" />
<hkern g1="AE,OE" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="AE,OE" 	g2="nine" 	k="10" />
<hkern g1="AE,OE" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="AE,OE" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="AE,OE" 	g2="question" 	k="41" />
<hkern g1="AE,OE" 	g2="s" 	k="20" />
<hkern g1="AE,OE" 	g2="seven" 	k="20" />
<hkern g1="AE,OE" 	g2="uniFB02" 	k="41" />
<hkern g1="AE,OE" 	g2="three" 	k="41" />
<hkern g1="AE,OE" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="AE,OE" 	g2="underscore" 	k="-41" />
<hkern g1="AE,OE" 	g2="v" 	k="31" />
<hkern g1="AE,OE" 	g2="x" 	k="41" />
<hkern g1="AE,OE" 	g2="zero,six" 	k="41" />
<hkern g1="AE,OE" 	g2="ae" 	k="20" />
<hkern g1="AE,OE" 	g2="five" 	k="20" />
<hkern g1="AE,OE" 	g2="section" 	k="20" />
<hkern g1="AE,OE" 	g2="two" 	k="20" />
<hkern g1="AE,OE" 	g2="z" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="J" 	k="41" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="X" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="eight" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="four" 	k="102" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="nine" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="question" 	k="41" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="s" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="slash" 	k="-20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="uniFB02" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="underscore" 	k="-82" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="v" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="x" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="zero,six" 	k="41" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="ae" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="parenright" 	k="31" />
<hkern g1="F,sterling" 	g2="J" 	k="72" />
<hkern g1="F,sterling" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="F,sterling" 	g2="X" 	k="20" />
<hkern g1="F,sterling" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-82" />
<hkern g1="F,sterling" 	g2="backslash" 	k="-41" />
<hkern g1="F,sterling" 	g2="colon,semicolon" 	k="61" />
<hkern g1="F,sterling" 	g2="eight" 	k="72" />
<hkern g1="F,sterling" 	g2="exclamdown" 	k="51" />
<hkern g1="F,sterling" 	g2="four" 	k="92" />
<hkern g1="F,sterling" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="F,sterling" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="F,sterling" 	g2="nine" 	k="41" />
<hkern g1="F,sterling" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="F,sterling" 	g2="one" 	k="-41" />
<hkern g1="F,sterling" 	g2="paragraph" 	k="-41" />
<hkern g1="F,sterling" 	g2="questiondown" 	k="51" />
<hkern g1="F,sterling" 	g2="s" 	k="82" />
<hkern g1="F,sterling" 	g2="seven" 	k="-20" />
<hkern g1="F,sterling" 	g2="slash" 	k="-20" />
<hkern g1="F,sterling" 	g2="uniFB02" 	k="41" />
<hkern g1="F,sterling" 	g2="three" 	k="20" />
<hkern g1="F,sterling" 	g2="trademark" 	k="-82" />
<hkern g1="F,sterling" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="61" />
<hkern g1="F,sterling" 	g2="underscore" 	k="-41" />
<hkern g1="F,sterling" 	g2="v" 	k="41" />
<hkern g1="F,sterling" 	g2="x" 	k="51" />
<hkern g1="F,sterling" 	g2="zero,six" 	k="82" />
<hkern g1="F,sterling" 	g2="AE" 	k="92" />
<hkern g1="F,sterling" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="F,sterling" 	g2="dollar,S" 	k="20" />
<hkern g1="F,sterling" 	g2="ae" 	k="82" />
<hkern g1="F,sterling" 	g2="ampersand" 	k="92" />
<hkern g1="F,sterling" 	g2="five" 	k="20" />
<hkern g1="F,sterling" 	g2="section" 	k="20" />
<hkern g1="F,sterling" 	g2="two" 	k="20" />
<hkern g1="F,sterling" 	g2="z" 	k="82" />
<hkern g1="F,sterling" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="F,sterling" 	g2="j" 	k="51" />
<hkern g1="F,sterling" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="41" />
<hkern g1="G" 	g2="J" 	k="20" />
<hkern g1="G" 	g2="backslash" 	k="41" />
<hkern g1="G" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="H,U,paragraph,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="20" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="eight" 	k="10" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="four" 	k="20" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="nine" 	k="10" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="trademark" 	k="-41" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="zero,six" 	k="20" />
<hkern g1="J" 	g2="J" 	k="41" />
<hkern g1="J" 	g2="underscore" 	k="20" />
<hkern g1="K" 	g2="J" 	k="-20" />
<hkern g1="K" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="K" 	g2="T" 	k="41" />
<hkern g1="K" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="K" 	g2="V" 	k="82" />
<hkern g1="K" 	g2="X" 	k="72" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="K" 	g2="degree" 	k="20" />
<hkern g1="K" 	g2="eight" 	k="41" />
<hkern g1="K" 	g2="four" 	k="102" />
<hkern g1="K" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="K" 	g2="nine" 	k="20" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="K" 	g2="one" 	k="-41" />
<hkern g1="K" 	g2="question" 	k="82" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="K" 	g2="s" 	k="41" />
<hkern g1="K" 	g2="slash" 	k="-20" />
<hkern g1="K" 	g2="uniFB02" 	k="61" />
<hkern g1="K" 	g2="three" 	k="20" />
<hkern g1="K" 	g2="trademark" 	k="-41" />
<hkern g1="K" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="41" />
<hkern g1="K" 	g2="underscore" 	k="-143" />
<hkern g1="K" 	g2="v" 	k="72" />
<hkern g1="K" 	g2="x" 	k="72" />
<hkern g1="K" 	g2="zero,six" 	k="41" />
<hkern g1="K" 	g2="AE" 	k="51" />
<hkern g1="K" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="K" 	g2="dollar,S" 	k="31" />
<hkern g1="K" 	g2="ae" 	k="31" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="z" 	k="41" />
<hkern g1="K" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="K" 	g2="j" 	k="10" />
<hkern g1="K" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="K" 	g2="bracketleft" 	k="82" />
<hkern g1="L" 	g2="J" 	k="-61" />
<hkern g1="L" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="L" 	g2="T" 	k="133" />
<hkern g1="L" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="L" 	g2="V" 	k="113" />
<hkern g1="L" 	g2="X" 	k="20" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="L" 	g2="asterisk,ordfeminine,ordmasculine" 	k="143" />
<hkern g1="L" 	g2="backslash" 	k="143" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="L" 	g2="degree" 	k="133" />
<hkern g1="L" 	g2="eight" 	k="20" />
<hkern g1="L" 	g2="four" 	k="102" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="L" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="L" 	g2="nine" 	k="20" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="L" 	g2="one" 	k="82" />
<hkern g1="L" 	g2="paragraph" 	k="143" />
<hkern g1="L" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="L" 	g2="question" 	k="205" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="92" />
<hkern g1="L" 	g2="seven" 	k="102" />
<hkern g1="L" 	g2="slash" 	k="-61" />
<hkern g1="L" 	g2="uniFB02" 	k="10" />
<hkern g1="L" 	g2="trademark" 	k="123" />
<hkern g1="L" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="L" 	g2="underscore" 	k="-143" />
<hkern g1="L" 	g2="v" 	k="82" />
<hkern g1="L" 	g2="x" 	k="20" />
<hkern g1="L" 	g2="zero,six" 	k="61" />
<hkern g1="L" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="L" 	g2="dollar,S" 	k="20" />
<hkern g1="M,N,Ntilde" 	g2="backslash" 	k="20" />
<hkern g1="M,N,Ntilde" 	g2="one" 	k="10" />
<hkern g1="M,N,Ntilde" 	g2="question" 	k="10" />
<hkern g1="M,N,Ntilde" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="M,N,Ntilde" 	g2="trademark" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="51" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="degree" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="questiondown" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="uniFB02" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="-41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="j" 	k="-164" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="20" />
<hkern g1="P" 	g2="J" 	k="102" />
<hkern g1="P" 	g2="V" 	k="31" />
<hkern g1="P" 	g2="X" 	k="51" />
<hkern g1="P" 	g2="backslash" 	k="41" />
<hkern g1="P" 	g2="degree" 	k="-41" />
<hkern g1="P" 	g2="eight" 	k="10" />
<hkern g1="P" 	g2="four" 	k="61" />
<hkern g1="P" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="P" 	g2="questiondown" 	k="113" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="slash" 	k="102" />
<hkern g1="P" 	g2="uniFB02" 	k="-10" />
<hkern g1="P" 	g2="three" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="61" />
<hkern g1="P" 	g2="v" 	k="20" />
<hkern g1="P" 	g2="x" 	k="72" />
<hkern g1="P" 	g2="AE" 	k="92" />
<hkern g1="P" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="P" 	g2="ae" 	k="31" />
<hkern g1="P" 	g2="ampersand" 	k="72" />
<hkern g1="P" 	g2="five" 	k="20" />
<hkern g1="P" 	g2="two" 	k="10" />
<hkern g1="P" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="P" 	g2="parenright" 	k="31" />
<hkern g1="P" 	g2="j" 	k="20" />
<hkern g1="P" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="61" />
<hkern g1="P" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="P" 	g2="Z" 	k="41" />
<hkern g1="P" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="20" />
<hkern g1="P" 	g2="bracketright,braceright" 	k="31" />
<hkern g1="R" 	g2="J" 	k="61" />
<hkern g1="R" 	g2="X" 	k="41" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="degree" 	k="-31" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="question" 	k="10" />
<hkern g1="R" 	g2="questiondown" 	k="20" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="R" 	g2="uniFB02" 	k="-10" />
<hkern g1="R" 	g2="three" 	k="20" />
<hkern g1="R" 	g2="underscore" 	k="-41" />
<hkern g1="R" 	g2="ae" 	k="10" />
<hkern g1="dollar,S" 	g2="J" 	k="61" />
<hkern g1="dollar,S" 	g2="T" 	k="20" />
<hkern g1="dollar,S" 	g2="X" 	k="31" />
<hkern g1="dollar,S" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="20" />
<hkern g1="dollar,S" 	g2="colon,semicolon" 	k="10" />
<hkern g1="dollar,S" 	g2="degree" 	k="31" />
<hkern g1="dollar,S" 	g2="four" 	k="-20" />
<hkern g1="dollar,S" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="dollar,S" 	g2="nine" 	k="20" />
<hkern g1="dollar,S" 	g2="one" 	k="20" />
<hkern g1="dollar,S" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="questiondown" 	k="20" />
<hkern g1="dollar,S" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="dollar,S" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="dollar,S" 	g2="seven" 	k="20" />
<hkern g1="dollar,S" 	g2="three" 	k="20" />
<hkern g1="dollar,S" 	g2="underscore" 	k="41" />
<hkern g1="dollar,S" 	g2="v" 	k="20" />
<hkern g1="dollar,S" 	g2="x" 	k="20" />
<hkern g1="T" 	g2="J" 	k="20" />
<hkern g1="T" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="V" 	k="-41" />
<hkern g1="T" 	g2="X" 	k="20" />
<hkern g1="T" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-82" />
<hkern g1="T" 	g2="backslash" 	k="-41" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="degree" 	k="-61" />
<hkern g1="T" 	g2="eight" 	k="10" />
<hkern g1="T" 	g2="exclamdown" 	k="41" />
<hkern g1="T" 	g2="four" 	k="164" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="T" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="one" 	k="-82" />
<hkern g1="T" 	g2="paragraph" 	k="-102" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="T" 	g2="question" 	k="-61" />
<hkern g1="T" 	g2="questiondown" 	k="113" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="T" 	g2="s" 	k="61" />
<hkern g1="T" 	g2="seven" 	k="-61" />
<hkern g1="T" 	g2="slash" 	k="41" />
<hkern g1="T" 	g2="three" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-102" />
<hkern g1="T" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="41" />
<hkern g1="T" 	g2="underscore" 	k="20" />
<hkern g1="T" 	g2="x" 	k="10" />
<hkern g1="T" 	g2="zero,six" 	k="31" />
<hkern g1="T" 	g2="AE" 	k="82" />
<hkern g1="T" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="T" 	g2="ae" 	k="61" />
<hkern g1="T" 	g2="ampersand" 	k="61" />
<hkern g1="T" 	g2="five" 	k="-20" />
<hkern g1="T" 	g2="two" 	k="-41" />
<hkern g1="T" 	g2="z" 	k="41" />
<hkern g1="T" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="T" 	g2="j" 	k="20" />
<hkern g1="T" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="T" 	g2="exclam" 	k="-41" />
<hkern g1="T" 	g2="parenleft" 	k="41" />
<hkern g1="Thorn" 	g2="J" 	k="143" />
<hkern g1="Thorn" 	g2="T" 	k="41" />
<hkern g1="Thorn" 	g2="V" 	k="20" />
<hkern g1="Thorn" 	g2="X" 	k="61" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="one" 	k="41" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="51" />
<hkern g1="Thorn" 	g2="question" 	k="61" />
<hkern g1="Thorn" 	g2="questiondown" 	k="51" />
<hkern g1="Thorn" 	g2="slash" 	k="61" />
<hkern g1="Thorn" 	g2="uniFB02" 	k="-10" />
<hkern g1="Thorn" 	g2="three" 	k="41" />
<hkern g1="Thorn" 	g2="underscore" 	k="61" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="AE" 	k="61" />
<hkern g1="Thorn" 	g2="ae" 	k="20" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="two" 	k="51" />
<hkern g1="Thorn" 	g2="j" 	k="20" />
<hkern g1="Thorn" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="31" />
<hkern g1="Thorn" 	g2="Z" 	k="41" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="J" 	k="51" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="X" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="questiondown" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="slash" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="underscore" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="x" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="AE" 	k="31" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="j" 	k="10" />
<hkern g1="V" 	g2="J" 	k="51" />
<hkern g1="V" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="V" 	g2="T" 	k="-41" />
<hkern g1="V" 	g2="X" 	k="41" />
<hkern g1="V" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-72" />
<hkern g1="V" 	g2="backslash" 	k="-61" />
<hkern g1="V" 	g2="colon,semicolon" 	k="41" />
<hkern g1="V" 	g2="degree" 	k="-61" />
<hkern g1="V" 	g2="eight" 	k="31" />
<hkern g1="V" 	g2="exclamdown" 	k="61" />
<hkern g1="V" 	g2="four" 	k="143" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="V" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="V" 	g2="nine" 	k="10" />
<hkern g1="V" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="V" 	g2="one" 	k="-61" />
<hkern g1="V" 	g2="paragraph" 	k="-61" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="72" />
<hkern g1="V" 	g2="questiondown" 	k="154" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="V" 	g2="s" 	k="113" />
<hkern g1="V" 	g2="seven" 	k="-20" />
<hkern g1="V" 	g2="slash" 	k="82" />
<hkern g1="V" 	g2="trademark" 	k="-82" />
<hkern g1="V" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="61" />
<hkern g1="V" 	g2="underscore" 	k="20" />
<hkern g1="V" 	g2="v" 	k="31" />
<hkern g1="V" 	g2="x" 	k="72" />
<hkern g1="V" 	g2="zero,six" 	k="51" />
<hkern g1="V" 	g2="AE" 	k="72" />
<hkern g1="V" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="V" 	g2="dollar,S" 	k="31" />
<hkern g1="V" 	g2="ae" 	k="113" />
<hkern g1="V" 	g2="ampersand" 	k="61" />
<hkern g1="V" 	g2="five" 	k="10" />
<hkern g1="V" 	g2="z" 	k="51" />
<hkern g1="V" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="V" 	g2="j" 	k="41" />
<hkern g1="V" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="72" />
<hkern g1="V" 	g2="Z" 	k="20" />
<hkern g1="V" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="V" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="V" 	g2="exclam" 	k="-20" />
<hkern g1="X" 	g2="J" 	k="-20" />
<hkern g1="X" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="X" 	g2="T" 	k="20" />
<hkern g1="X" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="X" 	g2="V" 	k="41" />
<hkern g1="X" 	g2="X" 	k="61" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="X" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="X" 	g2="colon,semicolon" 	k="10" />
<hkern g1="X" 	g2="degree" 	k="41" />
<hkern g1="X" 	g2="eight" 	k="41" />
<hkern g1="X" 	g2="exclamdown" 	k="20" />
<hkern g1="X" 	g2="four" 	k="102" />
<hkern g1="X" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="31" />
<hkern g1="X" 	g2="nine" 	k="31" />
<hkern g1="X" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="X" 	g2="one" 	k="-20" />
<hkern g1="X" 	g2="question" 	k="61" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="X" 	g2="s" 	k="41" />
<hkern g1="X" 	g2="seven" 	k="20" />
<hkern g1="X" 	g2="slash" 	k="-41" />
<hkern g1="X" 	g2="uniFB02" 	k="41" />
<hkern g1="X" 	g2="trademark" 	k="-61" />
<hkern g1="X" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="51" />
<hkern g1="X" 	g2="underscore" 	k="-102" />
<hkern g1="X" 	g2="v" 	k="41" />
<hkern g1="X" 	g2="x" 	k="61" />
<hkern g1="X" 	g2="zero,six" 	k="61" />
<hkern g1="X" 	g2="AE" 	k="61" />
<hkern g1="X" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="31" />
<hkern g1="X" 	g2="dollar,S" 	k="31" />
<hkern g1="X" 	g2="ae" 	k="31" />
<hkern g1="X" 	g2="ampersand" 	k="41" />
<hkern g1="X" 	g2="z" 	k="20" />
<hkern g1="X" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="X" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="X" 	g2="Z" 	k="20" />
<hkern g1="X" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="51" />
<hkern g1="X" 	g2="parenleft" 	k="31" />
<hkern g1="Z" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Z" 	g2="T" 	k="-20" />
<hkern g1="Z" 	g2="X" 	k="20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="Z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="Z" 	g2="eight" 	k="10" />
<hkern g1="Z" 	g2="four" 	k="102" />
<hkern g1="Z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="Z" 	g2="one" 	k="-41" />
<hkern g1="Z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="Z" 	g2="slash" 	k="-20" />
<hkern g1="Z" 	g2="uniFB02" 	k="20" />
<hkern g1="Z" 	g2="trademark" 	k="-61" />
<hkern g1="Z" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="Z" 	g2="underscore" 	k="-61" />
<hkern g1="Z" 	g2="v" 	k="41" />
<hkern g1="Z" 	g2="zero,six" 	k="51" />
<hkern g1="Z" 	g2="ampersand" 	k="41" />
<hkern g1="Z" 	g2="five" 	k="10" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="102" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="143" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="one" 	k="20" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="dollar,S" 	k="51" />
<hkern g1="z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="z" 	g2="backslash" 	k="143" />
<hkern g1="z" 	g2="four" 	k="41" />
<hkern g1="z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="31" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="z" 	g2="one" 	k="41" />
<hkern g1="z" 	g2="underscore" 	k="-61" />
<hkern g1="z" 	g2="v" 	k="20" />
<hkern g1="z" 	g2="x" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="20" />
<hkern g1="z" 	g2="two" 	k="20" />
<hkern g1="ampersand" 	g2="AE" 	k="-41" />
<hkern g1="ampersand" 	g2="J" 	k="-41" />
<hkern g1="ampersand" 	g2="T" 	k="61" />
<hkern g1="ampersand" 	g2="V" 	k="41" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="ampersand" 	g2="Z" 	k="-41" />
<hkern g1="ampersand" 	g2="ae" 	k="-20" />
<hkern g1="ampersand" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="ampersand" 	g2="uniFB02" 	k="-20" />
<hkern g1="ampersand" 	g2="v" 	k="20" />
<hkern g1="currency" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="degree" 	g2="AE" 	k="184" />
<hkern g1="degree" 	g2="J" 	k="20" />
<hkern g1="degree" 	g2="T" 	k="-61" />
<hkern g1="degree" 	g2="V" 	k="-61" />
<hkern g1="degree" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="degree" 	g2="uniFB02" 	k="-41" />
<hkern g1="degree" 	g2="v" 	k="-82" />
<hkern g1="degree" 	g2="x" 	k="-20" />
<hkern g1="degree" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="degree" 	g2="dollar,S" 	k="-41" />
<hkern g1="degree" 	g2="X" 	k="41" />
<hkern g1="degree" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="percent" 	g2="ae" 	k="-41" />
<hkern g1="percent" 	g2="asterisk,ordfeminine,ordmasculine" 	k="102" />
<hkern g1="percent" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-41" />
<hkern g1="section" 	g2="ae" 	k="-20" />
<hkern g1="section" 	g2="uniFB02" 	k="-20" />
<hkern g1="section" 	g2="v" 	k="-20" />
<hkern g1="section" 	g2="x" 	k="-20" />
<hkern g1="section" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="section" 	g2="j" 	k="-20" />
<hkern g1="section" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="-20" />
<hkern g1="section" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-20" />
<hkern g1="section" 	g2="s" 	k="-20" />
<hkern g1="section" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="-20" />
<hkern g1="section" 	g2="z" 	k="-20" />
<hkern g1="trademark" 	g2="AE" 	k="41" />
<hkern g1="trademark" 	g2="J" 	k="20" />
<hkern g1="trademark" 	g2="ae" 	k="-61" />
<hkern g1="trademark" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="trademark" 	g2="uniFB02" 	k="-61" />
<hkern g1="trademark" 	g2="v" 	k="-41" />
<hkern g1="trademark" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-61" />
<hkern g1="trademark" 	g2="s" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="AE" 	k="184" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="dollar,S" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="T" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="V" 	k="-72" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="X" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Z" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="ampersand" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="currency,Euro" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="degree" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="exclam" 	k="-10" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="four" 	k="31" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="nine" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="one" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="paragraph" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="percent" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="question" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="questiondown" 	k="184" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="seven" 	k="-102" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="slash" 	k="297" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="uniFB02" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="three" 	k="-10" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="trademark" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="two" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="underscore" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="v" 	k="-102" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="x" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="z" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="zero,six" 	k="-20" />
<hkern g1="backslash" 	g2="AE" 	k="-61" />
<hkern g1="backslash" 	g2="J" 	k="-102" />
<hkern g1="backslash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="backslash" 	g2="T" 	k="41" />
<hkern g1="backslash" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="backslash" 	g2="V" 	k="82" />
<hkern g1="backslash" 	g2="X" 	k="-41" />
<hkern g1="backslash" 	g2="asterisk,ordfeminine,ordmasculine" 	k="297" />
<hkern g1="backslash" 	g2="j" 	k="-246" />
<hkern g1="backslash" 	g2="v" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="V" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-205" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="bracketleft,braceleft" 	g2="ae" 	k="31" />
<hkern g1="bracketright" 	g2="J" 	k="41" />
<hkern g1="bracketright" 	g2="T" 	k="51" />
<hkern g1="bracketright" 	g2="X" 	k="61" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="T" 	k="20" />
<hkern g1="colon,semicolon" 	g2="V" 	k="41" />
<hkern g1="colon,semicolon" 	g2="X" 	k="10" />
<hkern g1="colon,semicolon" 	g2="v" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="x" 	k="20" />
<hkern g1="exclam" 	g2="J" 	k="41" />
<hkern g1="exclam" 	g2="T" 	k="-41" />
<hkern g1="exclam" 	g2="V" 	k="-20" />
<hkern g1="exclam" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-10" />
<hkern g1="exclamdown" 	g2="AE" 	k="-20" />
<hkern g1="exclamdown" 	g2="J" 	k="-41" />
<hkern g1="exclamdown" 	g2="T" 	k="41" />
<hkern g1="exclamdown" 	g2="V" 	k="61" />
<hkern g1="exclamdown" 	g2="X" 	k="20" />
<hkern g1="exclamdown" 	g2="j" 	k="-123" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="AE" 	k="-41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="AE" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="J" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="T" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="V" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="X" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="v" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="x" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="dollar,S" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="uniFB02" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="z" 	k="31" />
<hkern g1="parenleft" 	g2="J" 	k="31" />
<hkern g1="parenleft" 	g2="j" 	k="-143" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="AE" 	k="-72" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="J" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="72" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="72" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="ae" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="dollar,S" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uniFB02" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="41" />
<hkern g1="question" 	g2="AE" 	k="82" />
<hkern g1="question" 	g2="J" 	k="92" />
<hkern g1="question" 	g2="V" 	k="20" />
<hkern g1="question" 	g2="X" 	k="31" />
<hkern g1="question" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="question" 	g2="v" 	k="-20" />
<hkern g1="question" 	g2="Z" 	k="20" />
<hkern g1="question" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="question" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="questiondown" 	g2="J" 	k="-123" />
<hkern g1="questiondown" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="102" />
<hkern g1="questiondown" 	g2="T" 	k="113" />
<hkern g1="questiondown" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="72" />
<hkern g1="questiondown" 	g2="V" 	k="133" />
<hkern g1="questiondown" 	g2="asterisk,ordfeminine,ordmasculine" 	k="164" />
<hkern g1="questiondown" 	g2="j" 	k="-143" />
<hkern g1="questiondown" 	g2="v" 	k="82" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="questiondown" 	g2="ae" 	k="41" />
<hkern g1="questiondown" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="questiondown" 	g2="uniFB02" 	k="41" />
<hkern g1="questiondown" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="102" />
<hkern g1="questiondown" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="questiondown" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="41" />
<hkern g1="questiondown" 	g2="s" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-72" />
<hkern g1="quoteleft,quotedblleft" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="uniFB02" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="v" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="ae" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="92" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="slash" 	g2="AE" 	k="82" />
<hkern g1="slash" 	g2="J" 	k="20" />
<hkern g1="slash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="T" 	k="-61" />
<hkern g1="slash" 	g2="V" 	k="-61" />
<hkern g1="slash" 	g2="v" 	k="61" />
<hkern g1="slash" 	g2="ae" 	k="143" />
<hkern g1="slash" 	g2="x" 	k="143" />
<hkern g1="slash" 	g2="dollar,S" 	k="41" />
<hkern g1="slash" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="143" />
<hkern g1="slash" 	g2="uniFB02" 	k="61" />
<hkern g1="slash" 	g2="z" 	k="143" />
<hkern g1="slash" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="143" />
<hkern g1="slash" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="slash" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="slash" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="143" />
<hkern g1="slash" 	g2="s" 	k="143" />
<hkern g1="underscore" 	g2="AE" 	k="-123" />
<hkern g1="underscore" 	g2="J" 	k="-184" />
<hkern g1="underscore" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="underscore" 	g2="T" 	k="20" />
<hkern g1="underscore" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="underscore" 	g2="V" 	k="20" />
<hkern g1="underscore" 	g2="X" 	k="-102" />
<hkern g1="underscore" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="underscore" 	g2="j" 	k="-205" />
<hkern g1="underscore" 	g2="v" 	k="20" />
<hkern g1="underscore" 	g2="ae" 	k="20" />
<hkern g1="underscore" 	g2="x" 	k="-82" />
<hkern g1="underscore" 	g2="Z" 	k="-41" />
<hkern g1="underscore" 	g2="dollar,S" 	k="-41" />
<hkern g1="underscore" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="underscore" 	g2="uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="z" 	k="-82" />
<hkern g1="underscore" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="61" />
<hkern g1="underscore" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="underscore" 	g2="s" 	k="-20" />
<hkern g1="underscore" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="-61" />
<hkern g1="c,cent,ccedilla" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="102" />
<hkern g1="c,cent,ccedilla" 	g2="bracketright,braceright" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="bracketleft" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="c,cent,ccedilla" 	g2="degree" 	k="-31" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="four" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="nine" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="51" />
<hkern g1="c,cent,ccedilla" 	g2="parenright" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="questiondown" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="c,cent,ccedilla" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="section" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="seven" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="uniFB02" 	k="-10" />
<hkern g1="c,cent,ccedilla" 	g2="three" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="two" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="v" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="x" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="z" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="zero,six" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="143" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="degree" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="nine" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="one" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="102" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="questiondown" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="seven" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="three" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="two" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="72" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="ae" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="paragraph" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="percent" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="underscore" 	k="10" />
<hkern g1="eth" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="eth" 	g2="backslash" 	k="20" />
<hkern g1="eth" 	g2="degree" 	k="-41" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="eth" 	g2="question" 	k="20" />
<hkern g1="eth" 	g2="questiondown" 	k="20" />
<hkern g1="eth" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="eth" 	g2="three" 	k="20" />
<hkern g1="eth" 	g2="trademark" 	k="-61" />
<hkern g1="eth" 	g2="zero,six" 	k="-20" />
<hkern g1="eth" 	g2="underscore" 	k="41" />
<hkern g1="f" 	g2="ampersand" 	k="20" />
<hkern g1="f" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-123" />
<hkern g1="f" 	g2="backslash" 	k="-133" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-102" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-61" />
<hkern g1="f" 	g2="degree" 	k="-82" />
<hkern g1="f" 	g2="eight" 	k="-41" />
<hkern g1="f" 	g2="four" 	k="61" />
<hkern g1="f" 	g2="nine" 	k="-72" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="f" 	g2="one" 	k="-123" />
<hkern g1="f" 	g2="parenright" 	k="-41" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="f" 	g2="question" 	k="-61" />
<hkern g1="f" 	g2="questiondown" 	k="20" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-143" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-184" />
<hkern g1="f" 	g2="seven" 	k="-102" />
<hkern g1="f" 	g2="three" 	k="-61" />
<hkern g1="f" 	g2="trademark" 	k="-184" />
<hkern g1="f" 	g2="two" 	k="-41" />
<hkern g1="f" 	g2="v" 	k="-20" />
<hkern g1="f" 	g2="zero,six" 	k="-41" />
<hkern g1="f" 	g2="ae" 	k="20" />
<hkern g1="f" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="f" 	g2="paragraph" 	k="-102" />
<hkern g1="f" 	g2="percent" 	k="-41" />
<hkern g1="f" 	g2="underscore" 	k="20" />
<hkern g1="f" 	g2="five" 	k="-41" />
<hkern g1="f" 	g2="j" 	k="-82" />
<hkern g1="f" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="f" 	g2="slash" 	k="61" />
<hkern g1="k" 	g2="backslash" 	k="82" />
<hkern g1="k" 	g2="bracketleft" 	k="31" />
<hkern g1="k" 	g2="eight" 	k="10" />
<hkern g1="k" 	g2="four" 	k="61" />
<hkern g1="k" 	g2="nine" 	k="-20" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="k" 	g2="seven" 	k="20" />
<hkern g1="k" 	g2="three" 	k="-20" />
<hkern g1="k" 	g2="two" 	k="-20" />
<hkern g1="k" 	g2="v" 	k="51" />
<hkern g1="k" 	g2="x" 	k="41" />
<hkern g1="k" 	g2="z" 	k="20" />
<hkern g1="k" 	g2="zero,six" 	k="20" />
<hkern g1="k" 	g2="V" 	k="31" />
<hkern g1="k" 	g2="X" 	k="20" />
<hkern g1="k" 	g2="ae" 	k="20" />
<hkern g1="k" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="k" 	g2="underscore" 	k="-143" />
<hkern g1="k" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="k" 	g2="s" 	k="31" />
<hkern g1="k" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="d,uniFB02" 	g2="uniFB02" 	k="41" />
<hkern g1="l" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="l" 	g2="backslash" 	k="82" />
<hkern g1="l" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="l" 	g2="degree" 	k="41" />
<hkern g1="l" 	g2="eight" 	k="-20" />
<hkern g1="l" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="l" 	g2="one" 	k="41" />
<hkern g1="l" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="l" 	g2="question" 	k="41" />
<hkern g1="l" 	g2="questiondown" 	k="-20" />
<hkern g1="l" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="l" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="l" 	g2="seven" 	k="31" />
<hkern g1="l" 	g2="uniFB02" 	k="10" />
<hkern g1="l" 	g2="three" 	k="-20" />
<hkern g1="l" 	g2="two" 	k="-41" />
<hkern g1="l" 	g2="v" 	k="51" />
<hkern g1="l" 	g2="x" 	k="-20" />
<hkern g1="l" 	g2="zero,six" 	k="-20" />
<hkern g1="l" 	g2="underscore" 	k="-164" />
<hkern g1="l" 	g2="five" 	k="-10" />
<hkern g1="l" 	g2="slash" 	k="-82" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="143" />
<hkern g1="h,m,n,ntilde" 	g2="degree" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="nine" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="one" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="102" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="seven" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="two" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="paragraph" 	k="61" />
<hkern g1="h,m,n,ntilde" 	g2="percent" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="143" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="degree" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="nine" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="one" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="102" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="questiondown" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="seven" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="three" 	k="51" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="trademark" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="two" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V" 	k="82" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="paragraph" 	k="51" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="percent" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="underscore" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="J" 	k="72" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="113" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="r" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="r" 	g2="ampersand" 	k="82" />
<hkern g1="r" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="r" 	g2="backslash" 	k="41" />
<hkern g1="r" 	g2="bracketright,braceright" 	k="41" />
<hkern g1="r" 	g2="bracketleft" 	k="51" />
<hkern g1="r" 	g2="degree" 	k="-51" />
<hkern g1="r" 	g2="four" 	k="72" />
<hkern g1="r" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="20" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="r" 	g2="one" 	k="41" />
<hkern g1="r" 	g2="parenright" 	k="31" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="r" 	g2="questiondown" 	k="82" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="r" 	g2="section" 	k="-20" />
<hkern g1="r" 	g2="seven" 	k="-41" />
<hkern g1="r" 	g2="uniFB02" 	k="-31" />
<hkern g1="r" 	g2="three" 	k="20" />
<hkern g1="r" 	g2="v" 	k="-31" />
<hkern g1="r" 	g2="x" 	k="20" />
<hkern g1="r" 	g2="V" 	k="61" />
<hkern g1="r" 	g2="X" 	k="92" />
<hkern g1="r" 	g2="ae" 	k="20" />
<hkern g1="r" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="31" />
<hkern g1="r" 	g2="paragraph" 	k="-61" />
<hkern g1="r" 	g2="underscore" 	k="20" />
<hkern g1="r" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="r" 	g2="slash" 	k="82" />
<hkern g1="r" 	g2="s" 	k="10" />
<hkern g1="r" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="61" />
<hkern g1="r" 	g2="AE" 	k="70" />
<hkern g1="r" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="41" />
<hkern g1="r" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="r" 	g2="dollar,S" 	k="51" />
<hkern g1="r" 	g2="parenleft" 	k="41" />
<hkern g1="s" 	g2="backslash" 	k="123" />
<hkern g1="s" 	g2="degree" 	k="31" />
<hkern g1="s" 	g2="nine" 	k="10" />
<hkern g1="s" 	g2="one" 	k="20" />
<hkern g1="s" 	g2="question" 	k="41" />
<hkern g1="s" 	g2="seven" 	k="61" />
<hkern g1="s" 	g2="three" 	k="20" />
<hkern g1="s" 	g2="trademark" 	k="41" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="s" 	g2="v" 	k="20" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="paragraph" 	k="51" />
<hkern g1="t" 	g2="ampersand" 	k="20" />
<hkern g1="t" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="t" 	g2="backslash" 	k="143" />
<hkern g1="t" 	g2="degree" 	k="10" />
<hkern g1="t" 	g2="eight" 	k="10" />
<hkern g1="t" 	g2="four" 	k="82" />
<hkern g1="t" 	g2="nine" 	k="41" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="t" 	g2="one" 	k="51" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="question" 	k="41" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="t" 	g2="section" 	k="31" />
<hkern g1="t" 	g2="seven" 	k="61" />
<hkern g1="t" 	g2="uniFB02" 	k="31" />
<hkern g1="t" 	g2="trademark" 	k="61" />
<hkern g1="t" 	g2="two" 	k="20" />
<hkern g1="t" 	g2="v" 	k="20" />
<hkern g1="t" 	g2="zero,six" 	k="41" />
<hkern g1="t" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="t" 	g2="paragraph" 	k="41" />
<hkern g1="t" 	g2="underscore" 	k="-82" />
<hkern g1="t" 	g2="five" 	k="20" />
<hkern g1="v" 	g2="ampersand" 	k="41" />
<hkern g1="v" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-102" />
<hkern g1="v" 	g2="backslash" 	k="41" />
<hkern g1="v" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="v" 	g2="degree" 	k="-82" />
<hkern g1="v" 	g2="eight" 	k="-20" />
<hkern g1="v" 	g2="four" 	k="41" />
<hkern g1="v" 	g2="nine" 	k="-61" />
<hkern g1="v" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="72" />
<hkern g1="v" 	g2="question" 	k="-61" />
<hkern g1="v" 	g2="questiondown" 	k="51" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-72" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="v" 	g2="seven" 	k="-31" />
<hkern g1="v" 	g2="uniFB02" 	k="-31" />
<hkern g1="v" 	g2="three" 	k="20" />
<hkern g1="v" 	g2="x" 	k="61" />
<hkern g1="v" 	g2="z" 	k="20" />
<hkern g1="v" 	g2="zero,six" 	k="-20" />
<hkern g1="v" 	g2="X" 	k="61" />
<hkern g1="v" 	g2="ae" 	k="31" />
<hkern g1="v" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="v" 	g2="paragraph" 	k="-61" />
<hkern g1="v" 	g2="underscore" 	k="20" />
<hkern g1="v" 	g2="five" 	k="-20" />
<hkern g1="v" 	g2="j" 	k="31" />
<hkern g1="v" 	g2="slash" 	k="51" />
<hkern g1="v" 	g2="s" 	k="10" />
<hkern g1="v" 	g2="AE" 	k="51" />
<hkern g1="v" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="v" 	g2="dollar,S" 	k="20" />
<hkern g1="w" 	g2="backslash" 	k="102" />
<hkern g1="w" 	g2="one" 	k="20" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="w" 	g2="three" 	k="20" />
<hkern g1="w" 	g2="two" 	k="20" />
<hkern g1="w" 	g2="underscore" 	k="61" />
<hkern g1="x" 	g2="ampersand" 	k="41" />
<hkern g1="x" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="x" 	g2="backslash" 	k="102" />
<hkern g1="x" 	g2="colon,semicolon" 	k="20" />
<hkern g1="x" 	g2="degree" 	k="-20" />
<hkern g1="x" 	g2="four" 	k="61" />
<hkern g1="x" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="x" 	g2="seven" 	k="20" />
<hkern g1="x" 	g2="two" 	k="20" />
<hkern g1="x" 	g2="v" 	k="61" />
<hkern g1="x" 	g2="z" 	k="20" />
<hkern g1="x" 	g2="zero,six" 	k="20" />
<hkern g1="x" 	g2="V" 	k="41" />
<hkern g1="x" 	g2="ae" 	k="20" />
<hkern g1="x" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="x" 	g2="underscore" 	k="-82" />
<hkern g1="x" 	g2="j" 	k="41" />
<hkern g1="x" 	g2="slash" 	k="-20" />
<hkern g1="x" 	g2="s" 	k="20" />
<hkern g1="x" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="x" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="x" 	g2="dollar,S" 	k="31" />
</font>
</defs></svg> 