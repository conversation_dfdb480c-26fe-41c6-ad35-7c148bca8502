<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="montserratbold" horiz-adv-x="1226" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="579" />
<glyph unicode="&#xfb01;" horiz-adv-x="1572" d="M16 831v246h170v49q0 189 111.5 299.5t314.5 110.5q159 0 244 -59l-84 -232q-55 39 -129 39q-145 0 -145 -160v-47h282v-246h-274v-831h-320v831h-170zM1065 1434q0 76 55.5 127t143.5 51t143 -49t55 -123q0 -80 -55 -132.5t-143 -52.5t-143.5 51.5t-55.5 127.5zM1104 0 v1102h319v-1102h-319z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1409" d="M16 831v246h170v49q0 189 111.5 299.5t314.5 110.5q159 0 244 -59l-84 -232q-55 39 -129 39q-145 0 -145 -160v-47h282v-246h-274v-831h-320v831h-170zM940 0v1520h320v-1520h-320z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="579" />
<glyph unicode=" "  horiz-adv-x="579" />
<glyph unicode="&#x09;" horiz-adv-x="579" />
<glyph unicode="&#xa0;" horiz-adv-x="579" />
<glyph unicode="!" horiz-adv-x="591" d="M100 170q0 78 55.5 130t141.5 52t141.5 -52t55.5 -130t-56 -132t-141 -54t-141 54t-56 132zM106 1434h381l-63 -936h-254z" />
<glyph unicode="&#x22;" horiz-adv-x="894" d="M100 1434h273l-23 -582h-229zM522 1434h273l-23 -582h-229z" />
<glyph unicode="#" horiz-adv-x="1474" d="M41 328v223h295l43 332h-268v223h295l40 328h230l-41 -328h301l41 328h229l-41 -328h267l2 -223h-295l-41 -332h266v-223h-293l-41 -328h-231l41 328h-301l-41 -328h-230l41 328h-268zM565 551h301l43 332h-301z" />
<glyph unicode="$" horiz-adv-x="1306" d="M57 141l113 250q77 -57 182 -96t215 -49v342q-58 14 -98.5 25.5t-95 31.5t-92 41t-77 53.5t-63.5 70t-39.5 89.5t-15.5 113q0 171 123.5 292t357.5 148v227h205v-223q250 -20 416 -125l-103 -252q-156 89 -313 107v-348q57 -13 98.5 -24.5t94.5 -31t90.5 -40.5t76.5 -53 t63 -69t39 -88.5t15 -111.5q0 -168 -122.5 -288t-354.5 -148v-230h-205v223q-151 9 -288 53t-222 111zM416 1028q0 -51 38.5 -83t112.5 -56v291q-151 -35 -151 -152zM772 254q74 17 111 53.5t37 87.5t-38 84t-110 58v-283z" />
<glyph unicode="%" horiz-adv-x="1796" d="M55 1055q0 180 99 287.5t258 107.5q158 0 256 -107t98 -288t-98 -288.5t-256 -107.5t-257.5 108t-99.5 288zM262 1055q0 -111 41 -169.5t109 -58.5q70 0 109.5 57.5t39.5 170.5q0 112 -39.5 169.5t-109.5 57.5q-68 0 -109 -58t-41 -169zM287 0l979 1434h243l-979 -1434 h-243zM1030 379q0 180 98.5 287.5t255.5 107.5q159 0 258 -107.5t99 -287.5t-99 -287.5t-258 -107.5q-157 0 -255.5 107.5t-98.5 287.5zM1235 379q0 -112 40.5 -169.5t108.5 -57.5q69 0 109.5 58t40.5 169t-40.5 169t-109.5 58q-68 0 -108.5 -57.5t-40.5 -169.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1490" d="M74 369q0 134 74.5 234.5t240.5 193.5q-77 81 -112 153.5t-35 149.5q0 157 122 254.5t324 97.5q187 0 299.5 -89.5t112.5 -240.5q0 -110 -67 -196t-214 -166l262 -244q48 98 72 223l256 -80q-42 -190 -131 -325l168 -156l-174 -203l-172 160q-205 -160 -479 -160 q-237 0 -392 111.5t-155 282.5zM387 403q0 -79 69.5 -128.5t186.5 -49.5q149 0 268 84l-346 324q-97 -56 -137.5 -109.5t-40.5 -120.5zM532 1104q0 -42 23 -80t88 -102q104 56 145 99.5t41 94.5q0 54 -37 86.5t-104 32.5q-72 0 -114 -36.5t-42 -94.5z" />
<glyph unicode="'" horiz-adv-x="471" d="M100 1434h273l-23 -582h-229z" />
<glyph unicode="(" horiz-adv-x="731" d="M150 561q0 282 66 528.5t190 430.5h305q-248 -438 -248 -959q0 -262 59.5 -496.5t188.5 -461.5h-305q-124 187 -190 432t-66 526z" />
<glyph unicode=")" horiz-adv-x="733" d="M20 -397q130 227 190 461t60 497q0 523 -250 959h306q126 -183 192 -429.5t66 -529.5t-66 -527.5t-192 -430.5h-306z" />
<glyph unicode="*" horiz-adv-x="888" d="M27 993l223 121l-223 123l98 168l225 -135l-2 250h193l-4 -250l225 135l100 -168l-223 -123l223 -121l-100 -168l-225 133l4 -247h-193l2 250l-225 -136z" />
<glyph unicode="+" d="M127 592v250h354v348h264v-348h357v-250h-357v-348h-264v348h-354z" />
<glyph unicode="," horiz-adv-x="536" d="M72 184q0 88 56 142.5t142 54.5q87 0 142 -55t55 -142q0 -42 -10.5 -82t-44.5 -122l-117 -295h-203l90 325q-52 20 -81 66t-29 108z" />
<glyph unicode="-" horiz-adv-x="790" d="M106 449v256h580v-256h-580z" />
<glyph unicode="." horiz-adv-x="536" d="M70 184q0 88 56.5 142.5t141.5 54.5t142 -54.5t57 -142.5q0 -87 -57.5 -143.5t-141.5 -56.5t-141 56.5t-57 143.5z" />
<glyph unicode="/" horiz-adv-x="802" d="M-68 -205l676 1929h285l-676 -1929h-285z" />
<glyph unicode="0" horiz-adv-x="1390" d="M80 717q0 174 46.5 315t128.5 233.5t194.5 142.5t244.5 50q133 0 245.5 -50t194.5 -142.5t128.5 -233.5t46.5 -315t-46.5 -315t-128.5 -234t-194.5 -143t-245.5 -50q-132 0 -244.5 50t-194.5 143t-128.5 234t-46.5 315zM416 717q0 -235 74.5 -348t203.5 -113 q131 0 206 113t75 348t-75 348t-206 113q-129 0 -203.5 -113t-74.5 -348z" />
<glyph unicode="1" horiz-adv-x="802" d="M14 1167v267h619v-1434h-332v1167h-287z" />
<glyph unicode="2" horiz-adv-x="1208" d="M2 1192q85 124 232.5 195t336.5 71q241 0 387 -115t146 -309q0 -116 -49.5 -219t-190.5 -233l-329 -312h616v-270h-1083v215l553 522q88 84 118.5 144t30.5 120q0 86 -58 132.5t-171 46.5q-192 0 -297 -144z" />
<glyph unicode="3" horiz-adv-x="1212" d="M-2 129l129 254q82 -61 189.5 -95t220.5 -34q125 0 196.5 48.5t71.5 135.5q0 176 -268 176h-152v220l295 333h-610v267h1003v-215l-323 -369q189 -30 289 -140t100 -272q0 -93 -36.5 -175t-107 -147t-186.5 -103t-264 -38q-152 0 -298 40.5t-249 113.5z" />
<glyph unicode="4" horiz-adv-x="1411" d="M63 301v223l676 910h348l-626 -863h385v269h313v-269h234v-270h-234v-301h-323v301h-773z" />
<glyph unicode="5" horiz-adv-x="1218" d="M20 129l132 254q82 -62 188.5 -95.5t218.5 -33.5q125 0 196.5 49.5t71.5 136.5q0 92 -74.5 139.5t-258.5 47.5h-367l74 807h874v-267h-598l-22 -272h116q155 0 270.5 -33t185 -94t103 -140t33.5 -177q0 -77 -22 -147t-70 -130.5t-117 -104.5t-168 -69t-219 -25 q-151 0 -297.5 40.5t-249.5 113.5z" />
<glyph unicode="6" horiz-adv-x="1304" d="M80 686q0 183 53.5 329.5t150 243t231 148t296.5 51.5q243 0 399 -98l-123 -244q-109 72 -268 72q-183 0 -289.5 -106.5t-113.5 -309.5q127 127 354 127q211 0 352.5 -123.5t141.5 -322.5q0 -143 -72.5 -253t-195.5 -167.5t-275 -57.5q-300 0 -470.5 184t-170.5 527z M451 436q0 -95 67 -154t184 -59q107 0 173.5 58t66.5 155q0 98 -66.5 156.5t-177.5 58.5q-109 0 -178 -60.5t-69 -154.5z" />
<glyph unicode="7" horiz-adv-x="1269" d="M53 930v504h1149v-215l-532 -1219h-361l514 1163h-475v-233h-295z" />
<glyph unicode="8" horiz-adv-x="1351" d="M72 416q0 227 217 338q-166 103 -166 295q0 187 152 298t399 111q250 0 402.5 -111t152.5 -298q0 -190 -168 -295q219 -110 219 -338q0 -202 -165.5 -321.5t-440.5 -119.5q-273 0 -437.5 119.5t-164.5 321.5zM408 426q0 -95 71.5 -151t194.5 -56q124 0 197 56t73 151 t-72.5 150t-197.5 55q-123 0 -194.5 -55t-71.5 -150zM453 1034q0 -81 59.5 -128.5t161.5 -47.5q103 0 164 47.5t61 128.5q0 84 -61.5 132t-163.5 48q-101 0 -161 -48t-60 -132z" />
<glyph unicode="9" horiz-adv-x="1304" d="M41 981q0 143 72.5 252.5t195.5 167t275 57.5q302 0 471.5 -183.5t169.5 -526.5q0 -365 -199.5 -569t-531.5 -204q-114 0 -218.5 26t-179.5 73l123 243q104 -71 268 -71q182 0 289.5 107.5t114.5 310.5q-129 -129 -356 -129q-211 0 -352.5 123.5t-141.5 322.5zM365 997 q0 -98 66.5 -156.5t176.5 -58.5q108 0 177 60.5t69 154.5t-67 153.5t-183 59.5q-107 0 -173 -58t-66 -155z" />
<glyph unicode=":" horiz-adv-x="536" d="M70 184q0 88 56.5 142.5t141.5 54.5t142 -54.5t57 -142.5q0 -87 -57.5 -143.5t-141.5 -56.5t-141 56.5t-57 143.5zM70 920q0 88 56.5 142t141.5 54t142 -54t57 -142q0 -87 -57.5 -144t-141.5 -57t-141 57t-57 144z" />
<glyph unicode=";" horiz-adv-x="536" d="M70 920q0 88 56.5 142t141.5 54t142 -54t57 -142q0 -87 -57.5 -144t-141.5 -57t-141 57t-57 144zM72 184q0 88 56 142.5t142 54.5q87 0 142 -55t55 -142q0 -42 -10.5 -82t-44.5 -122l-117 -295h-203l90 325q-52 20 -81 66t-29 108z" />
<glyph unicode="&#x3c;" d="M127 584v266l975 371v-254l-682 -250l682 -248v-256z" />
<glyph unicode="=" d="M127 338v250h975v-250h-975zM127 846v250h975v-250h-975z" />
<glyph unicode="&#x3e;" d="M127 213v256l682 248l-682 250v254l975 -371v-266z" />
<glyph unicode="?" horiz-adv-x="1206" d="M-2 1184q86 130 232.5 202t347.5 72q235 0 377.5 -100.5t142.5 -278.5q0 -77 -25.5 -142t-63.5 -106.5t-83 -83.5t-83 -75t-63.5 -78.5t-25.5 -95.5h-310q0 58 17.5 109.5t46 90t62.5 72.5t68 64t62.5 59t46 63t17.5 70q0 75 -60.5 119.5t-160.5 44.5q-190 0 -297 -152z M403 170q0 78 55.5 130t141.5 52t141.5 -52t55.5 -130t-56 -132t-141 -54t-141 54t-56 132z" />
<glyph unicode="@" horiz-adv-x="2119" d="M80 520q0 199 71.5 371.5t199.5 296.5t314 195t406 71q212 0 393.5 -67.5t307.5 -186t197 -283.5t71 -356q0 -271 -114 -423t-306 -152q-105 0 -177 45.5t-104 132.5q-111 -178 -350 -178q-201 0 -337 145t-136 373q0 227 135.5 371.5t337.5 144.5t316 -135v121h278v-664 q0 -131 98 -131q73 0 112.5 86t39.5 260q0 206 -95 366t-268.5 248.5t-398.5 88.5q-169 0 -311.5 -55.5t-241 -153.5t-154 -235t-55.5 -296q0 -162 54.5 -300t151.5 -236t238.5 -153.5t309.5 -55.5q219 0 403 90l66 -189q-92 -46 -218.5 -72t-250.5 -26q-218 0 -402.5 71.5 t-311.5 196.5t-198 299t-71 375zM799 504q0 -130 70 -206.5t184 -76.5q113 0 184.5 75.5t71.5 207.5q0 130 -71.5 204t-184.5 74q-114 0 -184 -74.5t-70 -203.5z" />
<glyph unicode="A" horiz-adv-x="1568" d="M-18 0l639 1434h327l641 -1434h-348l-127 307h-665l-127 -307h-340zM555 559h455l-228 549z" />
<glyph unicode="B" horiz-adv-x="1566" d="M170 0v1434h700q263 0 403 -100.5t140 -272.5q0 -106 -50.5 -187.5t-139.5 -128.5q123 -39 192.5 -130.5t69.5 -223.5q0 -188 -147 -289.5t-427 -101.5h-741zM500 250h387q264 0 264 176q0 178 -264 178h-387v-354zM500 846h329q122 0 186 43t64 127q0 168 -250 168h-329 v-338z" />
<glyph unicode="C" horiz-adv-x="1501" d="M80 717q0 213 101 382.5t278.5 264t398.5 94.5q186 0 337.5 -65.5t252.5 -188.5l-213 -196q-145 168 -361 168q-201 0 -329.5 -128.5t-128.5 -330.5t128.5 -330.5t329.5 -128.5q217 0 361 170l213 -197q-101 -125 -253 -190.5t-339 -65.5q-164 0 -308 55.5t-247 153 t-162 236t-59 297.5z" />
<glyph unicode="D" horiz-adv-x="1691" d="M170 0v1434h651q233 0 412.5 -88t279 -251.5t99.5 -377.5t-99.5 -377.5t-279 -251.5t-412.5 -88h-651zM502 272h303q215 0 343 120t128 325t-128 324.5t-343 119.5h-303v-889z" />
<glyph unicode="E" horiz-adv-x="1374" d="M170 0v1434h1083v-267h-753v-311h665v-258h-665v-332h780v-266h-1110z" />
<glyph unicode="F" horiz-adv-x="1308" d="M170 0v1434h1083v-267h-751v-379h663v-266h-663v-522h-332z" />
<glyph unicode="G" horiz-adv-x="1579" d="M80 717q0 160 59 298t163 235t250.5 152.5t315.5 55.5q189 0 342.5 -63.5t257.5 -184.5l-213 -196q-152 162 -370 162q-209 0 -339 -127.5t-130 -331.5q0 -201 130 -330t335 -129q144 0 258 61v420h303v-581q-117 -88 -270.5 -135.5t-309.5 -47.5q-167 0 -312 55.5 t-248.5 153t-162.5 235.5t-59 298z" />
<glyph unicode="H" horiz-adv-x="1654" d="M170 0v1434h332v-566h651v566h332v-1434h-332v588h-651v-588h-332z" />
<glyph unicode="I" horiz-adv-x="671" d="M170 0v1434h332v-1434h-332z" />
<glyph unicode="J" horiz-adv-x="1107" d="M-33 184l185 222q118 -158 266 -158q203 0 203 237v682h-504v267h833v-930q0 -264 -130.5 -396.5t-385.5 -132.5q-146 0 -267.5 54t-199.5 155z" />
<glyph unicode="K" horiz-adv-x="1515" d="M170 0v1434h330v-670l635 670h368l-594 -639l629 -795h-387l-459 563l-192 -201v-362h-330z" />
<glyph unicode="L" horiz-adv-x="1236" d="M170 0v1434h332v-1164h719v-270h-1051z" />
<glyph unicode="M" horiz-adv-x="1955" d="M170 0v1434h274l537 -891l528 891h273l4 -1434h-311l-2 860l-422 -708h-150l-420 690v-842h-311z" />
<glyph unicode="N" horiz-adv-x="1654" d="M170 0v1434h274l713 -871v871h328v-1434h-273l-714 870v-870h-328z" />
<glyph unicode="O" horiz-adv-x="1728" d="M80 717q0 210 101 379.5t280.5 265.5t402.5 96t402.5 -95.5t281 -265t101.5 -380.5q0 -158 -59.5 -296t-163.5 -236t-249.5 -154t-312.5 -56q-223 0 -402.5 96t-280.5 266t-101 380zM416 717q0 -202 127 -330.5t321 -128.5t321.5 128.5t127.5 330.5t-127.5 330.5 t-321.5 128.5t-321 -128.5t-127 -330.5z" />
<glyph unicode="P" horiz-adv-x="1499" d="M170 0v1434h621q288 0 455 -140t167 -381q0 -239 -167.5 -378.5t-454.5 -139.5h-289v-395h-332zM502 666h270q150 0 227.5 64t77.5 183q0 120 -77.5 185t-227.5 65h-270v-497z" />
<glyph unicode="Q" horiz-adv-x="1728" d="M80 717q0 210 101 379.5t280.5 265.5t402.5 96t402.5 -95.5t281 -265t101.5 -380.5q0 -249 -140 -438t-372 -263q51 -54 99 -77t103 -23q133 0 234 107l147 -177q-144 -176 -387 -176q-160 0 -289 69t-294 243q-195 24 -347.5 124.5t-237.5 259.5t-85 351zM416 717 q0 -202 127 -330.5t321 -128.5t321.5 128.5t127.5 330.5t-127.5 330.5t-321.5 128.5t-321 -128.5t-127 -330.5z" />
<glyph unicode="R" horiz-adv-x="1505" d="M170 0v1434h621q288 0 455 -140t167 -381q0 -159 -77 -275t-218 -177l322 -461h-357l-276 399h-16h-289v-399h-332zM502 664h270q150 0 227.5 65t77.5 184q0 120 -77.5 185t-227.5 65h-270v-499z" />
<glyph unicode="S" horiz-adv-x="1306" d="M57 141l113 250q93 -67 219.5 -108t253.5 -41q141 0 209 41.5t68 111.5q0 43 -28.5 74t-77 51t-111 35.5t-131.5 32t-138 36t-131.5 51.5t-111 75t-77 110t-28.5 152q0 93 37.5 173t110.5 141.5t190 96.5t266 35q138 0 269 -32.5t229 -94.5l-103 -252q-198 113 -397 113 q-140 0 -206 -45.5t-66 -118.5q0 -42 28.5 -73t76.5 -50t110.5 -34.5t132 -31.5t138.5 -35.5t131.5 -51t110.5 -73.5t76.5 -108.5t28.5 -150.5q0 -92 -38 -171.5t-111.5 -141t-191.5 -97t-267 -35.5q-169 0 -328.5 46t-255.5 120z" />
<glyph unicode="T" horiz-adv-x="1265" d="M8 1163v271h1249v-271h-458v-1163h-332v1163h-459z" />
<glyph unicode="U" horiz-adv-x="1613" d="M158 631v803h331v-791q0 -385 320 -385q157 0 238 93t81 292v791h328v-803q0 -315 -170.5 -485.5t-478.5 -170.5t-478.5 170.5t-170.5 485.5z" />
<glyph unicode="V" horiz-adv-x="1527" d="M-18 1434h358l436 -1024l443 1024h329l-620 -1434h-328z" />
<glyph unicode="W" horiz-adv-x="2381" d="M45 1434h344l324 -1008l338 1008h307l328 -1016l333 1016h318l-469 -1434h-357l-315 971l-326 -971h-354z" />
<glyph unicode="X" horiz-adv-x="1462" d="M6 0l531 729l-504 705h377l329 -465l324 465h358l-499 -693l534 -741h-383l-346 498l-340 -498h-381z" />
<glyph unicode="Y" horiz-adv-x="1384" d="M-29 1434h353l383 -637l383 637h325l-557 -926v-508h-332v512z" />
<glyph unicode="Z" horiz-adv-x="1374" d="M78 0v215l786 948h-770v271h1206v-215l-784 -949h815v-270h-1253z" />
<glyph unicode="[" horiz-adv-x="753" d="M170 -397v1917h555v-250h-236v-1417h236v-250h-555z" />
<glyph unicode="\" horiz-adv-x="802" d="M-88 1724h285l675 -1929h-284z" />
<glyph unicode="]" horiz-adv-x="753" d="M31 -147h233v1417h-233v250h553v-1917h-553v250z" />
<glyph unicode="^" horiz-adv-x="1228" d="M117 289l370 856h256l369 -856h-246l-252 604l-252 -604h-245z" />
<glyph unicode="_" horiz-adv-x="1024" d="M0 0h1024v-184h-1024v184z" />
<glyph unicode="`" horiz-adv-x="1228" d="M180 1538h344l277 -295h-250z" />
<glyph unicode="a" horiz-adv-x="1263" d="M76 317q0 154 115.5 242t357.5 88h254q0 105 -63.5 161t-190.5 56q-88 0 -173 -27.5t-145 -74.5l-114 223q90 64 215.5 98.5t259.5 34.5q257 0 393.5 -121.5t136.5 -367.5v-629h-299v137q-90 -153 -336 -153q-189 0 -300 93.5t-111 239.5zM387 332q0 -61 48 -97t132 -36 q82 0 145.5 38t90.5 111v113h-219q-197 0 -197 -129z" />
<glyph unicode="b" horiz-adv-x="1413" d="M147 0v1520h320v-535q121 133 332 133q115 0 216 -40.5t174.5 -113t116 -179.5t42.5 -234t-42.5 -234t-116 -179.5t-174.5 -113t-216 -40.5q-227 0 -346 143v-127h-306zM463 551q0 -140 78.5 -222.5t201.5 -82.5t202 82.5t79 222.5t-79 222.5t-202 82.5t-201.5 -82.5 t-78.5 -222.5z" />
<glyph unicode="c" horiz-adv-x="1210" d="M66 551q0 164 78.5 293.5t218.5 201.5t317 72q174 0 304 -73t190 -207l-248 -133q-86 151 -248 151q-125 0 -207 -82t-82 -223t82 -223t207 -82q164 0 248 151l248 -135q-60 -132 -190 -205t-304 -73q-177 0 -317 72t-218.5 201.5t-78.5 293.5z" />
<glyph unicode="d" horiz-adv-x="1417" d="M70 551q0 169 73 299.5t198 199t280 68.5q211 0 329 -133v535h320v-1520h-305v127q-119 -143 -344 -143q-155 0 -280 68.5t-198 199t-73 299.5zM393 551q0 -139 80 -222t203 -83q121 0 200.5 83t79.5 222t-79.5 222t-200.5 83q-123 0 -203 -83t-80 -222z" />
<glyph unicode="e" horiz-adv-x="1292" d="M66 551q0 162 76.5 292t210.5 202.5t300 72.5q162 0 292 -68t206 -199t76 -304q0 -5 -6 -86h-834q23 -103 106.5 -162t208.5 -59q165 0 277 106l170 -184q-155 -178 -455 -178q-187 0 -330.5 73t-220.5 202t-77 292zM383 647h543q-17 105 -90.5 167.5t-180.5 62.5 q-109 0 -182 -62t-90 -168z" />
<glyph unicode="f" horiz-adv-x="792" d="M16 831v246h170v49q0 189 111.5 299.5t314.5 110.5q159 0 244 -59l-84 -232q-55 39 -129 39q-145 0 -145 -160v-47h282v-246h-274v-831h-320v831h-170z" />
<glyph unicode="g" horiz-adv-x="1433" d="M66 586q0 157 74 279.5t199 187.5t277 65q241 0 365 -157v141h303v-918q0 -598 -618 -598q-160 0 -303 39t-238 113l127 229q70 -57 176.5 -91t212.5 -34q167 0 245.5 75t78.5 226v47q-125 -137 -349 -137q-152 0 -277 65t-199 188t-74 280zM389 586q0 -121 82 -196 t209 -75t208 75t81 196t-81 195.5t-208 74.5t-209 -75t-82 -195z" />
<glyph unicode="h" horiz-adv-x="1415" d="M147 0v1520h320v-533q131 131 350 131q99 0 181 -29.5t144.5 -87.5t97 -152.5t34.5 -217.5v-631h-320v582q0 131 -57.5 195.5t-165.5 64.5q-121 0 -192.5 -75t-71.5 -222v-545h-320z" />
<glyph unicode="i" horiz-adv-x="616" d="M109 1434q0 76 55 127t143 51t143.5 -49t55.5 -123q0 -80 -55.5 -132.5t-143.5 -52.5t-143 51.5t-55 127.5zM147 0v1102h320v-1102h-320z" />
<glyph unicode="j" horiz-adv-x="628" d="M-190 -352l86 231q55 -39 135 -39q62 0 96.5 40t34.5 120v1102h319v-1100q0 -193 -109 -304.5t-306 -111.5q-168 0 -256 62zM123 1434q0 76 55.5 127t143.5 51t143 -49t55 -123q0 -80 -55 -132.5t-143 -52.5t-143.5 51.5t-55.5 127.5z" />
<glyph unicode="k" horiz-adv-x="1351" d="M147 0v1520h320v-861l467 443h381l-459 -467l500 -635h-387l-348 432l-154 -151v-281h-320z" />
<glyph unicode="l" horiz-adv-x="616" d="M147 0v1520h320v-1520h-320z" />
<glyph unicode="m" horiz-adv-x="2148" d="M147 0v1102h306v-127q125 143 346 143q118 0 211 -45.5t151 -134.5q66 85 170 132.5t228 47.5q206 0 328 -122t122 -365v-631h-319v582q0 130 -55 195t-154 65q-112 0 -178 -73t-66 -216v-553h-319v582q0 260 -209 260q-110 0 -176 -73t-66 -216v-553h-320z" />
<glyph unicode="n" horiz-adv-x="1415" d="M147 0v1102h306v-129q133 145 364 145q99 0 181 -29.5t144.5 -87.5t97 -152.5t34.5 -217.5v-631h-320v582q0 131 -57.5 195.5t-165.5 64.5q-121 0 -192.5 -75t-71.5 -222v-545h-320z" />
<glyph unicode="o" horiz-adv-x="1341" d="M66 551q0 248 171.5 407.5t434.5 159.5t433.5 -159.5t170.5 -407.5t-170.5 -407.5t-433.5 -159.5t-434.5 159.5t-171.5 407.5zM389 551q0 -139 80 -222t203 -83t201.5 82.5t78.5 222.5t-78.5 222.5t-201.5 82.5t-203 -83t-80 -222z" />
<glyph unicode="p" horiz-adv-x="1413" d="M147 -397v1499h306v-127q119 143 346 143q115 0 216 -40.5t174.5 -113t116 -179.5t42.5 -234t-42.5 -234t-116 -179.5t-174.5 -113t-216 -40.5q-211 0 -332 133v-514h-320zM463 551q0 -140 78.5 -222.5t201.5 -82.5t202 82.5t79 222.5t-79 222.5t-202 82.5t-201.5 -82.5 t-78.5 -222.5z" />
<glyph unicode="q" horiz-adv-x="1413" d="M66 551q0 127 42.5 234t116 179.5t174.5 113t217 40.5q226 0 345 -143v127h305v-1499h-320v514q-118 -133 -330 -133q-116 0 -217 40.5t-174.5 113t-116 179.5t-42.5 234zM389 551q0 -139 80 -222t203 -83q121 0 200.5 83t79.5 222t-79.5 222t-200.5 83q-123 0 -203 -83 t-80 -222z" />
<glyph unicode="r" horiz-adv-x="882" d="M147 0v1102h306v-146q119 162 378 162v-295q-54 4 -71 4q-137 0 -215 -77t-78 -230v-520h-320z" />
<glyph unicode="s" horiz-adv-x="1087" d="M41 102l106 230q75 -48 179 -77.5t204 -29.5q203 0 203 101q0 29 -22.5 48t-61 29.5t-88.5 18t-105.5 16t-110.5 20.5t-105 35.5t-88.5 56.5t-61 87.5t-22.5 124.5q0 160 136 258t374 98q116 0 231 -25.5t192 -70.5l-106 -227q-147 82 -317 82q-102 0 -153.5 -28.5 t-51.5 -74.5q0 -38 36.5 -60.5t95.5 -33.5t129.5 -21t141.5 -29.5t130 -51t95.5 -94t36.5 -150.5q0 -157 -138.5 -253.5t-381.5 -96.5q-137 0 -268 33t-209 85z" />
<glyph unicode="t" horiz-adv-x="890" d="M16 831v246h170v269h320v-269h274v-246h-274v-448q0 -70 36 -107.5t101 -37.5q76 0 129 41l86 -226q-97 -69 -260 -69q-197 0 -304.5 100.5t-107.5 294.5v452h-170z" />
<glyph unicode="u" horiz-adv-x="1406" d="M141 479v623h320v-576q0 -266 223 -266q115 0 184.5 75t69.5 222v545h319v-1102h-303v131q-63 -71 -151.5 -109t-190.5 -38q-216 0 -343.5 124.5t-127.5 370.5z" />
<glyph unicode="v" horiz-adv-x="1224" d="M-16 1102h329l305 -750l316 750h307l-465 -1102h-330z" />
<glyph unicode="w" horiz-adv-x="1918" d="M2 1102h301l260 -746l271 746h270l262 -746l268 746h283l-399 -1102h-308l-247 684l-256 -684h-308z" />
<glyph unicode="x" horiz-adv-x="1218" d="M8 0l424 555l-409 547h358l233 -322l240 322h340l-410 -539l426 -563h-362l-242 338l-250 -338h-348z" />
<glyph unicode="y" horiz-adv-x="1224" d="M-20 -313l116 227q80 -70 180 -70q66 0 107.5 32t73.5 106l4 10l-477 1110h329l310 -748l311 748h307l-498 -1170q-76 -190 -186.5 -268t-269.5 -78q-86 0 -170 27t-137 74z" />
<glyph unicode="z" horiz-adv-x="1112" d="M72 0v193l561 663h-547v246h950v-193l-561 -663h580v-246h-983z" />
<glyph unicode="{" horiz-adv-x="800" d="M106 436v250h68q90 0 90 96v371q0 178 98 272.5t289 94.5h119v-250h-37q-74 0 -111.5 -39.5t-37.5 -112.5v-327q0 -103 -33 -155.5t-105 -74.5q73 -22 105.5 -74t32.5 -155v-328q0 -73 37.5 -112t111.5 -39h37v-250h-119q-191 0 -289 94t-98 272v371q0 96 -90 96h-68z " />
<glyph unicode="|" horiz-adv-x="632" d="M170 -397v1917h293v-1917h-293z" />
<glyph unicode="}" horiz-adv-x="800" d="M31 -147h37q74 0 111.5 39t37.5 112v328q0 103 32.5 155t104.5 74q-72 22 -104.5 74.5t-32.5 155.5v327q0 73 -37.5 112.5t-111.5 39.5h-37v250h119q191 0 289 -94.5t98 -272.5v-371q0 -96 90 -96h67v-250h-67q-90 0 -90 -96v-371q0 -178 -98 -272t-289 -94h-119v250z " />
<glyph unicode="~" d="M102 532q3 189 87 290.5t221 101.5q54 0 104 -18.5t87 -45t70 -52.5t66 -44.5t62 -18.5q57 0 90.5 43t36.5 117h200q-4 -189 -87.5 -290t-219.5 -101q-53 0 -103 18.5t-87.5 44.5t-70.5 52t-66.5 44.5t-63.5 18.5q-55 0 -89.5 -43t-37.5 -117h-199z" />
<glyph unicode="&#xa1;" horiz-adv-x="591" d="M100 932q0 78 56 132t141 54t141 -54t56 -132q0 -79 -55.5 -131.5t-141.5 -52.5t-141.5 52.5t-55.5 131.5zM106 -315l64 919h254l63 -919h-381z" />
<glyph unicode="&#xa2;" horiz-adv-x="1210" d="M66 551q0 219 136 371t357 186v240h205v-234q145 -17 251.5 -88t158.5 -188l-248 -133q-59 106 -162 139v-586q105 33 162 139l248 -135q-52 -115 -159 -186t-251 -88v-234h-205v240q-221 34 -357 186t-136 371zM389 551q0 -103 46 -176.5t124 -106.5v566 q-78 -33 -124 -106.5t-46 -176.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1368" d="M61 0v266h205v352h-205v199h205v53q0 275 175.5 431.5t490.5 156.5q243 0 411 -94l-100 -262q-123 74 -307 74q-164 0 -250 -75.5t-86 -223.5v-60h492v-199h-492v-352h703v-266h-1242z" />
<glyph unicode="&#xa4;" horiz-adv-x="1433" d="M51 170l209 209q-82 124 -82 278q0 147 76 271l-203 202l178 187l207 -205q131 78 281 78q151 0 278 -78l207 205l178 -187l-200 -200q75 -128 75 -273q0 -150 -81 -280l206 -207l-178 -186l-217 215q-125 -72 -268 -72t-271 72l-217 -215zM430 657q0 -116 83.5 -197 t203.5 -81q119 0 204 81.5t85 196.5t-85 197t-204 82q-120 0 -203.5 -81.5t-83.5 -197.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1507" d="M-29 1434h355l438 -641l442 641h332l-543 -797h308v-170h-383v-113h383v-170h-383v-184h-332v184h-383v170h383v113h-383v170h309z" />
<glyph unicode="&#xa6;" horiz-adv-x="632" d="M170 319h293v-716h-293v716zM170 803v717h293v-717h-293z" />
<glyph unicode="&#xa7;" horiz-adv-x="1069" d="M43 -100l94 225q70 -51 170.5 -82t194.5 -31q88 0 137 32.5t49 88.5q0 43 -35 70.5t-90.5 43t-123 29t-134.5 35t-122.5 54t-90.5 94t-35 147.5q0 166 131 264q-88 78 -88 213q0 165 135 266t383 101q101 0 220.5 -26t189.5 -72l-92 -224q-143 89 -336 89 q-217 0 -217 -121q0 -35 27 -59.5t71 -38t100.5 -28t115 -27.5t115 -39.5t100.5 -61t71 -94.5t27 -138q0 -152 -131 -250q92 -79 92 -217q0 -161 -127 -262.5t-346 -101.5q-126 0 -253.5 34t-201.5 87zM334 629q0 -23 7.5 -41t26.5 -32.5t35 -24t50 -19.5t54 -15.5t64 -15 t64 -14.5q98 45 98 135q0 22 -7.5 40t-26.5 32.5t-34.5 24t-50.5 20t-53 15.5t-64 15.5t-63 14.5q-46 -21 -73 -56t-27 -79z" />
<glyph unicode="&#xa8;" horiz-adv-x="1228" d="M264 1395q0 62 41.5 101.5t102.5 39.5t101 -39.5t40 -101.5t-40 -102t-101 -40t-102.5 40t-41.5 102zM680 1395q0 62 40 101.5t101 39.5t102.5 -39.5t41.5 -101.5t-41.5 -102t-102.5 -40t-101 40t-40 102z" />
<glyph unicode="&#xa9;" horiz-adv-x="1607" d="M80 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM221 717q0 -164 74.5 -299t208 -213t297.5 -78t298.5 78.5 t210.5 214.5t76 301t-74 299t-207.5 210.5t-299.5 76.5t-300 -78t-209 -213t-75 -299zM391 717q0 181 122.5 297t313.5 116q122 0 214 -52t137 -142l-185 -109q-56 97 -168 97q-82 0 -138 -56.5t-56 -150.5t56 -150.5t138 -56.5q113 0 168 96l185 -106q-45 -92 -137 -144.5 t-214 -52.5q-191 0 -313.5 116.5t-122.5 297.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="843" d="M57 1059q0 93 74 144.5t227 51.5h160q-3 121 -158 121q-54 0 -109 -17t-93 -46l-76 139q58 39 142 61.5t167 22.5q348 0 348 -295v-373h-204v80q-57 -90 -216 -90q-126 0 -194 56t-68 145zM266 1067q0 -34 29.5 -53t81.5 -19q102 0 141 76v68h-127q-125 0 -125 -72z" />
<glyph unicode="&#xab;" horiz-adv-x="1163" d="M74 551l288 401h287l-280 -401l280 -399h-287zM537 551l288 401h287l-281 -401l281 -399h-287z" />
<glyph unicode="&#xac;" d="M127 594v248h975v-606h-264v358h-711z" />
<glyph unicode="&#xad;" horiz-adv-x="790" d="M106 449v256h580v-256h-580z" />
<glyph unicode="&#xae;" horiz-adv-x="1607" d="M80 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM221 717q0 -164 74.5 -299t208 -213t297.5 -78t298.5 78.5 t210.5 214.5t76 301t-74 299t-207.5 210.5t-299.5 76.5t-300 -78t-209 -213t-75 -299zM489 319v795h347q162 0 254 -76.5t92 -206.5q0 -175 -150 -245l174 -267h-213l-149 230h-8h-134v-230h-213zM700 709h121q72 0 112 32t40 90q0 60 -40 92.5t-112 32.5h-121v-247z" />
<glyph unicode="&#xaf;" horiz-adv-x="1228" d="M236 1300v181h757v-181h-757z" />
<glyph unicode="&#xb0;" horiz-adv-x="856" d="M68 1100q0 148 104.5 250t255.5 102q149 0 253.5 -102t104.5 -250q0 -147 -104 -248.5t-254 -101.5q-151 0 -255.5 101.5t-104.5 248.5zM238 1100q0 -83 54 -138t136 -55q81 0 135.5 55.5t54.5 137.5q0 83 -54 137.5t-136 54.5t-136 -54.5t-54 -137.5z" />
<glyph unicode="&#xb1;" d="M127 0v250h975v-250h-975zM127 762v242h354v335h264v-335h357v-242h-357v-336h-264v336h-354z" />
<glyph unicode="&#xb2;" horiz-adv-x="880" d="M37 1368q52 78 146.5 123t226.5 45q165 0 260.5 -71t95.5 -187q0 -66 -33.5 -124t-128.5 -140l-196 -172h391v-172h-715v137l354 307q58 50 79.5 83t21.5 65q0 42 -37.5 69t-110.5 27q-128 0 -190 -88z" />
<glyph unicode="&#xb3;" horiz-adv-x="880" d="M37 750l82 163q57 -38 135 -60t156 -22q87 0 131.5 27t44.5 74q0 96 -162 96h-121v139l186 183h-415v170h692v-138l-207 -202q123 -18 188.5 -84t65.5 -164q0 -116 -103 -197.5t-296 -81.5q-109 0 -212.5 27t-164.5 70z" />
<glyph unicode="&#xb4;" horiz-adv-x="1228" d="M428 1243l277 295h344l-371 -295h-250z" />
<glyph unicode="&#xb5;" horiz-adv-x="1413" d="M147 -397v1499h320v-576q0 -135 58 -200.5t165 -65.5q117 0 186.5 75t69.5 222v545h320v-1102h-289v135q-49 -78 -125.5 -114.5t-163.5 -36.5q-135 0 -221 71v-452h-320z" />
<glyph unicode="&#xb6;" horiz-adv-x="1404" d="M29 1128q0 179 134 285.5t357 106.5h715v-1725h-260v1493h-248v-1493h-260v942q-193 3 -315.5 109t-122.5 282z" />
<glyph unicode="&#xb7;" horiz-adv-x="618" d="M111 578q0 90 55.5 144t142.5 54q89 0 144 -54t55 -144t-55.5 -145.5t-143.5 -55.5q-87 0 -142.5 55.5t-55.5 145.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1228" d="M356 -432l50 137q67 -31 133 -31q106 0 106 66q0 59 -100 59h-70l53 217h170l-28 -118q89 -10 133.5 -56.5t44.5 -113.5q0 -93 -82 -148t-219 -55q-116 0 -191 43z" />
<glyph unicode="&#xb9;" horiz-adv-x="880" d="M150 670v170h210v510h-192v170h422v-680h184v-170h-624z" />
<glyph unicode="&#xba;" horiz-adv-x="874" d="M49 1198q0 147 110 242.5t279 95.5t278 -95t109 -243q0 -149 -109 -244.5t-278 -95.5t-279 95.5t-110 244.5zM266 1198q0 -77 48 -123.5t124 -46.5q75 0 122.5 46.5t47.5 123.5t-47.5 122.5t-122.5 45.5q-76 0 -124 -45.5t-48 -122.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1163" d="M51 152l281 399l-281 401h287l289 -401l-289 -399h-287zM514 152l281 399l-281 401h287l289 -401l-289 -399h-287z" />
<glyph unicode="&#xbc;" horiz-adv-x="2138" d="M150 584v170h210v510h-192v170h422v-680h184v-170h-624zM459 0l979 1434h243l-979 -1434h-243zM1282 172v143l387 535h244l-367 -504h219v143h197v-143h137v-174h-137v-172h-219v172h-461z" />
<glyph unicode="&#xbd;" horiz-adv-x="2138" d="M150 584v170h210v510h-192v170h422v-680h184v-170h-624zM459 0l979 1434h243l-979 -1434h-243zM1294 698q52 78 146.5 123t226.5 45q165 0 260.5 -71t95.5 -187q0 -66 -33 -124t-128 -140l-197 -172h391v-172h-715v137l355 307q58 49 79 82.5t21 65.5q0 42 -37 69 t-110 27q-129 0 -191 -88z" />
<glyph unicode="&#xbe;" horiz-adv-x="2138" d="M37 664l82 163q57 -38 135 -60t156 -22q87 0 131.5 27t44.5 74q0 96 -162 96h-121v139l186 183h-415v170h692v-138l-207 -202q123 -18 188.5 -84t65.5 -164q0 -116 -103 -197.5t-296 -81.5q-109 0 -212.5 27t-164.5 70zM459 0l979 1434h243l-979 -1434h-243zM1282 172 v143l387 535h244l-367 -504h219v143h197v-143h137v-174h-137v-172h-219v172h-461z" />
<glyph unicode="&#xbf;" horiz-adv-x="1206" d="M106 35q0 65 19.5 120.5t50 95.5t67 75t73.5 66t67 62t49 69t19 81h309q0 -78 -33 -144t-79.5 -111t-93 -86t-79.5 -87t-33 -94q0 -67 60.5 -109.5t159.5 -42.5q196 0 296 152l248 -145q-182 -275 -579 -275q-235 0 -378 99t-143 274zM410 932q0 78 54.5 132t139.5 54 q86 0 141.5 -54t55.5 -132q0 -79 -55 -131.5t-142 -52.5q-85 0 -139.5 52.5t-54.5 131.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1568" d="M-18 0l639 1434h327l641 -1434h-348l-127 307h-665l-127 -307h-340zM350 1845h344l277 -295h-250zM555 559h455l-228 549z" />
<glyph unicode="&#xc1;" horiz-adv-x="1568" d="M-18 0l639 1434h327l641 -1434h-348l-127 307h-665l-127 -307h-340zM555 559h455l-228 549zM598 1550l276 295h345l-371 -295h-250z" />
<glyph unicode="&#xc2;" horiz-adv-x="1568" d="M-18 0l639 1434h327l641 -1434h-348l-127 307h-665l-127 -307h-340zM373 1550l270 295h283l270 -295h-233l-179 156l-178 -156h-233zM555 559h455l-228 549z" />
<glyph unicode="&#xc3;" horiz-adv-x="1568" d="M-18 0l639 1434h327l641 -1434h-348l-127 307h-665l-127 -307h-340zM391 1559q3 137 68 218t172 81q48 0 94 -20.5t75 -45.5t61.5 -45.5t56.5 -20.5q41 0 68 30t30 81h162q-3 -133 -68 -213t-172 -80q-48 0 -94 20t-75 44.5t-61.5 44.5t-56.5 20q-42 0 -68.5 -30 t-29.5 -84h-162zM555 559h455l-228 549z" />
<glyph unicode="&#xc4;" horiz-adv-x="1568" d="M-18 0l639 1434h327l641 -1434h-348l-127 307h-665l-127 -307h-340zM434 1702q0 62 41.5 101.5t102.5 39.5t101 -39.5t40 -101.5t-40 -101.5t-101 -39.5t-102.5 39.5t-41.5 101.5zM555 559h455l-228 549zM850 1702q0 62 40 101.5t101 39.5t102.5 -39.5t41.5 -101.5 t-41.5 -101.5t-102.5 -39.5t-101 39.5t-40 101.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1568" d="M-18 0l639 1434h327l641 -1434h-348l-127 307h-665l-127 -307h-340zM530 1792q0 103 72.5 175.5t179.5 72.5q108 0 182 -72.5t74 -175.5q0 -105 -74 -176.5t-182 -71.5q-107 0 -179.5 71.5t-72.5 176.5zM555 559h455l-228 549zM659 1792q0 -54 35 -89.5t88 -35.5 q54 0 89.5 35.5t35.5 89.5t-35.5 89.5t-89.5 35.5q-52 0 -87.5 -35.5t-35.5 -89.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2211" d="M-18 0l809 1434h1300v-267h-754v-311h668v-258h-668v-332h781v-266h-1110v307h-519l-167 -307h-340zM627 559h381v608h-47z" />
<glyph unicode="&#xc7;" horiz-adv-x="1501" d="M80 717q0 213 101 382.5t278.5 264t398.5 94.5q186 0 337.5 -65.5t252.5 -188.5l-213 -196q-145 168 -361 168q-201 0 -329.5 -128.5t-128.5 -330.5t128.5 -330.5t329.5 -128.5q217 0 361 170l213 -197q-96 -120 -239.5 -185t-321.5 -71l-19 -77q89 -10 134 -56.5 t45 -113.5q0 -93 -82.5 -148t-219.5 -55q-115 0 -190 43l49 137q67 -31 133 -31q107 0 107 66q0 59 -101 59h-69l45 187q-283 43 -461 243.5t-178 487.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1374" d="M170 0v1434h1083v-267h-753v-311h665v-258h-665v-332h780v-266h-1110zM279 1845h344l276 -295h-250z" />
<glyph unicode="&#xc9;" horiz-adv-x="1374" d="M170 0v1434h1083v-267h-753v-311h665v-258h-665v-332h780v-266h-1110zM526 1550l277 295h344l-371 -295h-250z" />
<glyph unicode="&#xca;" horiz-adv-x="1374" d="M170 0v1434h1083v-267h-753v-311h665v-258h-665v-332h780v-266h-1110zM301 1550l270 295h283l270 -295h-233l-178 156l-178 -156h-234z" />
<glyph unicode="&#xcb;" horiz-adv-x="1374" d="M170 0v1434h1083v-267h-753v-311h665v-258h-665v-332h780v-266h-1110zM362 1702q0 62 41.5 101.5t102.5 39.5t101 -39.5t40 -101.5t-40 -101.5t-101 -39.5t-102.5 39.5t-41.5 101.5zM778 1702q0 62 40.5 101.5t101.5 39.5t102 -39.5t41 -101.5t-41 -101.5t-102 -39.5 t-101.5 39.5t-40.5 101.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="671" d="M-98 1845h344l276 -295h-250zM170 0v1434h332v-1434h-332z" />
<glyph unicode="&#xcd;" horiz-adv-x="671" d="M150 1550l276 295h344l-371 -295h-249zM170 0v1434h332v-1434h-332z" />
<glyph unicode="&#xce;" horiz-adv-x="671" d="M-76 1550l271 295h282l271 -295h-234l-178 156l-178 -156h-234zM170 0v1434h332v-1434h-332z" />
<glyph unicode="&#xcf;" horiz-adv-x="671" d="M-14 1702q0 62 41 101.5t102 39.5t101 -39.5t40 -101.5t-40 -101.5t-101 -39.5t-102 39.5t-41 101.5zM170 0v1434h332v-1434h-332zM401 1702q0 62 40.5 101.5t101.5 39.5t102 -39.5t41 -101.5t-41 -101.5t-102 -39.5t-101.5 39.5t-40.5 101.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1718" d="M2 618v222h195v594h651q233 0 412 -88t278.5 -251.5t99.5 -377.5t-99.5 -377.5t-278.5 -251.5t-412 -88h-651v618h-195zM528 272h303q215 0 343.5 120t128.5 325q0 204 -128.5 324t-343.5 120h-303v-321h359v-222h-359v-346z" />
<glyph unicode="&#xd1;" horiz-adv-x="1654" d="M170 0v1434h274l713 -871v871h328v-1434h-273l-714 870v-870h-328zM434 1559q3 137 68 218t172 81q48 0 94 -20.5t75 -45.5t61.5 -45.5t56.5 -20.5q41 0 68 30t30 81h162q-3 -133 -68 -213t-172 -80q-48 0 -94 20t-75 44.5t-61.5 44.5t-56.5 20q-42 0 -68.5 -30 t-29.5 -84h-162z" />
<glyph unicode="&#xd2;" horiz-adv-x="1728" d="M80 717q0 210 101 379.5t280.5 265.5t402.5 96t402.5 -95.5t281 -265t101.5 -380.5q0 -158 -59.5 -296t-163.5 -236t-249.5 -154t-312.5 -56q-223 0 -402.5 96t-280.5 266t-101 380zM416 717q0 -202 127 -330.5t321 -128.5t321.5 128.5t127.5 330.5t-127.5 330.5 t-321.5 128.5t-321 -128.5t-127 -330.5zM430 1845h344l277 -295h-250z" />
<glyph unicode="&#xd3;" horiz-adv-x="1728" d="M80 717q0 210 101 379.5t280.5 265.5t402.5 96t402.5 -95.5t281 -265t101.5 -380.5q0 -158 -59.5 -296t-163.5 -236t-249.5 -154t-312.5 -56q-223 0 -402.5 96t-280.5 266t-101 380zM416 717q0 -202 127 -330.5t321 -128.5t321.5 128.5t127.5 330.5t-127.5 330.5 t-321.5 128.5t-321 -128.5t-127 -330.5zM678 1550l276 295h344l-370 -295h-250z" />
<glyph unicode="&#xd4;" horiz-adv-x="1728" d="M80 717q0 210 101 379.5t280.5 265.5t402.5 96t402.5 -95.5t281 -265t101.5 -380.5q0 -158 -59.5 -296t-163.5 -236t-249.5 -154t-312.5 -56q-223 0 -402.5 96t-280.5 266t-101 380zM416 717q0 -202 127 -330.5t321 -128.5t321.5 128.5t127.5 330.5t-127.5 330.5 t-321.5 128.5t-321 -128.5t-127 -330.5zM453 1550l270 295h283l270 -295h-234l-178 156l-178 -156h-233z" />
<glyph unicode="&#xd5;" horiz-adv-x="1728" d="M80 717q0 210 101 379.5t280.5 265.5t402.5 96t402.5 -95.5t281 -265t101.5 -380.5q0 -158 -59.5 -296t-163.5 -236t-249.5 -154t-312.5 -56q-223 0 -402.5 96t-280.5 266t-101 380zM416 717q0 -202 127 -330.5t321 -128.5t321.5 128.5t127.5 330.5t-127.5 330.5 t-321.5 128.5t-321 -128.5t-127 -330.5zM471 1559q3 137 68 218t172 81q48 0 93.5 -20.5t74.5 -45.5t61.5 -45.5t56.5 -20.5q42 0 69 30t30 81h161q-3 -133 -67.5 -213t-171.5 -80q-48 0 -94 20t-75 44.5t-61.5 44.5t-56.5 20q-42 0 -68.5 -30t-29.5 -84h-162z" />
<glyph unicode="&#xd6;" horiz-adv-x="1728" d="M80 717q0 210 101 379.5t280.5 265.5t402.5 96t402.5 -95.5t281 -265t101.5 -380.5q0 -158 -59.5 -296t-163.5 -236t-249.5 -154t-312.5 -56q-223 0 -402.5 96t-280.5 266t-101 380zM416 717q0 -202 127 -330.5t321 -128.5t321.5 128.5t127.5 330.5t-127.5 330.5 t-321.5 128.5t-321 -128.5t-127 -330.5zM514 1702q0 62 41 101.5t102 39.5t101.5 -39.5t40.5 -101.5t-40.5 -101.5t-101.5 -39.5t-102 39.5t-41 101.5zM930 1702q0 62 40 101.5t101 39.5t102 -39.5t41 -101.5t-41 -101.5t-102 -39.5t-101 39.5t-40 101.5z" />
<glyph unicode="&#xd7;" d="M184 461l254 256l-254 256l172 180l258 -260l258 260l172 -180l-253 -256l253 -256l-172 -180l-258 260l-258 -260z" />
<glyph unicode="&#xd8;" horiz-adv-x="1728" d="M80 717q0 210 101 379.5t280.5 265.5t402.5 96q193 0 367 -78l139 197h215l-205 -289q127 -102 198 -249t71 -322q0 -158 -59.5 -296t-163.5 -236t-249.5 -154t-312.5 -56q-197 0 -364 78l-140 -196h-215l205 288q-128 101 -199 249t-71 323zM416 717q0 -191 114 -318 l527 738q-89 39 -193 39q-194 0 -321 -128.5t-127 -330.5zM674 297q89 -39 190 -39q194 0 321.5 128.5t127.5 330.5q0 189 -115 317z" />
<glyph unicode="&#xd9;" horiz-adv-x="1613" d="M158 631v803h331v-791q0 -385 320 -385q157 0 238 93t81 292v791h328v-803q0 -315 -170.5 -485.5t-478.5 -170.5t-478.5 170.5t-170.5 485.5zM375 1845h344l276 -295h-250z" />
<glyph unicode="&#xda;" horiz-adv-x="1613" d="M158 631v803h331v-791q0 -385 320 -385q157 0 238 93t81 292v791h328v-803q0 -315 -170.5 -485.5t-478.5 -170.5t-478.5 170.5t-170.5 485.5zM623 1550l276 295h344l-371 -295h-249z" />
<glyph unicode="&#xdb;" horiz-adv-x="1613" d="M158 631v803h331v-791q0 -385 320 -385q157 0 238 93t81 292v791h328v-803q0 -315 -170.5 -485.5t-478.5 -170.5t-478.5 170.5t-170.5 485.5zM397 1550l271 295h282l271 -295h-234l-178 156l-178 -156h-234z" />
<glyph unicode="&#xdc;" horiz-adv-x="1613" d="M158 631v803h331v-791q0 -385 320 -385q157 0 238 93t81 292v791h328v-803q0 -315 -170.5 -485.5t-478.5 -170.5t-478.5 170.5t-170.5 485.5zM459 1702q0 62 41 101.5t102 39.5t101 -39.5t40 -101.5t-40 -101.5t-101 -39.5t-102 39.5t-41 101.5zM874 1702 q0 62 40.5 101.5t101.5 39.5t102 -39.5t41 -101.5t-41 -101.5t-102 -39.5t-101.5 39.5t-40.5 101.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1384" d="M-29 1434h353l383 -637l383 637h325l-557 -926v-508h-332v512zM506 1550l276 295h344l-370 -295h-250z" />
<glyph unicode="&#xde;" horiz-adv-x="1499" d="M170 0v1434h332v-158h289q288 0 455 -139.5t167 -380.5t-167 -380.5t-455 -139.5h-289v-236h-332zM502 506h270q150 0 227.5 65t77.5 185t-77.5 185t-227.5 65h-270v-500z" />
<glyph unicode="&#xdf;" horiz-adv-x="1419" d="M147 0v989q0 132 43.5 237.5t120 172.5t177.5 102t219 35q241 0 381.5 -120.5t140.5 -307.5q0 -186 -156 -305q132 -44 206.5 -140.5t74.5 -238.5q0 -206 -141.5 -323t-374.5 -117q-144 0 -242 32l37 254q77 -24 172 -24q104 0 164.5 48.5t60.5 137.5q0 90 -65.5 138 t-182.5 48h-110v252q115 3 182.5 61t67.5 154q0 87 -57.5 139t-157.5 52q-112 0 -176 -64t-64 -184v-1028h-320z" />
<glyph unicode="&#xe0;" horiz-adv-x="1263" d="M76 317q0 154 115.5 242t357.5 88h254q0 105 -63.5 161t-190.5 56q-88 0 -173 -27.5t-145 -74.5l-114 223q90 64 215.5 98.5t259.5 34.5q257 0 393.5 -121.5t136.5 -367.5v-629h-299v137q-90 -153 -336 -153q-189 0 -300 93.5t-111 239.5zM190 1538h345l276 -295h-250z M387 332q0 -61 48 -97t132 -36q82 0 145.5 38t90.5 111v113h-219q-197 0 -197 -129z" />
<glyph unicode="&#xe1;" horiz-adv-x="1263" d="M76 317q0 154 115.5 242t357.5 88h254q0 105 -63.5 161t-190.5 56q-88 0 -173 -27.5t-145 -74.5l-114 223q90 64 215.5 98.5t259.5 34.5q257 0 393.5 -121.5t136.5 -367.5v-629h-299v137q-90 -153 -336 -153q-189 0 -300 93.5t-111 239.5zM387 332q0 -61 48 -97t132 -36 q82 0 145.5 38t90.5 111v113h-219q-197 0 -197 -129zM438 1243l277 295h344l-371 -295h-250z" />
<glyph unicode="&#xe2;" horiz-adv-x="1263" d="M76 317q0 154 115.5 242t357.5 88h254q0 105 -63.5 161t-190.5 56q-88 0 -173 -27.5t-145 -74.5l-114 223q90 64 215.5 98.5t259.5 34.5q257 0 393.5 -121.5t136.5 -367.5v-629h-299v137q-90 -153 -336 -153q-189 0 -300 93.5t-111 239.5zM213 1243l270 295h283l270 -295 h-233l-178 156l-179 -156h-233zM387 332q0 -61 48 -97t132 -36q82 0 145.5 38t90.5 111v113h-219q-197 0 -197 -129z" />
<glyph unicode="&#xe3;" horiz-adv-x="1263" d="M76 317q0 154 115.5 242t357.5 88h254q0 105 -63.5 161t-190.5 56q-88 0 -173 -27.5t-145 -74.5l-114 223q90 64 215.5 98.5t259.5 34.5q257 0 393.5 -121.5t136.5 -367.5v-629h-299v137q-90 -153 -336 -153q-189 0 -300 93.5t-111 239.5zM231 1251q3 137 68 218t172 81 q48 0 94 -20.5t75 -45t61.5 -45t56.5 -20.5q41 0 68 30t30 81h162q-3 -133 -68 -213t-172 -80q-48 0 -93.5 20t-74.5 44.5t-61.5 44.5t-56.5 20q-42 0 -69 -30.5t-30 -84.5h-162zM387 332q0 -61 48 -97t132 -36q82 0 145.5 38t90.5 111v113h-219q-197 0 -197 -129z" />
<glyph unicode="&#xe4;" horiz-adv-x="1263" d="M76 317q0 154 115.5 242t357.5 88h254q0 105 -63.5 161t-190.5 56q-88 0 -173 -27.5t-145 -74.5l-114 223q90 64 215.5 98.5t259.5 34.5q257 0 393.5 -121.5t136.5 -367.5v-629h-299v137q-90 -153 -336 -153q-189 0 -300 93.5t-111 239.5zM274 1395q0 62 41.5 101.5 t102.5 39.5t101 -39.5t40 -101.5t-40 -102t-101 -40t-102.5 40t-41.5 102zM387 332q0 -61 48 -97t132 -36q82 0 145.5 38t90.5 111v113h-219q-197 0 -197 -129zM690 1395q0 62 40 101.5t101 39.5t102.5 -39.5t41.5 -101.5t-41.5 -102t-102.5 -40t-101 40t-40 102z" />
<glyph unicode="&#xe5;" horiz-adv-x="1263" d="M76 317q0 154 115.5 242t357.5 88h254q0 105 -63.5 161t-190.5 56q-88 0 -173 -27.5t-145 -74.5l-114 223q90 64 215.5 98.5t259.5 34.5q257 0 393.5 -121.5t136.5 -367.5v-629h-299v137q-90 -153 -336 -153q-189 0 -300 93.5t-111 239.5zM371 1460q0 103 72.5 175.5 t179.5 72.5q108 0 182 -72.5t74 -175.5q0 -105 -74 -176.5t-182 -71.5q-107 0 -179.5 71.5t-72.5 176.5zM387 332q0 -61 48 -97t132 -36q82 0 145.5 38t90.5 111v113h-219q-197 0 -197 -129zM500 1460q0 -54 35 -89.5t88 -35.5q54 0 89.5 35.5t35.5 89.5t-35.5 89.5 t-89.5 35.5q-51 0 -87 -35.5t-36 -89.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="2039" d="M76 330q0 144 116.5 231.5t356.5 87.5h254q0 102 -63.5 158.5t-188.5 56.5q-89 0 -174.5 -27.5t-145.5 -74.5l-114 223q89 64 214.5 98.5t258.5 34.5q277 0 405 -155q76 74 180.5 114.5t225.5 40.5q158 0 287.5 -68.5t206.5 -198t77 -296.5q0 -51 -6 -92h-836 q22 -104 105 -163.5t209 -59.5q160 0 278 106l170 -184q-161 -178 -454 -178q-307 0 -477 190q-126 -190 -422 -190q-215 0 -339 94.5t-124 251.5zM387 336q0 -65 48.5 -101t135.5 -36q108 0 170 57.5t62 155.5v51h-219q-197 0 -197 -127zM1126 649h537q-17 108 -91.5 168 t-174.5 60t-175.5 -60t-95.5 -168z" />
<glyph unicode="&#xe7;" horiz-adv-x="1210" d="M66 551q0 164 78.5 293.5t218.5 201.5t317 72q174 0 304 -73t190 -207l-248 -133q-86 151 -248 151q-125 0 -207 -82t-82 -223t82 -223t207 -82q164 0 248 151l248 -135q-57 -127 -179.5 -199.5t-287.5 -78.5l-21 -86q89 -10 133.5 -56.5t44.5 -113.5q0 -93 -82 -148 t-219 -55q-115 0 -190 43l49 137q67 -31 133 -31q107 0 107 66q0 59 -101 59h-69l49 197q-215 40 -345 190.5t-130 364.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1292" d="M66 551q0 162 76.5 292t210.5 202.5t300 72.5q162 0 292 -68t206 -199t76 -304q0 -5 -6 -86h-834q23 -103 106.5 -162t208.5 -59q165 0 277 106l170 -184q-155 -178 -455 -178q-187 0 -330.5 73t-220.5 202t-77 292zM213 1538h344l277 -295h-250zM383 647h543 q-17 105 -90.5 167.5t-180.5 62.5q-109 0 -182 -62t-90 -168z" />
<glyph unicode="&#xe9;" horiz-adv-x="1292" d="M66 551q0 162 76.5 292t210.5 202.5t300 72.5q162 0 292 -68t206 -199t76 -304q0 -5 -6 -86h-834q23 -103 106.5 -162t208.5 -59q165 0 277 106l170 -184q-155 -178 -455 -178q-187 0 -330.5 73t-220.5 202t-77 292zM383 647h543q-17 105 -90.5 167.5t-180.5 62.5 q-109 0 -182 -62t-90 -168zM461 1243l276 295h344l-370 -295h-250z" />
<glyph unicode="&#xea;" horiz-adv-x="1292" d="M66 551q0 162 76.5 292t210.5 202.5t300 72.5q162 0 292 -68t206 -199t76 -304q0 -5 -6 -86h-834q23 -103 106.5 -162t208.5 -59q165 0 277 106l170 -184q-155 -178 -455 -178q-187 0 -330.5 73t-220.5 202t-77 292zM236 1243l270 295h282l271 -295h-234l-178 156 l-178 -156h-233zM383 647h543q-17 105 -90.5 167.5t-180.5 62.5q-109 0 -182 -62t-90 -168z" />
<glyph unicode="&#xeb;" horiz-adv-x="1292" d="M66 551q0 162 76.5 292t210.5 202.5t300 72.5q162 0 292 -68t206 -199t76 -304q0 -5 -6 -86h-834q23 -103 106.5 -162t208.5 -59q165 0 277 106l170 -184q-155 -178 -455 -178q-187 0 -330.5 73t-220.5 202t-77 292zM297 1395q0 62 41 101.5t102 39.5t101.5 -39.5 t40.5 -101.5t-40.5 -102t-101.5 -40t-102 40t-41 102zM383 647h543q-17 105 -90.5 167.5t-180.5 62.5q-109 0 -182 -62t-90 -168zM713 1395q0 62 40 101.5t101 39.5t102 -39.5t41 -101.5t-41 -102t-102 -40t-101 40t-40 102z" />
<glyph unicode="&#xec;" horiz-adv-x="616" d="M-127 1538h344l277 -295h-250zM147 0v1102h320v-1102h-320z" />
<glyph unicode="&#xed;" horiz-adv-x="616" d="M121 1243l276 295h344l-370 -295h-250zM147 0v1102h320v-1102h-320z" />
<glyph unicode="&#xee;" horiz-adv-x="616" d="M-53 1243l223 295h274l224 -295h-228l-133 143l-133 -143h-227zM147 0v1102h320v-1102h-320z" />
<glyph unicode="&#xef;" horiz-adv-x="616" d="M6 1395q0 61 38 100t95 39q58 0 96.5 -39t38.5 -100t-38.5 -100.5t-96.5 -39.5q-57 0 -95 39.5t-38 100.5zM147 0v1102h320v-1102h-320zM340 1395q0 61 38.5 100t96.5 39q57 0 95 -39t38 -100t-38 -100.5t-95 -39.5q-58 0 -96.5 39.5t-38.5 100.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1345" d="M66 453q0 137 69.5 243t187 161.5t263.5 55.5q234 0 360 -147v4q0 249 -119 377l-518 -199l-65 162l383 147q-64 15 -142 15q-149 0 -276 -43l-39 248q157 41 328 41q284 0 475 -129l184 71l66 -162l-109 -41q164 -209 164 -557q0 -339 -176.5 -532t-483.5 -193 q-238 0 -395 132t-157 346zM391 444q0 -100 70.5 -160.5t181.5 -60.5q118 0 190 63.5t72 157.5q0 96 -72.5 159t-183.5 63q-118 0 -188 -60.5t-70 -161.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1415" d="M147 0v1102h306v-129q133 145 364 145q99 0 181 -29.5t144.5 -87.5t97 -152.5t34.5 -217.5v-631h-320v582q0 131 -57.5 195.5t-165.5 64.5q-121 0 -192.5 -75t-71.5 -222v-545h-320zM319 1251q3 137 68 218t172 81q48 0 94 -20.5t75 -45t61.5 -45t56.5 -20.5q41 0 68 30 t30 81h162q-3 -133 -68 -213t-172 -80q-48 0 -93.5 20t-74.5 44.5t-61.5 44.5t-56.5 20q-42 0 -69 -30.5t-30 -84.5h-162z" />
<glyph unicode="&#xf2;" horiz-adv-x="1341" d="M66 551q0 248 171.5 407.5t434.5 159.5t433.5 -159.5t170.5 -407.5t-170.5 -407.5t-433.5 -159.5t-434.5 159.5t-171.5 407.5zM233 1538h345l276 -295h-250zM389 551q0 -139 80 -222t203 -83t201.5 82.5t78.5 222.5t-78.5 222.5t-201.5 82.5t-203 -83t-80 -222z" />
<glyph unicode="&#xf3;" horiz-adv-x="1341" d="M66 551q0 248 171.5 407.5t434.5 159.5t433.5 -159.5t170.5 -407.5t-170.5 -407.5t-433.5 -159.5t-434.5 159.5t-171.5 407.5zM389 551q0 -139 80 -222t203 -83t201.5 82.5t78.5 222.5t-78.5 222.5t-201.5 82.5t-203 -83t-80 -222zM481 1243l277 295h344l-371 -295h-250z " />
<glyph unicode="&#xf4;" horiz-adv-x="1341" d="M66 551q0 248 171.5 407.5t434.5 159.5t433.5 -159.5t170.5 -407.5t-170.5 -407.5t-433.5 -159.5t-434.5 159.5t-171.5 407.5zM256 1243l270 295h283l270 -295h-233l-178 156l-179 -156h-233zM389 551q0 -139 80 -222t203 -83t201.5 82.5t78.5 222.5t-78.5 222.5 t-201.5 82.5t-203 -83t-80 -222z" />
<glyph unicode="&#xf5;" horiz-adv-x="1341" d="M66 551q0 248 171.5 407.5t434.5 159.5t433.5 -159.5t170.5 -407.5t-170.5 -407.5t-433.5 -159.5t-434.5 159.5t-171.5 407.5zM274 1251q3 137 68 218t172 81q48 0 94 -20.5t75 -45t61.5 -45t56.5 -20.5q41 0 68 30t30 81h162q-3 -133 -68 -213t-172 -80q-48 0 -93.5 20 t-74.5 44.5t-61.5 44.5t-56.5 20q-42 0 -69 -30.5t-30 -84.5h-162zM389 551q0 -139 80 -222t203 -83t201.5 82.5t78.5 222.5t-78.5 222.5t-201.5 82.5t-203 -83t-80 -222z" />
<glyph unicode="&#xf6;" horiz-adv-x="1341" d="M66 551q0 248 171.5 407.5t434.5 159.5t433.5 -159.5t170.5 -407.5t-170.5 -407.5t-433.5 -159.5t-434.5 159.5t-171.5 407.5zM317 1395q0 62 41.5 101.5t102.5 39.5t101 -39.5t40 -101.5t-40 -102t-101 -40t-102.5 40t-41.5 102zM389 551q0 -139 80 -222t203 -83 t201.5 82.5t78.5 222.5t-78.5 222.5t-201.5 82.5t-203 -83t-80 -222zM733 1395q0 62 40 101.5t101 39.5t102.5 -39.5t41.5 -101.5t-41.5 -102t-102.5 -40t-101 40t-40 102z" />
<glyph unicode="&#xf7;" d="M127 592v250h975v-250h-975zM444 293q0 73 49.5 120.5t120.5 47.5t120.5 -47.5t49.5 -120.5q0 -74 -49.5 -123t-120.5 -49t-120.5 49t-49.5 123zM444 1145q0 74 49 121t121 47t121 -47t49 -121t-49.5 -123t-120.5 -49t-120.5 49t-49.5 123z" />
<glyph unicode="&#xf8;" horiz-adv-x="1341" d="M66 551q0 248 171.5 407.5t434.5 159.5q140 0 258 -47l108 166h160l-151 -231q109 -76 169 -193.5t60 -261.5q0 -248 -170.5 -407.5t-433.5 -159.5q-144 0 -260 49l-117 -178h-158l158 241q-108 76 -168.5 194.5t-60.5 260.5zM389 551q0 -126 68 -207l321 494 q-49 18 -106 18q-123 0 -203 -83t-80 -222zM563 266q47 -20 109 -20q123 0 201.5 82.5t78.5 222.5q0 125 -65 209z" />
<glyph unicode="&#xf9;" horiz-adv-x="1406" d="M141 479v623h320v-576q0 -266 223 -266q115 0 184.5 75t69.5 222v545h319v-1102h-303v131q-63 -71 -151.5 -109t-190.5 -38q-216 0 -343.5 124.5t-127.5 370.5zM264 1538h344l277 -295h-250z" />
<glyph unicode="&#xfa;" horiz-adv-x="1406" d="M141 479v623h320v-576q0 -266 223 -266q115 0 184.5 75t69.5 222v545h319v-1102h-303v131q-63 -71 -151.5 -109t-190.5 -38q-216 0 -343.5 124.5t-127.5 370.5zM512 1243l276 295h345l-371 -295h-250z" />
<glyph unicode="&#xfb;" horiz-adv-x="1406" d="M141 479v623h320v-576q0 -266 223 -266q115 0 184.5 75t69.5 222v545h319v-1102h-303v131q-63 -71 -151.5 -109t-190.5 -38q-216 0 -343.5 124.5t-127.5 370.5zM287 1243l270 295h283l270 -295h-233l-179 156l-178 -156h-233z" />
<glyph unicode="&#xfc;" horiz-adv-x="1406" d="M141 479v623h320v-576q0 -266 223 -266q115 0 184.5 75t69.5 222v545h319v-1102h-303v131q-63 -71 -151.5 -109t-190.5 -38q-216 0 -343.5 124.5t-127.5 370.5zM348 1395q0 62 41.5 101.5t102.5 39.5t101 -39.5t40 -101.5t-40 -102t-101 -40t-102.5 40t-41.5 102z M764 1395q0 62 40 101.5t101 39.5t102.5 -39.5t41.5 -101.5t-41.5 -102t-102.5 -40t-101 40t-40 102z" />
<glyph unicode="&#xfd;" horiz-adv-x="1224" d="M-20 -313l116 227q80 -70 180 -70q66 0 107.5 32t73.5 106l4 10l-477 1110h329l310 -748l311 748h307l-498 -1170q-76 -190 -186.5 -268t-269.5 -78q-86 0 -170 27t-137 74zM403 1243l277 295h344l-371 -295h-250z" />
<glyph unicode="&#xfe;" horiz-adv-x="1413" d="M147 -397v1917h320v-529q118 127 332 127q115 0 216 -40.5t174.5 -113t116 -179.5t42.5 -234t-42.5 -234t-116 -179.5t-174.5 -113t-216 -40.5q-211 0 -332 133v-514h-320zM463 551q0 -140 78.5 -222.5t201.5 -82.5t202 82.5t79 222.5t-79 222.5t-202 82.5t-201.5 -82.5 t-78.5 -222.5z" />
<glyph unicode="&#xff;" horiz-adv-x="1224" d="M-20 -313l116 227q80 -70 180 -70q66 0 107.5 32t73.5 106l4 10l-477 1110h329l310 -748l311 748h307l-498 -1170q-76 -190 -186.5 -268t-269.5 -78q-86 0 -170 27t-137 74zM240 1395q0 62 41 101.5t102 39.5t101 -39.5t40 -101.5t-40 -102t-101 -40t-102 40t-41 102z M655 1395q0 62 40.5 101.5t101.5 39.5t102 -39.5t41 -101.5t-41 -102t-102 -40t-101.5 40t-40.5 102z" />
<glyph unicode="&#x152;" horiz-adv-x="2326" d="M80 715q0 215 99.5 379t278.5 252t412 88h1336v-267h-754v-311h666v-258h-666v-332h780v-266h-1362q-233 0 -412 88t-278.5 251t-99.5 376zM416 715q0 -203 128 -323t343 -120h235v889h-235q-215 0 -343 -120t-128 -326z" />
<glyph unicode="&#x153;" horiz-adv-x="2179" d="M66 551q0 163 78 293t215.5 202t308.5 72q140 0 253 -49t187 -139q74 90 185.5 139t246.5 49q162 0 291.5 -68t206 -198.5t76.5 -302.5q0 -41 -7 -88h-835q23 -103 106.5 -162t210.5 -59q160 0 275 106l172 -184q-158 -178 -457 -178q-151 0 -271 49.5t-198 140.5 q-160 -190 -442 -190q-171 0 -308.5 72t-215.5 202t-78 293zM389 551q0 -139 80 -222t203 -83t201.5 82.5t78.5 222.5t-78.5 222.5t-201.5 82.5t-203 -83t-80 -222zM1268 647h540q-15 105 -87.5 167.5t-180.5 62.5q-109 0 -182 -62t-90 -168z" />
<glyph unicode="&#x178;" horiz-adv-x="1384" d="M-29 1434h353l383 -637l383 637h325l-557 -926v-508h-332v512zM342 1702q0 62 41 101.5t102 39.5t101.5 -39.5t40.5 -101.5t-40.5 -101.5t-101.5 -39.5t-102 39.5t-41 101.5zM758 1702q0 62 40 101.5t101 39.5t102 -39.5t41 -101.5t-41 -101.5t-102 -39.5t-101 39.5 t-40 101.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1228" d="M203 1243l270 295h283l270 -295h-233l-179 156l-178 -156h-233z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1228" d="M221 1251q3 137 68 218t172 81q48 0 94 -20.5t75 -45t61.5 -45t56.5 -20.5q41 0 68 30t30 81h162q-3 -133 -68 -213t-172 -80q-48 0 -94 20t-75 44.5t-61.5 44.5t-56.5 20q-42 0 -68.5 -30t-29.5 -85h-162z" />
<glyph unicode="&#x2000;" horiz-adv-x="1020" />
<glyph unicode="&#x2001;" horiz-adv-x="2040" />
<glyph unicode="&#x2002;" horiz-adv-x="1020" />
<glyph unicode="&#x2003;" horiz-adv-x="2040" />
<glyph unicode="&#x2004;" horiz-adv-x="680" />
<glyph unicode="&#x2005;" horiz-adv-x="510" />
<glyph unicode="&#x2006;" horiz-adv-x="340" />
<glyph unicode="&#x2007;" horiz-adv-x="340" />
<glyph unicode="&#x2008;" horiz-adv-x="255" />
<glyph unicode="&#x2009;" horiz-adv-x="408" />
<glyph unicode="&#x200a;" horiz-adv-x="113" />
<glyph unicode="&#x2010;" horiz-adv-x="790" d="M106 449v256h580v-256h-580z" />
<glyph unicode="&#x2011;" horiz-adv-x="790" d="M106 449v256h580v-256h-580z" />
<glyph unicode="&#x2012;" horiz-adv-x="790" d="M106 449v256h580v-256h-580z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 477v199h1024v-199h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 477v199h2048v-199h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="536" d="M70 1020q0 42 10.5 83t44.5 122l119 295h202l-90 -326q52 -22 80.5 -67t28.5 -107q0 -88 -55.5 -142.5t-141.5 -54.5q-87 0 -142.5 55t-55.5 142z" />
<glyph unicode="&#x2019;" horiz-adv-x="536" d="M72 1339q0 88 56 142.5t142 54.5q87 0 142 -55t55 -142q0 -41 -10 -81.5t-43 -122.5l-119 -295h-203l88 323q-51 22 -79.5 67.5t-28.5 108.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="536" d="M72 184q0 88 56 142.5t142 54.5q87 0 142 -55t55 -142q0 -42 -10.5 -82t-44.5 -122l-117 -295h-203l90 325q-52 20 -81 66t-29 108z" />
<glyph unicode="&#x201c;" horiz-adv-x="1019" d="M70 1020q0 42 10.5 83t44.5 122l119 295h202l-90 -326q52 -22 80.5 -67t28.5 -107q0 -88 -55.5 -142.5t-141.5 -54.5q-87 0 -142.5 55t-55.5 142zM553 1020q0 42 10.5 83t44.5 122l119 295h203l-90 -326q108 -46 108 -174q0 -88 -55 -142.5t-141 -54.5q-87 0 -143 55 t-56 142z" />
<glyph unicode="&#x201d;" horiz-adv-x="1019" d="M72 1339q0 88 56 142.5t142 54.5q87 0 142 -55t55 -142q0 -41 -10 -81.5t-43 -122.5l-119 -295h-203l88 323q-51 22 -79.5 67.5t-28.5 108.5zM555 1339q0 88 56.5 142.5t142.5 54.5t141 -55t55 -142q0 -41 -10 -81.5t-43 -122.5l-119 -295h-203l89 323q-51 22 -80 67.5 t-29 108.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="1019" d="M72 184q0 88 56 142.5t142 54.5q87 0 142 -55t55 -142q0 -42 -10.5 -82t-44.5 -122l-117 -295h-203l90 325q-52 20 -81 66t-29 108zM555 184q0 88 56.5 142.5t142.5 54.5t141 -54.5t55 -142.5q0 -42 -10.5 -82t-44.5 -122l-117 -295h-203l91 325q-52 21 -81.5 66.5 t-29.5 107.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="733" d="M111 573q0 111 74.5 184.5t181.5 73.5q106 0 181 -73.5t75 -184.5t-75 -185.5t-181 -74.5q-107 0 -181.5 74.5t-74.5 185.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1634" d="M70 184q0 88 56.5 142.5t141.5 54.5t142 -54.5t57 -142.5q0 -87 -57.5 -143.5t-141.5 -56.5t-141 56.5t-57 143.5zM616 184q0 88 57 142.5t142 54.5t142 -54.5t57 -142.5q0 -87 -57.5 -143.5t-141.5 -56.5t-141.5 56.5t-57.5 143.5zM1165 184q0 88 57 142.5t142 54.5 t142 -54.5t57 -142.5q0 -87 -57.5 -143.5t-141.5 -56.5t-141.5 56.5t-57.5 143.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="408" />
<glyph unicode="&#x2039;" horiz-adv-x="700" d="M74 551l288 401h287l-280 -401l280 -399h-287z" />
<glyph unicode="&#x203a;" horiz-adv-x="700" d="M51 152l281 399l-281 401h287l289 -401l-289 -399h-287z" />
<glyph unicode="&#x205f;" horiz-adv-x="510" />
<glyph unicode="&#x20ac;" horiz-adv-x="1679" d="M61 489v173h201q-2 18 -2 55t2 55h-201v172h232q71 233 272 373.5t471 140.5q187 0 337.5 -65t252.5 -189l-211 -196q-148 168 -362 168q-135 0 -241.5 -62t-164.5 -170h518v-172h-569q-2 -18 -2 -55t2 -55h569v-173h-518q58 -108 164.5 -169.5t241.5 -61.5 q215 0 362 170l211 -197q-101 -125 -252 -190.5t-338 -65.5q-270 0 -471 140.5t-272 373.5h-232z" />
<glyph unicode="&#x2122;" horiz-adv-x="2142" d="M8 1257v177h809v-177h-291v-673h-229v673h-289zM924 584v850h188l340 -512l332 512h186l4 -850h-215l-2 485l-258 -397h-102l-258 385v-473h-215z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1105" d="M0 0v1106h1106v-1106h-1106z" />
<hkern u1="&#x2a;" u2="&#xee;" k="-164" />
<hkern u1="&#x2f;" u2="&#xf0;" k="90" />
<hkern u1="&#x2f;" u2="i" k="-27" />
<hkern u1="F" u2="&#xef;" k="-33" />
<hkern u1="F" u2="&#xee;" k="-59" />
<hkern u1="Q" u2="&#x7d;" k="-27" />
<hkern u1="Q" u2="_" k="-82" />
<hkern u1="Q" u2="]" k="-27" />
<hkern u1="Q" u2="&#x2f;" k="-102" />
<hkern u1="T" u2="&#xef;" k="-47" />
<hkern u1="T" u2="&#xee;" k="-94" />
<hkern u1="T" u2="&#xec;" k="-4" />
<hkern u1="V" u2="&#xef;" k="-63" />
<hkern u1="V" u2="&#xee;" k="-92" />
<hkern u1="V" u2="&#xec;" k="-43" />
<hkern u1="V" u2="i" k="10" />
<hkern u1="W" u2="&#xef;" k="-63" />
<hkern u1="W" u2="&#xee;" k="-92" />
<hkern u1="W" u2="&#xec;" k="-43" />
<hkern u1="W" u2="i" k="10" />
<hkern u1="Y" u2="&#xef;" k="-78" />
<hkern u1="Y" u2="&#xee;" k="-84" />
<hkern u1="Y" u2="&#xec;" k="-78" />
<hkern u1="Y" u2="i" k="20" />
<hkern u1="_" u2="y" k="-41" />
<hkern u1="f" u2="&#xef;" k="-123" />
<hkern u1="f" u2="&#xee;" k="-109" />
<hkern u1="f" u2="&#xec;" k="-131" />
<hkern u1="i" u2="\" k="35" />
<hkern u1="j" u2="\" k="35" />
<hkern u1="q" u2="j" k="-102" />
<hkern u1="&#xa3;" u2="&#xef;" k="-27" />
<hkern u1="&#xa3;" u2="&#xee;" k="-68" />
<hkern u1="&#xaa;" u2="&#xee;" k="-164" />
<hkern u1="&#xba;" u2="&#xee;" k="-164" />
<hkern u1="&#xbf;" u2="y" k="53" />
<hkern u1="&#xdd;" u2="&#xef;" k="-78" />
<hkern u1="&#xdd;" u2="&#xee;" k="-84" />
<hkern u1="&#xdd;" u2="&#xec;" k="-78" />
<hkern u1="&#xdd;" u2="i" k="20" />
<hkern u1="&#xee;" u2="&#xba;" k="-164" />
<hkern u1="&#xee;" u2="&#xaa;" k="-164" />
<hkern u1="&#xee;" u2="&#x3f;" k="-82" />
<hkern u1="&#xee;" u2="&#x2a;" k="-164" />
<hkern u1="&#x178;" u2="&#xef;" k="-78" />
<hkern u1="&#x178;" u2="&#xee;" k="-84" />
<hkern u1="&#x178;" u2="&#xec;" k="-78" />
<hkern u1="&#x178;" u2="i" k="20" />
<hkern u1="&#x2018;" u2="&#xec;" k="-61" />
<hkern u1="&#x201c;" u2="&#xec;" k="-61" />
<hkern g1="ampersand" 	g2="ampersand" 	k="8" />
<hkern g1="ampersand" 	g2="backslash" 	k="164" />
<hkern g1="ampersand" 	g2="bracketright,braceright" 	k="27" />
<hkern g1="ampersand" 	g2="colon,semicolon" 	k="16" />
<hkern g1="ampersand" 	g2="degree" 	k="102" />
<hkern g1="ampersand" 	g2="exclam" 	k="27" />
<hkern g1="ampersand" 	g2="exclamdown" 	k="20" />
<hkern g1="ampersand" 	g2="four" 	k="-12" />
<hkern g1="ampersand" 	g2="one" 	k="61" />
<hkern g1="ampersand" 	g2="paragraph" 	k="115" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="6" />
<hkern g1="ampersand" 	g2="question" 	k="133" />
<hkern g1="ampersand" 	g2="questiondown" 	k="12" />
<hkern g1="ampersand" 	g2="quoteleft,quotedblleft" 	k="55" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="29" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="ampersand" 	g2="seven" 	k="29" />
<hkern g1="ampersand" 	g2="slash" 	k="-61" />
<hkern g1="ampersand" 	g2="three" 	k="12" />
<hkern g1="ampersand" 	g2="trademark" 	k="74" />
<hkern g1="ampersand" 	g2="two" 	k="27" />
<hkern g1="ampersand" 	g2="underscore" 	k="-43" />
<hkern g1="currency" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="currency" 	g2="questiondown" 	k="8" />
<hkern g1="currency" 	g2="seven" 	k="14" />
<hkern g1="currency" 	g2="two" 	k="14" />
<hkern g1="currency" 	g2="underscore" 	k="8" />
<hkern g1="currency" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-8" />
<hkern g1="degree" 	g2="backslash" 	k="-115" />
<hkern g1="degree" 	g2="four" 	k="82" />
<hkern g1="degree" 	g2="one" 	k="-94" />
<hkern g1="degree" 	g2="percent" 	k="-53" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="135" />
<hkern g1="degree" 	g2="question" 	k="-41" />
<hkern g1="degree" 	g2="questiondown" 	k="117" />
<hkern g1="degree" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="degree" 	g2="seven" 	k="-76" />
<hkern g1="degree" 	g2="slash" 	k="111" />
<hkern g1="degree" 	g2="three" 	k="-37" />
<hkern g1="degree" 	g2="two" 	k="-61" />
<hkern g1="degree" 	g2="five" 	k="-8" />
<hkern g1="degree" 	g2="nine" 	k="-70" />
<hkern g1="degree" 	g2="zero,six" 	k="-4" />
<hkern g1="percent" 	g2="backslash" 	k="70" />
<hkern g1="percent" 	g2="degree" 	k="49" />
<hkern g1="percent" 	g2="four" 	k="-66" />
<hkern g1="percent" 	g2="one" 	k="41" />
<hkern g1="percent" 	g2="percent" 	k="256" />
<hkern g1="percent" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="percent" 	g2="question" 	k="111" />
<hkern g1="percent" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="percent" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="percent" 	g2="quotedbl,quotesingle" 	k="49" />
<hkern g1="percent" 	g2="seven" 	k="29" />
<hkern g1="percent" 	g2="slash" 	k="-2" />
<hkern g1="percent" 	g2="three" 	k="-20" />
<hkern g1="percent" 	g2="two" 	k="-20" />
<hkern g1="percent" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="percent" 	g2="five" 	k="-41" />
<hkern g1="percent" 	g2="parenright" 	k="41" />
<hkern g1="percent" 	g2="eight" 	k="-41" />
<hkern g1="section" 	g2="four" 	k="-29" />
<hkern g1="section" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="section" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="section" 	g2="seven" 	k="-8" />
<hkern g1="section" 	g2="slash" 	k="-35" />
<hkern g1="section" 	g2="underscore" 	k="-8" />
<hkern g1="section" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="section" 	g2="eight" 	k="-20" />
<hkern g1="trademark" 	g2="backslash" 	k="-82" />
<hkern g1="trademark" 	g2="exclamdown" 	k="-12" />
<hkern g1="trademark" 	g2="questiondown" 	k="14" />
<hkern g1="trademark" 	g2="seven" 	k="-33" />
<hkern g1="trademark" 	g2="slash" 	k="76" />
<hkern g1="trademark" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="trademark" 	g2="nine" 	k="-61" />
<hkern g1="yen" 	g2="backslash" 	k="-102" />
<hkern g1="yen" 	g2="bracketright,braceright" 	k="-53" />
<hkern g1="yen" 	g2="colon,semicolon" 	k="82" />
<hkern g1="yen" 	g2="exclam" 	k="-41" />
<hkern g1="yen" 	g2="exclamdown" 	k="41" />
<hkern g1="yen" 	g2="four" 	k="20" />
<hkern g1="yen" 	g2="one" 	k="-61" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="yen" 	g2="questiondown" 	k="82" />
<hkern g1="yen" 	g2="quoteright,quotedblright" 	k="-27" />
<hkern g1="yen" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="yen" 	g2="seven" 	k="-49" />
<hkern g1="yen" 	g2="slash" 	k="61" />
<hkern g1="yen" 	g2="three" 	k="-20" />
<hkern g1="yen" 	g2="underscore" 	k="20" />
<hkern g1="yen" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="53" />
<hkern g1="yen" 	g2="zero,six" 	k="27" />
<hkern g1="yen" 	g2="parenright" 	k="-53" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="backslash" 	g2="ampersand" 	k="-29" />
<hkern g1="backslash" 	g2="backslash" 	k="150" />
<hkern g1="backslash" 	g2="bracketright,braceright" 	k="-88" />
<hkern g1="backslash" 	g2="colon,semicolon" 	k="-109" />
<hkern g1="backslash" 	g2="degree" 	k="123" />
<hkern g1="backslash" 	g2="exclamdown" 	k="-82" />
<hkern g1="backslash" 	g2="five" 	k="-12" />
<hkern g1="backslash" 	g2="four" 	k="8" />
<hkern g1="backslash" 	g2="guillemotright,guilsinglright" 	k="-10" />
<hkern g1="backslash" 	g2="one" 	k="41" />
<hkern g1="backslash" 	g2="paragraph" 	k="61" />
<hkern g1="backslash" 	g2="parenright" 	k="-61" />
<hkern g1="backslash" 	g2="percent" 	k="20" />
<hkern g1="backslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-184" />
<hkern g1="backslash" 	g2="question" 	k="123" />
<hkern g1="backslash" 	g2="questiondown" 	k="-61" />
<hkern g1="backslash" 	g2="quoteleft,quotedblleft" 	k="96" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="96" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="backslash" 	g2="section" 	k="-61" />
<hkern g1="backslash" 	g2="seven" 	k="102" />
<hkern g1="backslash" 	g2="slash" 	k="-14" />
<hkern g1="backslash" 	g2="three" 	k="-20" />
<hkern g1="backslash" 	g2="trademark" 	k="123" />
<hkern g1="backslash" 	g2="two" 	k="-20" />
<hkern g1="backslash" 	g2="underscore" 	k="-287" />
<hkern g1="backslash" 	g2="zero,six" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="backslash" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="exclamdown" 	k="-12" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="33" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="-47" />
<hkern g1="bracketleft,braceleft" 	g2="paragraph" 	k="-12" />
<hkern g1="bracketleft,braceleft" 	g2="parenright" 	k="-27" />
<hkern g1="bracketleft,braceleft" 	g2="question" 	k="-27" />
<hkern g1="bracketleft,braceleft" 	g2="quotedbl,quotesingle" 	k="-27" />
<hkern g1="bracketleft,braceleft" 	g2="seven" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="slash" 	k="-88" />
<hkern g1="bracketleft,braceleft" 	g2="three" 	k="-12" />
<hkern g1="bracketleft,braceleft" 	g2="trademark" 	k="-109" />
<hkern g1="bracketleft,braceleft" 	g2="two" 	k="-12" />
<hkern g1="bracketleft,braceleft" 	g2="underscore" 	k="-68" />
<hkern g1="bracketleft,braceleft" 	g2="exclam" 	k="-12" />
<hkern g1="bracketleft,braceleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="8" />
<hkern g1="bracketleft,braceleft" 	g2="yen" 	k="-53" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="43" />
<hkern g1="colon,semicolon" 	g2="question" 	k="20" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="slash" 	k="-109" />
<hkern g1="colon,semicolon" 	g2="underscore" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="yen" 	k="82" />
<hkern g1="exclam" 	g2="bracketright,braceright" 	k="-12" />
<hkern g1="exclam" 	g2="one" 	k="-20" />
<hkern g1="exclam" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-12" />
<hkern g1="exclam" 	g2="quoteleft,quotedblleft" 	k="-12" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="-12" />
<hkern g1="exclam" 	g2="quotedbl,quotesingle" 	k="-12" />
<hkern g1="exclam" 	g2="seven" 	k="-8" />
<hkern g1="exclam" 	g2="trademark" 	k="-27" />
<hkern g1="exclam" 	g2="yen" 	k="-41" />
<hkern g1="exclamdown" 	g2="backslash" 	k="113" />
<hkern g1="exclamdown" 	g2="bracketright,braceright" 	k="-12" />
<hkern g1="exclamdown" 	g2="one" 	k="41" />
<hkern g1="exclamdown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-12" />
<hkern g1="exclamdown" 	g2="question" 	k="12" />
<hkern g1="exclamdown" 	g2="quoteleft,quotedblleft" 	k="-12" />
<hkern g1="exclamdown" 	g2="quoteright,quotedblright" 	k="-12" />
<hkern g1="exclamdown" 	g2="slash" 	k="-82" />
<hkern g1="exclamdown" 	g2="trademark" 	k="12" />
<hkern g1="exclamdown" 	g2="underscore" 	k="-41" />
<hkern g1="exclamdown" 	g2="yen" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="111" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="five" 	k="-8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="-8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="one" 	k="12" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-12" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="question" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="questiondown" 	k="-8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="slash" 	k="-10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="underscore" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="backslash" 	k="133" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="four" 	k="-8" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="one" 	k="43" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="paragraph" 	k="172" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="percent" 	k="70" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="question" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="section" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="seven" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="three" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="two" 	k="33" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="zero,six" 	k="-8" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-12" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="yen" 	k="53" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="currency,Euro" 	k="-8" />
<hkern g1="parenleft" 	g2="backslash" 	k="-88" />
<hkern g1="parenleft" 	g2="bracketright,braceright" 	k="-27" />
<hkern g1="parenleft" 	g2="four" 	k="41" />
<hkern g1="parenleft" 	g2="one" 	k="-20" />
<hkern g1="parenleft" 	g2="slash" 	k="-74" />
<hkern g1="parenleft" 	g2="trademark" 	k="-68" />
<hkern g1="parenleft" 	g2="underscore" 	k="-12" />
<hkern g1="parenleft" 	g2="yen" 	k="-53" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="193" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="degree" 	k="135" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclamdown" 	k="-12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="guillemotright,guilsinglright" 	k="-12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="paragraph" 	k="293" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="135" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="115" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="questiondown" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="43" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="43" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="113" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="slash" 	k="-184" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="two" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="underscore" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero,six" 	k="27" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclam" 	k="-12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="currency,Euro" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="-20" />
<hkern g1="question" 	g2="ampersand" 	k="20" />
<hkern g1="question" 	g2="degree" 	k="-20" />
<hkern g1="question" 	g2="exclamdown" 	k="8" />
<hkern g1="question" 	g2="four" 	k="102" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="question" 	g2="question" 	k="35" />
<hkern g1="question" 	g2="questiondown" 	k="209" />
<hkern g1="question" 	g2="quoteleft,quotedblleft" 	k="-53" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="-53" />
<hkern g1="question" 	g2="slash" 	k="61" />
<hkern g1="question" 	g2="three" 	k="31" />
<hkern g1="question" 	g2="underscore" 	k="31" />
<hkern g1="question" 	g2="exclam" 	k="8" />
<hkern g1="question" 	g2="yen" 	k="31" />
<hkern g1="question" 	g2="eight" 	k="20" />
<hkern g1="question" 	g2="bracketleft" 	k="16" />
<hkern g1="question" 	g2="nine" 	k="-20" />
<hkern g1="questiondown" 	g2="ampersand" 	k="29" />
<hkern g1="questiondown" 	g2="backslash" 	k="174" />
<hkern g1="questiondown" 	g2="bracketright,braceright" 	k="-27" />
<hkern g1="questiondown" 	g2="colon,semicolon" 	k="12" />
<hkern g1="questiondown" 	g2="degree" 	k="129" />
<hkern g1="questiondown" 	g2="four" 	k="94" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="questiondown" 	g2="one" 	k="94" />
<hkern g1="questiondown" 	g2="paragraph" 	k="186" />
<hkern g1="questiondown" 	g2="percent" 	k="123" />
<hkern g1="questiondown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-12" />
<hkern g1="questiondown" 	g2="question" 	k="279" />
<hkern g1="questiondown" 	g2="questiondown" 	k="29" />
<hkern g1="questiondown" 	g2="quoteleft,quotedblleft" 	k="53" />
<hkern g1="questiondown" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle" 	k="94" />
<hkern g1="questiondown" 	g2="section" 	k="-12" />
<hkern g1="questiondown" 	g2="seven" 	k="94" />
<hkern g1="questiondown" 	g2="slash" 	k="-82" />
<hkern g1="questiondown" 	g2="trademark" 	k="88" />
<hkern g1="questiondown" 	g2="underscore" 	k="-102" />
<hkern g1="questiondown" 	g2="zero,six" 	k="109" />
<hkern g1="questiondown" 	g2="exclam" 	k="12" />
<hkern g1="questiondown" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="82" />
<hkern g1="questiondown" 	g2="yen" 	k="102" />
<hkern g1="questiondown" 	g2="sterling" 	k="-27" />
<hkern g1="questiondown" 	g2="currency,Euro" 	k="20" />
<hkern g1="questiondown" 	g2="eight" 	k="94" />
<hkern g1="quoteleft,quotedblleft" 	g2="ampersand" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="backslash" 	k="-96" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="-12" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="70" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-70" />
<hkern g1="quoteleft,quotedblleft" 	g2="paragraph" 	k="-70" />
<hkern g1="quoteleft,quotedblleft" 	g2="percent" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="question" 	k="-70" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteleft,quotedblleft" 	k="-68" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteright,quotedblright" 	k="-68" />
<hkern g1="quoteleft,quotedblleft" 	g2="section" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-76" />
<hkern g1="quoteleft,quotedblleft" 	g2="slash" 	k="96" />
<hkern g1="quoteleft,quotedblleft" 	g2="three" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="trademark" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="two" 	k="-14" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclam" 	k="-12" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="nine" 	k="-55" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="backslash" 	k="-96" />
<hkern g1="quoteright,quotedblright" 	g2="exclamdown" 	k="-12" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="119" />
<hkern g1="quoteright,quotedblright" 	g2="one" 	k="-90" />
<hkern g1="quoteright,quotedblright" 	g2="paragraph" 	k="-70" />
<hkern g1="quoteright,quotedblright" 	g2="percent" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="43" />
<hkern g1="quoteright,quotedblright" 	g2="question" 	k="-70" />
<hkern g1="quoteright,quotedblright" 	g2="questiondown" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="quoteleft,quotedblleft" 	k="-68" />
<hkern g1="quoteright,quotedblright" 	g2="quoteright,quotedblright" 	k="-68" />
<hkern g1="quoteright,quotedblright" 	g2="section" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-49" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="96" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="-29" />
<hkern g1="quoteright,quotedblright" 	g2="trademark" 	k="-74" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="exclam" 	k="-12" />
<hkern g1="quoteright,quotedblright" 	g2="yen" 	k="-27" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="backslash" 	k="-123" />
<hkern g1="quotedbl,quotesingle" 	g2="bracketright,braceright" 	k="-27" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="degree" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="five" 	k="-8" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-90" />
<hkern g1="quotedbl,quotesingle" 	g2="paragraph" 	k="-70" />
<hkern g1="quotedbl,quotesingle" 	g2="percent" 	k="-49" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="quotedbl,quotesingle" 	g2="question" 	k="-55" />
<hkern g1="quotedbl,quotesingle" 	g2="questiondown" 	k="61" />
<hkern g1="quotedbl,quotesingle" 	g2="quotedbl,quotesingle" 	k="-92" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-82" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-29" />
<hkern g1="quotedbl,quotesingle" 	g2="trademark" 	k="-70" />
<hkern g1="quotedbl,quotesingle" 	g2="two" 	k="-35" />
<hkern g1="quotedbl,quotesingle" 	g2="underscore" 	k="8" />
<hkern g1="quotedbl,quotesingle" 	g2="zero,six" 	k="-8" />
<hkern g1="quotedbl,quotesingle" 	g2="exclam" 	k="-12" />
<hkern g1="quotedbl,quotesingle" 	g2="yen" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="currency,Euro" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="-4" />
<hkern g1="quotedbl,quotesingle" 	g2="nine" 	k="-76" />
<hkern g1="slash" 	g2="ampersand" 	k="70" />
<hkern g1="slash" 	g2="backslash" 	k="-29" />
<hkern g1="slash" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="31" />
<hkern g1="slash" 	g2="degree" 	k="-90" />
<hkern g1="slash" 	g2="exclamdown" 	k="133" />
<hkern g1="slash" 	g2="four" 	k="131" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="82" />
<hkern g1="slash" 	g2="one" 	k="-61" />
<hkern g1="slash" 	g2="parenright" 	k="-74" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="193" />
<hkern g1="slash" 	g2="questiondown" 	k="154" />
<hkern g1="slash" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="slash" 	g2="quoteright,quotedblright" 	k="-96" />
<hkern g1="slash" 	g2="quotedbl,quotesingle" 	k="-123" />
<hkern g1="slash" 	g2="section" 	k="41" />
<hkern g1="slash" 	g2="seven" 	k="-14" />
<hkern g1="slash" 	g2="slash" 	k="150" />
<hkern g1="slash" 	g2="trademark" 	k="-90" />
<hkern g1="slash" 	g2="underscore" 	k="240" />
<hkern g1="slash" 	g2="zero,six" 	k="41" />
<hkern g1="slash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="145" />
<hkern g1="slash" 	g2="yen" 	k="-82" />
<hkern g1="slash" 	g2="sterling" 	k="20" />
<hkern g1="slash" 	g2="eight" 	k="53" />
<hkern g1="underscore" 	g2="backslash" 	k="266" />
<hkern g1="underscore" 	g2="bracketright,braceright" 	k="-68" />
<hkern g1="underscore" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="underscore" 	g2="exclamdown" 	k="-41" />
<hkern g1="underscore" 	g2="five" 	k="-20" />
<hkern g1="underscore" 	g2="four" 	k="49" />
<hkern g1="underscore" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="underscore" 	g2="one" 	k="14" />
<hkern g1="underscore" 	g2="paragraph" 	k="106" />
<hkern g1="underscore" 	g2="parenright" 	k="-12" />
<hkern g1="underscore" 	g2="percent" 	k="49" />
<hkern g1="underscore" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="underscore" 	g2="question" 	k="37" />
<hkern g1="underscore" 	g2="quotedbl,quotesingle" 	k="8" />
<hkern g1="underscore" 	g2="section" 	k="-49" />
<hkern g1="underscore" 	g2="slash" 	k="-246" />
<hkern g1="underscore" 	g2="three" 	k="-49" />
<hkern g1="underscore" 	g2="trademark" 	k="102" />
<hkern g1="underscore" 	g2="two" 	k="-55" />
<hkern g1="underscore" 	g2="zero,six" 	k="41" />
<hkern g1="underscore" 	g2="yen" 	k="20" />
<hkern g1="underscore" 	g2="sterling" 	k="-29" />
<hkern g1="underscore" 	g2="currency,Euro" 	k="8" />
<hkern g1="underscore" 	g2="nine" 	k="-20" />
<hkern g1="eight" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="10" />
<hkern g1="eight" 	g2="J" 	k="4" />
<hkern g1="eight" 	g2="T" 	k="20" />
<hkern g1="eight" 	g2="V,W" 	k="37" />
<hkern g1="eight" 	g2="X" 	k="47" />
<hkern g1="eight" 	g2="Y,Yacute,Ydieresis" 	k="47" />
<hkern g1="eight" 	g2="Z" 	k="4" />
<hkern g1="eight" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-8" />
<hkern g1="eight" 	g2="backslash" 	k="61" />
<hkern g1="eight" 	g2="j" 	k="16" />
<hkern g1="eight" 	g2="percent" 	k="20" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="eight" 	g2="question" 	k="20" />
<hkern g1="eight" 	g2="questiondown" 	k="41" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="-4" />
<hkern g1="eight" 	g2="section" 	k="-20" />
<hkern g1="eight" 	g2="x" 	k="20" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="five" 	g2="J" 	k="20" />
<hkern g1="five" 	g2="T" 	k="20" />
<hkern g1="five" 	g2="V,W" 	k="16" />
<hkern g1="five" 	g2="X" 	k="27" />
<hkern g1="five" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="five" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="five" 	g2="backslash" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="20" />
<hkern g1="five" 	g2="x" 	k="23" />
<hkern g1="five" 	g2="degree" 	k="14" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="seven" 	k="25" />
<hkern g1="five" 	g2="three" 	k="10" />
<hkern g1="five" 	g2="two" 	k="10" />
<hkern g1="five" 	g2="v,w,y,yacute,ydieresis" 	k="23" />
<hkern g1="four" 	g2="J" 	k="-12" />
<hkern g1="four" 	g2="T" 	k="109" />
<hkern g1="four" 	g2="V,W" 	k="115" />
<hkern g1="four" 	g2="X" 	k="23" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="180" />
<hkern g1="four" 	g2="Z" 	k="8" />
<hkern g1="four" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="percent" 	k="61" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="four" 	g2="question" 	k="115" />
<hkern g1="four" 	g2="questiondown" 	k="-20" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="53" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="four" 	g2="section" 	k="-51" />
<hkern g1="four" 	g2="x" 	k="33" />
<hkern g1="four" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="degree" 	k="61" />
<hkern g1="four" 	g2="five" 	k="4" />
<hkern g1="four" 	g2="seven" 	k="86" />
<hkern g1="four" 	g2="three" 	k="14" />
<hkern g1="four" 	g2="two" 	k="4" />
<hkern g1="four" 	g2="v,w,y,yacute,ydieresis" 	k="47" />
<hkern g1="four" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="four" 	g2="dollar,S" 	k="8" />
<hkern g1="four" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-55" />
<hkern g1="four" 	g2="slash" 	k="-49" />
<hkern g1="four" 	g2="underscore" 	k="-41" />
<hkern g1="four" 	g2="ampersand" 	k="-41" />
<hkern g1="four" 	g2="colon,semicolon" 	k="-16" />
<hkern g1="four" 	g2="currency,Euro" 	k="-20" />
<hkern g1="four" 	g2="eight" 	k="-20" />
<hkern g1="four" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="four" 	g2="sterling" 	k="-20" />
<hkern g1="four" 	g2="nine" 	k="8" />
<hkern g1="four" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-33" />
<hkern g1="four" 	g2="one" 	k="51" />
<hkern g1="four" 	g2="paragraph" 	k="47" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="four" 	g2="t,uniFB01,uniFB02" 	k="-6" />
<hkern g1="four" 	g2="trademark" 	k="41" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="123" />
<hkern g1="seven" 	g2="J" 	k="49" />
<hkern g1="seven" 	g2="V,W" 	k="-10" />
<hkern g1="seven" 	g2="X" 	k="4" />
<hkern g1="seven" 	g2="Y,Yacute,Ydieresis" 	k="-23" />
<hkern g1="seven" 	g2="Z" 	k="8" />
<hkern g1="seven" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-49" />
<hkern g1="seven" 	g2="backslash" 	k="-20" />
<hkern g1="seven" 	g2="j" 	k="45" />
<hkern g1="seven" 	g2="percent" 	k="-12" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="100" />
<hkern g1="seven" 	g2="question" 	k="-31" />
<hkern g1="seven" 	g2="questiondown" 	k="117" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-61" />
<hkern g1="seven" 	g2="section" 	k="20" />
<hkern g1="seven" 	g2="x" 	k="20" />
<hkern g1="seven" 	g2="yen" 	k="-33" />
<hkern g1="seven" 	g2="degree" 	k="-20" />
<hkern g1="seven" 	g2="five" 	k="35" />
<hkern g1="seven" 	g2="three" 	k="14" />
<hkern g1="seven" 	g2="v,w,y,yacute,ydieresis" 	k="-6" />
<hkern g1="seven" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="seven" 	g2="dollar,S" 	k="8" />
<hkern g1="seven" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="76" />
<hkern g1="seven" 	g2="slash" 	k="80" />
<hkern g1="seven" 	g2="underscore" 	k="61" />
<hkern g1="seven" 	g2="ampersand" 	k="55" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="41" />
<hkern g1="seven" 	g2="currency,Euro" 	k="41" />
<hkern g1="seven" 	g2="eight" 	k="35" />
<hkern g1="seven" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="102" />
<hkern g1="seven" 	g2="sterling" 	k="25" />
<hkern g1="seven" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="seven" 	g2="one" 	k="-20" />
<hkern g1="seven" 	g2="paragraph" 	k="-49" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="seven" 	g2="t,uniFB01,uniFB02" 	k="14" />
<hkern g1="seven" 	g2="trademark" 	k="-82" />
<hkern g1="seven" 	g2="exclamdown" 	k="49" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="29" />
<hkern g1="seven" 	g2="s" 	k="61" />
<hkern g1="seven" 	g2="z" 	k="45" />
<hkern g1="seven" 	g2="zero,six" 	k="41" />
<hkern g1="seven" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="seven" 	g2="four" 	k="133" />
<hkern g1="seven" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="41" />
<hkern g1="seven" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="49" />
<hkern g1="six" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-8" />
<hkern g1="six" 	g2="T" 	k="20" />
<hkern g1="six" 	g2="V,W" 	k="20" />
<hkern g1="six" 	g2="X" 	k="8" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="six" 	g2="asterisk,ordfeminine,ordmasculine" 	k="14" />
<hkern g1="six" 	g2="j" 	k="16" />
<hkern g1="six" 	g2="percent" 	k="41" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="six" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="8" />
<hkern g1="six" 	g2="section" 	k="-41" />
<hkern g1="six" 	g2="x" 	k="31" />
<hkern g1="six" 	g2="degree" 	k="31" />
<hkern g1="six" 	g2="seven" 	k="8" />
<hkern g1="six" 	g2="v,w,y,yacute,ydieresis" 	k="31" />
<hkern g1="six" 	g2="slash" 	k="-20" />
<hkern g1="six" 	g2="ampersand" 	k="-20" />
<hkern g1="six" 	g2="currency,Euro" 	k="-20" />
<hkern g1="six" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="six" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-14" />
<hkern g1="three" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-4" />
<hkern g1="three" 	g2="T" 	k="16" />
<hkern g1="three" 	g2="V,W" 	k="20" />
<hkern g1="three" 	g2="X" 	k="20" />
<hkern g1="three" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="three" 	g2="j" 	k="14" />
<hkern g1="three" 	g2="percent" 	k="20" />
<hkern g1="three" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="three" 	g2="x" 	k="33" />
<hkern g1="three" 	g2="degree" 	k="31" />
<hkern g1="three" 	g2="five" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="18" />
<hkern g1="three" 	g2="three" 	k="10" />
<hkern g1="three" 	g2="two" 	k="10" />
<hkern g1="three" 	g2="v,w,y,yacute,ydieresis" 	k="16" />
<hkern g1="two" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-12" />
<hkern g1="two" 	g2="J" 	k="-33" />
<hkern g1="two" 	g2="T" 	k="8" />
<hkern g1="two" 	g2="V,W" 	k="27" />
<hkern g1="two" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="two" 	g2="backslash" 	k="8" />
<hkern g1="two" 	g2="percent" 	k="-20" />
<hkern g1="two" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="two" 	g2="x" 	k="8" />
<hkern g1="two" 	g2="seven" 	k="4" />
<hkern g1="two" 	g2="v,w,y,yacute,ydieresis" 	k="-4" />
<hkern g1="two" 	g2="slash" 	k="-20" />
<hkern g1="two" 	g2="underscore" 	k="-61" />
<hkern g1="two" 	g2="ampersand" 	k="8" />
<hkern g1="two" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="8" />
<hkern g1="two" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="two" 	g2="z" 	k="8" />
<hkern g1="two" 	g2="zero,six" 	k="4" />
<hkern g1="two" 	g2="four" 	k="20" />
<hkern g1="two" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="4" />
<hkern g1="zero,nine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="23" />
<hkern g1="zero,nine" 	g2="J" 	k="53" />
<hkern g1="zero,nine" 	g2="T" 	k="41" />
<hkern g1="zero,nine" 	g2="V,W" 	k="51" />
<hkern g1="zero,nine" 	g2="X" 	k="74" />
<hkern g1="zero,nine" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="zero,nine" 	g2="Z" 	k="31" />
<hkern g1="zero,nine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-8" />
<hkern g1="zero,nine" 	g2="backslash" 	k="41" />
<hkern g1="zero,nine" 	g2="j" 	k="16" />
<hkern g1="zero,nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="27" />
<hkern g1="zero,nine" 	g2="question" 	k="41" />
<hkern g1="zero,nine" 	g2="questiondown" 	k="61" />
<hkern g1="zero,nine" 	g2="quotedbl,quotesingle" 	k="-8" />
<hkern g1="zero,nine" 	g2="x" 	k="20" />
<hkern g1="zero,nine" 	g2="yen" 	k="27" />
<hkern g1="zero,nine" 	g2="degree" 	k="-4" />
<hkern g1="zero,nine" 	g2="seven" 	k="8" />
<hkern g1="zero,nine" 	g2="three" 	k="20" />
<hkern g1="zero,nine" 	g2="two" 	k="12" />
<hkern g1="zero,nine" 	g2="v,w,y,yacute,ydieresis" 	k="-8" />
<hkern g1="zero,nine" 	g2="slash" 	k="41" />
<hkern g1="zero,nine" 	g2="underscore" 	k="41" />
<hkern g1="zero,nine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-8" />
<hkern g1="zero,nine" 	g2="one" 	k="12" />
<hkern g1="zero,nine" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V,W" 	k="78" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="74" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="88" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,ordfeminine,ordmasculine" 	k="197" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="degree" 	k="197" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="eight" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="exclamdown" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="four" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotright,guilsinglright" 	k="-14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="nine" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="88" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="paragraph" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-66" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="questiondown" 	k="-41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="74" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="74" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="86" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="seven" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t,uniFB01,uniFB02" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="three" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="156" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="underscore" 	k="-111" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v,w,y,yacute,ydieresis" 	k="57" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="74" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="z" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="zero,six" 	k="23" />
<hkern g1="B" 	g2="T" 	k="20" />
<hkern g1="B" 	g2="V,W" 	k="10" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-12" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="B" 	g2="trademark" 	k="12" />
<hkern g1="B" 	g2="underscore" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="29" />
<hkern g1="C,Ccedilla,Euro" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="T" 	k="14" />
<hkern g1="C,Ccedilla,Euro" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="12" />
<hkern g1="C,Ccedilla,Euro" 	g2="V,W" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="X" 	k="29" />
<hkern g1="C,Ccedilla,Euro" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="C,Ccedilla,Euro" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="C,Ccedilla,Euro" 	g2="backslash" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="colon,semicolon" 	k="16" />
<hkern g1="C,Ccedilla,Euro" 	g2="degree" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="eight" 	k="25" />
<hkern g1="C,Ccedilla,Euro" 	g2="exclamdown" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="four" 	k="29" />
<hkern g1="C,Ccedilla,Euro" 	g2="guillemotright,guilsinglright" 	k="12" />
<hkern g1="C,Ccedilla,Euro" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="12" />
<hkern g1="C,Ccedilla,Euro" 	g2="nine" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="C,Ccedilla,Euro" 	g2="one" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="paragraph" 	k="-8" />
<hkern g1="C,Ccedilla,Euro" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-25" />
<hkern g1="C,Ccedilla,Euro" 	g2="questiondown" 	k="14" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteleft,quotedblleft" 	k="2" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="s" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="three" 	k="4" />
<hkern g1="C,Ccedilla,Euro" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="29" />
<hkern g1="C,Ccedilla,Euro" 	g2="underscore" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="x" 	k="29" />
<hkern g1="C,Ccedilla,Euro" 	g2="z" 	k="29" />
<hkern g1="C,Ccedilla,Euro" 	g2="zero,six" 	k="39" />
<hkern g1="C,Ccedilla,Euro" 	g2="dollar,S" 	k="12" />
<hkern g1="C,Ccedilla,Euro" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="ampersand" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="section" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="two" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="V,W" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="X" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="colon,semicolon" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="eight" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="exclamdown" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="four" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="nine" 	k="4" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="question" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="seven" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t,uniFB01,uniFB02" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="three" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="underscore" 	k="-14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,w,y,yacute,ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="z" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="zero,six" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="five" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="section" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="two" 	k="8" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="59" />
<hkern g1="F" 	g2="J" 	k="31" />
<hkern g1="F" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="F" 	g2="T" 	k="-10" />
<hkern g1="F" 	g2="X" 	k="29" />
<hkern g1="F" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="F" 	g2="backslash" 	k="-20" />
<hkern g1="F" 	g2="degree" 	k="-61" />
<hkern g1="F" 	g2="eight" 	k="29" />
<hkern g1="F" 	g2="exclamdown" 	k="20" />
<hkern g1="F" 	g2="four" 	k="86" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="F" 	g2="nine" 	k="-16" />
<hkern g1="F" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="F" 	g2="one" 	k="-31" />
<hkern g1="F" 	g2="paragraph" 	k="-20" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="F" 	g2="question" 	k="-6" />
<hkern g1="F" 	g2="questiondown" 	k="90" />
<hkern g1="F" 	g2="quoteleft,quotedblleft" 	k="-29" />
<hkern g1="F" 	g2="quoteright,quotedblright" 	k="-49" />
<hkern g1="F" 	g2="quotedbl,quotesingle" 	k="-82" />
<hkern g1="F" 	g2="s" 	k="20" />
<hkern g1="F" 	g2="seven" 	k="-20" />
<hkern g1="F" 	g2="slash" 	k="80" />
<hkern g1="F" 	g2="trademark" 	k="-61" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="29" />
<hkern g1="F" 	g2="underscore" 	k="41" />
<hkern g1="F" 	g2="v,w,y,yacute,ydieresis" 	k="-10" />
<hkern g1="F" 	g2="x" 	k="31" />
<hkern g1="F" 	g2="z" 	k="29" />
<hkern g1="F" 	g2="zero,six" 	k="25" />
<hkern g1="F" 	g2="dollar,S" 	k="4" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="F" 	g2="ampersand" 	k="45" />
<hkern g1="F" 	g2="five" 	k="29" />
<hkern g1="F" 	g2="section" 	k="8" />
<hkern g1="F" 	g2="two" 	k="-10" />
<hkern g1="F" 	g2="parenright" 	k="8" />
<hkern g1="F" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="F" 	g2="exclam" 	k="-27" />
<hkern g1="F" 	g2="j" 	k="31" />
<hkern g1="F" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="sterling" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="92" />
<hkern g1="sterling" 	g2="J" 	k="41" />
<hkern g1="sterling" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="sterling" 	g2="X" 	k="20" />
<hkern g1="sterling" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="sterling" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-29" />
<hkern g1="sterling" 	g2="backslash" 	k="-29" />
<hkern g1="sterling" 	g2="colon,semicolon" 	k="61" />
<hkern g1="sterling" 	g2="eight" 	k="66" />
<hkern g1="sterling" 	g2="exclamdown" 	k="51" />
<hkern g1="sterling" 	g2="four" 	k="51" />
<hkern g1="sterling" 	g2="guillemotright,guilsinglright" 	k="33" />
<hkern g1="sterling" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="sterling" 	g2="nine" 	k="41" />
<hkern g1="sterling" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="sterling" 	g2="one" 	k="-29" />
<hkern g1="sterling" 	g2="paragraph" 	k="-14" />
<hkern g1="sterling" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="12" />
<hkern g1="sterling" 	g2="questiondown" 	k="39" />
<hkern g1="sterling" 	g2="quoteleft,quotedblleft" 	k="12" />
<hkern g1="sterling" 	g2="quoteright,quotedblright" 	k="-12" />
<hkern g1="sterling" 	g2="s" 	k="82" />
<hkern g1="sterling" 	g2="seven" 	k="-8" />
<hkern g1="sterling" 	g2="slash" 	k="-27" />
<hkern g1="sterling" 	g2="t,uniFB01,uniFB02" 	k="41" />
<hkern g1="sterling" 	g2="three" 	k="20" />
<hkern g1="sterling" 	g2="trademark" 	k="-55" />
<hkern g1="sterling" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="49" />
<hkern g1="sterling" 	g2="underscore" 	k="-29" />
<hkern g1="sterling" 	g2="v,w,y,yacute,ydieresis" 	k="41" />
<hkern g1="sterling" 	g2="x" 	k="51" />
<hkern g1="sterling" 	g2="z" 	k="82" />
<hkern g1="sterling" 	g2="zero,six" 	k="70" />
<hkern g1="sterling" 	g2="dollar,S" 	k="20" />
<hkern g1="sterling" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="sterling" 	g2="ampersand" 	k="66" />
<hkern g1="sterling" 	g2="five" 	k="33" />
<hkern g1="sterling" 	g2="section" 	k="20" />
<hkern g1="sterling" 	g2="two" 	k="20" />
<hkern g1="sterling" 	g2="bracketright,braceright" 	k="-12" />
<hkern g1="sterling" 	g2="j" 	k="51" />
<hkern g1="sterling" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="41" />
<hkern g1="G" 	g2="backslash" 	k="29" />
<hkern g1="G" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="H,I,M,N,paragraph,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="V,W" 	k="8" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="63" />
<hkern g1="K" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="K" 	g2="T" 	k="98" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="K" 	g2="V,W" 	k="82" />
<hkern g1="K" 	g2="X" 	k="84" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="K" 	g2="degree" 	k="20" />
<hkern g1="K" 	g2="eight" 	k="47" />
<hkern g1="K" 	g2="four" 	k="90" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="12" />
<hkern g1="K" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="74" />
<hkern g1="K" 	g2="nine" 	k="20" />
<hkern g1="K" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="K" 	g2="one" 	k="-41" />
<hkern g1="K" 	g2="paragraph" 	k="27" />
<hkern g1="K" 	g2="question" 	k="82" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="47" />
<hkern g1="K" 	g2="s" 	k="29" />
<hkern g1="K" 	g2="slash" 	k="-8" />
<hkern g1="K" 	g2="t,uniFB01,uniFB02" 	k="49" />
<hkern g1="K" 	g2="three" 	k="8" />
<hkern g1="K" 	g2="trademark" 	k="-41" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="35" />
<hkern g1="K" 	g2="underscore" 	k="-131" />
<hkern g1="K" 	g2="v,w,y,yacute,ydieresis" 	k="78" />
<hkern g1="K" 	g2="x" 	k="92" />
<hkern g1="K" 	g2="z" 	k="53" />
<hkern g1="K" 	g2="zero,six" 	k="47" />
<hkern g1="K" 	g2="dollar,S" 	k="31" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="two" 	k="-6" />
<hkern g1="K" 	g2="bracketright,braceright" 	k="-27" />
<hkern g1="K" 	g2="j" 	k="4" />
<hkern g1="K" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="K" 	g2="bracketleft" 	k="29" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-12" />
<hkern g1="L" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="L" 	g2="T" 	k="133" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="L" 	g2="V,W" 	k="100" />
<hkern g1="L" 	g2="X" 	k="20" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="147" />
<hkern g1="L" 	g2="asterisk,ordfeminine,ordmasculine" 	k="143" />
<hkern g1="L" 	g2="backslash" 	k="143" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="L" 	g2="degree" 	k="133" />
<hkern g1="L" 	g2="eight" 	k="8" />
<hkern g1="L" 	g2="four" 	k="35" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-8" />
<hkern g1="L" 	g2="nine" 	k="20" />
<hkern g1="L" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="L" 	g2="one" 	k="70" />
<hkern g1="L" 	g2="paragraph" 	k="170" />
<hkern g1="L" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="L" 	g2="question" 	k="178" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="94" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="98" />
<hkern g1="L" 	g2="seven" 	k="102" />
<hkern g1="L" 	g2="slash" 	k="-35" />
<hkern g1="L" 	g2="t,uniFB01,uniFB02" 	k="10" />
<hkern g1="L" 	g2="three" 	k="-12" />
<hkern g1="L" 	g2="trademark" 	k="150" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="8" />
<hkern g1="L" 	g2="underscore" 	k="-102" />
<hkern g1="L" 	g2="v,w,y,yacute,ydieresis" 	k="76" />
<hkern g1="L" 	g2="x" 	k="8" />
<hkern g1="L" 	g2="zero,six" 	k="35" />
<hkern g1="L" 	g2="dollar,S" 	k="8" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-12" />
<hkern g1="L" 	g2="ampersand" 	k="-12" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V,W" 	k="47" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="51" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="degree" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-8" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="questiondown" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="35" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="27" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="33" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="-14" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketright,braceright" 	k="-16" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="j" 	k="-164" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="14" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="92" />
<hkern g1="P" 	g2="J" 	k="66" />
<hkern g1="P" 	g2="T" 	k="59" />
<hkern g1="P" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="27" />
<hkern g1="P" 	g2="V,W" 	k="37" />
<hkern g1="P" 	g2="X" 	k="51" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="P" 	g2="backslash" 	k="41" />
<hkern g1="P" 	g2="degree" 	k="-41" />
<hkern g1="P" 	g2="eight" 	k="16" />
<hkern g1="P" 	g2="four" 	k="61" />
<hkern g1="P" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="12" />
<hkern g1="P" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="23" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="55" />
<hkern g1="P" 	g2="questiondown" 	k="100" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="slash" 	k="90" />
<hkern g1="P" 	g2="t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="P" 	g2="three" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="74" />
<hkern g1="P" 	g2="v,w,y,yacute,ydieresis" 	k="8" />
<hkern g1="P" 	g2="x" 	k="66" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="P" 	g2="ampersand" 	k="59" />
<hkern g1="P" 	g2="five" 	k="20" />
<hkern g1="P" 	g2="two" 	k="10" />
<hkern g1="P" 	g2="parenright" 	k="10" />
<hkern g1="P" 	g2="bracketright,braceright" 	k="10" />
<hkern g1="P" 	g2="j" 	k="20" />
<hkern g1="P" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="P" 	g2="Z" 	k="29" />
<hkern g1="P" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="20" />
<hkern g1="R" 	g2="J" 	k="20" />
<hkern g1="R" 	g2="T" 	k="20" />
<hkern g1="R" 	g2="X" 	k="41" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="degree" 	k="-31" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="6" />
<hkern g1="R" 	g2="question" 	k="10" />
<hkern g1="R" 	g2="questiondown" 	k="20" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="R" 	g2="t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="R" 	g2="three" 	k="8" />
<hkern g1="R" 	g2="underscore" 	k="-29" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="dollar,S" 	g2="T" 	k="20" />
<hkern g1="dollar,S" 	g2="X" 	k="25" />
<hkern g1="dollar,S" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="dollar,S" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="20" />
<hkern g1="dollar,S" 	g2="colon,semicolon" 	k="10" />
<hkern g1="dollar,S" 	g2="degree" 	k="31" />
<hkern g1="dollar,S" 	g2="exclamdown" 	k="6" />
<hkern g1="dollar,S" 	g2="four" 	k="-20" />
<hkern g1="dollar,S" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-29" />
<hkern g1="dollar,S" 	g2="nine" 	k="20" />
<hkern g1="dollar,S" 	g2="one" 	k="20" />
<hkern g1="dollar,S" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="questiondown" 	k="20" />
<hkern g1="dollar,S" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="dollar,S" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="dollar,S" 	g2="seven" 	k="20" />
<hkern g1="dollar,S" 	g2="three" 	k="8" />
<hkern g1="dollar,S" 	g2="underscore" 	k="41" />
<hkern g1="dollar,S" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="dollar,S" 	g2="x" 	k="20" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="72" />
<hkern g1="T" 	g2="J" 	k="20" />
<hkern g1="T" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="T" 	k="-20" />
<hkern g1="T" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="T" 	g2="V,W" 	k="27" />
<hkern g1="T" 	g2="X" 	k="61" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="27" />
<hkern g1="T" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="T" 	g2="backslash" 	k="-61" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="degree" 	k="-82" />
<hkern g1="T" 	g2="eight" 	k="20" />
<hkern g1="T" 	g2="exclamdown" 	k="41" />
<hkern g1="T" 	g2="four" 	k="172" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="33" />
<hkern g1="T" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="74" />
<hkern g1="T" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="one" 	k="-82" />
<hkern g1="T" 	g2="paragraph" 	k="-61" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="68" />
<hkern g1="T" 	g2="question" 	k="-41" />
<hkern g1="T" 	g2="questiondown" 	k="106" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-49" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-49" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-27" />
<hkern g1="T" 	g2="s" 	k="76" />
<hkern g1="T" 	g2="seven" 	k="-41" />
<hkern g1="T" 	g2="slash" 	k="41" />
<hkern g1="T" 	g2="t,uniFB01,uniFB02" 	k="-12" />
<hkern g1="T" 	g2="three" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-102" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="T" 	g2="v,w,y,yacute,ydieresis" 	k="66" />
<hkern g1="T" 	g2="x" 	k="66" />
<hkern g1="T" 	g2="z" 	k="20" />
<hkern g1="T" 	g2="zero,six" 	k="41" />
<hkern g1="T" 	g2="dollar,S" 	k="10" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="55" />
<hkern g1="T" 	g2="ampersand" 	k="55" />
<hkern g1="T" 	g2="five" 	k="16" />
<hkern g1="T" 	g2="two" 	k="-47" />
<hkern g1="T" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="T" 	g2="exclam" 	k="-41" />
<hkern g1="T" 	g2="j" 	k="-88" />
<hkern g1="T" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="25" />
<hkern g1="T" 	g2="parenleft" 	k="16" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="61" />
<hkern g1="Thorn" 	g2="J" 	k="82" />
<hkern g1="Thorn" 	g2="T" 	k="51" />
<hkern g1="Thorn" 	g2="V,W" 	k="20" />
<hkern g1="Thorn" 	g2="X" 	k="61" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="one" 	k="41" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="45" />
<hkern g1="Thorn" 	g2="question" 	k="61" />
<hkern g1="Thorn" 	g2="questiondown" 	k="51" />
<hkern g1="Thorn" 	g2="slash" 	k="61" />
<hkern g1="Thorn" 	g2="t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="Thorn" 	g2="three" 	k="41" />
<hkern g1="Thorn" 	g2="trademark" 	k="12" />
<hkern g1="Thorn" 	g2="underscore" 	k="61" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="two" 	k="51" />
<hkern g1="Thorn" 	g2="j" 	k="20" />
<hkern g1="Thorn" 	g2="Z" 	k="35" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="31" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="T" 	k="25" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="questiondown" 	k="8" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="underscore" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="8" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="j" 	k="10" />
<hkern g1="V,W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="78" />
<hkern g1="V,W" 	g2="J" 	k="31" />
<hkern g1="V,W" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="47" />
<hkern g1="V,W" 	g2="T" 	k="27" />
<hkern g1="V,W" 	g2="X" 	k="53" />
<hkern g1="V,W" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="V,W" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-66" />
<hkern g1="V,W" 	g2="backslash" 	k="-61" />
<hkern g1="V,W" 	g2="colon,semicolon" 	k="53" />
<hkern g1="V,W" 	g2="degree" 	k="-49" />
<hkern g1="V,W" 	g2="eight" 	k="37" />
<hkern g1="V,W" 	g2="exclamdown" 	k="61" />
<hkern g1="V,W" 	g2="four" 	k="156" />
<hkern g1="V,W" 	g2="guillemotright,guilsinglright" 	k="33" />
<hkern g1="V,W" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="53" />
<hkern g1="V,W" 	g2="nine" 	k="16" />
<hkern g1="V,W" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="V,W" 	g2="one" 	k="-61" />
<hkern g1="V,W" 	g2="paragraph" 	k="-35" />
<hkern g1="V,W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="78" />
<hkern g1="V,W" 	g2="questiondown" 	k="141" />
<hkern g1="V,W" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="V,W" 	g2="quoteright,quotedblright" 	k="-55" />
<hkern g1="V,W" 	g2="s" 	k="125" />
<hkern g1="V,W" 	g2="seven" 	k="-20" />
<hkern g1="V,W" 	g2="slash" 	k="82" />
<hkern g1="V,W" 	g2="t,uniFB01,uniFB02" 	k="12" />
<hkern g1="V,W" 	g2="three" 	k="-12" />
<hkern g1="V,W" 	g2="trademark" 	k="-82" />
<hkern g1="V,W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="68" />
<hkern g1="V,W" 	g2="underscore" 	k="20" />
<hkern g1="V,W" 	g2="v,w,y,yacute,ydieresis" 	k="37" />
<hkern g1="V,W" 	g2="x" 	k="66" />
<hkern g1="V,W" 	g2="z" 	k="63" />
<hkern g1="V,W" 	g2="zero,six" 	k="51" />
<hkern g1="V,W" 	g2="dollar,S" 	k="31" />
<hkern g1="V,W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="113" />
<hkern g1="V,W" 	g2="ampersand" 	k="49" />
<hkern g1="V,W" 	g2="five" 	k="23" />
<hkern g1="V,W" 	g2="parenright" 	k="-27" />
<hkern g1="V,W" 	g2="bracketright,braceright" 	k="-53" />
<hkern g1="V,W" 	g2="exclam" 	k="-20" />
<hkern g1="V,W" 	g2="j" 	k="41" />
<hkern g1="V,W" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="72" />
<hkern g1="V,W" 	g2="Z" 	k="20" />
<hkern g1="V,W" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="74" />
<hkern g1="X" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="X" 	g2="T" 	k="61" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="X" 	g2="V,W" 	k="53" />
<hkern g1="X" 	g2="X" 	k="20" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="X" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="X" 	g2="colon,semicolon" 	k="16" />
<hkern g1="X" 	g2="degree" 	k="41" />
<hkern g1="X" 	g2="eight" 	k="47" />
<hkern g1="X" 	g2="exclamdown" 	k="20" />
<hkern g1="X" 	g2="four" 	k="115" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="X" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="43" />
<hkern g1="X" 	g2="nine" 	k="37" />
<hkern g1="X" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="X" 	g2="one" 	k="-20" />
<hkern g1="X" 	g2="question" 	k="61" />
<hkern g1="X" 	g2="questiondown" 	k="-12" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="X" 	g2="s" 	k="41" />
<hkern g1="X" 	g2="seven" 	k="20" />
<hkern g1="X" 	g2="slash" 	k="-29" />
<hkern g1="X" 	g2="t,uniFB01,uniFB02" 	k="53" />
<hkern g1="X" 	g2="trademark" 	k="-61" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="57" />
<hkern g1="X" 	g2="underscore" 	k="-90" />
<hkern g1="X" 	g2="v,w,y,yacute,ydieresis" 	k="68" />
<hkern g1="X" 	g2="x" 	k="88" />
<hkern g1="X" 	g2="z" 	k="33" />
<hkern g1="X" 	g2="zero,six" 	k="74" />
<hkern g1="X" 	g2="dollar,S" 	k="25" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="X" 	g2="ampersand" 	k="41" />
<hkern g1="X" 	g2="bracketright,braceright" 	k="-12" />
<hkern g1="X" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="X" 	g2="Z" 	k="20" />
<hkern g1="X" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="45" />
<hkern g1="X" 	g2="parenleft" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="88" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V,W" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="X" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="12" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="degree" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eight" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="exclamdown" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="four" 	k="197" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="nine" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="one" 	k="-74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="paragraph" 	k="-35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="questiondown" 	k="162" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="-14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="145" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="seven" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t,uniFB01,uniFB02" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="three" 	k="-10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="underscore" 	k="8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,w,y,yacute,ydieresis" 	k="88" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="zero,six" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="dollar,S" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="five" 	k="43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="section" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="two" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright,braceright" 	k="-68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="exclam" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="-74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenleft" 	k="14" />
<hkern g1="Z" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="Z" 	g2="X" 	k="20" />
<hkern g1="Z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-12" />
<hkern g1="Z" 	g2="eight" 	k="4" />
<hkern g1="Z" 	g2="exclamdown" 	k="-12" />
<hkern g1="Z" 	g2="four" 	k="35" />
<hkern g1="Z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="14" />
<hkern g1="Z" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="Z" 	g2="one" 	k="-41" />
<hkern g1="Z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="Z" 	g2="question" 	k="-12" />
<hkern g1="Z" 	g2="slash" 	k="-8" />
<hkern g1="Z" 	g2="t,uniFB01,uniFB02" 	k="8" />
<hkern g1="Z" 	g2="trademark" 	k="-61" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="8" />
<hkern g1="Z" 	g2="underscore" 	k="-61" />
<hkern g1="Z" 	g2="v,w,y,yacute,ydieresis" 	k="14" />
<hkern g1="Z" 	g2="zero,six" 	k="31" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-6" />
<hkern g1="Z" 	g2="ampersand" 	k="14" />
<hkern g1="Z" 	g2="five" 	k="4" />
<hkern g1="Z" 	g2="bracketright,braceright" 	k="-27" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="131" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="V,W" 	k="27" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="68" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="131" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="one" 	k="8" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="question" 	k="-53" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="dollar,S" 	k="16" />
<hkern g1="z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="z" 	g2="backslash" 	k="117" />
<hkern g1="z" 	g2="four" 	k="14" />
<hkern g1="z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="25" />
<hkern g1="z" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="z" 	g2="one" 	k="14" />
<hkern g1="z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-12" />
<hkern g1="z" 	g2="t,uniFB01,uniFB02" 	k="-6" />
<hkern g1="z" 	g2="underscore" 	k="-35" />
<hkern g1="z" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="z" 	g2="x" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="8" />
<hkern g1="z" 	g2="two" 	k="8" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-14" />
<hkern g1="ampersand" 	g2="J" 	k="-55" />
<hkern g1="ampersand" 	g2="T" 	k="49" />
<hkern g1="ampersand" 	g2="V,W" 	k="41" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="ampersand" 	g2="Z" 	k="-14" />
<hkern g1="ampersand" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="ampersand" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="ampersand" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="ampersand" 	g2="v,w,y,yacute,ydieresis" 	k="33" />
<hkern g1="ampersand" 	g2="x" 	k="12" />
<hkern g1="currency" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-8" />
<hkern g1="degree" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="197" />
<hkern g1="degree" 	g2="J" 	k="20" />
<hkern g1="degree" 	g2="T" 	k="-82" />
<hkern g1="degree" 	g2="V,W" 	k="-49" />
<hkern g1="degree" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="degree" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="degree" 	g2="t,uniFB01,uniFB02" 	k="-41" />
<hkern g1="degree" 	g2="v,w,y,yacute,ydieresis" 	k="-70" />
<hkern g1="degree" 	g2="x" 	k="-20" />
<hkern g1="degree" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="degree" 	g2="dollar,S" 	k="-41" />
<hkern g1="degree" 	g2="X" 	k="41" />
<hkern g1="degree" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="degree" 	g2="s" 	k="12" />
<hkern g1="percent" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-14" />
<hkern g1="percent" 	g2="asterisk,ordfeminine,ordmasculine" 	k="102" />
<hkern g1="percent" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-53" />
<hkern g1="section" 	g2="J" 	k="-8" />
<hkern g1="section" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-8" />
<hkern g1="section" 	g2="t,uniFB01,uniFB02" 	k="-8" />
<hkern g1="section" 	g2="v,w,y,yacute,ydieresis" 	k="-8" />
<hkern g1="section" 	g2="x" 	k="-8" />
<hkern g1="section" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-8" />
<hkern g1="section" 	g2="s" 	k="-8" />
<hkern g1="section" 	g2="j" 	k="-8" />
<hkern g1="section" 	g2="b,h,k,l,germandbls,thorn" 	k="-8" />
<hkern g1="section" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-8" />
<hkern g1="section" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="-8" />
<hkern g1="section" 	g2="z" 	k="-8" />
<hkern g1="trademark" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="41" />
<hkern g1="trademark" 	g2="J" 	k="41" />
<hkern g1="trademark" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-49" />
<hkern g1="trademark" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="trademark" 	g2="t,uniFB01,uniFB02" 	k="-74" />
<hkern g1="trademark" 	g2="v,w,y,yacute,ydieresis" 	k="-14" />
<hkern g1="trademark" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-49" />
<hkern g1="trademark" 	g2="s" 	k="-49" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="197" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="J" 	k="27" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="dollar,S" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="T" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="V,W" 	k="-66" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="X" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Z" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="ampersand" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="bracketright,braceright" 	k="-12" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="currency,Euro" 	k="-8" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="degree" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="exclam" 	k="-16" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="four" 	k="25" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="guillemotright,guilsinglright" 	k="-33" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="nine" 	k="-55" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="one" 	k="-49" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="paragraph" 	k="-70" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="percent" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="question" 	k="-55" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="questiondown" 	k="158" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="seven" 	k="-49" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="slash" 	k="270" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="t,uniFB01,uniFB02" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="three" 	k="2" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="trademark" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="two" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="underscore" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="v,w,y,yacute,ydieresis" 	k="-76" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="x" 	k="-33" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="z" 	k="-29" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="zero,six" 	k="-8" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-61" />
<hkern g1="backslash" 	g2="J" 	k="-61" />
<hkern g1="backslash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="backslash" 	g2="T" 	k="41" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="backslash" 	g2="V,W" 	k="82" />
<hkern g1="backslash" 	g2="X" 	k="-41" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="backslash" 	g2="asterisk,ordfeminine,ordmasculine" 	k="270" />
<hkern g1="backslash" 	g2="j" 	k="-246" />
<hkern g1="backslash" 	g2="v,w,y,yacute,ydieresis" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="T" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="V,W" 	k="-53" />
<hkern g1="bracketleft,braceleft" 	g2="X" 	k="-12" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="-68" />
<hkern g1="bracketleft,braceleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-12" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-205" />
<hkern g1="bracketleft,braceleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="bracketright" 	g2="T" 	k="16" />
<hkern g1="bracketright" 	g2="X" 	k="20" />
<hkern g1="bracketright" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-70" />
<hkern g1="colon,semicolon" 	g2="T" 	k="20" />
<hkern g1="colon,semicolon" 	g2="V,W" 	k="53" />
<hkern g1="colon,semicolon" 	g2="X" 	k="16" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="colon,semicolon" 	g2="v,w,y,yacute,ydieresis" 	k="-8" />
<hkern g1="colon,semicolon" 	g2="t,uniFB01,uniFB02" 	k="-27" />
<hkern g1="colon,semicolon" 	g2="x" 	k="20" />
<hkern g1="exclam" 	g2="J" 	k="-45" />
<hkern g1="exclam" 	g2="T" 	k="-41" />
<hkern g1="exclam" 	g2="V,W" 	k="-20" />
<hkern g1="exclam" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="exclam" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-16" />
<hkern g1="exclamdown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-20" />
<hkern g1="exclamdown" 	g2="J" 	k="-20" />
<hkern g1="exclamdown" 	g2="T" 	k="41" />
<hkern g1="exclamdown" 	g2="V,W" 	k="61" />
<hkern g1="exclamdown" 	g2="X" 	k="20" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="exclamdown" 	g2="j" 	k="-150" />
<hkern g1="exclamdown" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="-61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V,W" 	k="33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="47" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t,uniFB01,uniFB02" 	k="-12" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="47" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="J" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-8" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="T" 	k="74" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="V,W" 	k="53" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="X" 	k="43" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="68" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="v,w,y,yacute,ydieresis" 	k="33" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="x" 	k="53" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="dollar,S" 	k="29" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="z" 	k="25" />
<hkern g1="parenleft" 	g2="T" 	k="14" />
<hkern g1="parenleft" 	g2="j" 	k="-211" />
<hkern g1="parenright" 	g2="T" 	k="70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-66" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="J" 	k="-111" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="68" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V,W" 	k="78" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="74" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v,w,y,yacute,ydieresis" 	k="66" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t,uniFB01,uniFB02" 	k="8" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-29" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="dollar,S" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="question" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="94" />
<hkern g1="question" 	g2="J" 	k="63" />
<hkern g1="question" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="question" 	g2="V,W" 	k="33" />
<hkern g1="question" 	g2="X" 	k="43" />
<hkern g1="question" 	g2="Y,Yacute,Ydieresis" 	k="33" />
<hkern g1="question" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-14" />
<hkern g1="question" 	g2="v,w,y,yacute,ydieresis" 	k="-20" />
<hkern g1="question" 	g2="Z" 	k="20" />
<hkern g1="question" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-53" />
<hkern g1="questiondown" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="115" />
<hkern g1="questiondown" 	g2="T" 	k="125" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="72" />
<hkern g1="questiondown" 	g2="V,W" 	k="145" />
<hkern g1="questiondown" 	g2="X" 	k="-27" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="166" />
<hkern g1="questiondown" 	g2="asterisk,ordfeminine,ordmasculine" 	k="176" />
<hkern g1="questiondown" 	g2="j" 	k="-143" />
<hkern g1="questiondown" 	g2="v,w,y,yacute,ydieresis" 	k="109" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="questiondown" 	g2="t,uniFB01,uniFB02" 	k="14" />
<hkern g1="questiondown" 	g2="x" 	k="-27" />
<hkern g1="questiondown" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="74" />
<hkern g1="questiondown" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="102" />
<hkern g1="questiondown" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="14" />
<hkern g1="questiondown" 	g2="s" 	k="8" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="74" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="12" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-49" />
<hkern g1="quoteleft,quotedblleft" 	g2="V,W" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-14" />
<hkern g1="quoteleft,quotedblleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="v,w,y,yacute,ydieresis" 	k="-66" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="t,uniFB01,uniFB02" 	k="-47" />
<hkern g1="quoteleft,quotedblleft" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="quoteleft,quotedblleft" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="74" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="6" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-49" />
<hkern g1="quoteright,quotedblright" 	g2="V,W" 	k="-55" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="-8" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-49" />
<hkern g1="quoteright,quotedblright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="v,w,y,yacute,ydieresis" 	k="-29" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="t,uniFB01,uniFB02" 	k="-12" />
<hkern g1="quoteright,quotedblright" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="37" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="86" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-27" />
<hkern g1="quotedbl,quotesingle" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="82" />
<hkern g1="slash" 	g2="J" 	k="20" />
<hkern g1="slash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="T" 	k="-61" />
<hkern g1="slash" 	g2="V,W" 	k="-61" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-61" />
<hkern g1="slash" 	g2="j" 	k="-12" />
<hkern g1="slash" 	g2="v,w,y,yacute,ydieresis" 	k="61" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="131" />
<hkern g1="slash" 	g2="t,uniFB01,uniFB02" 	k="49" />
<hkern g1="slash" 	g2="x" 	k="102" />
<hkern g1="slash" 	g2="dollar,S" 	k="41" />
<hkern g1="slash" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="131" />
<hkern g1="slash" 	g2="z" 	k="131" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="131" />
<hkern g1="slash" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="131" />
<hkern g1="slash" 	g2="s" 	k="131" />
<hkern g1="underscore" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-111" />
<hkern g1="underscore" 	g2="J" 	k="-158" />
<hkern g1="underscore" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="underscore" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="underscore" 	g2="V,W" 	k="20" />
<hkern g1="underscore" 	g2="X" 	k="-90" />
<hkern g1="underscore" 	g2="Y,Yacute,Ydieresis" 	k="8" />
<hkern g1="underscore" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="underscore" 	g2="j" 	k="-231" />
<hkern g1="underscore" 	g2="v,w,y,yacute,ydieresis" 	k="33" />
<hkern g1="underscore" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="underscore" 	g2="t,uniFB01,uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="x" 	k="-70" />
<hkern g1="underscore" 	g2="Z" 	k="-41" />
<hkern g1="underscore" 	g2="dollar,S" 	k="-41" />
<hkern g1="underscore" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="underscore" 	g2="z" 	k="-29" />
<hkern g1="underscore" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="49" />
<hkern g1="underscore" 	g2="s" 	k="-20" />
<hkern g1="underscore" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="-61" />
<hkern g1="c,cent,ccedilla" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="c,cent,ccedilla" 	g2="T" 	k="145" />
<hkern g1="c,cent,ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="27" />
<hkern g1="c,cent,ccedilla" 	g2="V,W" 	k="53" />
<hkern g1="c,cent,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="80" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="35" />
<hkern g1="c,cent,ccedilla" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-33" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="90" />
<hkern g1="c,cent,ccedilla" 	g2="bracketright,braceright" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="bracketleft" 	k="8" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-16" />
<hkern g1="c,cent,ccedilla" 	g2="degree" 	k="-37" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="four" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="b,h,k,l,germandbls,thorn" 	k="8" />
<hkern g1="c,cent,ccedilla" 	g2="nine" 	k="8" />
<hkern g1="c,cent,ccedilla" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="45" />
<hkern g1="c,cent,ccedilla" 	g2="parenright" 	k="47" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="49" />
<hkern g1="c,cent,ccedilla" 	g2="questiondown" 	k="8" />
<hkern g1="c,cent,ccedilla" 	g2="quoteleft,quotedblleft" 	k="-43" />
<hkern g1="c,cent,ccedilla" 	g2="quoteright,quotedblright" 	k="-53" />
<hkern g1="c,cent,ccedilla" 	g2="section" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="seven" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="t,uniFB01,uniFB02" 	k="-16" />
<hkern g1="c,cent,ccedilla" 	g2="three" 	k="29" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="25" />
<hkern g1="c,cent,ccedilla" 	g2="two" 	k="8" />
<hkern g1="c,cent,ccedilla" 	g2="v,w,y,yacute,ydieresis" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="x" 	k="29" />
<hkern g1="c,cent,ccedilla" 	g2="z" 	k="8" />
<hkern g1="c,cent,ccedilla" 	g2="zero,six" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="193" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V,W" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="131" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="degree" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="nine" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="one" 	k="49" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="90" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="questiondown" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="-2" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="seven" 	k="55" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="three" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="two" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="78" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="paragraph" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="percent" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="35" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="underscore" 	k="16" />
<hkern g1="eth" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="eth" 	g2="backslash" 	k="20" />
<hkern g1="eth" 	g2="degree" 	k="-41" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="eth" 	g2="question" 	k="8" />
<hkern g1="eth" 	g2="questiondown" 	k="8" />
<hkern g1="eth" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="eth" 	g2="three" 	k="8" />
<hkern g1="eth" 	g2="trademark" 	k="-49" />
<hkern g1="eth" 	g2="zero,six" 	k="-8" />
<hkern g1="eth" 	g2="underscore" 	k="41" />
<hkern g1="f" 	g2="ampersand" 	k="33" />
<hkern g1="f" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-82" />
<hkern g1="f" 	g2="backslash" 	k="-127" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-90" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-35" />
<hkern g1="f" 	g2="degree" 	k="-70" />
<hkern g1="f" 	g2="eight" 	k="-14" />
<hkern g1="f" 	g2="four" 	k="49" />
<hkern g1="f" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="f" 	g2="nine" 	k="-51" />
<hkern g1="f" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="f" 	g2="one" 	k="-82" />
<hkern g1="f" 	g2="parenright" 	k="-82" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="f" 	g2="question" 	k="-49" />
<hkern g1="f" 	g2="questiondown" 	k="33" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-63" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-117" />
<hkern g1="f" 	g2="seven" 	k="-76" />
<hkern g1="f" 	g2="three" 	k="-35" />
<hkern g1="f" 	g2="trademark" 	k="-131" />
<hkern g1="f" 	g2="two" 	k="-29" />
<hkern g1="f" 	g2="v,w,y,yacute,ydieresis" 	k="-20" />
<hkern g1="f" 	g2="zero,six" 	k="-14" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="f" 	g2="paragraph" 	k="-76" />
<hkern g1="f" 	g2="percent" 	k="-29" />
<hkern g1="f" 	g2="underscore" 	k="47" />
<hkern g1="f" 	g2="exclam" 	k="-12" />
<hkern g1="f" 	g2="five" 	k="-14" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="12" />
<hkern g1="f" 	g2="j" 	k="-94" />
<hkern g1="f" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="f" 	g2="slash" 	k="61" />
<hkern g1="k" 	g2="T" 	k="18" />
<hkern g1="k" 	g2="V,W" 	k="10" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="k" 	g2="ampersand" 	k="27" />
<hkern g1="k" 	g2="backslash" 	k="82" />
<hkern g1="k" 	g2="bracketleft" 	k="10" />
<hkern g1="k" 	g2="eight" 	k="23" />
<hkern g1="k" 	g2="four" 	k="61" />
<hkern g1="k" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="53" />
<hkern g1="k" 	g2="nine" 	k="-20" />
<hkern g1="k" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="k" 	g2="one" 	k="-27" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="k" 	g2="question" 	k="-27" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="k" 	g2="seven" 	k="-6" />
<hkern g1="k" 	g2="three" 	k="-20" />
<hkern g1="k" 	g2="two" 	k="-47" />
<hkern g1="k" 	g2="v,w,y,yacute,ydieresis" 	k="63" />
<hkern g1="k" 	g2="x" 	k="41" />
<hkern g1="k" 	g2="z" 	k="27" />
<hkern g1="k" 	g2="zero,six" 	k="20" />
<hkern g1="k" 	g2="X" 	k="8" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="k" 	g2="underscore" 	k="-117" />
<hkern g1="k" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="8" />
<hkern g1="k" 	g2="s" 	k="37" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="d,l,uniFB02" 	g2="t,uniFB01,uniFB02" 	k="-80" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="asterisk,ordfeminine,ordmasculine" 	k="49" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="backslash" 	k="131" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="degree" 	k="31" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="nine" 	k="14" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-12" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="one" 	k="39" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="question" 	k="90" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="seven" 	k="39" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="trademark" 	k="49" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="two" 	k="10" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="paragraph" 	k="35" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="percent" 	k="55" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="135" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V,W" 	k="82" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk,ordfeminine,ordmasculine" 	k="29" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="131" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="degree" 	k="37" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="nine" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="one" 	k="49" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="90" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="questiondown" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="-2" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="seven" 	k="49" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="three" 	k="45" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="trademark" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="two" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v,w,y,yacute,ydieresis" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="35" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="14" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="18" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="paragraph" 	k="45" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="percent" 	k="49" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="underscore" 	k="49" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="41" />
<hkern g1="r" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="r" 	g2="T" 	k="43" />
<hkern g1="r" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="r" 	g2="V,W" 	k="49" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="72" />
<hkern g1="r" 	g2="ampersand" 	k="55" />
<hkern g1="r" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="r" 	g2="backslash" 	k="41" />
<hkern g1="r" 	g2="bracketright,braceright" 	k="14" />
<hkern g1="r" 	g2="bracketleft" 	k="16" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-12" />
<hkern g1="r" 	g2="degree" 	k="-72" />
<hkern g1="r" 	g2="eight" 	k="-6" />
<hkern g1="r" 	g2="four" 	k="45" />
<hkern g1="r" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="18" />
<hkern g1="r" 	g2="b,h,k,l,germandbls,thorn" 	k="8" />
<hkern g1="r" 	g2="nine" 	k="-27" />
<hkern g1="r" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="r" 	g2="one" 	k="-12" />
<hkern g1="r" 	g2="parenright" 	k="10" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="r" 	g2="question" 	k="-27" />
<hkern g1="r" 	g2="questiondown" 	k="70" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-74" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-74" />
<hkern g1="r" 	g2="section" 	k="-20" />
<hkern g1="r" 	g2="seven" 	k="-47" />
<hkern g1="r" 	g2="t,uniFB01,uniFB02" 	k="-31" />
<hkern g1="r" 	g2="three" 	k="8" />
<hkern g1="r" 	g2="two" 	k="-12" />
<hkern g1="r" 	g2="v,w,y,yacute,ydieresis" 	k="-31" />
<hkern g1="r" 	g2="x" 	k="8" />
<hkern g1="r" 	g2="zero,six" 	k="-12" />
<hkern g1="r" 	g2="X" 	k="80" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="r" 	g2="paragraph" 	k="-61" />
<hkern g1="r" 	g2="underscore" 	k="33" />
<hkern g1="r" 	g2="five" 	k="-6" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="-12" />
<hkern g1="r" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="8" />
<hkern g1="r" 	g2="slash" 	k="82" />
<hkern g1="r" 	g2="s" 	k="4" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="92" />
<hkern g1="r" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="14" />
<hkern g1="r" 	g2="dollar,S" 	k="16" />
<hkern g1="r" 	g2="parenleft" 	k="14" />
<hkern g1="s" 	g2="backslash" 	k="123" />
<hkern g1="s" 	g2="degree" 	k="25" />
<hkern g1="s" 	g2="nine" 	k="10" />
<hkern g1="s" 	g2="one" 	k="8" />
<hkern g1="s" 	g2="question" 	k="29" />
<hkern g1="s" 	g2="seven" 	k="49" />
<hkern g1="s" 	g2="three" 	k="20" />
<hkern g1="s" 	g2="trademark" 	k="29" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="s" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="paragraph" 	k="45" />
<hkern g1="t" 	g2="ampersand" 	k="14" />
<hkern g1="t" 	g2="asterisk,ordfeminine,ordmasculine" 	k="29" />
<hkern g1="t" 	g2="backslash" 	k="131" />
<hkern g1="t" 	g2="degree" 	k="10" />
<hkern g1="t" 	g2="eight" 	k="16" />
<hkern g1="t" 	g2="four" 	k="41" />
<hkern g1="t" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="t" 	g2="nine" 	k="14" />
<hkern g1="t" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="t" 	g2="one" 	k="31" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="question" 	k="29" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="8" />
<hkern g1="t" 	g2="section" 	k="10" />
<hkern g1="t" 	g2="seven" 	k="35" />
<hkern g1="t" 	g2="t,uniFB01,uniFB02" 	k="25" />
<hkern g1="t" 	g2="trademark" 	k="49" />
<hkern g1="t" 	g2="two" 	k="8" />
<hkern g1="t" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="t" 	g2="zero,six" 	k="29" />
<hkern g1="t" 	g2="paragraph" 	k="41" />
<hkern g1="t" 	g2="underscore" 	k="-55" />
<hkern g1="t" 	g2="five" 	k="8" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="T" 	k="35" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="49" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="ampersand" 	k="53" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-76" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="backslash" 	k="41" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="colon,semicolon" 	k="-8" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="degree" 	k="-70" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="eight" 	k="-8" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="four" 	k="68" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="33" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="nine" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="one" 	k="-41" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="66" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="question" 	k="-74" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="questiondown" 	k="78" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="quoteleft,quotedblleft" 	k="-66" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="seven" 	k="-31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="t,uniFB01,uniFB02" 	k="-31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="three" 	k="-6" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="two" 	k="-41" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="v,w,y,yacute,ydieresis" 	k="27" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="x" 	k="61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="z" 	k="20" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="zero,six" 	k="-8" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="X" 	k="61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="paragraph" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="underscore" 	k="33" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="five" 	k="-8" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="j" 	k="31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="slash" 	k="72" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="s" 	k="16" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="57" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="dollar,S" 	k="8" />
<hkern g1="x" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="x" 	g2="T" 	k="45" />
<hkern g1="x" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="x" 	g2="V,W" 	k="47" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="x" 	g2="ampersand" 	k="41" />
<hkern g1="x" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-33" />
<hkern g1="x" 	g2="backslash" 	k="90" />
<hkern g1="x" 	g2="colon,semicolon" 	k="20" />
<hkern g1="x" 	g2="degree" 	k="-20" />
<hkern g1="x" 	g2="eight" 	k="12" />
<hkern g1="x" 	g2="four" 	k="74" />
<hkern g1="x" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="53" />
<hkern g1="x" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="x" 	g2="one" 	k="-27" />
<hkern g1="x" 	g2="question" 	k="-41" />
<hkern g1="x" 	g2="seven" 	k="8" />
<hkern g1="x" 	g2="two" 	k="-20" />
<hkern g1="x" 	g2="v,w,y,yacute,ydieresis" 	k="61" />
<hkern g1="x" 	g2="z" 	k="20" />
<hkern g1="x" 	g2="zero,six" 	k="20" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="x" 	g2="paragraph" 	k="-12" />
<hkern g1="x" 	g2="underscore" 	k="-70" />
<hkern g1="x" 	g2="j" 	k="-53" />
<hkern g1="x" 	g2="slash" 	k="-20" />
<hkern g1="x" 	g2="s" 	k="20" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="x" 	g2="dollar,S" 	k="10" />
</font>
</defs></svg> 