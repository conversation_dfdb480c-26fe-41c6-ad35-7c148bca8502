<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="montserrat_alternatesregular" horiz-adv-x="1388" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="536" />
<glyph unicode="&#xfb01;" horiz-adv-x="1384" d="M203 0v1210q0 147 84.5 233.5t240.5 86.5q135 0 215 -68l-49 -108q-62 53 -159 53q-94 0 -142.5 -51.5t-48.5 -151.5v-127h332v-123h-328v-954h-145zM1004 1417q0 42 31 72.5t75 30.5t75.5 -30t31.5 -71q0 -45 -30.5 -75.5t-76.5 -30.5q-44 0 -75 30.5t-31 73.5zM1036 0 v1077h146v-1077h-146z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1275" d="M35 954v123h192v43q0 198 102.5 304t280.5 106q184 0 282.5 -105t98.5 -305v-1014q0 -196 180 -196q58 0 99 18l10 -121q-64 -22 -131 -22q-145 0 -224 80.5t-79 226.5v1045q0 133 -61 200.5t-175 67.5t-175.5 -67.5t-61.5 -200.5v-60h327v-123h-327v-954h-146v954h-192z " />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="536" />
<glyph unicode=" "  horiz-adv-x="536" />
<glyph unicode="&#x09;" horiz-adv-x="536" />
<glyph unicode="&#xa0;" horiz-adv-x="536" />
<glyph unicode="!" horiz-adv-x="532" d="M158 96q0 44 31 74.5t77 30.5t76.5 -30.5t30.5 -74.5q0 -43 -31 -74.5t-76 -31.5t-76.5 31.5t-31.5 74.5zM180 1434h172l-30 -1004h-113z" />
<glyph unicode="&#x22;" horiz-adv-x="763" d="M141 1434h129l-10 -541h-108zM492 1434h129l-11 -541h-108z" />
<glyph unicode="#" horiz-adv-x="1425" d="M59 389v113h306l53 430h-293v112h307l47 390h113l-49 -390h422l47 390h110l-47 -390h289v-112h-303l-53 -430h290v-113h-305l-47 -389h-110l47 389h-420l-49 -389h-111l47 389h-291zM475 502h422l53 430h-420z" />
<glyph unicode="$" horiz-adv-x="1259" d="M96 174l60 117q72 -70 187.5 -117.5t242.5 -54.5v538q-57 14 -96.5 25t-91 30t-86.5 39t-72 50t-59 65t-36 83t-14 104q0 157 115.5 265t339.5 126v235h102v-233q248 -10 416 -127l-51 -121q-170 109 -365 119v-543q47 -11 77.5 -19t75 -21.5t74 -25.5t66 -31t61 -39 t49.5 -47t39.5 -58t23.5 -70t9 -84q0 -159 -121 -267.5t-354 -121.5v-236h-102v234q-150 7 -282 58t-208 128zM281 1057q0 -45 16 -82t42 -63t67.5 -47.5t82.5 -35.5t97 -28v512q-151 -13 -228 -83t-77 -173zM688 119q162 12 244 80.5t82 171.5q0 47 -18 85t-45.5 64 t-72.5 47.5t-87.5 35t-102.5 28.5v-512z" />
<glyph unicode="%" horiz-adv-x="1697" d="M86 1055q0 175 89.5 282t234.5 107q144 0 233.5 -106.5t89.5 -282.5t-89.5 -282.5t-233.5 -106.5q-145 0 -234.5 107t-89.5 282zM188 1055q0 -137 60 -217t162 -80q101 0 161 79.5t60 217.5t-60 217.5t-161 79.5q-102 0 -162 -80t-60 -217zM299 0l979 1434h121 l-979 -1434h-121zM965 379q0 174 90 281.5t233 107.5q145 0 234.5 -107t89.5 -282t-89.5 -282t-234.5 -107q-143 0 -233 107.5t-90 281.5zM1067 379q0 -138 60 -217.5t161 -79.5q102 0 161.5 79.5t59.5 217.5t-59.5 217.5t-161.5 79.5q-101 0 -161 -79.5t-60 -217.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1370" d="M100 352q0 128 82.5 230.5t278.5 216.5q-102 105 -141 180t-39 160q0 137 98.5 221t263.5 84q153 0 243.5 -76t90.5 -209q0 -107 -72.5 -192t-247.5 -187l394 -405q67 119 96 276l119 -37q-40 -192 -127 -329l204 -211l-83 -97l-199 205q-182 -194 -479 -194 q-212 0 -347 101t-135 263zM244 367q0 -117 95 -187.5t251 -70.5q240 0 385 161l-432 445q-169 -97 -234 -174t-65 -174zM416 1141q0 -64 33 -121.5t129 -155.5q155 89 214.5 152.5t59.5 140.5q0 81 -55.5 129.5t-153.5 48.5q-106 0 -166.5 -53t-60.5 -141z" />
<glyph unicode="'" horiz-adv-x="413" d="M141 1434h129l-10 -541h-108z" />
<glyph unicode="(" horiz-adv-x="673" d="M213 561q0 281 61.5 528t176.5 431h139q-234 -406 -234 -959q0 -552 234 -958h-139q-115 188 -176.5 433t-61.5 525z" />
<glyph unicode=")" horiz-adv-x="673" d="M82 -397q235 403 235 958q0 556 -235 959h139q116 -184 178 -430.5t62 -528.5q0 -281 -62 -526t-178 -432h-139z" />
<glyph unicode="*" horiz-adv-x="790" d="M45 1006l256 143l-256 143l49 88l256 -151l-2 291h94v-291l256 151l47 -88l-253 -143l253 -143l-47 -86l-256 149v-291h-94l2 291l-256 -149z" />
<glyph unicode="+" horiz-adv-x="1177" d="M143 655v121h381v375h129v-375h381v-121h-381v-372h-129v372h-381z" />
<glyph unicode="," horiz-adv-x="434" d="M111 102q0 47 31 79t77 32q47 0 78 -32.5t31 -78.5q0 -35 -29 -118l-86 -283h-98l69 297q-33 9 -53 37t-20 67z" />
<glyph unicode="-" horiz-adv-x="782" d="M123 492v126h536v-126h-536z" />
<glyph unicode="." horiz-adv-x="434" d="M109 102q0 47 31.5 79t76.5 32t78 -32t33 -79t-33 -79.5t-78 -32.5t-76.5 32.5t-31.5 79.5z" />
<glyph unicode="/" horiz-adv-x="686" d="M-51 -205l676 1929h133l-676 -1929h-133z" />
<glyph unicode="0" horiz-adv-x="1355" d="M106 717q0 224 74 390.5t203 252.5t295 86q124 0 229.5 -50t181.5 -142.5t119 -230.5t43 -306t-43 -306t-119 -230.5t-181.5 -142.5t-229.5 -50q-166 0 -295 86t-203 252.5t-74 390.5zM256 717q0 -282 115 -438t307 -156t307 156t115 438t-115 438t-307 156t-307 -156 t-115 -438z" />
<glyph unicode="1" horiz-adv-x="739" d="M18 1303v131h486v-1434h-148v1303h-338z" />
<glyph unicode="2" horiz-adv-x="1163" d="M37 1241q84 99 214.5 152t293.5 53q219 0 347 -104t128 -283q0 -112 -50 -214t-186 -235l-487 -479h788v-131h-1005v104l596 584q113 112 152.5 192t39.5 162q0 126 -87 197.5t-249 71.5q-253 0 -391 -160z" />
<glyph unicode="3" horiz-adv-x="1155" d="M18 172l72 117q71 -74 190 -120t255 -46q176 0 272 77t96 212q0 133 -96.5 208.5t-290.5 75.5h-102v109l397 498h-733v131h921v-105l-405 -510q225 -12 343 -121t118 -286q0 -188 -135 -306t-385 -118q-155 0 -294.5 49.5t-222.5 134.5z" />
<glyph unicode="4" horiz-adv-x="1353" d="M82 377v106l760 951h164l-734 -926h623v332h143v-332h287v-131h-287v-377h-147v377h-809z" />
<glyph unicode="5" horiz-adv-x="1159" d="M45 172l72 117q71 -74 188.5 -120t251.5 -46q179 0 276 79.5t97 213.5q0 72 -24 125t-78 93t-148.5 60.5t-228.5 20.5h-306l74 719h785v-131h-656l-49 -457h184q596 0 596 -424q0 -192 -134 -313t-386 -121q-155 0 -293 49.5t-221 134.5z" />
<glyph unicode="6" horiz-adv-x="1247" d="M106 705q0 179 47.5 320.5t134.5 233t207 139.5t267 48q212 0 340 -78l-60 -119q-103 68 -278 68q-237 0 -372.5 -151t-135.5 -437q0 -53 6 -117q53 117 170 181.5t268 64.5q216 0 350 -117.5t134 -312.5q0 -198 -138.5 -319t-351.5 -121q-284 0 -436 187.5t-152 529.5z M309 420q0 -126 101 -216.5t278 -90.5q156 0 253 85t97 226t-97.5 225t-262.5 84q-163 0 -266 -89t-103 -224z" />
<glyph unicode="7" horiz-adv-x="1206" d="M66 1034v400h1052v-105l-600 -1329h-160l590 1303h-737v-269h-145z" />
<glyph unicode="8" horiz-adv-x="1306" d="M98 399q0 128 71.5 218t205.5 135q-109 43 -167 123t-58 192q0 174 136.5 276.5t364.5 102.5q229 0 367.5 -102.5t138.5 -276.5q0 -112 -59 -192t-170 -123q135 -44 207.5 -135t72.5 -218q0 -190 -150 -300.5t-407 -110.5q-258 0 -405.5 110t-147.5 301zM248 401 q0 -134 106.5 -211t296.5 -77t298 77t108 211q0 133 -108 210t-298 77q-189 0 -296 -77t-107 -210zM297 1063q0 -119 94 -188.5t260 -69.5t261.5 69t95.5 187q0 121 -97 190.5t-260 69.5t-258.5 -69t-95.5 -189z" />
<glyph unicode="9" horiz-adv-x="1247" d="M63 1006q0 198 138.5 319t351.5 121q284 0 436 -187.5t152 -529.5q0 -179 -47.5 -320.5t-134.5 -233t-207 -139.5t-267 -48q-212 0 -340 78l60 118q101 -67 278 -67q237 0 372.5 151t135.5 437q0 51 -6 116q-53 -117 -170 -181.5t-268 -64.5q-215 0 -349.5 118 t-134.5 313zM209 1010q0 -141 97.5 -225.5t262.5 -84.5q163 0 266 89.5t103 224.5q0 126 -101 216.5t-278 90.5q-156 0 -253 -85t-97 -226z" />
<glyph unicode=":" horiz-adv-x="434" d="M109 102q0 47 31.5 79t76.5 32t78 -32t33 -79t-33 -79.5t-78 -32.5t-76.5 32.5t-31.5 79.5zM109 975q0 47 31.5 78.5t76.5 31.5t78 -32t33 -78q0 -47 -33 -80t-78 -33q-44 0 -76 33t-32 80z" />
<glyph unicode=";" horiz-adv-x="434" d="M109 975q0 47 31.5 78.5t76.5 31.5t78 -32t33 -78q0 -47 -33 -80t-78 -33q-44 0 -76 33t-32 80zM111 102q0 47 31 79t77 32q47 0 78 -32.5t31 -78.5q0 -35 -29 -118l-86 -283h-98l69 297q-33 9 -53 37t-20 67z" />
<glyph unicode="&#x3c;" horiz-adv-x="1177" d="M143 649v135l891 359v-129l-753 -297l753 -297v-127z" />
<glyph unicode="=" horiz-adv-x="1177" d="M143 420v121h891v-121h-891zM143 893v121h891v-121h-891z" />
<glyph unicode="&#x3e;" horiz-adv-x="1177" d="M143 293v127l754 297l-754 297v129l891 -359v-135z" />
<glyph unicode="?" horiz-adv-x="1161" d="M27 1233q175 213 510 213q216 0 343.5 -96t127.5 -260q0 -71 -20.5 -131t-54 -102.5t-73.5 -80.5t-79.5 -74t-73 -73.5t-54 -88.5t-20.5 -110h-152q0 69 20.5 127.5t54 100.5t73.5 80t79.5 73t73 71t54 83.5t20.5 101.5q0 112 -88 181t-242 69q-254 0 -391 -166zM451 96 q0 44 31 74.5t75 30.5q46 0 77.5 -30.5t31.5 -74.5q0 -43 -32 -74.5t-77 -31.5q-44 0 -75 31.5t-31 74.5z" />
<glyph unicode="@" horiz-adv-x="2115" d="M106 520q0 202 70.5 374.5t195.5 293.5t303.5 189.5t387.5 68.5q206 0 382 -66t300.5 -183.5t195 -286t70.5 -367.5q0 -260 -90.5 -406.5t-249.5 -146.5q-101 0 -163 59t-68 164q-62 -106 -169 -164.5t-241 -58.5q-219 0 -363.5 149.5t-144.5 380.5q0 229 144.5 378 t363.5 149q132 0 237.5 -56.5t168.5 -159.5v207h133v-776q0 -81 35 -118.5t92 -37.5q96 0 152.5 115t56.5 320q0 237 -106.5 419.5t-298 281.5t-437.5 99q-186 0 -343.5 -60.5t-267.5 -168t-171.5 -260t-61.5 -332.5t61 -333t170 -261.5t265 -169.5t340 -61q97 0 202 23 t191 67l35 -100q-89 -46 -205 -70.5t-223 -24.5q-208 0 -385 69.5t-301 191.5t-193.5 295t-69.5 374zM657 520q0 -183 110 -297t282 -114q169 0 279 113t110 298q0 183 -110 295.5t-279 112.5q-172 0 -282 -112.5t-110 -295.5z" />
<glyph unicode="A" horiz-adv-x="1640" d="M221 0v813q0 308 160.5 470.5t437.5 162.5q278 0 439 -162.5t161 -470.5v-813h-149v422h-901v-422h-148zM369 553h901v274q0 240 -118.5 362t-332.5 122q-212 0 -331 -122.5t-119 -361.5v-274z" />
<glyph unicode="B" horiz-adv-x="1544" d="M233 0v1434h619q239 0 371.5 -97t132.5 -272q0 -119 -58.5 -202t-160.5 -124q141 -31 218 -121.5t77 -234.5q0 -185 -136.5 -284t-402.5 -99h-660zM385 125h506q389 0 389 270q0 269 -389 269h-506v-539zM385 788h457q174 0 268 66.5t94 194.5t-94 194t-268 66h-457v-521 z" />
<glyph unicode="C" horiz-adv-x="1472" d="M106 717q0 207 97.5 374t269.5 261t385 94q159 0 294.5 -53t229.5 -156l-96 -96q-167 170 -424 170q-171 0 -309.5 -77t-217.5 -213t-79 -304t79 -304t217.5 -213t309.5 -77q258 0 424 172l96 -96q-94 -103 -230 -157t-296 -54q-159 0 -298 55t-238.5 151.5t-156.5 232 t-57 290.5z" />
<glyph unicode="D" horiz-adv-x="1691" d="M233 0v1434h584q226 0 401.5 -91t271 -254t95.5 -372t-95.5 -372t-271 -254t-401.5 -91h-584zM385 131h424q283 0 455 163t172 423t-172 423t-455 163h-424v-1172z" />
<glyph unicode="E" horiz-adv-x="1298" d="M113 387q0 130 76 219.5t202 124.5q-102 38 -159.5 123.5t-57.5 200.5q0 169 144.5 280t410.5 111q132 0 255.5 -30t211.5 -85l-47 -121q-189 107 -414 107q-198 0 -303.5 -75.5t-105.5 -197.5q0 -121 84.5 -186t240.5 -65h357v-131h-363q-176 0 -278.5 -67t-102.5 -198 q0 -129 111.5 -204.5t329.5 -75.5q152 0 282.5 42.5t212.5 119.5l57 -115q-89 -84 -235 -130t-324 -46q-282 0 -433.5 110t-151.5 289z" />
<glyph unicode="F" horiz-adv-x="1232" d="M221 0v942q0 235 147.5 369.5t415.5 134.5q260 0 418 -113l-55 -125q-139 103 -356 103q-210 0 -314 -95.5t-104 -275.5v-201h684v-131h-684v-608h-152z" />
<glyph unicode="G" horiz-adv-x="1583" d="M106 713q0 157 57 293t157 232.5t241 152t303 55.5q164 0 301.5 -52.5t231.5 -154.5l-94 -96q-171 168 -433 168q-175 0 -315.5 -77t-219.5 -213.5t-79 -307.5q0 -111 32 -207t87 -165.5t129 -119.5t159.5 -75t176.5 -25q243 0 403 153v443h146v-717h-134v121 q-167 -133 -426 -133q-142 0 -271.5 48.5t-230.5 138t-161 229t-60 309.5z" />
<glyph unicode="H" horiz-adv-x="1665" d="M233 0v1434h152v-639h897v639h150v-1434h-150v662h-897v-662h-152z" />
<glyph unicode="I" horiz-adv-x="911" d="M90 0v131h291v1172h-291v131h731v-131h-291v-1172h291v-131h-731z" />
<glyph unicode="J" horiz-adv-x="1024" d="M-10 -12l86 102q119 -176 307 -176q268 0 268 315v1074h-524v131h674v-1198q0 -226 -105.5 -339.5t-310.5 -113.5q-122 0 -225.5 53.5t-169.5 151.5z" />
<glyph unicode="K" horiz-adv-x="1456" d="M233 0v1434h152v-867l842 867h174l-621 -650l664 -784h-180l-586 674l-293 -297v-377h-152z" />
<glyph unicode="L" horiz-adv-x="1206" d="M233 0v1434h152v-1303h803v-131h-955z" />
<glyph unicode="M" horiz-adv-x="2510" d="M233 0v1434h146v-240q60 124 175.5 188t276.5 64q168 0 288 -70t173 -198q62 132 185 200t290 68q246 0 383.5 -141t137.5 -410v-895h-150v891q0 209 -105 314.5t-288 105.5q-193 0 -301.5 -111.5t-108.5 -341.5v-858h-149v891q0 210 -101.5 315t-285.5 105 q-194 0 -304 -111.5t-110 -341.5v-858h-152z" />
<glyph unicode="N" horiz-adv-x="1636" d="M233 0v1434h146v-242q137 254 485 254q258 0 404.5 -153.5t146.5 -438.5v-854h-151v850q0 227 -112.5 344t-315.5 117q-213 0 -332 -124.5t-119 -371.5v-815h-152z" />
<glyph unicode="O" horiz-adv-x="1718" d="M106 717q0 206 97.5 373t270 261.5t386.5 94.5q159 0 298.5 -55.5t239 -151.5t157 -231.5t57.5 -290.5t-57.5 -290.5t-157 -231.5t-239 -151.5t-298.5 -55.5q-214 0 -386.5 94.5t-270 261.5t-97.5 373zM256 717q0 -169 78.5 -305t216.5 -212.5t309 -76.5q258 0 429 169 t171 425t-171 425t-429 169q-171 0 -309 -76.5t-216.5 -212.5t-78.5 -305z" />
<glyph unicode="P" horiz-adv-x="1470" d="M233 0v1434h537q274 0 430 -131t156 -361q0 -229 -156 -359t-430 -130h-385v-453h-152zM385 586h381q213 0 325.5 93t112.5 263q0 173 -112 267t-326 94h-381v-717z" />
<glyph unicode="Q" horiz-adv-x="1718" d="M106 717q0 206 97.5 373t270 261.5t386.5 94.5q159 0 298.5 -55.5t239 -151.5t157 -231.5t57.5 -290.5q0 -146 -51 -275t-140.5 -223.5t-215 -154.5t-271.5 -72v-268h-150v268q-146 12 -272 72t-215.5 155t-140 224t-50.5 274zM256 717q0 -235 149.5 -400t382.5 -190v354 h142v-354q232 23 381 188t149 402q0 126 -45.5 236t-125 188.5t-191 124t-238.5 45.5q-171 0 -309 -77t-216.5 -213t-78.5 -304z" />
<glyph unicode="R" horiz-adv-x="1480" d="M233 0v1434h537q274 0 430 -131t156 -361q0 -168 -85 -283.5t-243 -166.5l350 -492h-166l-327 461q-55 -6 -115 -6h-385v-455h-152zM385 584h381q213 0 325.5 93.5t112.5 264.5q0 173 -112 267t-326 94h-381v-719z" />
<glyph unicode="S" horiz-adv-x="1259" d="M96 174l60 117q78 -77 206.5 -125.5t266.5 -48.5q192 0 288.5 70.5t96.5 183.5q0 63 -30 109.5t-81.5 74.5t-117.5 49.5t-139.5 38.5t-146.5 36.5t-139 49t-117.5 69.5t-81.5 105t-30 150q0 81 32 150.5t94.5 124.5t165.5 86.5t236 31.5q122 0 240.5 -33.5t204.5 -93.5 l-51 -121q-183 119 -394 119q-187 0 -282.5 -73t-95.5 -187q0 -63 30 -109.5t81 -75t117.5 -50t139.5 -38.5t146 -36.5t139.5 -48.5t117.5 -69t81 -104t30 -147q0 -81 -32.5 -150.5t-96 -124t-168 -85.5t-237.5 -31q-161 0 -307 51t-226 135z" />
<glyph unicode="T" horiz-adv-x="1189" d="M-4 1288q265 158 598 158q338 0 600 -158l-55 -119q-213 123 -467 142v-1311h-152v1311q-248 -15 -469 -142z" />
<glyph unicode="U" horiz-adv-x="1628" d="M221 580v854h152v-850q0 -226 112 -343.5t312 -117.5q210 0 328 124t118 371v816h152v-1434h-146v242q-140 -254 -481 -254q-256 0 -401.5 153.5t-145.5 438.5z" />
<glyph unicode="V" horiz-adv-x="1429" d="M6 1434h164l549 -1246l553 1246h153l-634 -1434h-150z" />
<glyph unicode="W" horiz-adv-x="2449" d="M221 563v871h152v-865q0 -446 387 -446q187 0 289 109t102 337v865h149v-865q0 -446 392 -446q186 0 286.5 109t100.5 337v865h149v-871q0 -284 -140 -429.5t-396 -145.5q-170 0 -290 65t-177 187q-59 -122 -178 -187t-287 -65q-258 0 -398.5 145.5t-140.5 429.5z" />
<glyph unicode="X" horiz-adv-x="1343" d="M35 0l545 737l-510 697h174l432 -586l430 586h164l-508 -693l547 -741h-176l-463 631l-461 -631h-174z" />
<glyph unicode="Y" horiz-adv-x="1613" d="M211 952v482h151v-476q0 -227 113 -344.5t316 -117.5q213 0 331.5 124t118.5 371v443h152v-1018q0 -307 -159.5 -469t-442.5 -162q-354 0 -551 219l69 117q183 -201 482 -201q221 0 335.5 123t114.5 369v192q-139 -244 -465 -244q-267 0 -416 153t-149 439z" />
<glyph unicode="Z" horiz-adv-x="1353" d="M102 0v96l447 570h-318v127h416l400 510h-938v131h1138v-95l-428 -548h297v-127h-395l-416 -533h973v-131h-1176z" />
<glyph unicode="[" horiz-adv-x="651" d="M233 -397v1917h375v-121h-229v-1675h229v-121h-375z" />
<glyph unicode="\" horiz-adv-x="686" d="M-72 1724h133l676 -1929h-133z" />
<glyph unicode="]" horiz-adv-x="651" d="M43 -276h229v1675h-229v121h375v-1917h-375v121z" />
<glyph unicode="^" horiz-adv-x="1179" d="M170 293l358 848h125l357 -848h-121l-299 723l-299 -723h-121z" />
<glyph unicode="_" horiz-adv-x="1024" d="M0 0h1024v-98h-1024v98z" />
<glyph unicode="`" horiz-adv-x="1228" d="M272 1497h195l281 -254h-144z" />
<glyph unicode="a" d="M94 539q0 242 152 394t385 152q130 0 236 -54.5t173 -158.5v205h146v-1077h-139v213q-66 -108 -174 -165.5t-242 -57.5q-232 0 -384.5 153.5t-152.5 395.5zM242 539q0 -187 113.5 -303.5t287.5 -116.5q172 0 285.5 116.5t113.5 303.5t-113 303t-286 116 q-174 0 -287.5 -116t-113.5 -303z" />
<glyph unicode="b" d="M203 0v1520h145v-648q67 103 173.5 158t236.5 55q233 0 384.5 -152t151.5 -394t-152 -395.5t-384 -153.5q-134 0 -242 57.5t-174 165.5v-213h-139zM346 539q0 -187 114 -303.5t288 -116.5q173 0 287 116.5t114 303.5q0 186 -114 302.5t-287 116.5q-174 0 -288 -116 t-114 -303z" />
<glyph unicode="c" horiz-adv-x="1153" d="M94 539q0 241 156.5 393.5t398.5 152.5q139 0 248.5 -54.5t173.5 -158.5l-108 -73q-54 79 -135.5 119t-178.5 40q-179 0 -293 -116t-114 -303q0 -189 114 -304.5t293 -115.5q207 0 314 157l108 -73q-64 -103 -174 -158t-248 -55q-242 0 -398.5 154t-156.5 395z" />
<glyph unicode="d" d="M94 539q0 242 152 394t385 152q130 0 236 -54.5t173 -158.5v648h146v-1520h-139v213q-66 -108 -174 -165.5t-242 -57.5q-232 0 -384.5 153.5t-152.5 395.5zM242 539q0 -187 113.5 -303.5t287.5 -116.5q172 0 285.5 116.5t113.5 303.5t-113 303t-286 116 q-174 0 -287.5 -116t-113.5 -303z" />
<glyph unicode="e" horiz-adv-x="1236" d="M94 539q0 239 150.5 392.5t382.5 153.5q218 0 363.5 -144t152.5 -370l-885 -172q40 -131 148 -205.5t258 -74.5q209 0 329 141l82 -94q-72 -86 -178.5 -131t-234.5 -45q-251 0 -409.5 154.5t-158.5 394.5zM236 549q0 -31 2 -45l759 147q-24 136 -124 223t-246 87 q-172 0 -281.5 -114.5t-109.5 -297.5z" />
<glyph unicode="f" horiz-adv-x="669" d="M203 0v1210q0 147 84.5 233.5t240.5 86.5q135 0 215 -68l-49 -108q-62 53 -159 53q-94 0 -142.5 -51.5t-48.5 -151.5v-127h332v-123h-328v-954h-145z" />
<glyph unicode="g" horiz-adv-x="1404" d="M94 565q0 229 154 374.5t387 145.5q137 0 248 -55.5t178 -159.5v207h139v-946q0 -276 -134 -407.5t-404 -131.5q-150 0 -284 43.5t-218 122.5l73 111q77 -69 188.5 -108.5t235.5 -39.5q204 0 301 96t97 296v137q-67 -102 -176.5 -155.5t-243.5 -53.5q-152 0 -275.5 66 t-194.5 186t-71 272zM242 565q0 -174 114 -284.5t293 -110.5t294.5 110.5t115.5 284.5t-115.5 283.5t-294.5 109.5t-293 -109.5t-114 -283.5z" />
<glyph unicode="h" horiz-adv-x="1386" d="M203 0v1520h145v-631q60 94 164 145t240 51q203 0 322.5 -117.5t119.5 -342.5v-625h-145v610q0 169 -84.5 256.5t-239.5 87.5q-175 0 -276 -103t-101 -286v-565h-145z" />
<glyph unicode="i" horiz-adv-x="550" d="M170 1417q0 42 31 72.5t75 30.5t75.5 -30t31.5 -71q0 -45 -30.5 -75.5t-76.5 -30.5q-44 0 -75 30.5t-31 73.5zM203 0v1077h145v-1077h-145z" />
<glyph unicode="j" horiz-adv-x="561" d="M-184 -338l51 111q65 -56 168 -56q87 0 132.5 52t45.5 151v1157h145v-1157q0 -152 -82 -240t-233 -88q-148 0 -227 70zM180 1417q0 42 31.5 72.5t75.5 30.5t75 -30t31 -71q0 -45 -30.5 -75.5t-75.5 -30.5q-44 0 -75.5 30.5t-31.5 73.5z" />
<glyph unicode="k" horiz-adv-x="1230" d="M203 0v1520h145v-1031l643 588h180l-460 -440l503 -637h-178l-434 541l-254 -234v-307h-145z" />
<glyph unicode="l" horiz-adv-x="632" d="M203 299v1221h145v-1209q0 -196 180 -196q58 0 99 18l10 -121q-64 -22 -131 -22q-146 0 -224.5 81.5t-78.5 227.5z" />
<glyph unicode="m" horiz-adv-x="2172" d="M203 0v1077h139v-196q58 98 159.5 151t235.5 53q136 0 235 -57.5t148 -169.5q59 106 170.5 166.5t255.5 60.5q202 0 318 -117t116 -343v-625h-145v610q0 168 -80.5 256t-228.5 88q-167 0 -264 -103t-97 -286v-565h-145v610q0 169 -81 256.5t-230 87.5q-166 0 -263.5 -103 t-97.5 -286v-565h-145z" />
<glyph unicode="n" horiz-adv-x="1386" d="M203 0v1077h139v-198q59 99 165 152.5t245 53.5q203 0 322.5 -117.5t119.5 -342.5v-625h-145v610q0 169 -84.5 256.5t-239.5 87.5q-175 0 -276 -103t-101 -286v-565h-145z" />
<glyph unicode="o" horiz-adv-x="1284" d="M94 539q0 238 156 392t393 154t392 -154t155 -392q0 -118 -41.5 -220t-113.5 -174.5t-173.5 -113.5t-218.5 -41t-218.5 41t-174.5 113.5t-114.5 174.5t-41.5 220zM242 539q0 -187 113.5 -303.5t287.5 -116.5t286.5 116.5t112.5 303.5t-112.5 303t-286.5 116t-287.5 -116 t-113.5 -303z" />
<glyph unicode="p" d="M203 -397v1474h139v-213q66 107 174 164t242 57q233 0 384.5 -152t151.5 -394q0 -121 -41 -224t-112 -174t-170 -111t-213 -40q-130 0 -236.5 55t-173.5 158v-600h-145zM346 539q0 -187 114 -303.5t288 -116.5q173 0 287 116.5t114 303.5q0 185 -114.5 302t-286.5 117 q-174 0 -288 -116.5t-114 -302.5z" />
<glyph unicode="q" d="M94 539q0 242 152 394t385 152q134 0 242 -57t174 -164v213h139v-1474h-146v600q-67 -104 -173 -158.5t-236 -54.5q-114 0 -213 40t-170.5 111t-112.5 174t-41 224zM242 539q0 -187 113.5 -303.5t287.5 -116.5q172 0 285.5 116.5t113.5 303.5q0 185 -113.5 302 t-285.5 117q-174 0 -287.5 -116.5t-113.5 -302.5z" />
<glyph unicode="r" horiz-adv-x="821" d="M203 0v1077h139v-211q104 219 403 219v-141q-6 0 -17.5 1t-16.5 1q-170 0 -266.5 -104t-96.5 -293v-549h-145z" />
<glyph unicode="s" horiz-adv-x="1001" d="M53 123l66 115q67 -53 170 -87t213 -34q148 0 218.5 46.5t70.5 129.5q0 45 -24 76.5t-64.5 49t-93 30t-110.5 22.5t-115.5 22t-110 33t-93 51.5t-64.5 81.5t-24 119q0 136 113 221.5t313 85.5q105 0 209.5 -27.5t171.5 -72.5l-63 -117q-132 93 -318 93q-140 0 -210 -49 t-70 -130q0 -52 30 -87.5t79 -52.5t112 -32t128 -24.5t128 -30t112 -49t79 -82t30 -127.5q0 -141 -117.5 -224t-326.5 -83q-131 0 -251.5 37.5t-187.5 95.5z" />
<glyph unicode="t" horiz-adv-x="794" d="M193 303v1010h145v-236h326v-123h-326v-643q0 -96 47 -146t137 -50q99 0 160 55l51 -104q-82 -76 -225 -76q-152 0 -233.5 81.5t-81.5 231.5z" />
<glyph unicode="u" horiz-adv-x="1378" d="M193 453v624h145v-610q0 -170 84 -258t240 -88q170 0 268 104t98 287v565h146v-1077h-140v197q-57 -99 -158 -153t-229 -54q-211 0 -332.5 118.5t-121.5 344.5z" />
<glyph unicode="v" horiz-adv-x="1110" d="M2 1077h152l403 -917l408 917h143l-479 -1077h-150z" />
<glyph unicode="w" horiz-adv-x="2056" d="M193 481v596h145v-583q0 -191 74 -282t233 -91t235 92t76 285v579h146v-579q0 -194 75.5 -285.5t235.5 -91.5q157 0 231 91t74 282v583h146v-596q0 -240 -115.5 -365.5t-337.5 -125.5q-285 0 -383 227q-50 -117 -144.5 -172t-238.5 -55q-223 0 -337.5 125.5t-114.5 365.5 z" />
<glyph unicode="x" horiz-adv-x="1093" d="M39 0l426 553l-406 524h164l324 -422l323 422h160l-405 -524l430 -553h-166l-342 451l-344 -451h-164z" />
<glyph unicode="y" horiz-adv-x="1378" d="M193 504v573h145v-559q0 -170 84 -258t240 -88q170 0 268 104t98 287v514h146v-946q0 -276 -125 -407.5t-377 -131.5q-140 0 -267 44t-204 122l73 111q70 -70 172.5 -109t219.5 -39q187 0 274.5 96t87.5 296v125q-57 -94 -156 -145.5t-225 -51.5q-211 0 -332.5 118.5 t-121.5 344.5z" />
<glyph unicode="z" horiz-adv-x="1067" d="M96 0v96l307 389h-223v117h316l278 352h-665v123h852v-96l-299 -379h227v-117h-320l-286 -364h690v-121h-877z" />
<glyph unicode="{" horiz-adv-x="684" d="M123 500v123h70q92 0 92 104v520q0 273 280 273h76v-121h-55q-156 0 -156 -164v-506q0 -75 -22.5 -112.5t-73.5 -55.5q51 -18 73.5 -55.5t22.5 -112.5v-506q0 -163 156 -163h55v-121h-76q-280 0 -280 272v520q0 105 -92 105h-70z" />
<glyph unicode="|" horiz-adv-x="604" d="M233 -397v1917h136v-1917h-136z" />
<glyph unicode="}" horiz-adv-x="684" d="M43 -276h55q156 0 156 163v506q0 75 22.5 112.5t73.5 55.5q-51 18 -73.5 55.5t-22.5 112.5v506q0 164 -156 164h-55v121h78q278 0 278 -273v-520q0 -104 93 -104h69v-123h-69q-93 0 -93 -105v-520q0 -272 -278 -272h-78v121z" />
<glyph unicode="~" horiz-adv-x="1177" d="M127 569q4 150 73 230t181 80q54 0 104 -21.5t86.5 -52.5t71 -61.5t73.5 -52t77 -21.5q71 0 113.5 53t45.5 143h99q-4 -150 -73 -229.5t-181 -79.5q-54 0 -104 21.5t-86.5 51.5t-71 60.5t-73.5 52t-77 21.5q-72 0 -114.5 -52.5t-45.5 -142.5h-98z" />
<glyph unicode="&#xa1;" horiz-adv-x="532" d="M158 979q0 44 31 75t77 31t76.5 -30.5t30.5 -75.5q0 -43 -31 -74t-76 -31t-76.5 31t-31.5 74zM180 -315l29 960h113l30 -960h-172z" />
<glyph unicode="&#xa2;" horiz-adv-x="1153" d="M94 539q0 223 137 372.5t355 171.5v240h100v-238q126 -8 226 -62t159 -151l-108 -73q-97 144 -277 157v-835q181 13 277 155l108 -73q-60 -96 -160 -150.5t-225 -60.5v-238h-100v238q-218 22 -355 173t-137 374zM242 539q0 -171 95 -283t249 -133v831 q-154 -20 -249 -132.5t-95 -282.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1304" d="M61 0v131h232v535h-232v102h232v172q0 233 147.5 369.5t421.5 136.5q251 0 410 -109l-55 -125q-135 99 -367 99q-203 0 -305.5 -96t-102.5 -275v-172h568v-102h-568v-535h803v-131h-1184z" />
<glyph unicode="&#xa4;" horiz-adv-x="1433" d="M70 92l213 213q-127 149 -127 346q0 192 125 346l-211 211l84 92l219 -217q149 117 344 117q193 0 346 -115l215 215l86 -92l-211 -211q129 -152 129 -346q0 -196 -131 -346l213 -213l-86 -92l-219 219q-148 -113 -342 -113q-190 0 -342 113l-221 -219zM276 651 q0 -173 130 -297.5t311 -124.5q183 0 313.5 124.5t130.5 297.5q0 175 -130.5 300.5t-313.5 125.5q-181 0 -311 -125.5t-130 -300.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1423" d="M2 1434h160l553 -748l551 748h155l-583 -795h399v-90h-451v-193h451v-90h-451v-266h-149v266h-451v90h451v193h-451v90h400z" />
<glyph unicode="&#xa6;" horiz-adv-x="604" d="M233 319h136v-716h-136v716zM233 803v717h136v-717h-136z" />
<glyph unicode="&#xa7;" horiz-adv-x="1003" d="M57 -80l52 113q64 -56 163 -92.5t203 -36.5q131 0 204 50.5t73 145.5q0 61 -37 102.5t-96.5 63t-131 38.5t-143 39t-131 54t-96.5 92t-37 146q0 89 45.5 157.5t122.5 108.5q-121 76 -121 223q0 148 118 234t326 86q99 0 204 -28.5t167 -76.5l-51 -110q-134 96 -330 96 q-148 0 -225.5 -50t-77.5 -147q0 -60 37 -100.5t96.5 -62t131 -39t143 -40t131 -55t96.5 -94t37 -147.5q0 -85 -45 -154t-121 -108q121 -76 121 -226q0 -145 -113.5 -231t-300.5 -86q-118 0 -234.5 38t-179.5 97zM209 641q0 -44 19 -78t46 -55t74 -40t85 -29t99 -25.5 t97 -26.5q77 21 124.5 74t47.5 129q0 44 -19 78t-46 55t-74 40t-85 29t-98 25.5t-96 26.5q-79 -23 -126.5 -75.5t-47.5 -127.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="1228" d="M344 1378q0 37 25.5 62.5t62.5 25.5q36 0 62 -26t26 -62q0 -38 -26 -63t-62 -25q-37 0 -62.5 25t-25.5 63zM709 1378q0 36 26 62t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63t-62.5 -25q-36 0 -62 25t-26 63z" />
<glyph unicode="&#xa9;" horiz-adv-x="1656" d="M106 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM182 717q0 -179 84 -327t232.5 -234t328.5 -86 q135 0 255 51t206 137.5t136.5 207t50.5 255.5q0 180 -83.5 327.5t-231 231.5t-329.5 84t-331.5 -86t-233.5 -234t-84 -327zM420 717q0 187 120.5 306.5t305.5 119.5q103 0 188 -42t137 -116l-88 -65q-83 116 -239 116q-133 0 -220 -88t-87 -231t87.5 -231.5t219.5 -88.5 q156 0 239 117l88 -63q-51 -76 -136 -118t-189 -42q-185 0 -305.5 119.5t-120.5 306.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="821" d="M84 1061q0 84 64.5 134t203.5 50h215v33q0 84 -46.5 124t-141.5 40q-140 0 -232 -76l-43 72q53 41 129.5 65.5t155.5 24.5q140 0 213.5 -64t73.5 -190v-393h-101v98q-31 -48 -92 -76.5t-147 -28.5q-120 0 -186 51.5t-66 135.5zM186 1067q0 -56 45 -86.5t127 -30.5 q154 0 209 119v105h-202q-91 0 -135 -28.5t-44 -78.5z" />
<glyph unicode="&#xab;" horiz-adv-x="976" d="M102 539l297 389h136l-291 -389l291 -387h-136zM467 539l297 389h135l-291 -389l291 -387h-135z" />
<glyph unicode="&#xac;" horiz-adv-x="1177" d="M143 657v121h891v-489h-129v368h-762z" />
<glyph unicode="&#xad;" horiz-adv-x="782" d="M123 492v126h536v-126h-536z" />
<glyph unicode="&#xae;" horiz-adv-x="1656" d="M106 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM182 717q0 -179 84 -327t232.5 -234t328.5 -86 q135 0 255 51t206 137.5t136.5 207t50.5 255.5q0 180 -83.5 327.5t-231 231.5t-329.5 84t-331.5 -86t-233.5 -234t-84 -327zM547 303v827h319q152 0 240 -74t88 -200q0 -95 -48 -161t-136 -97l192 -295h-110l-179 277q-16 -2 -47 -2h-213v-275h-106zM651 668h209 q109 0 169.5 49.5t60.5 138.5q0 88 -60.5 136t-169.5 48h-209v-372z" />
<glyph unicode="&#xaf;" horiz-adv-x="1228" d="M299 1321v96h631v-96h-631z" />
<glyph unicode="&#xb0;" horiz-adv-x="858" d="M102 1120q0 135 95 229.5t231 94.5q138 0 233 -94.5t95 -229.5q0 -137 -95 -231t-233 -94q-137 0 -231.5 94.5t-94.5 230.5zM193 1120q0 -101 67.5 -170t167.5 -69q101 0 169.5 69t68.5 170q0 100 -68.5 169t-169.5 69q-100 0 -167.5 -68.5t-67.5 -169.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="1177" d="M143 0v121h891v-121h-891zM143 721v123h381v366h129v-366h381v-123h-381v-367h-129v367h-381z" />
<glyph unicode="&#xb2;" horiz-adv-x="880" d="M66 1399q108 129 331 129q160 0 246 -65.5t86 -168.5q0 -66 -33.5 -123.5t-126.5 -140.5l-303 -268h504v-92h-672v71l389 344q76 68 103.5 112.5t27.5 90.5q0 64 -56.5 105t-170.5 41q-164 0 -250 -95z" />
<glyph unicode="&#xb3;" horiz-adv-x="880" d="M63 770l48 82q48 -43 128 -69.5t175 -26.5q120 0 181.5 42.5t61.5 116.5q0 76 -59.5 118t-179.5 42h-86v76l256 276h-502v93h641v-72l-262 -287q146 -7 224.5 -73t78.5 -173q0 -111 -92.5 -182t-261.5 -71q-112 0 -206.5 29t-144.5 79z" />
<glyph unicode="&#xb4;" horiz-adv-x="1228" d="M481 1243l281 254h194l-331 -254h-144z" />
<glyph unicode="&#xb5;" d="M203 -397v1474h145v-610q0 -170 84.5 -258t241.5 -88q169 0 267.5 104t98.5 287v565h146v-1077h-139v201q-54 -105 -151.5 -158t-219.5 -53q-224 0 -328 153v-540h-145z" />
<glyph unicode="&#xb6;" horiz-adv-x="1294" d="M43 1165q0 163 120 259t320 96h578v-1725h-123v1610h-352v-1610h-123v1014q-188 2 -304 98.5t-116 257.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="516" d="M150 555q0 49 31 81t77 32t78.5 -32t32.5 -81t-32.5 -81t-78.5 -32t-77 32t-31 81z" />
<glyph unicode="&#xb8;" horiz-adv-x="1228" d="M397 -403l35 77q59 -39 135 -39q61 0 94 25.5t33 71.5q0 43 -32.5 68.5t-94.5 25.5h-47l49 186h88l-30 -118q83 -7 128.5 -52t45.5 -114q0 -82 -64 -130.5t-168 -48.5q-101 0 -172 48z" />
<glyph unicode="&#xb9;" horiz-adv-x="880" d="M172 670v92h248v665h-234v93h346v-758h222v-92h-582z" />
<glyph unicode="&#xba;" horiz-adv-x="847" d="M70 1202q0 142 100 234t254 92t254 -92t100 -234t-100 -235t-254 -93t-254 93t-100 235zM176 1202q0 -105 69.5 -170t178.5 -65t178.5 65t69.5 170q0 104 -69.5 170t-178.5 66t-178.5 -66t-69.5 -170z" />
<glyph unicode="&#xbb;" horiz-adv-x="976" d="M78 152l291 387l-291 389h135l297 -389l-297 -387h-135zM442 152l291 387l-291 389h136l296 -389l-296 -387h-136z" />
<glyph unicode="&#xbc;" horiz-adv-x="2107" d="M172 584v92h248v665h-234v93h346v-758h222v-92h-582zM504 0l979 1434h121l-979 -1434h-121zM1282 217v74l422 559h123l-412 -543h352v187h99v-187h180v-90h-180v-217h-107v217h-477z" />
<glyph unicode="&#xbd;" horiz-adv-x="2107" d="M172 584v92h248v665h-234v93h346v-758h222v-92h-582zM504 0l979 1434h121l-979 -1434h-121zM1292 729q108 129 332 129q160 0 246 -65t86 -168q0 -66 -33.5 -124t-126.5 -141l-303 -268h504v-92h-672v72l389 344q76 68 103.5 112t27.5 90q0 64 -56.5 105t-170.5 41 q-165 0 -250 -94z" />
<glyph unicode="&#xbe;" horiz-adv-x="2107" d="M63 684l48 82q48 -43 128 -69.5t175 -26.5q120 0 181.5 42.5t61.5 116.5q0 76 -59.5 118t-179.5 42h-86v76l256 276h-502v93h641v-72l-262 -287q146 -7 224.5 -73t78.5 -173q0 -111 -93 -182.5t-261 -71.5q-112 0 -206.5 29.5t-144.5 79.5zM504 0l979 1434h121 l-979 -1434h-121zM1282 217v74l422 559h123l-412 -543h352v187h99v-187h180v-90h-180v-217h-107v217h-477z" />
<glyph unicode="&#xbf;" horiz-adv-x="1161" d="M154 16q0 68 21 125t54 97.5t72.5 77t79 70.5t72.5 70t54 84.5t21 104.5h152q0 -65 -20.5 -121t-54 -96t-73.5 -76.5t-79.5 -70.5t-73 -69t-54 -80.5t-20.5 -98.5q0 -100 89 -166t241 -66q254 0 391 166l109 -82q-175 -213 -510 -213q-215 0 -343 94t-128 250zM498 979 q0 45 30.5 75.5t75.5 30.5q44 0 75.5 -31t31.5 -75q0 -43 -31.5 -74t-75.5 -31q-45 0 -75.5 31t-30.5 74z" />
<glyph unicode="&#xc0;" horiz-adv-x="1640" d="M221 0v813q0 308 160.5 470.5t437.5 162.5q278 0 439 -162.5t161 -470.5v-813h-149v422h-901v-422h-148zM369 553h901v274q0 240 -118.5 362t-332.5 122q-212 0 -331 -122.5t-119 -361.5v-274zM477 1804h195l280 -254h-143z" />
<glyph unicode="&#xc1;" horiz-adv-x="1640" d="M221 0v813q0 308 160.5 470.5t437.5 162.5q278 0 439 -162.5t161 -470.5v-813h-149v422h-901v-422h-148zM369 553h901v274q0 240 -118.5 362t-332.5 122q-212 0 -331 -122.5t-119 -361.5v-274zM686 1550l281 254h194l-332 -254h-143z" />
<glyph unicode="&#xc2;" horiz-adv-x="1640" d="M221 0v813q0 308 160.5 470.5t437.5 162.5q278 0 439 -162.5t161 -470.5v-813h-149v422h-901v-422h-148zM369 553h901v274q0 240 -118.5 362t-332.5 122q-212 0 -331 -122.5t-119 -361.5v-274zM481 1550l264 254h148l264 -254h-135l-203 170l-203 -170h-135z" />
<glyph unicode="&#xc3;" horiz-adv-x="1640" d="M221 0v813q0 308 160.5 470.5t437.5 162.5q278 0 439 -162.5t161 -470.5v-813h-149v422h-901v-422h-148zM369 553h901v274q0 240 -118.5 362t-332.5 122q-212 0 -331 -122.5t-119 -361.5v-274zM496 1571q3 103 52.5 164t131.5 61q35 0 68.5 -14.5t58.5 -35t48 -40.5 t47 -34.5t46 -14.5q46 0 75 34.5t32 92.5h88q-3 -100 -53.5 -160.5t-131.5 -60.5q-35 0 -68 14.5t-58 35t-48 40.5t-47.5 34.5t-46.5 14.5q-46 0 -74.5 -35t-31.5 -96h-88z" />
<glyph unicode="&#xc4;" horiz-adv-x="1640" d="M221 0v813q0 308 160.5 470.5t437.5 162.5q278 0 439 -162.5t161 -470.5v-813h-149v422h-901v-422h-148zM369 553h901v274q0 240 -118.5 362t-332.5 122q-212 0 -331 -122.5t-119 -361.5v-274zM549 1686q0 37 25.5 62.5t62.5 25.5q36 0 62 -26t26 -62q0 -38 -26 -63.5 t-62 -25.5q-37 0 -62.5 25.5t-25.5 63.5zM913 1686q0 36 26 62t62 26q37 0 63 -25.5t26 -62.5q0 -38 -26 -63.5t-63 -25.5q-36 0 -62 25.5t-26 63.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1640" d="M221 0v813q0 308 160.5 470.5t437.5 162.5q278 0 439 -162.5t161 -470.5v-813h-149v422h-901v-422h-148zM369 553h901v274q0 240 -118.5 362t-332.5 122q-212 0 -331 -122.5t-119 -361.5v-274zM604 1784q0 89 61.5 152t151.5 63q91 0 153 -63t62 -152q0 -87 -62 -148 t-153 -61t-152 61t-61 148zM680 1784q0 -59 38 -98t99 -39t100 39t39 98q0 60 -39.5 101.5t-99.5 41.5t-98.5 -40.5t-38.5 -102.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2189" d="M201 0v813q0 297 151.5 459t429.5 162h1295v-129h-834v-514h746v-125h-746v-537h864v-129h-1013v442h-742v-442h-151zM352 569h742v727h-301q-215 0 -328 -124.5t-113 -362.5v-240z" />
<glyph unicode="&#xc7;" horiz-adv-x="1472" d="M106 717q0 207 97.5 374t269.5 261t385 94q159 0 294.5 -53t229.5 -156l-96 -96q-167 170 -424 170q-171 0 -309.5 -77t-217.5 -213t-79 -304t79 -304t217.5 -213t309.5 -77q258 0 424 172l96 -96q-93 -102 -227 -155.5t-291 -55.5l-24 -94q83 -7 128.5 -52t45.5 -114 q0 -82 -64 -130.5t-168 -48.5q-101 0 -172 48l35 77q59 -39 135 -39q61 0 94 25.5t33 71.5q0 43 -32.5 68.5t-94.5 25.5h-47l43 166q-144 13 -268.5 73t-212.5 155t-138.5 223.5t-50.5 273.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1298" d="M113 387q0 130 76 219.5t202 124.5q-102 38 -159.5 123.5t-57.5 200.5q0 169 144.5 280t410.5 111q132 0 255.5 -30t211.5 -85l-47 -121q-189 107 -414 107q-198 0 -303.5 -75.5t-105.5 -197.5q0 -121 84.5 -186t240.5 -65h357v-131h-363q-176 0 -278.5 -67t-102.5 -198 q0 -129 111.5 -204.5t329.5 -75.5q152 0 282.5 42.5t212.5 119.5l57 -115q-89 -84 -235 -130t-324 -46q-282 0 -433.5 110t-151.5 289zM367 1804h194l281 -254h-144z" />
<glyph unicode="&#xc9;" horiz-adv-x="1298" d="M113 387q0 130 76 219.5t202 124.5q-102 38 -159.5 123.5t-57.5 200.5q0 169 144.5 280t410.5 111q132 0 255.5 -30t211.5 -85l-47 -121q-189 107 -414 107q-198 0 -303.5 -75.5t-105.5 -197.5q0 -121 84.5 -186t240.5 -65h357v-131h-363q-176 0 -278.5 -67t-102.5 -198 q0 -129 111.5 -204.5t329.5 -75.5q152 0 282.5 42.5t212.5 119.5l57 -115q-89 -84 -235 -130t-324 -46q-282 0 -433.5 110t-151.5 289zM575 1550l281 254h195l-332 -254h-144z" />
<glyph unicode="&#xca;" horiz-adv-x="1298" d="M113 387q0 130 76 219.5t202 124.5q-102 38 -159.5 123.5t-57.5 200.5q0 169 144.5 280t410.5 111q132 0 255.5 -30t211.5 -85l-47 -121q-189 107 -414 107q-198 0 -303.5 -75.5t-105.5 -197.5q0 -121 84.5 -186t240.5 -65h357v-131h-363q-176 0 -278.5 -67t-102.5 -198 q0 -129 111.5 -204.5t329.5 -75.5q152 0 282.5 42.5t212.5 119.5l57 -115q-89 -84 -235 -130t-324 -46q-282 0 -433.5 110t-151.5 289zM371 1550l264 254h147l265 -254h-136l-202 170l-203 -170h-135z" />
<glyph unicode="&#xcb;" horiz-adv-x="1298" d="M113 387q0 130 76 219.5t202 124.5q-102 38 -159.5 123.5t-57.5 200.5q0 169 144.5 280t410.5 111q132 0 255.5 -30t211.5 -85l-47 -121q-189 107 -414 107q-198 0 -303.5 -75.5t-105.5 -197.5q0 -121 84.5 -186t240.5 -65h357v-131h-363q-176 0 -278.5 -67t-102.5 -198 q0 -129 111.5 -204.5t329.5 -75.5q152 0 282.5 42.5t212.5 119.5l57 -115q-89 -84 -235 -130t-324 -46q-282 0 -433.5 110t-151.5 289zM438 1686q0 37 25.5 62.5t62.5 25.5q36 0 62 -26t26 -62q0 -38 -26 -63.5t-62 -25.5q-37 0 -62.5 25.5t-25.5 63.5zM803 1686q0 36 26 62 t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63.5t-62.5 -25.5q-36 0 -62 25.5t-26 63.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="911" d="M90 0v131h291v1172h-291v131h731v-131h-291v-1172h291v-131h-731zM115 1804h194l281 -254h-144z" />
<glyph unicode="&#xcd;" horiz-adv-x="911" d="M90 0v131h291v1172h-291v131h731v-131h-291v-1172h291v-131h-731zM324 1550l280 254h195l-332 -254h-143z" />
<glyph unicode="&#xce;" horiz-adv-x="911" d="M90 0v131h291v1172h-291v131h731v-131h-291v-1172h291v-131h-731zM119 1550l264 254h147l265 -254h-136l-202 170l-203 -170h-135z" />
<glyph unicode="&#xcf;" horiz-adv-x="911" d="M90 0v131h291v1172h-291v131h731v-131h-291v-1172h291v-131h-731zM186 1686q0 37 25.5 62.5t62.5 25.5q36 0 62 -26t26 -62q0 -38 -26 -63.5t-62 -25.5q-37 0 -62.5 25.5t-25.5 63.5zM551 1686q0 36 26 62t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63.5 t-62.5 -25.5q-36 0 -62 25.5t-26 63.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1701" d="M27 670v121h217v643h583q226 0 401.5 -91t271 -254t95.5 -372t-95.5 -372t-271 -254t-401.5 -91h-583v670h-217zM395 131h424q283 0 455 163t172 423t-172 423t-455 163h-424v-512h426v-121h-426v-539z" />
<glyph unicode="&#xd1;" horiz-adv-x="1636" d="M233 0v1434h146v-242q137 254 485 254q258 0 404.5 -153.5t146.5 -438.5v-854h-151v850q0 227 -112.5 344t-315.5 117q-213 0 -332 -124.5t-119 -371.5v-815h-152zM500 1571q3 103 52.5 164t131.5 61q35 0 68.5 -14.5t58.5 -35t48 -40.5t47 -34.5t46 -14.5q46 0 75 34.5 t32 92.5h88q-3 -100 -53 -160.5t-131 -60.5q-43 0 -84 21.5t-67.5 48t-58.5 48t-59 21.5q-46 0 -74.5 -35t-31.5 -96h-88z" />
<glyph unicode="&#xd2;" horiz-adv-x="1718" d="M106 717q0 206 97.5 373t270 261.5t386.5 94.5q159 0 298.5 -55.5t239 -151.5t157 -231.5t57.5 -290.5t-57.5 -290.5t-157 -231.5t-239 -151.5t-298.5 -55.5q-214 0 -386.5 94.5t-270 261.5t-97.5 373zM256 717q0 -169 78.5 -305t216.5 -212.5t309 -76.5q258 0 429 169 t171 425t-171 425t-429 169q-171 0 -309 -76.5t-216.5 -212.5t-78.5 -305zM518 1804h195l280 -254h-143z" />
<glyph unicode="&#xd3;" horiz-adv-x="1718" d="M106 717q0 206 97.5 373t270 261.5t386.5 94.5q159 0 298.5 -55.5t239 -151.5t157 -231.5t57.5 -290.5t-57.5 -290.5t-157 -231.5t-239 -151.5t-298.5 -55.5q-214 0 -386.5 94.5t-270 261.5t-97.5 373zM256 717q0 -169 78.5 -305t216.5 -212.5t309 -76.5q258 0 429 169 t171 425t-171 425t-429 169q-171 0 -309 -76.5t-216.5 -212.5t-78.5 -305zM727 1550l281 254h194l-332 -254h-143z" />
<glyph unicode="&#xd4;" horiz-adv-x="1718" d="M106 717q0 206 97.5 373t270 261.5t386.5 94.5q159 0 298.5 -55.5t239 -151.5t157 -231.5t57.5 -290.5t-57.5 -290.5t-157 -231.5t-239 -151.5t-298.5 -55.5q-214 0 -386.5 94.5t-270 261.5t-97.5 373zM256 717q0 -169 78.5 -305t216.5 -212.5t309 -76.5q258 0 429 169 t171 425t-171 425t-429 169q-171 0 -309 -76.5t-216.5 -212.5t-78.5 -305zM522 1550l264 254h148l264 -254h-135l-203 170l-203 -170h-135z" />
<glyph unicode="&#xd5;" horiz-adv-x="1718" d="M106 717q0 206 97.5 373t270 261.5t386.5 94.5q159 0 298.5 -55.5t239 -151.5t157 -231.5t57.5 -290.5t-57.5 -290.5t-157 -231.5t-239 -151.5t-298.5 -55.5q-214 0 -386.5 94.5t-270 261.5t-97.5 373zM256 717q0 -169 78.5 -305t216.5 -212.5t309 -76.5q258 0 429 169 t171 425t-171 425t-429 169q-171 0 -309 -76.5t-216.5 -212.5t-78.5 -305zM537 1571q3 103 52.5 164t131.5 61q35 0 68.5 -14.5t58.5 -35t48 -40.5t47 -34.5t46 -14.5q46 0 75 34.5t32 92.5h88q-3 -100 -53.5 -160.5t-131.5 -60.5q-35 0 -68 14.5t-58 35t-48 40.5 t-47.5 34.5t-46.5 14.5q-46 0 -74.5 -35t-31.5 -96h-88z" />
<glyph unicode="&#xd6;" horiz-adv-x="1718" d="M106 717q0 206 97.5 373t270 261.5t386.5 94.5q159 0 298.5 -55.5t239 -151.5t157 -231.5t57.5 -290.5t-57.5 -290.5t-157 -231.5t-239 -151.5t-298.5 -55.5q-214 0 -386.5 94.5t-270 261.5t-97.5 373zM256 717q0 -169 78.5 -305t216.5 -212.5t309 -76.5q258 0 429 169 t171 425t-171 425t-429 169q-171 0 -309 -76.5t-216.5 -212.5t-78.5 -305zM590 1686q0 37 25.5 62.5t62.5 25.5q36 0 62 -26t26 -62q0 -38 -26 -63.5t-62 -25.5q-37 0 -62.5 25.5t-25.5 63.5zM954 1686q0 36 26 62t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63.5 t-62.5 -25.5q-36 0 -62 25.5t-26 63.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1177" d="M233 444l271 273l-271 272l82 88l275 -274l272 274l82 -88l-270 -272l270 -273l-82 -88l-272 275l-275 -275z" />
<glyph unicode="&#xd8;" horiz-adv-x="1718" d="M106 717q0 206 97.5 373t270 261.5t386.5 94.5t393 -100l168 231h121l-207 -285q131 -100 204 -249t73 -326q0 -155 -57.5 -290.5t-157 -231.5t-239 -151.5t-298.5 -55.5q-217 0 -393 98l-166 -229h-123l205 284q-131 100 -204 249t-73 327zM256 717q0 -141 55.5 -260.5 t155.5 -200.5l704 975q-141 80 -311 80q-171 0 -309 -76.5t-216.5 -212.5t-78.5 -305zM549 201q143 -78 311 -78q258 0 429 169t171 425q0 140 -55 259t-152 200z" />
<glyph unicode="&#xd9;" horiz-adv-x="1628" d="M221 580v854h152v-850q0 -226 112 -343.5t312 -117.5q210 0 328 124t118 371v816h152v-1434h-146v242q-140 -254 -481 -254q-256 0 -401.5 153.5t-145.5 438.5zM461 1804h194l281 -254h-143z" />
<glyph unicode="&#xda;" horiz-adv-x="1628" d="M221 580v854h152v-850q0 -226 112 -343.5t312 -117.5q210 0 328 124t118 371v816h152v-1434h-146v242q-140 -254 -481 -254q-256 0 -401.5 153.5t-145.5 438.5zM670 1550l280 254h195l-332 -254h-143z" />
<glyph unicode="&#xdb;" horiz-adv-x="1628" d="M221 580v854h152v-850q0 -226 112 -343.5t312 -117.5q210 0 328 124t118 371v816h152v-1434h-146v242q-140 -254 -481 -254q-256 0 -401.5 153.5t-145.5 438.5zM465 1550l264 254h148l264 -254h-135l-203 170l-203 -170h-135z" />
<glyph unicode="&#xdc;" horiz-adv-x="1628" d="M221 580v854h152v-850q0 -226 112 -343.5t312 -117.5q210 0 328 124t118 371v816h152v-1434h-146v242q-140 -254 -481 -254q-256 0 -401.5 153.5t-145.5 438.5zM532 1686q0 37 26 62.5t63 25.5q36 0 62 -26t26 -62q0 -38 -26 -63.5t-62 -25.5q-37 0 -63 25.5t-26 63.5z M897 1686q0 36 26 62t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63.5t-62.5 -25.5q-36 0 -62 25.5t-26 63.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1613" d="M211 952v482h151v-476q0 -227 113 -344.5t316 -117.5q213 0 331.5 124t118.5 371v443h152v-1018q0 -307 -159.5 -469t-442.5 -162q-354 0 -551 219l69 117q183 -201 482 -201q221 0 335.5 123t114.5 369v192q-139 -244 -465 -244q-267 0 -416 153t-149 439zM668 1550 l280 254h195l-332 -254h-143z" />
<glyph unicode="&#xde;" horiz-adv-x="1470" d="M233 0v1434h152v-187h385q274 0 430 -130.5t156 -360.5t-156 -361t-430 -131h-385v-264h-152zM385 397h381q213 0 325.5 93.5t112.5 265.5q0 173 -112 266.5t-326 93.5h-381v-719z" />
<glyph unicode="&#xdf;" horiz-adv-x="1368" d="M203 0v1020q0 245 135.5 377.5t357.5 132.5q208 0 332.5 -110t124.5 -281q0 -118 -57 -206.5t-150 -135.5q151 -33 239.5 -134t88.5 -255q0 -193 -136 -305.5t-350 -112.5q-139 0 -229 33l25 122q86 -28 198 -28q155 0 249.5 78t94.5 217q0 137 -97.5 213.5t-260.5 76.5 h-119v125q169 1 266 84t97 222q0 122 -84 196t-232 74q-163 0 -255.5 -95t-92.5 -276v-1032h-145z" />
<glyph unicode="&#xe0;" d="M94 539q0 242 152 394t385 152q130 0 236 -54.5t173 -158.5v205h146v-1077h-139v213q-66 -108 -174 -165.5t-242 -57.5q-232 0 -384.5 153.5t-152.5 395.5zM242 539q0 -187 113.5 -303.5t287.5 -116.5q172 0 285.5 116.5t113.5 303.5t-113 303t-286 116 q-174 0 -287.5 -116t-113.5 -303zM332 1497h194l281 -254h-143z" />
<glyph unicode="&#xe1;" d="M94 539q0 242 152 394t385 152q130 0 236 -54.5t173 -158.5v205h146v-1077h-139v213q-66 -108 -174 -165.5t-242 -57.5q-232 0 -384.5 153.5t-152.5 395.5zM242 539q0 -187 113.5 -303.5t287.5 -116.5q172 0 285.5 116.5t113.5 303.5t-113 303t-286 116 q-174 0 -287.5 -116t-113.5 -303zM541 1243l280 254h195l-332 -254h-143z" />
<glyph unicode="&#xe2;" d="M94 539q0 242 152 394t385 152q130 0 236 -54.5t173 -158.5v205h146v-1077h-139v213q-66 -108 -174 -165.5t-242 -57.5q-232 0 -384.5 153.5t-152.5 395.5zM242 539q0 -187 113.5 -303.5t287.5 -116.5q172 0 285.5 116.5t113.5 303.5t-113 303t-286 116 q-174 0 -287.5 -116t-113.5 -303zM336 1243l264 254h148l264 -254h-135l-203 170l-203 -170h-135z" />
<glyph unicode="&#xe3;" d="M94 539q0 242 152 394t385 152q130 0 236 -54.5t173 -158.5v205h146v-1077h-139v213q-66 -108 -174 -165.5t-242 -57.5q-232 0 -384.5 153.5t-152.5 395.5zM242 539q0 -187 113.5 -303.5t287.5 -116.5q172 0 285.5 116.5t113.5 303.5t-113 303t-286 116 q-174 0 -287.5 -116t-113.5 -303zM350 1264q3 103 53 164t132 61q35 0 68.5 -14.5t58.5 -35t48 -40.5t47 -34.5t46 -14.5q46 0 74.5 34.5t31.5 92.5h88q-3 -100 -53 -161t-131 -61q-43 0 -84 22t-67 48t-58 48t-59 22q-46 0 -75 -35t-32 -96h-88z" />
<glyph unicode="&#xe4;" d="M94 539q0 242 152 394t385 152q130 0 236 -54.5t173 -158.5v205h146v-1077h-139v213q-66 -108 -174 -165.5t-242 -57.5q-232 0 -384.5 153.5t-152.5 395.5zM242 539q0 -187 113.5 -303.5t287.5 -116.5q172 0 285.5 116.5t113.5 303.5t-113 303t-286 116 q-174 0 -287.5 -116t-113.5 -303zM403 1378q0 37 26 62.5t63 25.5q36 0 62 -26t26 -62q0 -38 -26 -63t-62 -25q-37 0 -63 25t-26 63zM768 1378q0 36 26 62t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63t-62.5 -25q-36 0 -62 25t-26 63z" />
<glyph unicode="&#xe5;" d="M94 539q0 242 152 394t385 152q130 0 236 -54.5t173 -158.5v205h146v-1077h-139v213q-66 -108 -174 -165.5t-242 -57.5q-232 0 -384.5 153.5t-152.5 395.5zM242 539q0 -187 113.5 -303.5t287.5 -116.5q172 0 285.5 116.5t113.5 303.5t-113 303t-286 116 q-174 0 -287.5 -116t-113.5 -303zM459 1427q0 89 61.5 152t151.5 63q91 0 153 -63t62 -152q0 -87 -61.5 -147.5t-153.5 -60.5q-91 0 -152 60.5t-61 147.5zM535 1427q0 -59 38 -98t99 -39t100 39t39 98q0 61 -39.5 102.5t-99.5 41.5t-98.5 -41t-38.5 -103z" />
<glyph unicode="&#xe6;" horiz-adv-x="2021" d="M113 307q0 62 22 114t69 94.5t129.5 66.5t194.5 24h342v68q0 139 -77.5 211.5t-227.5 72.5q-103 0 -197 -34t-161 -93l-66 109q81 69 196.5 107t240.5 38q311 0 395 -221q71 104 186.5 162.5t253.5 58.5q217 0 361 -144t151 -372l-889 -172q40 -128 151 -203t263 -75 q197 0 326 141l82 -94q-150 -176 -410 -176q-155 0 -285 64t-207 188q-37 -88 -108 -146.5t-152 -82t-176 -23.5q-189 0 -298 87t-109 230zM256 311q0 -97 72.5 -152t201.5 -55q156 0 248 83t92 225v86h-338q-146 0 -211 -51t-65 -136zM1016 504l764 147q-25 136 -124 223 t-243 87q-175 0 -286 -113.5t-111 -290.5v-25v-28z" />
<glyph unicode="&#xe7;" horiz-adv-x="1153" d="M94 539q0 241 156.5 393.5t398.5 152.5q139 0 248.5 -54.5t173.5 -158.5l-108 -73q-54 79 -135.5 119t-178.5 40q-179 0 -293 -116t-114 -303q0 -189 114 -304.5t293 -115.5q207 0 314 157l108 -73q-64 -103 -174 -158t-248 -55h-4l-24 -96q83 -7 128.5 -52t45.5 -114 q0 -82 -64 -130.5t-168 -48.5q-101 0 -172 48l35 77q59 -39 135 -39q61 0 94 25.5t33 71.5q0 43 -32.5 68.5t-94.5 25.5h-47l45 170q-207 30 -336 178.5t-129 364.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1236" d="M94 539q0 239 150.5 392.5t382.5 153.5q218 0 363.5 -144t152.5 -370l-885 -172q40 -131 148 -205.5t258 -74.5q209 0 329 141l82 -94q-72 -86 -178.5 -131t-234.5 -45q-251 0 -409.5 154.5t-158.5 394.5zM236 549q0 -31 2 -45l759 147q-24 136 -124 223t-246 87 q-172 0 -281.5 -114.5t-109.5 -297.5zM272 1497h195l281 -254h-144z" />
<glyph unicode="&#xe9;" horiz-adv-x="1236" d="M94 539q0 239 150.5 392.5t382.5 153.5q218 0 363.5 -144t152.5 -370l-885 -172q40 -131 148 -205.5t258 -74.5q209 0 329 141l82 -94q-72 -86 -178.5 -131t-234.5 -45q-251 0 -409.5 154.5t-158.5 394.5zM236 549q0 -31 2 -45l759 147q-24 136 -124 223t-246 87 q-172 0 -281.5 -114.5t-109.5 -297.5zM481 1243l281 254h194l-331 -254h-144z" />
<glyph unicode="&#xea;" horiz-adv-x="1236" d="M94 539q0 239 150.5 392.5t382.5 153.5q218 0 363.5 -144t152.5 -370l-885 -172q40 -131 148 -205.5t258 -74.5q209 0 329 141l82 -94q-72 -86 -178.5 -131t-234.5 -45q-251 0 -409.5 154.5t-158.5 394.5zM236 549q0 -31 2 -45l759 147q-24 136 -124 223t-246 87 q-172 0 -281.5 -114.5t-109.5 -297.5zM276 1243l265 254h147l264 -254h-135l-203 170l-202 -170h-136z" />
<glyph unicode="&#xeb;" horiz-adv-x="1236" d="M94 539q0 239 150.5 392.5t382.5 153.5q218 0 363.5 -144t152.5 -370l-885 -172q40 -131 148 -205.5t258 -74.5q209 0 329 141l82 -94q-72 -86 -178.5 -131t-234.5 -45q-251 0 -409.5 154.5t-158.5 394.5zM236 549q0 -31 2 -45l759 147q-24 136 -124 223t-246 87 q-172 0 -281.5 -114.5t-109.5 -297.5zM344 1378q0 37 25.5 62.5t62.5 25.5q36 0 62 -26t26 -62q0 -38 -26 -63t-62 -25q-37 0 -62.5 25t-25.5 63zM709 1378q0 36 26 62t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63t-62.5 -25q-36 0 -62 25t-26 63z" />
<glyph unicode="&#xec;" horiz-adv-x="550" d="M-66 1497h195l281 -254h-144zM203 0v1077h145v-1077h-145z" />
<glyph unicode="&#xed;" horiz-adv-x="550" d="M143 1243l281 254h194l-331 -254h-144zM203 0v1077h145v-1077h-145z" />
<glyph unicode="&#xee;" horiz-adv-x="550" d="M-10 1243l215 254h143l215 -254h-131l-156 166l-155 -166h-131zM203 0v1077h145v-1077h-145z" />
<glyph unicode="&#xef;" horiz-adv-x="550" d="M55 1378q0 36 25 61t61 25q35 0 59.5 -24.5t24.5 -61.5q0 -38 -24 -63t-60 -25q-37 0 -61.5 25.5t-24.5 62.5zM203 0v1077h145v-1077h-145zM328 1378q0 35 24.5 60.5t59.5 25.5q37 0 61.5 -24.5t24.5 -61.5q0 -38 -24.5 -63t-61.5 -25q-35 0 -59.5 25.5t-24.5 62.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1296" d="M94 440q0 205 142.5 329t369.5 124q156 0 274 -71t171 -201q4 43 4 120q0 339 -195 498l-549 -229l-41 100l473 197q-100 41 -225 41q-157 0 -301 -45l-24 124q144 43 325 43q227 0 381 -98l182 76l41 -100l-125 -52q205 -202 205 -585q0 -336 -158 -529.5t-446 -193.5 q-220 0 -362 124.5t-142 327.5zM242 440q0 -147 100.5 -237t263.5 -90q91 0 166 27.5t123 73.5t74 104.5t26 121.5q0 139 -105.5 233.5t-268.5 94.5q-177 0 -278 -89.5t-101 -238.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1386" d="M203 0v1077h139v-198q59 99 165 152.5t245 53.5q203 0 322.5 -117.5t119.5 -342.5v-625h-145v610q0 169 -84.5 256.5t-239.5 87.5q-175 0 -276 -103t-101 -286v-565h-145zM373 1264q3 103 52.5 164t131.5 61q35 0 68.5 -14.5t58.5 -35t48 -40.5t47 -34.5t46 -14.5 q46 0 75 34.5t32 92.5h88q-3 -100 -53 -161t-131 -61q-43 0 -84 22t-67.5 48t-58.5 48t-59 22q-46 0 -74.5 -35t-31.5 -96h-88z" />
<glyph unicode="&#xf2;" horiz-adv-x="1284" d="M94 539q0 238 156 392t393 154t392 -154t155 -392q0 -118 -41.5 -220t-113.5 -174.5t-173.5 -113.5t-218.5 -41t-218.5 41t-174.5 113.5t-114.5 174.5t-41.5 220zM242 539q0 -187 113.5 -303.5t287.5 -116.5t286.5 116.5t112.5 303.5t-112.5 303t-286.5 116t-287.5 -116 t-113.5 -303zM299 1497h195l280 -254h-143z" />
<glyph unicode="&#xf3;" horiz-adv-x="1284" d="M94 539q0 238 156 392t393 154t392 -154t155 -392q0 -118 -41.5 -220t-113.5 -174.5t-173.5 -113.5t-218.5 -41t-218.5 41t-174.5 113.5t-114.5 174.5t-41.5 220zM242 539q0 -187 113.5 -303.5t287.5 -116.5t286.5 116.5t112.5 303.5t-112.5 303t-286.5 116t-287.5 -116 t-113.5 -303zM508 1243l280 254h195l-332 -254h-143z" />
<glyph unicode="&#xf4;" horiz-adv-x="1284" d="M94 539q0 238 156 392t393 154t392 -154t155 -392q0 -118 -41.5 -220t-113.5 -174.5t-173.5 -113.5t-218.5 -41t-218.5 41t-174.5 113.5t-114.5 174.5t-41.5 220zM242 539q0 -187 113.5 -303.5t287.5 -116.5t286.5 116.5t112.5 303.5t-112.5 303t-286.5 116t-287.5 -116 t-113.5 -303zM303 1243l264 254h148l264 -254h-135l-203 170l-203 -170h-135z" />
<glyph unicode="&#xf5;" horiz-adv-x="1284" d="M94 539q0 238 156 392t393 154t392 -154t155 -392q0 -118 -41.5 -220t-113.5 -174.5t-173.5 -113.5t-218.5 -41t-218.5 41t-174.5 113.5t-114.5 174.5t-41.5 220zM242 539q0 -187 113.5 -303.5t287.5 -116.5t286.5 116.5t112.5 303.5t-112.5 303t-286.5 116t-287.5 -116 t-113.5 -303zM317 1264q3 103 53 164t132 61q35 0 68.5 -14.5t58.5 -35t48 -40.5t47 -34.5t46 -14.5q46 0 75 34.5t32 92.5h88q-3 -100 -53.5 -161t-131.5 -61q-43 0 -84 22t-67 48t-58 48t-59 22q-46 0 -74.5 -35t-31.5 -96h-89z" />
<glyph unicode="&#xf6;" horiz-adv-x="1284" d="M94 539q0 238 156 392t393 154t392 -154t155 -392q0 -118 -41.5 -220t-113.5 -174.5t-173.5 -113.5t-218.5 -41t-218.5 41t-174.5 113.5t-114.5 174.5t-41.5 220zM242 539q0 -187 113.5 -303.5t287.5 -116.5t286.5 116.5t112.5 303.5t-112.5 303t-286.5 116t-287.5 -116 t-113.5 -303zM371 1378q0 37 25.5 62.5t62.5 25.5q36 0 62 -26t26 -62q0 -38 -26 -63t-62 -25q-37 0 -62.5 25t-25.5 63zM735 1378q0 36 26 62t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63t-62.5 -25q-36 0 -62 25t-26 63z" />
<glyph unicode="&#xf7;" horiz-adv-x="1177" d="M143 655v121h891v-121h-891zM492 326q0 42 28 70t68 28q42 0 70 -28t28 -70q0 -43 -28.5 -72t-69.5 -29q-40 0 -68 29t-28 72zM492 1108q0 44 28 72t68 28q42 0 70 -28t28 -72q0 -41 -28.5 -69.5t-69.5 -28.5q-40 0 -68 28.5t-28 69.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1284" d="M94 539q0 238 156 392t393 154q152 0 270 -65l131 197h97l-160 -240q98 -73 153.5 -187t55.5 -251q0 -118 -41.5 -220t-113.5 -174.5t-173.5 -113.5t-218.5 -41q-147 0 -274 65l-133 -200h-99l164 245q-98 75 -152.5 188t-54.5 251zM242 539q0 -205 135 -324l463 694 q-85 49 -197 49q-174 0 -287.5 -116t-113.5 -303zM444 168q85 -49 199 -49q174 0 286.5 116.5t112.5 303.5q0 206 -135 325z" />
<glyph unicode="&#xf9;" horiz-adv-x="1378" d="M193 453v624h145v-610q0 -170 84 -258t240 -88q170 0 268 104t98 287v565h146v-1077h-140v197q-57 -99 -158 -153t-229 -54q-211 0 -332.5 118.5t-121.5 344.5zM342 1497h195l280 -254h-143z" />
<glyph unicode="&#xfa;" horiz-adv-x="1378" d="M193 453v624h145v-610q0 -170 84 -258t240 -88q170 0 268 104t98 287v565h146v-1077h-140v197q-57 -99 -158 -153t-229 -54q-211 0 -332.5 118.5t-121.5 344.5zM551 1243l280 254h195l-332 -254h-143z" />
<glyph unicode="&#xfb;" horiz-adv-x="1378" d="M193 453v624h145v-610q0 -170 84 -258t240 -88q170 0 268 104t98 287v565h146v-1077h-140v197q-57 -99 -158 -153t-229 -54q-211 0 -332.5 118.5t-121.5 344.5zM346 1243l264 254h148l264 -254h-135l-203 170l-203 -170h-135z" />
<glyph unicode="&#xfc;" horiz-adv-x="1378" d="M193 453v624h145v-610q0 -170 84 -258t240 -88q170 0 268 104t98 287v565h146v-1077h-140v197q-57 -99 -158 -153t-229 -54q-211 0 -332.5 118.5t-121.5 344.5zM414 1378q0 37 25.5 62.5t62.5 25.5q36 0 62 -26t26 -62q0 -38 -26 -63t-62 -25q-37 0 -62.5 25t-25.5 63z M778 1378q0 36 26 62t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63t-62.5 -25q-36 0 -62 25t-26 63z" />
<glyph unicode="&#xfd;" horiz-adv-x="1378" d="M193 504v573h145v-559q0 -170 84 -258t240 -88q170 0 268 104t98 287v514h146v-946q0 -276 -125 -407.5t-377 -131.5q-140 0 -267 44t-204 122l73 111q70 -70 172.5 -109t219.5 -39q187 0 274.5 96t87.5 296v125q-57 -94 -156 -145.5t-225 -51.5q-211 0 -332.5 118.5 t-121.5 344.5zM551 1243l280 254h195l-332 -254h-143z" />
<glyph unicode="&#xfe;" d="M203 -397v1917h145v-646q66 102 172.5 156.5t237.5 54.5q233 0 384.5 -152t151.5 -394q0 -121 -41 -224t-112 -174t-170 -111t-213 -40q-130 0 -236.5 55t-173.5 158v-600h-145zM346 539q0 -187 114 -303.5t288 -116.5q173 0 287 116.5t114 303.5q0 185 -114.5 302 t-286.5 117q-174 0 -288 -116.5t-114 -302.5z" />
<glyph unicode="&#xff;" horiz-adv-x="1378" d="M193 504v573h145v-559q0 -170 84 -258t240 -88q170 0 268 104t98 287v514h146v-946q0 -276 -125 -407.5t-377 -131.5q-140 0 -267 44t-204 122l73 111q70 -70 172.5 -109t219.5 -39q187 0 274.5 96t87.5 296v125q-57 -94 -156 -145.5t-225 -51.5q-211 0 -332.5 118.5 t-121.5 344.5zM414 1378q0 37 25.5 62.5t62.5 25.5q36 0 62 -26t26 -62q0 -38 -26 -63t-62 -25q-37 0 -62.5 25t-25.5 63zM778 1378q0 36 26 62t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63t-62.5 -25q-36 0 -62 25t-26 63z" />
<glyph unicode="&#x152;" horiz-adv-x="2295" d="M106 715q0 210 95.5 373.5t270.5 254.5t402 91h1268v-131h-831v-510h741v-129h-741v-533h862v-131h-1299q-226 0 -401.5 91t-271 253.5t-95.5 370.5zM256 715q0 -260 171.5 -422t453.5 -162h278v1172h-278q-282 0 -453.5 -163.5t-171.5 -424.5z" />
<glyph unicode="&#x153;" horiz-adv-x="2220" d="M94 539q0 238 156 392t393 154q159 0 282.5 -73.5t188.5 -202.5q65 129 190 202.5t287 73.5q148 0 268.5 -65t191 -183t75.5 -268l-918 -172q41 -128 154 -203t268 -75q216 0 342 141l84 -94q-73 -86 -184.5 -131t-245.5 -45q-176 0 -308.5 74.5t-201.5 205.5 q-65 -131 -189.5 -205.5t-283.5 -74.5q-117 0 -218.5 41t-174.5 113.5t-114.5 174.5t-41.5 220zM242 539q0 -187 113.5 -303.5t287.5 -116.5t286.5 116.5t112.5 303.5t-112.5 303t-286.5 116t-287.5 -116t-113.5 -303zM1188 504l788 147q-27 136 -130.5 223t-254.5 87 q-177 0 -290 -113.5t-113 -290.5v-25v-28z" />
<glyph unicode="&#x178;" horiz-adv-x="1613" d="M211 952v482h151v-476q0 -227 113 -344.5t316 -117.5q213 0 331.5 124t118.5 371v443h152v-1018q0 -307 -159.5 -469t-442.5 -162q-354 0 -551 219l69 117q183 -201 482 -201q221 0 335.5 123t114.5 369v192q-139 -244 -465 -244q-267 0 -416 153t-149 439zM530 1686 q0 37 25.5 62.5t62.5 25.5q36 0 62.5 -26t26.5 -62q0 -38 -26.5 -63.5t-62.5 -25.5q-37 0 -62.5 25.5t-25.5 63.5zM895 1686q0 36 26 62t62 26q37 0 62.5 -25.5t25.5 -62.5q0 -38 -25.5 -63.5t-62.5 -25.5q-36 0 -62 25.5t-26 63.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1228" d="M276 1243l265 254h147l264 -254h-135l-203 170l-202 -170h-136z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1228" d="M291 1264q3 103 52.5 164t131.5 61q35 0 68.5 -14.5t58.5 -35t48 -40.5t47 -34.5t46 -14.5q46 0 75 34.5t32 92.5h88q-3 -100 -53 -161t-131 -61q-43 0 -84 22t-67.5 48t-58.5 48t-59 22q-46 0 -74.5 -35t-31.5 -96h-88z" />
<glyph unicode="&#x2000;" horiz-adv-x="999" />
<glyph unicode="&#x2001;" horiz-adv-x="1999" />
<glyph unicode="&#x2002;" horiz-adv-x="999" />
<glyph unicode="&#x2003;" horiz-adv-x="1999" />
<glyph unicode="&#x2004;" horiz-adv-x="666" />
<glyph unicode="&#x2005;" horiz-adv-x="499" />
<glyph unicode="&#x2006;" horiz-adv-x="333" />
<glyph unicode="&#x2007;" horiz-adv-x="333" />
<glyph unicode="&#x2008;" horiz-adv-x="249" />
<glyph unicode="&#x2009;" horiz-adv-x="399" />
<glyph unicode="&#x200a;" horiz-adv-x="111" />
<glyph unicode="&#x2010;" horiz-adv-x="782" d="M123 492v126h536v-126h-536z" />
<glyph unicode="&#x2011;" horiz-adv-x="782" d="M123 492v126h536v-126h-536z" />
<glyph unicode="&#x2012;" horiz-adv-x="782" d="M123 492v126h536v-126h-536z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 504v104h1024v-104h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 504v104h2048v-104h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="434" d="M109 1120q0 40 26 117l86 283h98l-69 -295q34 -11 54 -39t20 -66q0 -49 -31 -79.5t-76 -30.5q-48 0 -78 31.5t-30 78.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="434" d="M111 1419q0 49 31 80t77 31q47 0 78 -32.5t31 -78.5q0 -31 -27 -116l-88 -283h-98l71 295q-35 11 -55 38t-20 66z" />
<glyph unicode="&#x201a;" horiz-adv-x="434" d="M111 102q0 47 31 79t77 32q47 0 78 -32.5t31 -78.5q0 -35 -29 -118l-86 -283h-98l69 297q-33 9 -53 37t-20 67z" />
<glyph unicode="&#x201c;" horiz-adv-x="782" d="M109 1120q0 40 26 117l86 283h98l-69 -295q34 -11 54 -39t20 -66q0 -49 -31 -79.5t-76 -30.5q-48 0 -78 31.5t-30 78.5zM457 1120q0 40 26 117l86 283h99l-70 -295q34 -11 54 -39t20 -66q0 -49 -31 -79.5t-76 -30.5q-48 0 -78 31.5t-30 78.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="782" d="M111 1419q0 49 31 80t77 31q47 0 78 -32.5t31 -78.5q0 -31 -27 -116l-88 -283h-98l71 295q-35 11 -55 38t-20 66zM459 1419q0 49 31 80t77 31q47 0 78 -32.5t31 -78.5q0 -31 -27 -116l-88 -283h-98l72 295q-35 11 -55.5 38t-20.5 66z" />
<glyph unicode="&#x201e;" horiz-adv-x="782" d="M111 102q0 47 31 79t77 32q47 0 78 -32.5t31 -78.5q0 -35 -29 -118l-86 -283h-98l69 297q-33 9 -53 37t-20 67zM459 102q0 47 31 79t77 32q47 0 78 -32.5t31 -78.5q0 -35 -29 -118l-86 -283h-98l69 297q-33 9 -53 37t-20 67z" />
<glyph unicode="&#x2022;" horiz-adv-x="604" d="M150 561q0 65 44.5 109.5t108.5 44.5q63 0 108.5 -44.5t45.5 -109.5q0 -67 -45.5 -112.5t-108.5 -45.5q-64 0 -108.5 45.5t-44.5 112.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1325" d="M109 102q0 47 31.5 79t76.5 32t78 -32t33 -79t-33 -79.5t-78 -32.5t-76.5 32.5t-31.5 79.5zM553 102q0 47 32 79t77 32t77.5 -32t32.5 -79t-32.5 -79.5t-77.5 -32.5t-77 32.5t-32 79.5zM999 102q0 47 32 79t77 32t78 -32t33 -79t-33 -79.5t-78 -32.5t-77 32.5t-32 79.5z " />
<glyph unicode="&#x202f;" horiz-adv-x="399" />
<glyph unicode="&#x2039;" horiz-adv-x="612" d="M102 539l297 389h136l-291 -389l291 -387h-136z" />
<glyph unicode="&#x203a;" horiz-adv-x="612" d="M78 152l291 387l-291 389h135l297 -389l-297 -387h-135z" />
<glyph unicode="&#x205f;" horiz-adv-x="499" />
<glyph unicode="&#x20ac;" horiz-adv-x="1642" d="M61 530v91h222q-7 54 -7 96t7 96h-222v90h238q60 243 258.5 393t468.5 150q161 0 296.5 -53t231.5 -156l-98 -96q-167 170 -422 170q-208 0 -364.5 -112t-212.5 -296h702v-90h-725q-6 -47 -6 -96t6 -96h725v-91h-702q56 -183 212 -295t365 -112q256 0 422 172l98 -96 q-96 -104 -231.5 -157.5t-296.5 -53.5q-270 0 -468.5 149.5t-258.5 392.5h-238z" />
<glyph unicode="&#x2122;" horiz-adv-x="2091" d="M8 1339v95h750v-95h-318v-755h-112v755h-320zM889 584v850h92l393 -615l391 615h90l3 -850h-107l-2 655l-350 -551h-51l-351 539v-643h-108z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1075" d="M0 0v1075h1075v-1075h-1075z" />
<hkern u1="&#x2a;" u2="&#xee;" k="-164" />
<hkern u1="&#x2f;" u2="&#xf0;" k="98" />
<hkern u1="&#x2f;" u2="i" k="-10" />
<hkern u1="F" u2="&#xef;" k="-10" />
<hkern u1="F" u2="&#xee;" k="-51" />
<hkern u1="T" u2="&#xef;" k="-51" />
<hkern u1="T" u2="&#xee;" k="-82" />
<hkern u1="V" u2="&#xef;" k="-31" />
<hkern u1="V" u2="&#xee;" k="-66" />
<hkern u1="V" u2="&#xec;" k="-35" />
<hkern u1="V" u2="i" k="37" />
<hkern u1="f" u2="&#xef;" k="-123" />
<hkern u1="f" u2="&#xee;" k="-92" />
<hkern u1="f" u2="&#xec;" k="-139" />
<hkern u1="i" u2="\" k="102" />
<hkern u1="j" u2="\" k="102" />
<hkern u1="q" u2="j" k="-102" />
<hkern u1="&#xa3;" u2="&#xef;" k="-10" />
<hkern u1="&#xa3;" u2="&#xee;" k="-51" />
<hkern u1="&#xaa;" u2="&#xee;" k="-164" />
<hkern u1="&#xba;" u2="&#xee;" k="-164" />
<hkern u1="&#xee;" u2="&#xba;" k="-164" />
<hkern u1="&#xee;" u2="&#xaa;" k="-164" />
<hkern u1="&#xee;" u2="&#x3f;" k="-82" />
<hkern u1="&#xee;" u2="&#x2a;" k="-164" />
<hkern u1="&#x2018;" u2="&#xec;" k="-61" />
<hkern u1="&#x201c;" u2="&#xec;" k="-61" />
<hkern g1="ampersand" 	g2="ampersand" 	k="16" />
<hkern g1="ampersand" 	g2="backslash" 	k="164" />
<hkern g1="ampersand" 	g2="bracketright,braceright" 	k="10" />
<hkern g1="ampersand" 	g2="colon,semicolon" 	k="12" />
<hkern g1="ampersand" 	g2="degree" 	k="76" />
<hkern g1="ampersand" 	g2="exclam" 	k="23" />
<hkern g1="ampersand" 	g2="exclamdown" 	k="20" />
<hkern g1="ampersand" 	g2="four" 	k="-4" />
<hkern g1="ampersand" 	g2="one" 	k="61" />
<hkern g1="ampersand" 	g2="paragraph" 	k="82" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-10" />
<hkern g1="ampersand" 	g2="question" 	k="160" />
<hkern g1="ampersand" 	g2="questiondown" 	k="4" />
<hkern g1="ampersand" 	g2="quoteleft,quotedblleft" 	k="72" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="109" />
<hkern g1="ampersand" 	g2="seven" 	k="37" />
<hkern g1="ampersand" 	g2="slash" 	k="-88" />
<hkern g1="ampersand" 	g2="three" 	k="4" />
<hkern g1="ampersand" 	g2="trademark" 	k="66" />
<hkern g1="ampersand" 	g2="two" 	k="10" />
<hkern g1="ampersand" 	g2="underscore" 	k="-92" />
<hkern g1="currency" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="currency" 	g2="questiondown" 	k="16" />
<hkern g1="currency" 	g2="seven" 	k="31" />
<hkern g1="currency" 	g2="two" 	k="18" />
<hkern g1="currency" 	g2="underscore" 	k="16" />
<hkern g1="currency" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-16" />
<hkern g1="degree" 	g2="backslash" 	k="-106" />
<hkern g1="degree" 	g2="four" 	k="82" />
<hkern g1="degree" 	g2="one" 	k="-86" />
<hkern g1="degree" 	g2="percent" 	k="-45" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="127" />
<hkern g1="degree" 	g2="question" 	k="-41" />
<hkern g1="degree" 	g2="questiondown" 	k="133" />
<hkern g1="degree" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="degree" 	g2="seven" 	k="-92" />
<hkern g1="degree" 	g2="slash" 	k="119" />
<hkern g1="degree" 	g2="three" 	k="-33" />
<hkern g1="degree" 	g2="two" 	k="-61" />
<hkern g1="degree" 	g2="five" 	k="-16" />
<hkern g1="degree" 	g2="nine" 	k="-78" />
<hkern g1="degree" 	g2="zero,six" 	k="-8" />
<hkern g1="percent" 	g2="backslash" 	k="78" />
<hkern g1="percent" 	g2="degree" 	k="57" />
<hkern g1="percent" 	g2="four" 	k="-70" />
<hkern g1="percent" 	g2="one" 	k="41" />
<hkern g1="percent" 	g2="percent" 	k="244" />
<hkern g1="percent" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="percent" 	g2="question" 	k="119" />
<hkern g1="percent" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="percent" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="percent" 	g2="quotedbl,quotesingle" 	k="57" />
<hkern g1="percent" 	g2="seven" 	k="37" />
<hkern g1="percent" 	g2="slash" 	k="-2" />
<hkern g1="percent" 	g2="three" 	k="-20" />
<hkern g1="percent" 	g2="two" 	k="-20" />
<hkern g1="percent" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="percent" 	g2="five" 	k="-41" />
<hkern g1="percent" 	g2="parenright" 	k="41" />
<hkern g1="percent" 	g2="eight" 	k="-41" />
<hkern g1="section" 	g2="four" 	k="-37" />
<hkern g1="section" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="section" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="section" 	g2="seven" 	k="-16" />
<hkern g1="section" 	g2="slash" 	k="-51" />
<hkern g1="section" 	g2="underscore" 	k="-16" />
<hkern g1="section" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="section" 	g2="eight" 	k="-20" />
<hkern g1="trademark" 	g2="backslash" 	k="-82" />
<hkern g1="trademark" 	g2="exclamdown" 	k="-4" />
<hkern g1="trademark" 	g2="questiondown" 	k="31" />
<hkern g1="trademark" 	g2="seven" 	k="-25" />
<hkern g1="trademark" 	g2="slash" 	k="92" />
<hkern g1="trademark" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="trademark" 	g2="nine" 	k="-61" />
<hkern g1="yen" 	g2="backslash" 	k="-102" />
<hkern g1="yen" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="yen" 	g2="colon,semicolon" 	k="82" />
<hkern g1="yen" 	g2="exclam" 	k="-14" />
<hkern g1="yen" 	g2="exclamdown" 	k="41" />
<hkern g1="yen" 	g2="four" 	k="20" />
<hkern g1="yen" 	g2="one" 	k="-61" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="yen" 	g2="questiondown" 	k="82" />
<hkern g1="yen" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="yen" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="yen" 	g2="seven" 	k="-57" />
<hkern g1="yen" 	g2="slash" 	k="61" />
<hkern g1="yen" 	g2="three" 	k="-20" />
<hkern g1="yen" 	g2="underscore" 	k="20" />
<hkern g1="yen" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="45" />
<hkern g1="yen" 	g2="zero,six" 	k="23" />
<hkern g1="yen" 	g2="parenright" 	k="-20" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="backslash" 	g2="ampersand" 	k="-37" />
<hkern g1="backslash" 	g2="backslash" 	k="133" />
<hkern g1="backslash" 	g2="bracketright,braceright" 	k="-72" />
<hkern g1="backslash" 	g2="colon,semicolon" 	k="-92" />
<hkern g1="backslash" 	g2="degree" 	k="123" />
<hkern g1="backslash" 	g2="exclamdown" 	k="-82" />
<hkern g1="backslash" 	g2="five" 	k="-4" />
<hkern g1="backslash" 	g2="four" 	k="16" />
<hkern g1="backslash" 	g2="guillemotright,guilsinglright" 	k="-23" />
<hkern g1="backslash" 	g2="one" 	k="41" />
<hkern g1="backslash" 	g2="paragraph" 	k="61" />
<hkern g1="backslash" 	g2="parenright" 	k="-61" />
<hkern g1="backslash" 	g2="percent" 	k="20" />
<hkern g1="backslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-211" />
<hkern g1="backslash" 	g2="question" 	k="123" />
<hkern g1="backslash" 	g2="questiondown" 	k="-61" />
<hkern g1="backslash" 	g2="quoteleft,quotedblleft" 	k="113" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="113" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="backslash" 	g2="section" 	k="-61" />
<hkern g1="backslash" 	g2="seven" 	k="102" />
<hkern g1="backslash" 	g2="slash" 	k="-31" />
<hkern g1="backslash" 	g2="three" 	k="-20" />
<hkern g1="backslash" 	g2="trademark" 	k="123" />
<hkern g1="backslash" 	g2="two" 	k="-20" />
<hkern g1="backslash" 	g2="underscore" 	k="-287" />
<hkern g1="backslash" 	g2="zero,six" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="backslash" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="exclamdown" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="25" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="-31" />
<hkern g1="bracketleft,braceleft" 	g2="paragraph" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="parenright" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="question" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="seven" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="slash" 	k="-72" />
<hkern g1="bracketleft,braceleft" 	g2="three" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="trademark" 	k="-92" />
<hkern g1="bracketleft,braceleft" 	g2="two" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="underscore" 	k="-51" />
<hkern g1="bracketleft,braceleft" 	g2="exclam" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="yen" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="35" />
<hkern g1="colon,semicolon" 	g2="question" 	k="20" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="-16" />
<hkern g1="colon,semicolon" 	g2="slash" 	k="-92" />
<hkern g1="colon,semicolon" 	g2="underscore" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="yen" 	k="82" />
<hkern g1="exclam" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="exclam" 	g2="one" 	k="-20" />
<hkern g1="exclam" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="exclam" 	g2="quoteleft,quotedblleft" 	k="-4" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="-4" />
<hkern g1="exclam" 	g2="quotedbl,quotesingle" 	k="-4" />
<hkern g1="exclam" 	g2="seven" 	k="-16" />
<hkern g1="exclam" 	g2="trademark" 	k="-10" />
<hkern g1="exclam" 	g2="yen" 	k="-14" />
<hkern g1="exclamdown" 	g2="backslash" 	k="113" />
<hkern g1="exclamdown" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="exclamdown" 	g2="one" 	k="41" />
<hkern g1="exclamdown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="exclamdown" 	g2="question" 	k="4" />
<hkern g1="exclamdown" 	g2="quoteleft,quotedblleft" 	k="-4" />
<hkern g1="exclamdown" 	g2="quoteright,quotedblright" 	k="-4" />
<hkern g1="exclamdown" 	g2="slash" 	k="-82" />
<hkern g1="exclamdown" 	g2="trademark" 	k="4" />
<hkern g1="exclamdown" 	g2="underscore" 	k="-14" />
<hkern g1="exclamdown" 	g2="yen" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="119" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="five" 	k="-16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="-16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="one" 	k="4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="question" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="questiondown" 	k="-16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="slash" 	k="-23" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="underscore" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="backslash" 	k="133" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="four" 	k="-16" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="one" 	k="35" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="paragraph" 	k="180" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="percent" 	k="78" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="12" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="question" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="section" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="seven" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="three" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="two" 	k="25" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="zero,six" 	k="-16" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-4" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="yen" 	k="45" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="currency,Euro" 	k="-16" />
<hkern g1="parenleft" 	g2="backslash" 	k="-72" />
<hkern g1="parenleft" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="parenleft" 	g2="four" 	k="41" />
<hkern g1="parenleft" 	g2="one" 	k="-20" />
<hkern g1="parenleft" 	g2="slash" 	k="-66" />
<hkern g1="parenleft" 	g2="trademark" 	k="-51" />
<hkern g1="parenleft" 	g2="underscore" 	k="-4" />
<hkern g1="parenleft" 	g2="yen" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="201" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="degree" 	k="127" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclamdown" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="27" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="guillemotright,guilsinglright" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="paragraph" 	k="250" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="127" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="106" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="questiondown" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="113" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="slash" 	k="-211" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="two" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="underscore" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero,six" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclam" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="currency,Euro" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="-20" />
<hkern g1="question" 	g2="ampersand" 	k="47" />
<hkern g1="question" 	g2="degree" 	k="-20" />
<hkern g1="question" 	g2="exclamdown" 	k="16" />
<hkern g1="question" 	g2="four" 	k="102" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="question" 	g2="question" 	k="66" />
<hkern g1="question" 	g2="questiondown" 	k="225" />
<hkern g1="question" 	g2="quoteleft,quotedblleft" 	k="-45" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="-45" />
<hkern g1="question" 	g2="slash" 	k="61" />
<hkern g1="question" 	g2="three" 	k="31" />
<hkern g1="question" 	g2="underscore" 	k="43" />
<hkern g1="question" 	g2="exclam" 	k="16" />
<hkern g1="question" 	g2="yen" 	k="31" />
<hkern g1="question" 	g2="eight" 	k="20" />
<hkern g1="question" 	g2="bracketleft" 	k="39" />
<hkern g1="question" 	g2="nine" 	k="-20" />
<hkern g1="questiondown" 	g2="ampersand" 	k="37" />
<hkern g1="questiondown" 	g2="backslash" 	k="201" />
<hkern g1="questiondown" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="questiondown" 	g2="colon,semicolon" 	k="4" />
<hkern g1="questiondown" 	g2="degree" 	k="113" />
<hkern g1="questiondown" 	g2="four" 	k="86" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="questiondown" 	g2="one" 	k="86" />
<hkern g1="questiondown" 	g2="paragraph" 	k="178" />
<hkern g1="questiondown" 	g2="percent" 	k="123" />
<hkern g1="questiondown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="questiondown" 	g2="question" 	k="270" />
<hkern g1="questiondown" 	g2="questiondown" 	k="61" />
<hkern g1="questiondown" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="questiondown" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="questiondown" 	g2="section" 	k="-4" />
<hkern g1="questiondown" 	g2="seven" 	k="86" />
<hkern g1="questiondown" 	g2="slash" 	k="-82" />
<hkern g1="questiondown" 	g2="trademark" 	k="72" />
<hkern g1="questiondown" 	g2="underscore" 	k="-102" />
<hkern g1="questiondown" 	g2="zero,six" 	k="92" />
<hkern g1="questiondown" 	g2="exclam" 	k="4" />
<hkern g1="questiondown" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="82" />
<hkern g1="questiondown" 	g2="yen" 	k="102" />
<hkern g1="questiondown" 	g2="sterling" 	k="-10" />
<hkern g1="questiondown" 	g2="currency,Euro" 	k="47" />
<hkern g1="questiondown" 	g2="eight" 	k="86" />
<hkern g1="quoteleft,quotedblleft" 	g2="ampersand" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="backslash" 	k="-113" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="-4" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="78" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-78" />
<hkern g1="quoteleft,quotedblleft" 	g2="paragraph" 	k="-78" />
<hkern g1="quoteleft,quotedblleft" 	g2="percent" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="quoteleft,quotedblleft" 	g2="question" 	k="-78" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteleft,quotedblleft" 	k="-51" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="quoteleft,quotedblleft" 	g2="section" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-92" />
<hkern g1="quoteleft,quotedblleft" 	g2="slash" 	k="113" />
<hkern g1="quoteleft,quotedblleft" 	g2="three" 	k="-47" />
<hkern g1="quoteleft,quotedblleft" 	g2="trademark" 	k="-47" />
<hkern g1="quoteleft,quotedblleft" 	g2="two" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclam" 	k="-4" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="nine" 	k="-72" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="backslash" 	k="-113" />
<hkern g1="quoteright,quotedblright" 	g2="exclamdown" 	k="-4" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="115" />
<hkern g1="quoteright,quotedblright" 	g2="one" 	k="-98" />
<hkern g1="quoteright,quotedblright" 	g2="paragraph" 	k="-78" />
<hkern g1="quoteright,quotedblright" 	g2="percent" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="quoteright,quotedblright" 	g2="question" 	k="-78" />
<hkern g1="quoteright,quotedblright" 	g2="questiondown" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="quoteleft,quotedblleft" 	k="-51" />
<hkern g1="quoteright,quotedblright" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="quoteright,quotedblright" 	g2="section" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-57" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="113" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="-37" />
<hkern g1="quoteright,quotedblright" 	g2="trademark" 	k="-66" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="exclam" 	k="-4" />
<hkern g1="quoteright,quotedblright" 	g2="yen" 	k="-10" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="backslash" 	k="-123" />
<hkern g1="quotedbl,quotesingle" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="degree" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="five" 	k="-16" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-98" />
<hkern g1="quotedbl,quotesingle" 	g2="paragraph" 	k="-102" />
<hkern g1="quotedbl,quotesingle" 	g2="percent" 	k="-57" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="quotedbl,quotesingle" 	g2="question" 	k="-72" />
<hkern g1="quotedbl,quotesingle" 	g2="questiondown" 	k="100" />
<hkern g1="quotedbl,quotesingle" 	g2="quotedbl,quotesingle" 	k="-92" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-109" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-37" />
<hkern g1="quotedbl,quotesingle" 	g2="trademark" 	k="-78" />
<hkern g1="quotedbl,quotesingle" 	g2="two" 	k="-51" />
<hkern g1="quotedbl,quotesingle" 	g2="underscore" 	k="16" />
<hkern g1="quotedbl,quotesingle" 	g2="zero,six" 	k="-16" />
<hkern g1="quotedbl,quotesingle" 	g2="exclam" 	k="-4" />
<hkern g1="quotedbl,quotesingle" 	g2="yen" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="currency,Euro" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="-8" />
<hkern g1="quotedbl,quotesingle" 	g2="nine" 	k="-92" />
<hkern g1="slash" 	g2="ampersand" 	k="78" />
<hkern g1="slash" 	g2="backslash" 	k="-61" />
<hkern g1="slash" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="31" />
<hkern g1="slash" 	g2="degree" 	k="-98" />
<hkern g1="slash" 	g2="exclamdown" 	k="133" />
<hkern g1="slash" 	g2="four" 	k="139" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="109" />
<hkern g1="slash" 	g2="one" 	k="-61" />
<hkern g1="slash" 	g2="parenright" 	k="-66" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="201" />
<hkern g1="slash" 	g2="questiondown" 	k="154" />
<hkern g1="slash" 	g2="quoteleft,quotedblleft" 	k="-109" />
<hkern g1="slash" 	g2="quoteright,quotedblright" 	k="-113" />
<hkern g1="slash" 	g2="quotedbl,quotesingle" 	k="-123" />
<hkern g1="slash" 	g2="section" 	k="41" />
<hkern g1="slash" 	g2="seven" 	k="-31" />
<hkern g1="slash" 	g2="slash" 	k="133" />
<hkern g1="slash" 	g2="trademark" 	k="-98" />
<hkern g1="slash" 	g2="underscore" 	k="256" />
<hkern g1="slash" 	g2="zero,six" 	k="41" />
<hkern g1="slash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="137" />
<hkern g1="slash" 	g2="yen" 	k="-82" />
<hkern g1="slash" 	g2="sterling" 	k="20" />
<hkern g1="slash" 	g2="eight" 	k="45" />
<hkern g1="underscore" 	g2="backslash" 	k="266" />
<hkern g1="underscore" 	g2="bracketright,braceright" 	k="-51" />
<hkern g1="underscore" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="underscore" 	g2="exclamdown" 	k="-14" />
<hkern g1="underscore" 	g2="five" 	k="-20" />
<hkern g1="underscore" 	g2="four" 	k="57" />
<hkern g1="underscore" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="underscore" 	g2="one" 	k="18" />
<hkern g1="underscore" 	g2="paragraph" 	k="162" />
<hkern g1="underscore" 	g2="parenright" 	k="-4" />
<hkern g1="underscore" 	g2="percent" 	k="57" />
<hkern g1="underscore" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="underscore" 	g2="question" 	k="20" />
<hkern g1="underscore" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="underscore" 	g2="section" 	k="-57" />
<hkern g1="underscore" 	g2="slash" 	k="-272" />
<hkern g1="underscore" 	g2="three" 	k="-57" />
<hkern g1="underscore" 	g2="trademark" 	k="102" />
<hkern g1="underscore" 	g2="two" 	k="-72" />
<hkern g1="underscore" 	g2="zero,six" 	k="41" />
<hkern g1="underscore" 	g2="yen" 	k="20" />
<hkern g1="underscore" 	g2="sterling" 	k="-37" />
<hkern g1="underscore" 	g2="currency,Euro" 	k="16" />
<hkern g1="underscore" 	g2="nine" 	k="-20" />
<hkern g1="eight" 	g2="AE" 	k="10" />
<hkern g1="eight" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="8" />
<hkern g1="eight" 	g2="J" 	k="74" />
<hkern g1="eight" 	g2="T" 	k="10" />
<hkern g1="eight" 	g2="V" 	k="33" />
<hkern g1="eight" 	g2="X" 	k="43" />
<hkern g1="eight" 	g2="Z" 	k="8" />
<hkern g1="eight" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-16" />
<hkern g1="eight" 	g2="backslash" 	k="61" />
<hkern g1="eight" 	g2="j" 	k="16" />
<hkern g1="eight" 	g2="percent" 	k="20" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="eight" 	g2="question" 	k="20" />
<hkern g1="eight" 	g2="questiondown" 	k="41" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="-8" />
<hkern g1="eight" 	g2="section" 	k="-20" />
<hkern g1="eight" 	g2="v" 	k="-12" />
<hkern g1="eight" 	g2="x" 	k="8" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="five" 	g2="J" 	k="86" />
<hkern g1="five" 	g2="V" 	k="12" />
<hkern g1="five" 	g2="X" 	k="23" />
<hkern g1="five" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="five" 	g2="backslash" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="20" />
<hkern g1="five" 	g2="v" 	k="14" />
<hkern g1="five" 	g2="x" 	k="14" />
<hkern g1="five" 	g2="degree" 	k="31" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="seven" 	k="29" />
<hkern g1="five" 	g2="three" 	k="10" />
<hkern g1="five" 	g2="two" 	k="10" />
<hkern g1="four" 	g2="J" 	k="16" />
<hkern g1="four" 	g2="T" 	k="104" />
<hkern g1="four" 	g2="V" 	k="106" />
<hkern g1="four" 	g2="X" 	k="14" />
<hkern g1="four" 	g2="Z" 	k="16" />
<hkern g1="four" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="percent" 	k="61" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="four" 	g2="question" 	k="106" />
<hkern g1="four" 	g2="questiondown" 	k="-20" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="four" 	g2="section" 	k="-39" />
<hkern g1="four" 	g2="v" 	k="31" />
<hkern g1="four" 	g2="x" 	k="25" />
<hkern g1="four" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="degree" 	k="61" />
<hkern g1="four" 	g2="five" 	k="8" />
<hkern g1="four" 	g2="seven" 	k="102" />
<hkern g1="four" 	g2="three" 	k="31" />
<hkern g1="four" 	g2="two" 	k="8" />
<hkern g1="four" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="four" 	g2="dollar,S" 	k="16" />
<hkern g1="four" 	g2="ae" 	k="-59" />
<hkern g1="four" 	g2="slash" 	k="-57" />
<hkern g1="four" 	g2="underscore" 	k="-41" />
<hkern g1="four" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="-41" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="four" 	g2="ampersand" 	k="-41" />
<hkern g1="four" 	g2="colon,semicolon" 	k="-12" />
<hkern g1="four" 	g2="currency,Euro" 	k="-20" />
<hkern g1="four" 	g2="eight" 	k="-20" />
<hkern g1="four" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="four" 	g2="sterling" 	k="-20" />
<hkern g1="four" 	g2="nine" 	k="16" />
<hkern g1="four" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-25" />
<hkern g1="four" 	g2="one" 	k="51" />
<hkern g1="four" 	g2="paragraph" 	k="31" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="four" 	g2="uniFB02" 	k="-2" />
<hkern g1="four" 	g2="trademark" 	k="41" />
<hkern g1="seven" 	g2="AE" 	k="135" />
<hkern g1="seven" 	g2="J" 	k="37" />
<hkern g1="seven" 	g2="T" 	k="-20" />
<hkern g1="seven" 	g2="V" 	k="-10" />
<hkern g1="seven" 	g2="X" 	k="8" />
<hkern g1="seven" 	g2="Z" 	k="16" />
<hkern g1="seven" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-57" />
<hkern g1="seven" 	g2="backslash" 	k="-20" />
<hkern g1="seven" 	g2="j" 	k="43" />
<hkern g1="seven" 	g2="percent" 	k="-4" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="109" />
<hkern g1="seven" 	g2="question" 	k="-31" />
<hkern g1="seven" 	g2="questiondown" 	k="133" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-61" />
<hkern g1="seven" 	g2="section" 	k="20" />
<hkern g1="seven" 	g2="v" 	k="10" />
<hkern g1="seven" 	g2="x" 	k="47" />
<hkern g1="seven" 	g2="yen" 	k="-25" />
<hkern g1="seven" 	g2="degree" 	k="-8" />
<hkern g1="seven" 	g2="five" 	k="39" />
<hkern g1="seven" 	g2="three" 	k="18" />
<hkern g1="seven" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="39" />
<hkern g1="seven" 	g2="dollar,S" 	k="16" />
<hkern g1="seven" 	g2="ae" 	k="80" />
<hkern g1="seven" 	g2="slash" 	k="88" />
<hkern g1="seven" 	g2="underscore" 	k="61" />
<hkern g1="seven" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="8" />
<hkern g1="seven" 	g2="ampersand" 	k="72" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="41" />
<hkern g1="seven" 	g2="currency,Euro" 	k="41" />
<hkern g1="seven" 	g2="eight" 	k="39" />
<hkern g1="seven" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="102" />
<hkern g1="seven" 	g2="sterling" 	k="29" />
<hkern g1="seven" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="seven" 	g2="one" 	k="-20" />
<hkern g1="seven" 	g2="paragraph" 	k="-57" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="seven" 	g2="uniFB02" 	k="31" />
<hkern g1="seven" 	g2="trademark" 	k="-82" />
<hkern g1="seven" 	g2="exclamdown" 	k="57" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="seven" 	g2="s" 	k="61" />
<hkern g1="seven" 	g2="z" 	k="61" />
<hkern g1="seven" 	g2="zero,six" 	k="41" />
<hkern g1="seven" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="39" />
<hkern g1="seven" 	g2="bracketright,braceright" 	k="6" />
<hkern g1="seven" 	g2="four" 	k="133" />
<hkern g1="seven" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="53" />
<hkern g1="seven" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="57" />
<hkern g1="six" 	g2="AE" 	k="-16" />
<hkern g1="six" 	g2="J" 	k="51" />
<hkern g1="six" 	g2="T" 	k="31" />
<hkern g1="six" 	g2="V" 	k="20" />
<hkern g1="six" 	g2="X" 	k="16" />
<hkern g1="six" 	g2="asterisk,ordfeminine,ordmasculine" 	k="31" />
<hkern g1="six" 	g2="j" 	k="16" />
<hkern g1="six" 	g2="percent" 	k="41" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="six" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="six" 	g2="section" 	k="-41" />
<hkern g1="six" 	g2="v" 	k="18" />
<hkern g1="six" 	g2="x" 	k="31" />
<hkern g1="six" 	g2="degree" 	k="43" />
<hkern g1="six" 	g2="seven" 	k="16" />
<hkern g1="six" 	g2="slash" 	k="-20" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="six" 	g2="ampersand" 	k="-20" />
<hkern g1="six" 	g2="currency,Euro" 	k="-20" />
<hkern g1="six" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="six" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-18" />
<hkern g1="three" 	g2="AE" 	k="-8" />
<hkern g1="three" 	g2="J" 	k="53" />
<hkern g1="three" 	g2="V" 	k="20" />
<hkern g1="three" 	g2="X" 	k="20" />
<hkern g1="three" 	g2="j" 	k="12" />
<hkern g1="three" 	g2="percent" 	k="20" />
<hkern g1="three" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="three" 	g2="v" 	k="12" />
<hkern g1="three" 	g2="x" 	k="25" />
<hkern g1="three" 	g2="degree" 	k="31" />
<hkern g1="three" 	g2="five" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="27" />
<hkern g1="three" 	g2="three" 	k="10" />
<hkern g1="three" 	g2="two" 	k="10" />
<hkern g1="two" 	g2="AE" 	k="-4" />
<hkern g1="two" 	g2="J" 	k="-4" />
<hkern g1="two" 	g2="V" 	k="23" />
<hkern g1="two" 	g2="backslash" 	k="16" />
<hkern g1="two" 	g2="percent" 	k="-20" />
<hkern g1="two" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="two" 	g2="v" 	k="-8" />
<hkern g1="two" 	g2="x" 	k="16" />
<hkern g1="two" 	g2="seven" 	k="8" />
<hkern g1="two" 	g2="slash" 	k="-20" />
<hkern g1="two" 	g2="underscore" 	k="-61" />
<hkern g1="two" 	g2="ampersand" 	k="16" />
<hkern g1="two" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="16" />
<hkern g1="two" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="two" 	g2="z" 	k="16" />
<hkern g1="two" 	g2="zero,six" 	k="8" />
<hkern g1="two" 	g2="four" 	k="47" />
<hkern g1="two" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="8" />
<hkern g1="zero,nine" 	g2="AE" 	k="14" />
<hkern g1="zero,nine" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="zero,nine" 	g2="J" 	k="102" />
<hkern g1="zero,nine" 	g2="T" 	k="31" />
<hkern g1="zero,nine" 	g2="V" 	k="51" />
<hkern g1="zero,nine" 	g2="X" 	k="66" />
<hkern g1="zero,nine" 	g2="Z" 	k="43" />
<hkern g1="zero,nine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-16" />
<hkern g1="zero,nine" 	g2="backslash" 	k="41" />
<hkern g1="zero,nine" 	g2="j" 	k="16" />
<hkern g1="zero,nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="23" />
<hkern g1="zero,nine" 	g2="question" 	k="41" />
<hkern g1="zero,nine" 	g2="questiondown" 	k="61" />
<hkern g1="zero,nine" 	g2="quotedbl,quotesingle" 	k="-16" />
<hkern g1="zero,nine" 	g2="v" 	k="-16" />
<hkern g1="zero,nine" 	g2="x" 	k="20" />
<hkern g1="zero,nine" 	g2="yen" 	k="23" />
<hkern g1="zero,nine" 	g2="degree" 	k="-8" />
<hkern g1="zero,nine" 	g2="seven" 	k="16" />
<hkern g1="zero,nine" 	g2="three" 	k="20" />
<hkern g1="zero,nine" 	g2="two" 	k="4" />
<hkern g1="zero,nine" 	g2="slash" 	k="41" />
<hkern g1="zero,nine" 	g2="underscore" 	k="41" />
<hkern g1="zero,nine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-16" />
<hkern g1="zero,nine" 	g2="one" 	k="4" />
<hkern g1="zero,nine" 	g2="uniFB02" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,ordfeminine,ordmasculine" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="6" />
<hkern g1="B" 	g2="J" 	k="61" />
<hkern g1="B" 	g2="T" 	k="10" />
<hkern g1="B" 	g2="V" 	k="10" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="4" />
<hkern g1="B" 	g2="trademark" 	k="4" />
<hkern g1="B" 	g2="underscore" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="J" 	k="57" />
<hkern g1="C,Ccedilla,Euro" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="4" />
<hkern g1="C,Ccedilla,Euro" 	g2="V" 	k="16" />
<hkern g1="C,Ccedilla,Euro" 	g2="X" 	k="37" />
<hkern g1="C,Ccedilla,Euro" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="C,Ccedilla,Euro" 	g2="backslash" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="colon,semicolon" 	k="12" />
<hkern g1="C,Ccedilla,Euro" 	g2="degree" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="eight" 	k="29" />
<hkern g1="C,Ccedilla,Euro" 	g2="exclamdown" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="four" 	k="61" />
<hkern g1="C,Ccedilla,Euro" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="C,Ccedilla,Euro" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="4" />
<hkern g1="C,Ccedilla,Euro" 	g2="nine" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="C,Ccedilla,Euro" 	g2="one" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="paragraph" 	k="-16" />
<hkern g1="C,Ccedilla,Euro" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-29" />
<hkern g1="C,Ccedilla,Euro" 	g2="questiondown" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteleft,quotedblleft" 	k="-6" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteright,quotedblright" 	k="-33" />
<hkern g1="C,Ccedilla,Euro" 	g2="s" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="three" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="37" />
<hkern g1="C,Ccedilla,Euro" 	g2="underscore" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="x" 	k="37" />
<hkern g1="C,Ccedilla,Euro" 	g2="z" 	k="37" />
<hkern g1="C,Ccedilla,Euro" 	g2="zero,six" 	k="47" />
<hkern g1="C,Ccedilla,Euro" 	g2="AE" 	k="37" />
<hkern g1="C,Ccedilla,Euro" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="27" />
<hkern g1="C,Ccedilla,Euro" 	g2="dollar,S" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="ae" 	k="16" />
<hkern g1="C,Ccedilla,Euro" 	g2="ampersand" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="section" 	k="16" />
<hkern g1="C,Ccedilla,Euro" 	g2="two" 	k="16" />
<hkern g1="AE,OE" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="AE,OE" 	g2="V" 	k="20" />
<hkern g1="AE,OE" 	g2="X" 	k="18" />
<hkern g1="AE,OE" 	g2="colon,semicolon" 	k="16" />
<hkern g1="AE,OE" 	g2="eight" 	k="16" />
<hkern g1="AE,OE" 	g2="exclamdown" 	k="16" />
<hkern g1="AE,OE" 	g2="four" 	k="78" />
<hkern g1="AE,OE" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="AE,OE" 	g2="nine" 	k="8" />
<hkern g1="AE,OE" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="AE,OE" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="AE,OE" 	g2="question" 	k="31" />
<hkern g1="AE,OE" 	g2="s" 	k="16" />
<hkern g1="AE,OE" 	g2="seven" 	k="16" />
<hkern g1="AE,OE" 	g2="uniFB02" 	k="31" />
<hkern g1="AE,OE" 	g2="three" 	k="31" />
<hkern g1="AE,OE" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="16" />
<hkern g1="AE,OE" 	g2="underscore" 	k="-31" />
<hkern g1="AE,OE" 	g2="v" 	k="23" />
<hkern g1="AE,OE" 	g2="x" 	k="31" />
<hkern g1="AE,OE" 	g2="z" 	k="23" />
<hkern g1="AE,OE" 	g2="zero,six" 	k="33" />
<hkern g1="AE,OE" 	g2="ae" 	k="16" />
<hkern g1="AE,OE" 	g2="five" 	k="16" />
<hkern g1="AE,OE" 	g2="section" 	k="16" />
<hkern g1="AE,OE" 	g2="two" 	k="16" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="J" 	k="37" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="T" 	k="4" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="X" 	k="16" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="eight" 	k="29" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="four" 	k="82" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="nine" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="question" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="questiondown" 	k="-4" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="s" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="slash" 	k="-20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="uniFB02" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="three" 	k="-4" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="18" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="underscore" 	k="-82" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="v" 	k="29" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="x" 	k="29" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="zero,six" 	k="39" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="ae" 	k="8" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="parenright" 	k="23" />
<hkern g1="F,sterling" 	g2="J" 	k="72" />
<hkern g1="F,sterling" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="F,sterling" 	g2="X" 	k="20" />
<hkern g1="F,sterling" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="F,sterling" 	g2="backslash" 	k="-37" />
<hkern g1="F,sterling" 	g2="colon,semicolon" 	k="61" />
<hkern g1="F,sterling" 	g2="eight" 	k="70" />
<hkern g1="F,sterling" 	g2="exclamdown" 	k="51" />
<hkern g1="F,sterling" 	g2="four" 	k="78" />
<hkern g1="F,sterling" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="F,sterling" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="F,sterling" 	g2="nine" 	k="41" />
<hkern g1="F,sterling" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="F,sterling" 	g2="one" 	k="-37" />
<hkern g1="F,sterling" 	g2="paragraph" 	k="-31" />
<hkern g1="F,sterling" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="4" />
<hkern g1="F,sterling" 	g2="questiondown" 	k="47" />
<hkern g1="F,sterling" 	g2="quoteleft,quotedblleft" 	k="4" />
<hkern g1="F,sterling" 	g2="quoteright,quotedblright" 	k="-4" />
<hkern g1="F,sterling" 	g2="s" 	k="82" />
<hkern g1="F,sterling" 	g2="seven" 	k="-16" />
<hkern g1="F,sterling" 	g2="slash" 	k="-23" />
<hkern g1="F,sterling" 	g2="uniFB02" 	k="41" />
<hkern g1="F,sterling" 	g2="three" 	k="20" />
<hkern g1="F,sterling" 	g2="trademark" 	k="-72" />
<hkern g1="F,sterling" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="57" />
<hkern g1="F,sterling" 	g2="underscore" 	k="-37" />
<hkern g1="F,sterling" 	g2="v" 	k="41" />
<hkern g1="F,sterling" 	g2="x" 	k="51" />
<hkern g1="F,sterling" 	g2="z" 	k="82" />
<hkern g1="F,sterling" 	g2="zero,six" 	k="78" />
<hkern g1="F,sterling" 	g2="AE" 	k="92" />
<hkern g1="F,sterling" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="F,sterling" 	g2="dollar,S" 	k="20" />
<hkern g1="F,sterling" 	g2="ae" 	k="82" />
<hkern g1="F,sterling" 	g2="ampersand" 	k="82" />
<hkern g1="F,sterling" 	g2="five" 	k="25" />
<hkern g1="F,sterling" 	g2="section" 	k="20" />
<hkern g1="F,sterling" 	g2="two" 	k="20" />
<hkern g1="F,sterling" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="F,sterling" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="F,sterling" 	g2="j" 	k="51" />
<hkern g1="F,sterling" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="41" />
<hkern g1="G" 	g2="J" 	k="20" />
<hkern g1="G" 	g2="backslash" 	k="37" />
<hkern g1="G" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="H,U,paragraph,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="16" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="eight" 	k="8" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="four" 	k="20" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="nine" 	k="8" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="trademark" 	k="-41" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="zero,six" 	k="20" />
<hkern g1="J" 	g2="J" 	k="41" />
<hkern g1="J" 	g2="underscore" 	k="20" />
<hkern g1="K" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="K" 	g2="T" 	k="41" />
<hkern g1="K" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="K" 	g2="V" 	k="82" />
<hkern g1="K" 	g2="X" 	k="76" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="57" />
<hkern g1="K" 	g2="degree" 	k="20" />
<hkern g1="K" 	g2="eight" 	k="43" />
<hkern g1="K" 	g2="four" 	k="98" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="K" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="66" />
<hkern g1="K" 	g2="nine" 	k="20" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="K" 	g2="one" 	k="-41" />
<hkern g1="K" 	g2="paragraph" 	k="10" />
<hkern g1="K" 	g2="question" 	k="82" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="K" 	g2="s" 	k="37" />
<hkern g1="K" 	g2="slash" 	k="-16" />
<hkern g1="K" 	g2="uniFB02" 	k="57" />
<hkern g1="K" 	g2="three" 	k="16" />
<hkern g1="K" 	g2="trademark" 	k="-41" />
<hkern g1="K" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="39" />
<hkern g1="K" 	g2="underscore" 	k="-139" />
<hkern g1="K" 	g2="v" 	k="74" />
<hkern g1="K" 	g2="x" 	k="80" />
<hkern g1="K" 	g2="z" 	k="45" />
<hkern g1="K" 	g2="zero,six" 	k="43" />
<hkern g1="K" 	g2="AE" 	k="55" />
<hkern g1="K" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="K" 	g2="dollar,S" 	k="31" />
<hkern g1="K" 	g2="ae" 	k="29" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="two" 	k="-2" />
<hkern g1="K" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="K" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="K" 	g2="j" 	k="8" />
<hkern g1="K" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="K" 	g2="bracketleft" 	k="61" />
<hkern g1="L" 	g2="J" 	k="-61" />
<hkern g1="L" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="L" 	g2="T" 	k="133" />
<hkern g1="L" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="27" />
<hkern g1="L" 	g2="V" 	k="109" />
<hkern g1="L" 	g2="X" 	k="20" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="57" />
<hkern g1="L" 	g2="asterisk,ordfeminine,ordmasculine" 	k="143" />
<hkern g1="L" 	g2="backslash" 	k="143" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="L" 	g2="degree" 	k="133" />
<hkern g1="L" 	g2="eight" 	k="16" />
<hkern g1="L" 	g2="four" 	k="78" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-16" />
<hkern g1="L" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="27" />
<hkern g1="L" 	g2="nine" 	k="20" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="L" 	g2="one" 	k="78" />
<hkern g1="L" 	g2="paragraph" 	k="154" />
<hkern g1="L" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="L" 	g2="question" 	k="195" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="86" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="94" />
<hkern g1="L" 	g2="seven" 	k="102" />
<hkern g1="L" 	g2="slash" 	k="-51" />
<hkern g1="L" 	g2="uniFB02" 	k="10" />
<hkern g1="L" 	g2="three" 	k="-4" />
<hkern g1="L" 	g2="trademark" 	k="133" />
<hkern g1="L" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="16" />
<hkern g1="L" 	g2="underscore" 	k="-129" />
<hkern g1="L" 	g2="v" 	k="80" />
<hkern g1="L" 	g2="x" 	k="16" />
<hkern g1="L" 	g2="zero,six" 	k="51" />
<hkern g1="L" 	g2="AE" 	k="-4" />
<hkern g1="L" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="16" />
<hkern g1="L" 	g2="dollar,S" 	k="16" />
<hkern g1="L" 	g2="ae" 	k="-4" />
<hkern g1="L" 	g2="ampersand" 	k="-4" />
<hkern g1="M,N,Ntilde" 	g2="backslash" 	k="20" />
<hkern g1="M,N,Ntilde" 	g2="one" 	k="8" />
<hkern g1="M,N,Ntilde" 	g2="question" 	k="10" />
<hkern g1="M,N,Ntilde" 	g2="quoteleft,quotedblleft" 	k="23" />
<hkern g1="M,N,Ntilde" 	g2="trademark" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="43" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="51" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="degree" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-16" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="questiondown" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="39" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="uniFB02" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="23" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="25" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="-31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="j" 	k="-164" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="18" />
<hkern g1="P" 	g2="J" 	k="98" />
<hkern g1="P" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="P" 	g2="V" 	k="33" />
<hkern g1="P" 	g2="X" 	k="51" />
<hkern g1="P" 	g2="backslash" 	k="41" />
<hkern g1="P" 	g2="degree" 	k="-41" />
<hkern g1="P" 	g2="eight" 	k="12" />
<hkern g1="P" 	g2="four" 	k="61" />
<hkern g1="P" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="4" />
<hkern g1="P" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="59" />
<hkern g1="P" 	g2="questiondown" 	k="109" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="slash" 	k="98" />
<hkern g1="P" 	g2="uniFB02" 	k="-10" />
<hkern g1="P" 	g2="three" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="66" />
<hkern g1="P" 	g2="v" 	k="16" />
<hkern g1="P" 	g2="x" 	k="70" />
<hkern g1="P" 	g2="AE" 	k="92" />
<hkern g1="P" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="P" 	g2="ae" 	k="31" />
<hkern g1="P" 	g2="ampersand" 	k="68" />
<hkern g1="P" 	g2="five" 	k="20" />
<hkern g1="P" 	g2="two" 	k="10" />
<hkern g1="P" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="53" />
<hkern g1="P" 	g2="parenright" 	k="23" />
<hkern g1="P" 	g2="bracketright,braceright" 	k="23" />
<hkern g1="P" 	g2="j" 	k="20" />
<hkern g1="P" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="47" />
<hkern g1="P" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="P" 	g2="Z" 	k="37" />
<hkern g1="P" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="20" />
<hkern g1="R" 	g2="J" 	k="61" />
<hkern g1="R" 	g2="X" 	k="41" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="degree" 	k="-31" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="2" />
<hkern g1="R" 	g2="question" 	k="10" />
<hkern g1="R" 	g2="questiondown" 	k="20" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="R" 	g2="uniFB02" 	k="-10" />
<hkern g1="R" 	g2="three" 	k="16" />
<hkern g1="R" 	g2="underscore" 	k="-37" />
<hkern g1="R" 	g2="ae" 	k="10" />
<hkern g1="dollar,S" 	g2="J" 	k="61" />
<hkern g1="dollar,S" 	g2="T" 	k="18" />
<hkern g1="dollar,S" 	g2="X" 	k="29" />
<hkern g1="dollar,S" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="20" />
<hkern g1="dollar,S" 	g2="colon,semicolon" 	k="10" />
<hkern g1="dollar,S" 	g2="degree" 	k="31" />
<hkern g1="dollar,S" 	g2="exclamdown" 	k="2" />
<hkern g1="dollar,S" 	g2="four" 	k="-20" />
<hkern g1="dollar,S" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-37" />
<hkern g1="dollar,S" 	g2="nine" 	k="20" />
<hkern g1="dollar,S" 	g2="one" 	k="20" />
<hkern g1="dollar,S" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="questiondown" 	k="20" />
<hkern g1="dollar,S" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="dollar,S" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="dollar,S" 	g2="seven" 	k="20" />
<hkern g1="dollar,S" 	g2="three" 	k="16" />
<hkern g1="dollar,S" 	g2="underscore" 	k="41" />
<hkern g1="dollar,S" 	g2="v" 	k="20" />
<hkern g1="dollar,S" 	g2="x" 	k="20" />
<hkern g1="T" 	g2="J" 	k="16" />
<hkern g1="T" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="V" 	k="-37" />
<hkern g1="T" 	g2="X" 	k="25" />
<hkern g1="T" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-82" />
<hkern g1="T" 	g2="backslash" 	k="-41" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="degree" 	k="-61" />
<hkern g1="T" 	g2="eight" 	k="10" />
<hkern g1="T" 	g2="exclamdown" 	k="41" />
<hkern g1="T" 	g2="four" 	k="164" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="T" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="66" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="one" 	k="-82" />
<hkern g1="T" 	g2="paragraph" 	k="-92" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="63" />
<hkern g1="T" 	g2="question" 	k="-61" />
<hkern g1="T" 	g2="questiondown" 	k="109" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-72" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-78" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-31" />
<hkern g1="T" 	g2="s" 	k="66" />
<hkern g1="T" 	g2="seven" 	k="-61" />
<hkern g1="T" 	g2="slash" 	k="41" />
<hkern g1="T" 	g2="three" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-102" />
<hkern g1="T" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="39" />
<hkern g1="T" 	g2="underscore" 	k="16" />
<hkern g1="T" 	g2="x" 	k="8" />
<hkern g1="T" 	g2="z" 	k="41" />
<hkern g1="T" 	g2="zero,six" 	k="31" />
<hkern g1="T" 	g2="AE" 	k="82" />
<hkern g1="T" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="T" 	g2="ae" 	k="61" />
<hkern g1="T" 	g2="ampersand" 	k="61" />
<hkern g1="T" 	g2="five" 	k="-16" />
<hkern g1="T" 	g2="two" 	k="-41" />
<hkern g1="T" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="T" 	g2="bracketright,braceright" 	k="-14" />
<hkern g1="T" 	g2="j" 	k="20" />
<hkern g1="T" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="T" 	g2="exclam" 	k="-41" />
<hkern g1="T" 	g2="parenleft" 	k="31" />
<hkern g1="Thorn" 	g2="J" 	k="139" />
<hkern g1="Thorn" 	g2="T" 	k="37" />
<hkern g1="Thorn" 	g2="V" 	k="20" />
<hkern g1="Thorn" 	g2="X" 	k="61" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="one" 	k="41" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="49" />
<hkern g1="Thorn" 	g2="question" 	k="61" />
<hkern g1="Thorn" 	g2="questiondown" 	k="51" />
<hkern g1="Thorn" 	g2="slash" 	k="61" />
<hkern g1="Thorn" 	g2="uniFB02" 	k="-10" />
<hkern g1="Thorn" 	g2="three" 	k="41" />
<hkern g1="Thorn" 	g2="trademark" 	k="4" />
<hkern g1="Thorn" 	g2="underscore" 	k="61" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="AE" 	k="61" />
<hkern g1="Thorn" 	g2="ae" 	k="20" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="two" 	k="51" />
<hkern g1="Thorn" 	g2="j" 	k="20" />
<hkern g1="Thorn" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="31" />
<hkern g1="Thorn" 	g2="Z" 	k="39" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="J" 	k="51" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="X" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="18" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="questiondown" 	k="16" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="slash" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="underscore" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="x" 	k="16" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="AE" 	k="31" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="j" 	k="10" />
<hkern g1="V" 	g2="J" 	k="51" />
<hkern g1="V" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="V" 	g2="T" 	k="-37" />
<hkern g1="V" 	g2="X" 	k="45" />
<hkern g1="V" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-70" />
<hkern g1="V" 	g2="backslash" 	k="-61" />
<hkern g1="V" 	g2="colon,semicolon" 	k="45" />
<hkern g1="V" 	g2="degree" 	k="-57" />
<hkern g1="V" 	g2="eight" 	k="33" />
<hkern g1="V" 	g2="exclamdown" 	k="61" />
<hkern g1="V" 	g2="four" 	k="147" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="V" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="45" />
<hkern g1="V" 	g2="nine" 	k="12" />
<hkern g1="V" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="V" 	g2="one" 	k="-61" />
<hkern g1="V" 	g2="paragraph" 	k="-51" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="V" 	g2="questiondown" 	k="150" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-47" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-72" />
<hkern g1="V" 	g2="s" 	k="117" />
<hkern g1="V" 	g2="seven" 	k="-20" />
<hkern g1="V" 	g2="slash" 	k="82" />
<hkern g1="V" 	g2="uniFB02" 	k="4" />
<hkern g1="V" 	g2="three" 	k="-4" />
<hkern g1="V" 	g2="trademark" 	k="-82" />
<hkern g1="V" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="63" />
<hkern g1="V" 	g2="underscore" 	k="20" />
<hkern g1="V" 	g2="v" 	k="33" />
<hkern g1="V" 	g2="x" 	k="70" />
<hkern g1="V" 	g2="z" 	k="55" />
<hkern g1="V" 	g2="zero,six" 	k="51" />
<hkern g1="V" 	g2="AE" 	k="74" />
<hkern g1="V" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="43" />
<hkern g1="V" 	g2="dollar,S" 	k="31" />
<hkern g1="V" 	g2="ae" 	k="113" />
<hkern g1="V" 	g2="ampersand" 	k="57" />
<hkern g1="V" 	g2="five" 	k="14" />
<hkern g1="V" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="V" 	g2="parenright" 	k="-10" />
<hkern g1="V" 	g2="bracketright,braceright" 	k="-45" />
<hkern g1="V" 	g2="j" 	k="41" />
<hkern g1="V" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="72" />
<hkern g1="V" 	g2="Z" 	k="20" />
<hkern g1="V" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="V" 	g2="exclam" 	k="-20" />
<hkern g1="X" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="X" 	g2="T" 	k="25" />
<hkern g1="X" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="X" 	g2="V" 	k="45" />
<hkern g1="X" 	g2="X" 	k="47" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="39" />
<hkern g1="X" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="X" 	g2="colon,semicolon" 	k="12" />
<hkern g1="X" 	g2="degree" 	k="41" />
<hkern g1="X" 	g2="eight" 	k="43" />
<hkern g1="X" 	g2="exclamdown" 	k="20" />
<hkern g1="X" 	g2="four" 	k="106" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="X" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="35" />
<hkern g1="X" 	g2="nine" 	k="33" />
<hkern g1="X" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="X" 	g2="one" 	k="-20" />
<hkern g1="X" 	g2="question" 	k="61" />
<hkern g1="X" 	g2="questiondown" 	k="-4" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="-6" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="-16" />
<hkern g1="X" 	g2="s" 	k="41" />
<hkern g1="X" 	g2="seven" 	k="20" />
<hkern g1="X" 	g2="slash" 	k="-37" />
<hkern g1="X" 	g2="uniFB02" 	k="45" />
<hkern g1="X" 	g2="trademark" 	k="-61" />
<hkern g1="X" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="53" />
<hkern g1="X" 	g2="underscore" 	k="-98" />
<hkern g1="X" 	g2="v" 	k="51" />
<hkern g1="X" 	g2="x" 	k="72" />
<hkern g1="X" 	g2="z" 	k="25" />
<hkern g1="X" 	g2="zero,six" 	k="66" />
<hkern g1="X" 	g2="AE" 	k="66" />
<hkern g1="X" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="31" />
<hkern g1="X" 	g2="dollar,S" 	k="29" />
<hkern g1="X" 	g2="ae" 	k="33" />
<hkern g1="X" 	g2="ampersand" 	k="41" />
<hkern g1="X" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="X" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="X" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="X" 	g2="Z" 	k="20" />
<hkern g1="X" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="49" />
<hkern g1="X" 	g2="parenleft" 	k="23" />
<hkern g1="Z" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="Z" 	g2="T" 	k="-20" />
<hkern g1="Z" 	g2="X" 	k="20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="Z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="Z" 	g2="eight" 	k="8" />
<hkern g1="Z" 	g2="exclamdown" 	k="-4" />
<hkern g1="Z" 	g2="four" 	k="78" />
<hkern g1="Z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="31" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="Z" 	g2="one" 	k="-41" />
<hkern g1="Z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="Z" 	g2="question" 	k="-4" />
<hkern g1="Z" 	g2="slash" 	k="-16" />
<hkern g1="Z" 	g2="uniFB02" 	k="16" />
<hkern g1="Z" 	g2="trademark" 	k="-61" />
<hkern g1="Z" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="16" />
<hkern g1="Z" 	g2="underscore" 	k="-61" />
<hkern g1="Z" 	g2="v" 	k="31" />
<hkern g1="Z" 	g2="zero,six" 	k="43" />
<hkern g1="Z" 	g2="ae" 	k="-2" />
<hkern g1="Z" 	g2="ampersand" 	k="31" />
<hkern g1="Z" 	g2="five" 	k="8" />
<hkern g1="Z" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="10" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="90" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="139" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="one" 	k="16" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="question" 	k="-20" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="dollar,S" 	k="39" />
<hkern g1="z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="z" 	g2="backslash" 	k="133" />
<hkern g1="z" 	g2="four" 	k="31" />
<hkern g1="z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="29" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="z" 	g2="one" 	k="31" />
<hkern g1="z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="z" 	g2="uniFB02" 	k="-2" />
<hkern g1="z" 	g2="underscore" 	k="-51" />
<hkern g1="z" 	g2="v" 	k="20" />
<hkern g1="z" 	g2="x" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="16" />
<hkern g1="z" 	g2="two" 	k="16" />
<hkern g1="ampersand" 	g2="AE" 	k="-31" />
<hkern g1="ampersand" 	g2="J" 	k="-20" />
<hkern g1="ampersand" 	g2="T" 	k="61" />
<hkern g1="ampersand" 	g2="V" 	k="41" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="ampersand" 	g2="Z" 	k="-31" />
<hkern g1="ampersand" 	g2="ae" 	k="-20" />
<hkern g1="ampersand" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="ampersand" 	g2="uniFB02" 	k="-20" />
<hkern g1="ampersand" 	g2="v" 	k="25" />
<hkern g1="ampersand" 	g2="x" 	k="4" />
<hkern g1="currency" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-16" />
<hkern g1="degree" 	g2="AE" 	k="188" />
<hkern g1="degree" 	g2="J" 	k="20" />
<hkern g1="degree" 	g2="T" 	k="-61" />
<hkern g1="degree" 	g2="V" 	k="-57" />
<hkern g1="degree" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="degree" 	g2="uniFB02" 	k="-41" />
<hkern g1="degree" 	g2="v" 	k="-78" />
<hkern g1="degree" 	g2="x" 	k="-20" />
<hkern g1="degree" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="degree" 	g2="dollar,S" 	k="-41" />
<hkern g1="degree" 	g2="X" 	k="41" />
<hkern g1="degree" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="degree" 	g2="s" 	k="4" />
<hkern g1="percent" 	g2="ae" 	k="-31" />
<hkern g1="percent" 	g2="asterisk,ordfeminine,ordmasculine" 	k="102" />
<hkern g1="percent" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-45" />
<hkern g1="section" 	g2="ae" 	k="-16" />
<hkern g1="section" 	g2="uniFB02" 	k="-16" />
<hkern g1="section" 	g2="v" 	k="-16" />
<hkern g1="section" 	g2="x" 	k="-16" />
<hkern g1="section" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-16" />
<hkern g1="section" 	g2="s" 	k="-16" />
<hkern g1="section" 	g2="j" 	k="-16" />
<hkern g1="section" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="-16" />
<hkern g1="section" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-16" />
<hkern g1="section" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="-16" />
<hkern g1="section" 	g2="z" 	k="-16" />
<hkern g1="trademark" 	g2="AE" 	k="41" />
<hkern g1="trademark" 	g2="J" 	k="20" />
<hkern g1="trademark" 	g2="ae" 	k="-57" />
<hkern g1="trademark" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="trademark" 	g2="uniFB02" 	k="-66" />
<hkern g1="trademark" 	g2="v" 	k="-31" />
<hkern g1="trademark" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-57" />
<hkern g1="trademark" 	g2="s" 	k="-57" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="AE" 	k="188" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="dollar,S" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="T" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="V" 	k="-70" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="X" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Z" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="ampersand" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="currency,Euro" 	k="-16" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="degree" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="exclam" 	k="-12" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="four" 	k="29" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="guillemotright,guilsinglright" 	k="-25" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="nine" 	k="-72" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="one" 	k="-57" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="paragraph" 	k="-78" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="percent" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="66" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="question" 	k="-72" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="questiondown" 	k="174" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="seven" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="slash" 	k="287" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="uniFB02" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="three" 	k="-6" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="trademark" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="two" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="underscore" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="v" 	k="-92" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="x" 	k="-25" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="z" 	k="-37" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="zero,six" 	k="-16" />
<hkern g1="backslash" 	g2="AE" 	k="-61" />
<hkern g1="backslash" 	g2="J" 	k="-102" />
<hkern g1="backslash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="backslash" 	g2="T" 	k="41" />
<hkern g1="backslash" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="backslash" 	g2="V" 	k="82" />
<hkern g1="backslash" 	g2="X" 	k="-41" />
<hkern g1="backslash" 	g2="asterisk,ordfeminine,ordmasculine" 	k="287" />
<hkern g1="backslash" 	g2="j" 	k="-246" />
<hkern g1="backslash" 	g2="v" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="J" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="T" 	k="-14" />
<hkern g1="bracketleft,braceleft" 	g2="V" 	k="-45" />
<hkern g1="bracketleft,braceleft" 	g2="X" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-205" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="39" />
<hkern g1="bracketleft,braceleft" 	g2="ae" 	k="23" />
<hkern g1="bracketright" 	g2="J" 	k="31" />
<hkern g1="bracketright" 	g2="T" 	k="39" />
<hkern g1="bracketright" 	g2="X" 	k="47" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-16" />
<hkern g1="colon,semicolon" 	g2="T" 	k="20" />
<hkern g1="colon,semicolon" 	g2="V" 	k="45" />
<hkern g1="colon,semicolon" 	g2="X" 	k="12" />
<hkern g1="colon,semicolon" 	g2="v" 	k="-16" />
<hkern g1="colon,semicolon" 	g2="uniFB02" 	k="-10" />
<hkern g1="colon,semicolon" 	g2="x" 	k="20" />
<hkern g1="exclam" 	g2="J" 	k="31" />
<hkern g1="exclam" 	g2="T" 	k="-41" />
<hkern g1="exclam" 	g2="V" 	k="-20" />
<hkern g1="exclam" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-12" />
<hkern g1="exclamdown" 	g2="AE" 	k="-20" />
<hkern g1="exclamdown" 	g2="J" 	k="-41" />
<hkern g1="exclamdown" 	g2="T" 	k="41" />
<hkern g1="exclamdown" 	g2="V" 	k="61" />
<hkern g1="exclamdown" 	g2="X" 	k="20" />
<hkern g1="exclamdown" 	g2="j" 	k="-133" />
<hkern g1="exclamdown" 	g2="uniFB02" 	k="-8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="AE" 	k="-31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="uniFB02" 	k="-4" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="AE" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="J" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-16" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="T" 	k="66" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="V" 	k="45" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="X" 	k="35" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="v" 	k="25" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="uniFB02" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="x" 	k="45" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="dollar,S" 	k="37" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="z" 	k="29" />
<hkern g1="parenleft" 	g2="J" 	k="23" />
<hkern g1="parenleft" 	g2="j" 	k="-168" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="AE" 	k="-70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="J" 	k="-16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="63" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="74" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="66" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="ae" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uniFB02" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-37" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="dollar,S" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="31" />
<hkern g1="question" 	g2="AE" 	k="86" />
<hkern g1="question" 	g2="J" 	k="92" />
<hkern g1="question" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="question" 	g2="V" 	k="25" />
<hkern g1="question" 	g2="X" 	k="35" />
<hkern g1="question" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-31" />
<hkern g1="question" 	g2="v" 	k="-20" />
<hkern g1="question" 	g2="Z" 	k="20" />
<hkern g1="question" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="question" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="questiondown" 	g2="AE" 	k="-20" />
<hkern g1="questiondown" 	g2="J" 	k="-123" />
<hkern g1="questiondown" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="106" />
<hkern g1="questiondown" 	g2="T" 	k="117" />
<hkern g1="questiondown" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="72" />
<hkern g1="questiondown" 	g2="V" 	k="137" />
<hkern g1="questiondown" 	g2="X" 	k="-10" />
<hkern g1="questiondown" 	g2="asterisk,ordfeminine,ordmasculine" 	k="168" />
<hkern g1="questiondown" 	g2="j" 	k="-143" />
<hkern g1="questiondown" 	g2="v" 	k="92" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="questiondown" 	g2="ae" 	k="37" />
<hkern g1="questiondown" 	g2="uniFB02" 	k="31" />
<hkern g1="questiondown" 	g2="x" 	k="-10" />
<hkern g1="questiondown" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="questiondown" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="102" />
<hkern g1="questiondown" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="45" />
<hkern g1="questiondown" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="31" />
<hkern g1="questiondown" 	g2="s" 	k="16" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-72" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-47" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="-6" />
<hkern g1="quoteleft,quotedblleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-70" />
<hkern g1="quoteleft,quotedblleft" 	g2="ae" 	k="8" />
<hkern g1="quoteleft,quotedblleft" 	g2="uniFB02" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="66" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="4" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-78" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-72" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="-16" />
<hkern g1="quoteright,quotedblright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="v" 	k="-37" />
<hkern g1="quoteright,quotedblright" 	g2="ae" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="uniFB02" 	k="-4" />
<hkern g1="quoteright,quotedblright" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="57" />
<hkern g1="quoteright,quotedblright" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="90" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-31" />
<hkern g1="quotedbl,quotesingle" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="slash" 	g2="AE" 	k="82" />
<hkern g1="slash" 	g2="J" 	k="20" />
<hkern g1="slash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="T" 	k="-61" />
<hkern g1="slash" 	g2="V" 	k="-61" />
<hkern g1="slash" 	g2="j" 	k="-4" />
<hkern g1="slash" 	g2="v" 	k="61" />
<hkern g1="slash" 	g2="ae" 	k="139" />
<hkern g1="slash" 	g2="uniFB02" 	k="57" />
<hkern g1="slash" 	g2="x" 	k="129" />
<hkern g1="slash" 	g2="dollar,S" 	k="41" />
<hkern g1="slash" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="139" />
<hkern g1="slash" 	g2="z" 	k="139" />
<hkern g1="slash" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="139" />
<hkern g1="slash" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="slash" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="slash" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="139" />
<hkern g1="slash" 	g2="s" 	k="139" />
<hkern g1="underscore" 	g2="AE" 	k="-119" />
<hkern g1="underscore" 	g2="J" 	k="-180" />
<hkern g1="underscore" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="underscore" 	g2="T" 	k="16" />
<hkern g1="underscore" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="underscore" 	g2="V" 	k="20" />
<hkern g1="underscore" 	g2="X" 	k="-98" />
<hkern g1="underscore" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="underscore" 	g2="j" 	k="-215" />
<hkern g1="underscore" 	g2="v" 	k="25" />
<hkern g1="underscore" 	g2="Y,Yacute,Ydieresis" 	k="-4" />
<hkern g1="underscore" 	g2="ae" 	k="20" />
<hkern g1="underscore" 	g2="uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="x" 	k="-78" />
<hkern g1="underscore" 	g2="Z" 	k="-41" />
<hkern g1="underscore" 	g2="dollar,S" 	k="-41" />
<hkern g1="underscore" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="57" />
<hkern g1="underscore" 	g2="z" 	k="-61" />
<hkern g1="underscore" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="57" />
<hkern g1="underscore" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="underscore" 	g2="s" 	k="-20" />
<hkern g1="underscore" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="-61" />
<hkern g1="c,cent,ccedilla" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="51" />
<hkern g1="c,cent,ccedilla" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-25" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="98" />
<hkern g1="c,cent,ccedilla" 	g2="bracketright,braceright" 	k="23" />
<hkern g1="c,cent,ccedilla" 	g2="bracketleft" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-12" />
<hkern g1="c,cent,ccedilla" 	g2="degree" 	k="-33" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="four" 	k="47" />
<hkern g1="c,cent,ccedilla" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="nine" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="49" />
<hkern g1="c,cent,ccedilla" 	g2="parenright" 	k="43" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="57" />
<hkern g1="c,cent,ccedilla" 	g2="questiondown" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="quoteleft,quotedblleft" 	k="-35" />
<hkern g1="c,cent,ccedilla" 	g2="quoteright,quotedblright" 	k="-45" />
<hkern g1="c,cent,ccedilla" 	g2="section" 	k="23" />
<hkern g1="c,cent,ccedilla" 	g2="seven" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="uniFB02" 	k="-12" />
<hkern g1="c,cent,ccedilla" 	g2="three" 	k="37" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="29" />
<hkern g1="c,cent,ccedilla" 	g2="two" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="v" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="x" 	k="37" />
<hkern g1="c,cent,ccedilla" 	g2="z" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="zero,six" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="c,cent,ccedilla" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="V" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="139" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="degree" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="nine" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="one" 	k="57" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="98" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="questiondown" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="seven" 	k="59" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="three" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="two" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="74" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="ae" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="paragraph" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="percent" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="underscore" 	k="12" />
<hkern g1="eth" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="eth" 	g2="backslash" 	k="20" />
<hkern g1="eth" 	g2="degree" 	k="-41" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="39" />
<hkern g1="eth" 	g2="question" 	k="16" />
<hkern g1="eth" 	g2="questiondown" 	k="16" />
<hkern g1="eth" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="eth" 	g2="three" 	k="16" />
<hkern g1="eth" 	g2="trademark" 	k="-57" />
<hkern g1="eth" 	g2="zero,six" 	k="-16" />
<hkern g1="eth" 	g2="underscore" 	k="41" />
<hkern g1="f" 	g2="ampersand" 	k="25" />
<hkern g1="f" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-109" />
<hkern g1="f" 	g2="backslash" 	k="-131" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-98" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-51" />
<hkern g1="f" 	g2="degree" 	k="-78" />
<hkern g1="f" 	g2="eight" 	k="-31" />
<hkern g1="f" 	g2="four" 	k="57" />
<hkern g1="f" 	g2="nine" 	k="-63" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="f" 	g2="one" 	k="-109" />
<hkern g1="f" 	g2="parenright" 	k="-55" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="f" 	g2="question" 	k="-57" />
<hkern g1="f" 	g2="questiondown" 	k="25" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-113" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-160" />
<hkern g1="f" 	g2="seven" 	k="-92" />
<hkern g1="f" 	g2="three" 	k="-51" />
<hkern g1="f" 	g2="trademark" 	k="-164" />
<hkern g1="f" 	g2="two" 	k="-37" />
<hkern g1="f" 	g2="v" 	k="-20" />
<hkern g1="f" 	g2="zero,six" 	k="-31" />
<hkern g1="f" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="f" 	g2="ae" 	k="20" />
<hkern g1="f" 	g2="paragraph" 	k="-92" />
<hkern g1="f" 	g2="percent" 	k="-37" />
<hkern g1="f" 	g2="underscore" 	k="31" />
<hkern g1="f" 	g2="exclam" 	k="-4" />
<hkern g1="f" 	g2="five" 	k="-31" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="f" 	g2="j" 	k="-86" />
<hkern g1="f" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="f" 	g2="slash" 	k="61" />
<hkern g1="k" 	g2="ampersand" 	k="10" />
<hkern g1="k" 	g2="backslash" 	k="82" />
<hkern g1="k" 	g2="bracketleft" 	k="23" />
<hkern g1="k" 	g2="eight" 	k="14" />
<hkern g1="k" 	g2="four" 	k="61" />
<hkern g1="k" 	g2="nine" 	k="-20" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="43" />
<hkern g1="k" 	g2="one" 	k="-10" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="k" 	g2="question" 	k="-10" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-4" />
<hkern g1="k" 	g2="seven" 	k="10" />
<hkern g1="k" 	g2="three" 	k="-20" />
<hkern g1="k" 	g2="two" 	k="-31" />
<hkern g1="k" 	g2="v" 	k="55" />
<hkern g1="k" 	g2="x" 	k="41" />
<hkern g1="k" 	g2="z" 	k="23" />
<hkern g1="k" 	g2="zero,six" 	k="20" />
<hkern g1="k" 	g2="V" 	k="23" />
<hkern g1="k" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="45" />
<hkern g1="k" 	g2="X" 	k="16" />
<hkern g1="k" 	g2="ae" 	k="20" />
<hkern g1="k" 	g2="underscore" 	k="-133" />
<hkern g1="k" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="16" />
<hkern g1="k" 	g2="s" 	k="33" />
<hkern g1="k" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="d,uniFB02" 	g2="uniFB02" 	k="-4" />
<hkern g1="l" 	g2="asterisk,ordfeminine,ordmasculine" 	k="51" />
<hkern g1="l" 	g2="backslash" 	k="86" />
<hkern g1="l" 	g2="colon,semicolon" 	k="-37" />
<hkern g1="l" 	g2="degree" 	k="41" />
<hkern g1="l" 	g2="eight" 	k="-16" />
<hkern g1="l" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="l" 	g2="one" 	k="41" />
<hkern g1="l" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-57" />
<hkern g1="l" 	g2="question" 	k="41" />
<hkern g1="l" 	g2="questiondown" 	k="-16" />
<hkern g1="l" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="l" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="l" 	g2="seven" 	k="31" />
<hkern g1="l" 	g2="uniFB02" 	k="10" />
<hkern g1="l" 	g2="three" 	k="-20" />
<hkern g1="l" 	g2="two" 	k="-37" />
<hkern g1="l" 	g2="v" 	k="51" />
<hkern g1="l" 	g2="x" 	k="-16" />
<hkern g1="l" 	g2="zero,six" 	k="-10" />
<hkern g1="l" 	g2="paragraph" 	k="10" />
<hkern g1="l" 	g2="underscore" 	k="-139" />
<hkern g1="l" 	g2="five" 	k="-8" />
<hkern g1="l" 	g2="slash" 	k="-61" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk,ordfeminine,ordmasculine" 	k="57" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="139" />
<hkern g1="h,m,n,ntilde" 	g2="degree" 	k="43" />
<hkern g1="h,m,n,ntilde" 	g2="nine" 	k="31" />
<hkern g1="h,m,n,ntilde" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-4" />
<hkern g1="h,m,n,ntilde" 	g2="one" 	k="47" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="98" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="seven" 	k="47" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="57" />
<hkern g1="h,m,n,ntilde" 	g2="two" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="paragraph" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="percent" 	k="59" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk,ordfeminine,ordmasculine" 	k="37" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="139" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="degree" 	k="33" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="nine" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="one" 	k="57" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="98" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="questiondown" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="seven" 	k="57" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="three" 	k="49" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="trademark" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="two" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="39" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="18" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="47" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V" 	k="82" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="14" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="ae" 	k="6" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="paragraph" 	k="49" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="percent" 	k="57" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="underscore" 	k="57" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="J" 	k="68" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="113" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="AE" 	k="14" />
<hkern g1="r" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="r" 	g2="ampersand" 	k="72" />
<hkern g1="r" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="r" 	g2="backslash" 	k="41" />
<hkern g1="r" 	g2="bracketright,braceright" 	k="31" />
<hkern g1="r" 	g2="bracketleft" 	k="39" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="r" 	g2="degree" 	k="-59" />
<hkern g1="r" 	g2="eight" 	k="-2" />
<hkern g1="r" 	g2="four" 	k="61" />
<hkern g1="r" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="16" />
<hkern g1="r" 	g2="nine" 	k="-10" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="r" 	g2="one" 	k="20" />
<hkern g1="r" 	g2="parenright" 	k="23" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="r" 	g2="question" 	k="-10" />
<hkern g1="r" 	g2="questiondown" 	k="78" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-66" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-66" />
<hkern g1="r" 	g2="section" 	k="-20" />
<hkern g1="r" 	g2="seven" 	k="-43" />
<hkern g1="r" 	g2="uniFB02" 	k="-31" />
<hkern g1="r" 	g2="three" 	k="16" />
<hkern g1="r" 	g2="two" 	k="-4" />
<hkern g1="r" 	g2="v" 	k="-31" />
<hkern g1="r" 	g2="x" 	k="16" />
<hkern g1="r" 	g2="zero,six" 	k="-4" />
<hkern g1="r" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="r" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="47" />
<hkern g1="r" 	g2="V" 	k="57" />
<hkern g1="r" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="23" />
<hkern g1="r" 	g2="X" 	k="88" />
<hkern g1="r" 	g2="ae" 	k="18" />
<hkern g1="r" 	g2="paragraph" 	k="-61" />
<hkern g1="r" 	g2="underscore" 	k="25" />
<hkern g1="r" 	g2="five" 	k="-2" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="-4" />
<hkern g1="r" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="16" />
<hkern g1="r" 	g2="slash" 	k="82" />
<hkern g1="r" 	g2="s" 	k="8" />
<hkern g1="r" 	g2="AE" 	k="78" />
<hkern g1="r" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="r" 	g2="dollar,S" 	k="39" />
<hkern g1="r" 	g2="parenleft" 	k="31" />
<hkern g1="s" 	g2="backslash" 	k="123" />
<hkern g1="s" 	g2="degree" 	k="29" />
<hkern g1="s" 	g2="nine" 	k="10" />
<hkern g1="s" 	g2="one" 	k="16" />
<hkern g1="s" 	g2="question" 	k="37" />
<hkern g1="s" 	g2="seven" 	k="57" />
<hkern g1="s" 	g2="three" 	k="20" />
<hkern g1="s" 	g2="trademark" 	k="37" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="s" 	g2="v" 	k="20" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="paragraph" 	k="49" />
<hkern g1="t" 	g2="ampersand" 	k="18" />
<hkern g1="t" 	g2="asterisk,ordfeminine,ordmasculine" 	k="37" />
<hkern g1="t" 	g2="backslash" 	k="139" />
<hkern g1="t" 	g2="degree" 	k="10" />
<hkern g1="t" 	g2="eight" 	k="12" />
<hkern g1="t" 	g2="four" 	k="68" />
<hkern g1="t" 	g2="nine" 	k="31" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="t" 	g2="one" 	k="43" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="question" 	k="37" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="t" 	g2="section" 	k="23" />
<hkern g1="t" 	g2="seven" 	k="51" />
<hkern g1="t" 	g2="uniFB02" 	k="29" />
<hkern g1="t" 	g2="trademark" 	k="57" />
<hkern g1="t" 	g2="two" 	k="16" />
<hkern g1="t" 	g2="v" 	k="20" />
<hkern g1="t" 	g2="zero,six" 	k="37" />
<hkern g1="t" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="t" 	g2="paragraph" 	k="41" />
<hkern g1="t" 	g2="underscore" 	k="-72" />
<hkern g1="t" 	g2="five" 	k="16" />
<hkern g1="v" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="8" />
<hkern g1="v" 	g2="ampersand" 	k="45" />
<hkern g1="v" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-92" />
<hkern g1="v" 	g2="backslash" 	k="41" />
<hkern g1="v" 	g2="colon,semicolon" 	k="-16" />
<hkern g1="v" 	g2="degree" 	k="-78" />
<hkern g1="v" 	g2="eight" 	k="-16" />
<hkern g1="v" 	g2="four" 	k="51" />
<hkern g1="v" 	g2="nine" 	k="-61" />
<hkern g1="v" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="v" 	g2="one" 	k="-14" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="70" />
<hkern g1="v" 	g2="question" 	k="-66" />
<hkern g1="v" 	g2="questiondown" 	k="61" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-70" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-37" />
<hkern g1="v" 	g2="seven" 	k="-31" />
<hkern g1="v" 	g2="uniFB02" 	k="-31" />
<hkern g1="v" 	g2="three" 	k="10" />
<hkern g1="v" 	g2="two" 	k="-14" />
<hkern g1="v" 	g2="v" 	k="10" />
<hkern g1="v" 	g2="x" 	k="61" />
<hkern g1="v" 	g2="z" 	k="20" />
<hkern g1="v" 	g2="zero,six" 	k="-16" />
<hkern g1="v" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="v" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="25" />
<hkern g1="v" 	g2="X" 	k="61" />
<hkern g1="v" 	g2="ae" 	k="33" />
<hkern g1="v" 	g2="paragraph" 	k="-61" />
<hkern g1="v" 	g2="underscore" 	k="25" />
<hkern g1="v" 	g2="five" 	k="-16" />
<hkern g1="v" 	g2="j" 	k="31" />
<hkern g1="v" 	g2="slash" 	k="59" />
<hkern g1="v" 	g2="s" 	k="12" />
<hkern g1="v" 	g2="AE" 	k="53" />
<hkern g1="v" 	g2="dollar,S" 	k="16" />
<hkern g1="w" 	g2="backslash" 	k="102" />
<hkern g1="w" 	g2="one" 	k="16" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="w" 	g2="three" 	k="20" />
<hkern g1="w" 	g2="two" 	k="16" />
<hkern g1="w" 	g2="underscore" 	k="57" />
<hkern g1="x" 	g2="ampersand" 	k="41" />
<hkern g1="x" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-25" />
<hkern g1="x" 	g2="backslash" 	k="98" />
<hkern g1="x" 	g2="colon,semicolon" 	k="20" />
<hkern g1="x" 	g2="degree" 	k="-20" />
<hkern g1="x" 	g2="eight" 	k="4" />
<hkern g1="x" 	g2="four" 	k="66" />
<hkern g1="x" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="x" 	g2="one" 	k="-10" />
<hkern g1="x" 	g2="question" 	k="-14" />
<hkern g1="x" 	g2="seven" 	k="16" />
<hkern g1="x" 	g2="two" 	k="6" />
<hkern g1="x" 	g2="v" 	k="61" />
<hkern g1="x" 	g2="z" 	k="20" />
<hkern g1="x" 	g2="zero,six" 	k="20" />
<hkern g1="x" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="x" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="x" 	g2="V" 	k="43" />
<hkern g1="x" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="45" />
<hkern g1="x" 	g2="ae" 	k="20" />
<hkern g1="x" 	g2="paragraph" 	k="-4" />
<hkern g1="x" 	g2="underscore" 	k="-78" />
<hkern g1="x" 	g2="j" 	k="4" />
<hkern g1="x" 	g2="slash" 	k="-20" />
<hkern g1="x" 	g2="s" 	k="20" />
<hkern g1="x" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="x" 	g2="dollar,S" 	k="23" />
</font>
</defs></svg> 