<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="montserrat_alternatessemibold" horiz-adv-x="1404" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="565" />
<glyph unicode="&#xfb01;" horiz-adv-x="1478" d="M168 0v1155q0 175 102.5 277t290.5 102q148 0 230 -59l-72 -193q-62 45 -139 45q-162 0 -162 -176v-66h301v-204h-295v-881h-256zM1020 1427q0 65 47 109.5t117 44.5t117 -42t47 -105q0 -68 -46.5 -114t-117.5 -46q-70 0 -117 44t-47 109zM1055 0v1094h256v-1094h-256z " />
<glyph unicode="&#xfb02;" horiz-adv-x="1396" d="M23 881v204h180q3 210 128 329.5t339 119.5q225 0 344 -121t119 -340v-917q0 -164 153 -164q53 0 94 18l13 -205q-74 -26 -154 -26q-171 0 -266.5 93t-95.5 263v965q0 109 -54 166t-155 57t-155 -57t-54 -166v-15h293v-204h-293v-881h-256v881h-180z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="565" />
<glyph unicode=" "  horiz-adv-x="565" />
<glyph unicode="&#x09;" horiz-adv-x="565" />
<glyph unicode="&#xa0;" horiz-adv-x="565" />
<glyph unicode="!" horiz-adv-x="569" d="M121 145q0 67 47.5 112.5t118.5 45.5q70 0 116 -45.5t46 -112.5t-46 -113t-116 -46q-71 0 -118.5 46t-47.5 113zM135 1434h303l-51 -959h-203z" />
<glyph unicode="&#x22;" horiz-adv-x="845" d="M115 1434h221l-19 -568h-186zM510 1434h221l-18 -568h-187z" />
<glyph unicode="#" horiz-adv-x="1456" d="M47 348v187h299l47 364h-276v186h299l43 349h186l-43 -349h344l43 349h187l-43 -349h274l2 -186h-299l-45 -364h276v-187h-299l-43 -348h-186l43 348h-346l-43 -348h-186l43 348h-277zM532 535h347l45 364h-344z" />
<glyph unicode="$" horiz-adv-x="1288" d="M72 152l92 206q75 -62 184 -103.5t225 -51.5v409q-58 14 -98.5 25.5t-93.5 31t-89.5 40t-75 52t-61.5 68.5t-38 87.5t-15 109.5q0 165 121 282.5t350 139.5v231h168v-227q243 -13 416 -125l-84 -207q-159 92 -332 111v-414q59 -14 98.5 -25t94.5 -30.5t90.5 -39.5 t75.5 -51.5t63 -68t38.5 -87t15.5 -109.5q0 -165 -122 -281.5t-354 -140.5v-230h-168v228q-151 9 -286 55t-215 115zM367 1038q0 -68 52.5 -108t153.5 -70v365q-104 -17 -155 -66.5t-51 -120.5zM741 207q108 17 160.5 64t52.5 116q0 70 -55 111.5t-158 70.5v-362z" />
<glyph unicode="%" horiz-adv-x="1761" d="M68 1055q0 177 94 285t248 108t249 -107.5t95 -285.5t-95 -285.5t-249 -107.5t-248 108t-94 285zM233 1055q0 -117 48 -183.5t129 -66.5q84 0 130 65t46 185t-46 185t-130 65q-81 0 -129 -66.5t-48 -183.5zM291 0l979 1434h200l-978 -1434h-201zM1008 379q0 177 94 285 t248 108t249 -108t95 -285t-95 -285t-249 -108t-248 108t-94 285zM1174 379q0 -119 47 -184.5t129 -65.5t129 66t47 184t-47 184t-129 66t-129 -65.5t-47 -184.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1445" d="M84 362q0 133 78 234.5t254 200.5q-86 86 -123 160.5t-37 154.5q0 151 114 244.5t302 93.5q174 0 278.5 -85t104.5 -230q0 -110 -68.5 -196.5t-224.5 -174.5l309 -295q53 102 80 242l207 -68q-41 -191 -129 -328l180 -172l-141 -166l-181 175q-200 -172 -481 -172 q-228 0 -375 107t-147 275zM334 389q0 -90 80 -145.5t211 -55.5q178 0 311 109l-379 362q-122 -68 -172.5 -130t-50.5 -140zM489 1116q0 -48 27 -93t102 -120q123 67 171.5 117.5t48.5 109.5q0 63 -44 100.5t-122 37.5q-85 0 -134 -42.5t-49 -109.5z" />
<glyph unicode="'" horiz-adv-x="450" d="M115 1434h221l-19 -568h-186z" />
<glyph unicode="(" horiz-adv-x="710" d="M172 561q0 281 64.5 528t185.5 431h244q-242 -426 -242 -959q0 -532 242 -958h-244q-121 188 -185.5 432.5t-64.5 525.5z" />
<glyph unicode=")" horiz-adv-x="712" d="M45 -397q244 429 244 958q0 530 -244 959h244q121 -183 185.5 -429.5t64.5 -529.5q0 -282 -64 -526.5t-186 -431.5h-244z" />
<glyph unicode="*" horiz-adv-x="851" d="M33 997l235 129l-235 129l82 142l235 -140l-2 263h156l-2 -265l237 142l80 -142l-233 -129l233 -129l-80 -139l-237 139l2 -264h-156l2 262l-235 -137z" />
<glyph unicode="+" horiz-adv-x="1208" d="M133 614v205h363v359h219v-359h362v-205h-362v-358h-219v358h-363z" />
<glyph unicode="," horiz-adv-x="499" d="M86 156q0 76 47 123t119 47t119 -47.5t47 -122.5q0 -57 -47 -176l-105 -289h-166l82 313q-44 19 -70 58.5t-26 93.5z" />
<glyph unicode="-" horiz-adv-x="788" d="M113 463v213h563v-213h-563z" />
<glyph unicode="." horiz-adv-x="499" d="M82 156q0 75 48.5 122.5t119.5 47.5t119.5 -47.5t48.5 -122.5q0 -73 -49 -121.5t-119 -48.5t-119 48.5t-49 121.5z" />
<glyph unicode="/" horiz-adv-x="759" d="M-61 -205l675 1929h230l-676 -1929h-229z" />
<glyph unicode="0" horiz-adv-x="1378" d="M90 717q0 173 45.5 313.5t126 232.5t189.5 141.5t237 49.5q129 0 238.5 -49.5t190 -141.5t126 -232.5t45.5 -313.5t-45.5 -313.5t-126 -232.5t-190 -141.5t-238.5 -49.5q-128 0 -237 49.5t-189.5 141.5t-126 232.5t-45.5 313.5zM358 717q0 -251 89 -378.5t241 -127.5 q153 0 242.5 127.5t89.5 378.5t-89.5 378.5t-242.5 127.5q-152 0 -241 -127.5t-89 -378.5z" />
<glyph unicode="1" horiz-adv-x="780" d="M16 1210v224h570v-1434h-267v1210h-303z" />
<glyph unicode="2" horiz-adv-x="1191" d="M16 1217q83 113 225.5 175t319.5 62q234 0 373 -111t139 -301q0 -116 -49 -219t-188 -233l-383 -365h675v-225h-1056v178l569 541q96 92 130 160.5t34 136.5q0 99 -67.5 153t-198.5 54q-218 0 -336 -150z" />
<glyph unicode="3" horiz-adv-x="1191" d="M2 145l115 207q77 -65 188.5 -103t231.5 -38q142 0 222.5 58.5t80.5 158.5t-77 156.5t-235 56.5h-131v182l326 387h-651v224h977v-179l-349 -413q198 -26 303 -136.5t105 -275.5q0 -92 -35 -172.5t-103 -143t-178.5 -98.5t-250.5 -36q-153 0 -296.5 43.5t-242.5 121.5z " />
<glyph unicode="4" horiz-adv-x="1390" d="M70 326v184l706 924h285l-666 -883h469v289h250v-289h254v-225h-254v-326h-258v326h-786z" />
<glyph unicode="5" horiz-adv-x="1196" d="M27 145l112 207q79 -65 190.5 -103t231.5 -38q142 0 222.5 58.5t80.5 160.5q0 110 -86.5 166.5t-298.5 56.5h-346l74 781h842v-224h-617l-31 -331h138q155 0 271 -32.5t185.5 -92t103.5 -138t34 -176.5q0 -96 -35.5 -178.5t-103.5 -146t-178.5 -99.5t-250.5 -36 q-153 0 -296.5 43.5t-241.5 121.5z" />
<glyph unicode="6" horiz-adv-x="1284" d="M90 692q0 181 51.5 326t144 240t222 145.5t285.5 50.5q231 0 376 -92l-98 -201q-107 70 -272 70q-206 0 -323.5 -127t-117.5 -367v-2q129 150 383 150q215 0 354.5 -121.5t139.5 -319.5q0 -209 -149.5 -336.5t-374.5 -127.5q-295 0 -458 184.5t-163 527.5zM399 430 q0 -105 79.5 -173.5t217.5 -68.5q126 0 203.5 66.5t77.5 177.5q0 112 -78 178t-209 66q-128 0 -209.5 -69.5t-81.5 -176.5z" />
<glyph unicode="7" horiz-adv-x="1245" d="M57 965v469h1114v-179l-557 -1255h-284l538 1208h-563v-243h-248z" />
<glyph unicode="8" horiz-adv-x="1335" d="M82 412q0 236 229 342q-178 98 -178 301q0 183 146.5 291t386.5 108q241 0 388.5 -108t147.5 -291q0 -203 -182 -301q233 -106 233 -342q0 -199 -159.5 -315.5t-427.5 -116.5t-426 116.5t-158 315.5zM350 420q0 -109 84 -170.5t232 -61.5t233.5 62t85.5 170 q0 106 -85.5 167.5t-233.5 61.5q-147 0 -231.5 -61.5t-84.5 -167.5zM395 1042q0 -92 72.5 -146t198.5 -54q127 0 200.5 54t73.5 146q0 95 -74.5 149t-199.5 54t-198 -53.5t-73 -149.5z" />
<glyph unicode="9" horiz-adv-x="1284" d="M49 989q0 209 149.5 337t374.5 128q295 0 458 -184.5t163 -528.5q0 -181 -51 -326t-144 -240t-222 -145t-285 -50q-232 0 -377 92l98 200q106 -69 272 -69q207 0 324 126.5t117 366.5v2q-128 -149 -383 -149q-215 0 -354.5 121t-139.5 319zM307 1001q0 -112 78 -177.5 t209 -65.5q128 0 209.5 69.5t81.5 176.5q0 105 -79.5 173t-217.5 68q-126 0 -203.5 -66.5t-77.5 -177.5z" />
<glyph unicode=":" horiz-adv-x="499" d="M82 156q0 75 48.5 122.5t119.5 47.5t119.5 -47.5t48.5 -122.5q0 -73 -49 -121.5t-119 -48.5t-119 48.5t-49 121.5zM82 936q0 75 48.5 122.5t119.5 47.5t119.5 -47.5t48.5 -122.5q0 -73 -49 -121.5t-119 -48.5t-119 48.5t-49 121.5z" />
<glyph unicode=";" horiz-adv-x="499" d="M82 936q0 75 48.5 122.5t119.5 47.5t119.5 -47.5t48.5 -122.5q0 -73 -49 -121.5t-119 -48.5t-119 48.5t-49 121.5zM86 156q0 76 47 123t119 47t119 -47.5t47 -122.5q0 -57 -47 -176l-105 -289h-166l82 313q-44 19 -70 58.5t-26 93.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1208" d="M133 606v221l944 367v-213l-706 -264l706 -264v-213z" />
<glyph unicode="=" horiz-adv-x="1208" d="M133 367v204h944v-204h-944zM133 862v205h944v-205h-944z" />
<glyph unicode="&#x3e;" horiz-adv-x="1208" d="M133 240v213l707 264l-707 264v213l944 -367v-221z" />
<glyph unicode="?" horiz-adv-x="1189" d="M8 1204q184 250 555 250q229 0 365.5 -99t136.5 -274q0 -68 -19.5 -125.5t-50 -98.5t-68 -78t-75 -70t-68 -66t-50 -76t-19.5 -92h-260q0 61 19 115t49 94t66.5 75.5t73 67.5t66.5 63.5t49 70t19 79.5q0 87 -69 140t-187 53q-220 0 -334 -158zM422 145q0 67 46.5 112.5 t117.5 45.5q70 0 116 -45.5t46 -112.5t-46 -113t-116 -46q-71 0 -117.5 46t-46.5 113z" />
<glyph unicode="@" horiz-adv-x="2117" d="M90 520q0 200 71 372t198 295.5t310.5 194t399.5 70.5q209 0 388 -67t305 -185t197 -285t71 -360q0 -266 -105 -416.5t-289 -150.5q-99 0 -166.5 45.5t-93.5 132.5q-122 -178 -368 -178q-213 0 -352.5 146.5t-139.5 375.5q0 226 139.5 372t352.5 146q221 0 342 -145v133 h227v-697q0 -73 29.5 -106t76.5 -33q83 0 127.5 95t44.5 276q0 216 -98.5 383t-277.5 258.5t-410 91.5q-175 0 -323 -57t-250.5 -157.5t-159.5 -242t-57 -307.5q0 -168 56.5 -310.5t157.5 -243.5t247.5 -157.5t320.5 -56.5q218 0 397 90l55 -164q-91 -46 -212.5 -71 t-239.5 -25q-215 0 -397 70.5t-307.5 194.5t-196 297.5t-70.5 375.5zM748 510q0 -148 84 -236t219 -88q133 0 218 87t85 237q0 148 -85 233.5t-218 85.5q-135 0 -219 -86.5t-84 -232.5z" />
<glyph unicode="A" horiz-adv-x="1634" d="M180 0v795q0 316 172.5 487.5t464.5 171.5t464.5 -171.5t172.5 -487.5v-795h-266v369h-746v-369h-262zM442 592h746v227q0 199 -99 300.5t-274 101.5t-274 -101.5t-99 -300.5v-227z" />
<glyph unicode="B" horiz-adv-x="1558" d="M193 0v1434h671q254 0 391.5 -100t137.5 -273q0 -107 -49.5 -188.5t-135.5 -127.5q120 -38 189 -129.5t69 -226.5q0 -186 -143.5 -287.5t-417.5 -101.5h-712zM459 209h430q309 0 309 207t-309 207h-430v-414zM459 829h377q139 0 213.5 50t74.5 147q0 99 -74 149t-214 50 h-377v-396z" />
<glyph unicode="C" horiz-adv-x="1488" d="M90 717q0 211 99.5 379.5t275 263t393.5 94.5q175 0 321.5 -61.5t245.5 -178.5l-172 -161q-155 168 -383 168q-223 0 -367.5 -141.5t-144.5 -362.5t144.5 -362.5t367.5 -141.5q229 0 383 170l172 -164q-100 -117 -246 -178t-323 -61q-217 0 -392 94.5t-274.5 263 t-99.5 379.5z" />
<glyph unicode="D" horiz-adv-x="1691" d="M193 0v1434h626q230 0 408 -89t276.5 -252.5t98.5 -375.5t-98.5 -375.5t-276.5 -252.5t-408 -89h-626zM459 225h348q239 0 382.5 134.5t143.5 357.5t-143.5 357t-382.5 134h-348v-983z" />
<glyph unicode="E" horiz-adv-x="1341" d="M92 397q0 119 66 207t176 129q-85 44 -132.5 125t-47.5 182q0 85 38 158.5t111.5 131t192.5 91t270 33.5q136 0 264.5 -28t224.5 -78l-75 -211q-178 94 -404 94q-175 0 -264.5 -56t-89.5 -151q0 -92 65.5 -140t192.5 -48h379v-222h-393q-145 0 -225.5 -49.5t-80.5 -148.5 t97 -156t291 -57q136 0 263 36.5t214 102.5l86 -205q-96 -75 -248 -116t-328 -41q-309 0 -476 114.5t-167 302.5z" />
<glyph unicode="F" horiz-adv-x="1249" d="M180 0v897q0 261 167 409t460 148q259 0 416 -104l-84 -217q-129 88 -320 88q-185 0 -279 -82.5t-94 -241.5v-123h625v-225h-625v-549h-266z" />
<glyph unicode="G" horiz-adv-x="1581" d="M90 707q0 216 100 387t277 265.5t399 94.5q181 0 328 -59t248 -174l-168 -164q-164 164 -395 164q-230 0 -375.5 -142t-145.5 -370q0 -120 40 -217t108 -158.5t154.5 -94t183.5 -32.5q194 0 327 112v414h252v-733h-219v84q-152 -104 -385 -104q-147 0 -278 48.5 t-232 138.5t-160 229.5t-59 310.5z" />
<glyph unicode="H" horiz-adv-x="1658" d="M193 0v1434h266v-592h741v592h266v-1434h-266v614h-741v-614h-266z" />
<glyph unicode="I" horiz-adv-x="960" d="M84 0v223h264v987h-264v224h793v-224h-265v-987h265v-223h-793z" />
<glyph unicode="J" horiz-adv-x="1073" d="M-27 -18l148 182q118 -164 280 -164q228 0 228 264v946h-512v224h776v-1155q0 -252 -122.5 -378t-360.5 -126q-136 0 -250 54t-187 153z" />
<glyph unicode="K" horiz-adv-x="1492" d="M193 0v1434h266v-736l708 736h299l-602 -641l639 -793h-311l-506 600l-227 -233v-367h-266z" />
<glyph unicode="L" horiz-adv-x="1226" d="M193 0v1434h266v-1209h749v-225h-1015z" />
<glyph unicode="M" horiz-adv-x="2508" d="M193 0v1434h258v-189q65 100 178 154.5t260 54.5q152 0 264 -57.5t170 -161.5q72 104 195.5 161.5t275.5 57.5q252 0 393.5 -145.5t141.5 -421.5v-887h-267v877q0 171 -88.5 257.5t-232.5 86.5q-161 0 -255.5 -90.5t-94.5 -276.5v-854h-267v877q0 172 -83 258t-226 86 q-161 0 -258.5 -91t-97.5 -276v-854h-266z" />
<glyph unicode="N" horiz-adv-x="1632" d="M193 0v1434h258v-181q150 201 454 201q258 0 402.5 -158t144.5 -450v-846h-266v836q0 190 -91.5 287.5t-252.5 97.5q-176 0 -279.5 -103.5t-103.5 -308.5v-809h-266z" />
<glyph unicode="O" horiz-adv-x="1724" d="M90 717q0 209 100 378t276.5 264t395.5 95t395.5 -95t276.5 -263.5t100 -378.5t-100 -378.5t-276.5 -263.5t-395.5 -95t-395.5 95t-276.5 264t-100 378zM358 717q0 -219 143.5 -361.5t360.5 -142.5t360.5 142.5t143.5 361.5t-143.5 361.5t-360.5 142.5t-360.5 -142.5 t-143.5 -361.5z" />
<glyph unicode="P" horiz-adv-x="1486" d="M193 0v1434h589q283 0 447 -136.5t164 -373.5q0 -236 -164 -373t-447 -137h-323v-414h-266zM459 639h311q173 0 263.5 74.5t90.5 210.5t-90.5 210t-263.5 74h-311v-569z" />
<glyph unicode="Q" horiz-adv-x="1724" d="M90 717q0 209 100 378t276.5 264t395.5 95t395.5 -95t276.5 -263.5t100 -378.5q0 -188 -81.5 -344.5t-226.5 -256t-331 -126.5v-266h-266v266q-186 27 -331 126.5t-226.5 256t-81.5 344.5zM358 717q0 -186 104.5 -318.5t274.5 -171.5v316h248v-318q172 39 276.5 172.5 t104.5 319.5q0 219 -143.5 361.5t-360.5 142.5t-360.5 -142.5t-143.5 -361.5z" />
<glyph unicode="R" horiz-adv-x="1497" d="M193 0v1434h589q283 0 447 -136.5t164 -373.5q0 -162 -79.5 -279t-226.5 -174l330 -471h-287l-292 420q-18 -2 -56 -2h-323v-418h-266zM459 637h311q173 0 263.5 75t90.5 212q0 136 -90.5 210t-263.5 74h-311v-571z" />
<glyph unicode="S" horiz-adv-x="1288" d="M72 152l92 206q88 -70 215.5 -113.5t257.5 -43.5q160 0 238.5 51t78.5 135q0 49 -29 85.5t-78.5 59t-113.5 40t-134.5 34t-141 36.5t-134.5 51t-113.5 73t-78.5 108.5t-29 151.5q0 88 36 164.5t105 136t181.5 93.5t255.5 34q131 0 258 -33t219 -94l-84 -207 q-192 113 -395 113q-158 0 -234.5 -54t-76.5 -141q0 -61 47 -102.5t122 -64.5t165.5 -43t181 -49t165.5 -70.5t122 -118.5t47 -184q0 -87 -36 -163t-106 -135.5t-182.5 -93.5t-255.5 -34q-166 0 -320.5 47.5t-244.5 124.5z" />
<glyph unicode="T" horiz-adv-x="1263" d="M-16 1282q287 172 647 172q362 0 649 -172l-92 -205q-204 117 -422 140v-1217h-266v1217q-220 -23 -424 -140z" />
<glyph unicode="U" horiz-adv-x="1624" d="M180 588v846h266v-836q0 -190 91 -287.5t251 -97.5q172 0 274.5 103.5t102.5 308.5v809h267v-1434h-256v182q-145 -202 -451 -202q-255 0 -400 158.5t-145 449.5z" />
<glyph unicode="V" horiz-adv-x="1492" d="M-10 1434h289l477 -1106l481 1106h266l-624 -1434h-263z" />
<glyph unicode="W" horiz-adv-x="2439" d="M180 580v854h266v-846q0 -375 318 -375q321 0 321 375v846h267v-846q0 -375 323 -375q318 0 318 375v846h264v-854q0 -294 -150.5 -447t-431.5 -153q-153 0 -269.5 48t-186.5 132q-71 -85 -187 -132.5t-268 -47.5q-281 0 -432.5 153t-151.5 447z" />
<glyph unicode="X" horiz-adv-x="1419" d="M18 0l533 731l-504 703h301l367 -510l362 510h289l-502 -693l539 -741h-307l-389 545l-385 -545h-304z" />
<glyph unicode="Y" horiz-adv-x="1609" d="M170 952v482h266v-469q0 -188 91.5 -285.5t254.5 -97.5q175 0 278 103t103 306v443h267v-996q0 -317 -174 -487t-478 -170q-181 0 -328 52.5t-249 148.5l108 204q190 -172 465 -172q389 0 389 412v109q-146 -185 -422 -185q-272 0 -421.5 155.5t-149.5 446.5z" />
<glyph unicode="Z" horiz-adv-x="1378" d="M94 0v162l373 463h-254v211h424l303 374h-836v224h1182v-160l-356 -443h237v-210h-407l-320 -398h877v-223h-1223z" />
<glyph unicode="[" horiz-adv-x="716" d="M193 -397v1917h489v-205h-233v-1508h233v-204h-489z" />
<glyph unicode="\" horiz-adv-x="759" d="M-82 1724h229l676 -1929h-229z" />
<glyph unicode="]" horiz-adv-x="716" d="M35 -193h231v1508h-231v205h487v-1917h-487v204z" />
<glyph unicode="^" horiz-adv-x="1210" d="M135 291l367 852h209l364 -852h-201l-268 649l-270 -649h-201z" />
<glyph unicode="_" horiz-adv-x="1024" d="M0 0h1024v-156h-1024v156z" />
<glyph unicode="`" horiz-adv-x="1228" d="M209 1524h301l276 -281h-221z" />
<glyph unicode="a" d="M78 547q0 252 155.5 405.5t397.5 153.5q220 0 350 -145v133h256v-1094h-244v141q-128 -155 -362 -155q-242 0 -397.5 154.5t-155.5 406.5zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248z" />
<glyph unicode="b" d="M168 0v1520h256v-559q130 145 350 145q159 0 285 -68t198 -196t72 -295q0 -252 -156.5 -406.5t-398.5 -154.5q-234 0 -362 155v-141h-244zM420 547q0 -154 92 -248t233 -94t232.5 94t91.5 248t-91.5 248t-232.5 94t-233 -94t-92 -248z" />
<glyph unicode="c" horiz-adv-x="1189" d="M78 547q0 246 166 402.5t426 156.5q161 0 283 -65.5t184 -188.5l-197 -115q-96 152 -272 152q-144 0 -238 -93.5t-94 -248.5q0 -156 94 -249t238 -93q176 0 272 151l197 -114q-63 -123 -185 -189.5t-282 -66.5q-260 0 -426 157.5t-166 403.5z" />
<glyph unicode="d" horiz-adv-x="1406" d="M78 547q0 167 72 295t197.5 196t283.5 68q225 0 352 -148v562h256v-1520h-246v141q-128 -155 -362 -155q-158 0 -283.5 68.5t-197.5 197t-72 295.5zM336 547q0 -154 92.5 -248t233.5 -94t233 94t92 248t-92 248t-233 94t-233.5 -94t-92.5 -248z" />
<glyph unicode="e" horiz-adv-x="1271" d="M78 547q0 244 159 401.5t404 157.5q154 0 278 -64.5t199 -190.5t76 -292l-838 -164q40 -93 125.5 -142.5t206.5 -49.5q178 0 297 119l135 -158q-146 -178 -438 -178q-272 0 -438 157t-166 404zM326 559l618 119q-27 101 -108 162t-195 61q-141 0 -228 -92.5t-87 -247.5 v-2z" />
<glyph unicode="f" horiz-adv-x="722" d="M168 0v1155q0 175 102.5 277t290.5 102q148 0 230 -59l-72 -193q-62 45 -139 45q-162 0 -162 -176v-66h301v-204h-295v-881h-256z" />
<glyph unicode="g" horiz-adv-x="1421" d="M78 580q0 234 157.5 380t395.5 146q249 0 379 -158v146h243v-928q0 -578 -589 -578q-157 0 -299 40.5t-234 115.5l115 193q72 -60 181 -96t222 -36q180 0 264 82t84 250v58q-134 -146 -366 -146q-156 0 -281.5 66t-198.5 188t-73 277zM336 580q0 -139 93.5 -226.5 t240.5 -87.5q145 0 238 87.5t93 226.5q0 137 -93 223t-238 86q-147 0 -240.5 -86t-93.5 -223z" />
<glyph unicode="h" horiz-adv-x="1402" d="M168 0v1520h256v-553q130 139 364 139q100 0 182 -29t144 -86.5t96.5 -150t34.5 -213.5v-627h-256v594q0 144 -67.5 216.5t-192.5 72.5q-140 0 -222.5 -85t-82.5 -245v-553h-256z" />
<glyph unicode="i" horiz-adv-x="591" d="M133 1427q0 65 47 109.5t117 44.5t117 -42t47 -105q0 -68 -46.5 -114t-117.5 -46q-70 0 -117 44t-47 109zM168 0v1094h256v-1094h-256z" />
<glyph unicode="j" horiz-adv-x="604" d="M-190 -348l73 190q58 -43 150 -43q147 0 147 174v1121h256v-1114q0 -182 -100 -287t-281 -105q-161 0 -245 64zM143 1427q0 65 47 109.5t117 44.5t117 -42t47 -105q0 -68 -46.5 -114t-117.5 -46q-70 0 -117 44t-47 109z" />
<glyph unicode="k" horiz-adv-x="1302" d="M168 0v1520h256v-918l530 492h308l-457 -459l500 -635h-312l-379 469l-190 -180v-289h-256z" />
<glyph unicode="l" horiz-adv-x="688" d="M168 342v1178h256v-1160q0 -163 154 -163q52 0 92 20l12 -205q-71 -26 -154 -26q-171 0 -265.5 93t-94.5 263z" />
<glyph unicode="m" horiz-adv-x="2156" d="M168 0v1094h244v-140q127 152 358 152q123 0 218 -45.5t151 -136.5q67 86 173.5 134t235.5 48q207 0 328 -120t121 -359v-627h-256v594q0 144 -63.5 216.5t-182.5 72.5q-130 0 -207.5 -84.5t-77.5 -243.5v-555h-256v594q0 144 -63 216.5t-182 72.5q-131 0 -208 -84.5 t-77 -243.5v-555h-256z" />
<glyph unicode="n" horiz-adv-x="1402" d="M168 0v1094h244v-142q129 154 376 154q100 0 182 -29t144 -86.5t96.5 -150t34.5 -213.5v-627h-256v594q0 144 -67.5 216.5t-192.5 72.5q-140 0 -222.5 -85t-82.5 -245v-553h-256z" />
<glyph unicode="o" horiz-adv-x="1320" d="M78 547q0 245 164 402t417 157q255 0 419.5 -156.5t164.5 -402.5t-164.5 -403.5t-419.5 -157.5q-253 0 -417 158t-164 403zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248z" />
<glyph unicode="p" d="M168 -397v1491h244v-144q129 156 362 156q159 0 285 -68.5t198 -196.5t72 -294q0 -252 -156.5 -406.5t-398.5 -154.5q-221 0 -350 147v-530h-256zM420 547q0 -154 92 -248t233 -94t232.5 94t91.5 248t-91.5 248t-232.5 94t-233 -94t-92 -248z" />
<glyph unicode="q" d="M78 547q0 251 155.5 405t397.5 154q233 0 362 -156v144h244v-1491h-256v530q-129 -147 -350 -147q-242 0 -397.5 154.5t-155.5 406.5zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248z" />
<glyph unicode="r" horiz-adv-x="860" d="M168 0v1094h244v-160q111 172 389 172v-244q-34 6 -60 6q-149 0 -233 -86.5t-84 -251.5v-530h-256z" />
<glyph unicode="s" horiz-adv-x="1054" d="M43 109l98 194q73 -48 176.5 -78t204.5 -30q230 0 230 120q0 37 -29 61.5t-76.5 35.5t-108.5 22t-124 20t-124 31.5t-108.5 54t-76.5 89t-29 135.5q0 156 130 249t351 93q111 0 224.5 -25.5t185.5 -68.5l-99 -195q-140 82 -313 82q-113 0 -171 -33.5t-58 -89.5 q0 -44 37 -71.5t97 -40.5t131.5 -23.5t143.5 -29t132 -49t97 -91t37 -147.5q0 -155 -133 -246.5t-362 -91.5q-134 0 -261 34.5t-202 88.5z" />
<glyph unicode="t" horiz-adv-x="825" d="M160 354v981h256v-250h293v-204h-293v-521q0 -79 39.5 -121t111.5 -42q87 0 144 45l71 -183q-91 -73 -245 -73q-181 0 -279 94t-98 274z" />
<glyph unicode="u" horiz-adv-x="1394" d="M160 469v625h256v-590q0 -147 66 -220t190 -73q137 0 218 85t81 245v553h256v-1094h-244v139q-61 -74 -153 -113.5t-199 -39.5q-218 0 -344.5 121.5t-126.5 361.5z" />
<glyph unicode="v" horiz-adv-x="1183" d="M-10 1094h266l340 -811l350 811h246l-469 -1094h-264z" />
<glyph unicode="w" horiz-adv-x="2007" d="M160 489v605h256v-582q0 -156 53.5 -229.5t173.5 -73.5t177 74.5t57 228.5v582h256v-582q0 -154 56.5 -228.5t176.5 -74.5q119 0 173 74t54 229v582h256v-605q0 -239 -125 -371t-358 -132q-251 0 -362 170q-110 -170 -363 -170q-234 0 -357.5 131.5t-123.5 371.5z" />
<glyph unicode="x" horiz-adv-x="1173" d="M18 0l426 555l-407 539h285l268 -359l268 359h277l-410 -535l428 -559h-289l-280 379l-283 -379h-283z" />
<glyph unicode="y" horiz-adv-x="1394" d="M150 -256l114 193q70 -60 171 -95t210 -35q167 0 246.5 81.5t79.5 248.5v51q-61 -67 -149.5 -103t-190.5 -36q-219 0 -345 121t-126 362v562h256v-529q0 -145 66 -218t190 -73q138 0 218.5 84t80.5 244v492h256v-928q0 -578 -568 -578q-149 0 -284.5 40.5t-224.5 115.5z " />
<glyph unicode="z" horiz-adv-x="1110" d="M88 0v162l246 297h-170v188h325l199 242h-588v205h916v-162l-238 -287h176v-188h-331l-209 -252h618v-205h-944z" />
<glyph unicode="{" horiz-adv-x="757" d="M113 457v209h67q92 0 92 98v422q0 164 88.5 249t260.5 85h102v-205h-43q-75 0 -113.5 -40.5t-38.5 -115.5v-389q0 -93 -28.5 -140.5t-91.5 -68.5q63 -21 91.5 -68.5t28.5 -140.5v-389q0 -75 38.5 -115.5t113.5 -40.5h43v-204h-102q-172 0 -260.5 85t-88.5 249v421 q0 99 -92 99h-67z" />
<glyph unicode="|" horiz-adv-x="620" d="M193 -397v1917h235v-1917h-235z" />
<glyph unicode="}" horiz-adv-x="757" d="M35 -193h43q77 0 115 40.5t38 115.5v389q0 93 28 140.5t91 68.5q-63 21 -91 68.5t-28 140.5v389q0 75 -38 115.5t-115 40.5h-43v205h102q173 0 261.5 -85.5t88.5 -248.5v-422q0 -98 91 -98h69v-209h-69q-43 0 -67 -24.5t-24 -74.5v-421q0 -163 -88.5 -248.5t-261.5 -85.5 h-102v204z" />
<glyph unicode="~" horiz-adv-x="1208" d="M111 545q4 179 81.5 271.5t204.5 92.5q55 0 106 -19.5t89 -47.5t72 -56t69 -47.5t66 -19.5q61 0 97.5 47.5t39.5 126.5h162q-3 -180 -80.5 -272.5t-206.5 -92.5q-54 0 -104.5 19.5t-88 47t-72 55.5t-69.5 47.5t-67 19.5q-60 0 -96.5 -47t-39.5 -125h-163z" />
<glyph unicode="&#xa1;" horiz-adv-x="569" d="M121 946q0 68 47 114t119 46q70 0 116 -46t46 -114q0 -65 -46 -110t-116 -45q-71 0 -118.5 45t-47.5 110zM135 -315l49 933h203l51 -933h-303z" />
<glyph unicode="&#xa2;" horiz-adv-x="1189" d="M78 547q0 220 135 371t356 182v239h166v-235q138 -12 242 -76.5t160 -175.5l-197 -115q-75 121 -205 146v-672q131 25 205 145l197 -114q-57 -110 -161 -175t-241 -79v-234h-166v238q-221 31 -356 183t-135 372zM336 547q0 -127 64 -214.5t169 -115.5v660 q-106 -30 -169.5 -117t-63.5 -213z" />
<glyph unicode="&#xa3;" horiz-adv-x="1345" d="M61 0v223h215v410h-215v168h215v94q0 258 168 408.5t469 150.5q245 0 404 -96l-84 -217q-125 80 -334 80q-175 0 -265.5 -83t-90.5 -241v-96h518v-168h-518v-410h739v-223h-1221z" />
<glyph unicode="&#xa4;" horiz-adv-x="1433" d="M57 143l209 209q-96 140 -96 303q0 160 92 299l-205 203l146 154l211 -209q136 90 303 90q162 0 303 -88l209 207l145 -154l-203 -201q95 -138 95 -301q0 -164 -99 -307l207 -205l-145 -153l-217 215q-133 -84 -295 -84q-164 0 -295 86l-219 -217zM375 655 q0 -135 100.5 -231t241.5 -96t243.5 96t102.5 231q0 136 -102.5 233t-243.5 97t-241.5 -97t-100.5 -233z" />
<glyph unicode="&#xa5;" horiz-adv-x="1476" d="M-18 1434h282l481 -689l482 689h268l-555 -795h340v-143h-408v-140h408v-143h-408v-213h-266v213h-407v143h407v140h-407v143h338z" />
<glyph unicode="&#xa6;" horiz-adv-x="620" d="M193 319h235v-716h-235v716zM193 803v717h235v-717h-235z" />
<glyph unicode="&#xa7;" horiz-adv-x="1046" d="M49 -94l76 190q70 -53 169.5 -86t197.5 -33q103 0 161 38.5t58 107.5q0 48 -35.5 80t-92.5 49.5t-126 32t-137.5 36.5t-125.5 54.5t-92.5 93.5t-35.5 147q0 174 141 265q-96 79 -96 217q0 158 130.5 254t360.5 96q102 0 215.5 -26.5t177.5 -73.5l-75 -187 q-143 90 -334 90q-123 0 -185.5 -37.5t-62.5 -107.5q0 -40 27.5 -69t73 -44.5t103 -31.5t118 -29.5t118 -39.5t103 -60t73 -93t27.5 -137q0 -77 -37 -143t-104 -111q98 -78 98 -219q0 -155 -124 -251.5t-329 -96.5q-122 0 -245 35.5t-191 89.5zM289 633q0 -27 9.5 -49 t30 -39t42 -29t57.5 -23t62.5 -18t71.5 -17.5t71 -17.5q57 21 91 62t34 96q0 27 -9.5 49t-30 39t-41.5 28.5t-57 23t-62.5 18.5t-72 17.5t-71.5 17.5q-57 -21 -91 -62t-34 -96z" />
<glyph unicode="&#xa8;" horiz-adv-x="1228" d="M293 1389q0 55 35 89.5t88 34.5t88 -34.5t35 -89.5q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5zM690 1389q0 55 35 89.5t88 34.5t88 -34.5t35 -89.5q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1626" d="M90 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM207 717q0 -169 78 -309t217 -221t309 -81q169 0 308.5 81.5 t218.5 222.5t79 311t-77.5 308.5t-215.5 218t-309 79.5q-172 0 -311.5 -81t-218 -220.5t-78.5 -308.5zM401 717q0 183 122 300.5t311 117.5q115 0 204.5 -48t137.5 -131l-150 -98q-64 103 -195 103q-100 0 -166.5 -67t-66.5 -177t66.5 -177t166.5 -67q131 0 195 102l150 -94 q-48 -86 -137 -134t-205 -48q-189 0 -311 117.5t-122 300.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="835" d="M68 1061q0 89 70.5 140.5t217.5 51.5h181v7q0 139 -168 139q-58 0 -116.5 -18.5t-98.5 -49.5l-66 115q55 41 137.5 64.5t163.5 23.5q326 0 326 -283v-379h-166v74q-65 -82 -221 -82q-125 0 -192.5 54.5t-67.5 142.5zM238 1067q0 -41 34.5 -62.5t96.5 -21.5q123 0 168 86 v80h-156q-143 0 -143 -82z" />
<glyph unicode="&#xab;" horiz-adv-x="1095" d="M86 547l291 397h231l-284 -397l284 -395h-231zM512 547l291 397h231l-284 -397l284 -395h-231z" />
<glyph unicode="&#xac;" horiz-adv-x="1208" d="M133 616v205h944v-567h-219v362h-725z" />
<glyph unicode="&#xad;" horiz-adv-x="788" d="M113 463v213h563v-213h-563z" />
<glyph unicode="&#xae;" horiz-adv-x="1626" d="M90 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM207 717q0 -169 78 -309t217 -221t309 -81q169 0 308.5 81.5 t218.5 222.5t79 311t-77.5 308.5t-215.5 218t-309 79.5q-172 0 -311.5 -81t-218 -220.5t-78.5 -308.5zM510 313v807h336q159 0 249.5 -76t90.5 -204q0 -183 -162 -250l180 -277h-176l-160 246h-22h-160v-246h-176zM684 696h152q86 0 133 38t47 106t-47 104.5t-133 36.5h-152 v-285z" />
<glyph unicode="&#xaf;" horiz-adv-x="1228" d="M260 1307v153h709v-153h-709z" />
<glyph unicode="&#xb0;" horiz-adv-x="858" d="M80 1106q0 145 101 244.5t247 99.5t247 -99.5t101 -244.5q0 -143 -101 -242.5t-247 -99.5t-247 99.5t-101 242.5zM221 1106q0 -89 59.5 -148t147.5 -59t146.5 59.5t58.5 147.5q0 89 -58.5 149t-146.5 60t-147.5 -59.5t-59.5 -149.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="1208" d="M133 0v205h944v-205h-944zM133 748v202h363v346h219v-346h362v-202h-362v-345h-219v345h-363z" />
<glyph unicode="&#xb2;" horiz-adv-x="880" d="M49 1382q53 71 144 111.5t213 40.5q162 0 254 -70t92 -182q0 -66 -32.5 -123t-125.5 -139l-234 -205h426v-145h-696v114l367 320q64 57 87 93.5t23 72.5q0 49 -44 79.5t-132 30.5q-144 0 -213 -90z" />
<glyph unicode="&#xb3;" horiz-adv-x="880" d="M45 756l72 137q53 -39 131.5 -61.5t163.5 -22.5q99 0 149.5 31t50.5 86q0 116 -190 116h-107v119l205 213h-442v146h674v-115l-226 -231q130 -15 200.5 -81t70.5 -167q0 -115 -100.5 -193t-284.5 -78q-109 0 -210 28t-157 73z" />
<glyph unicode="&#xb4;" horiz-adv-x="1228" d="M442 1243l277 281h301l-356 -281h-222z" />
<glyph unicode="&#xb5;" d="M168 -397v1491h256v-590q0 -147 66.5 -220t191.5 -73q137 0 218 85t81 245v553h256v-1094h-240v145q-54 -82 -137 -120.5t-176 -38.5q-167 0 -260 96v-479h-256z" />
<glyph unicode="&#xb6;" horiz-adv-x="1363" d="M35 1141q0 174 128 276.5t343 102.5h665v-1725h-210v1532h-285v-1532h-211v965q-191 5 -310.5 107.5t-119.5 273.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="581" d="M123 571q0 74 48.5 122t119.5 48q73 0 120.5 -47.5t47.5 -122.5q0 -77 -47.5 -124.5t-120.5 -47.5q-72 0 -120 47.5t-48 124.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1228" d="M367 -424l47 121q62 -33 139 -33q109 0 109 74q0 67 -109 67h-64l56 209h139l-29 -114q88 -10 133.5 -56.5t45.5 -115.5q0 -89 -76.5 -142t-202.5 -53q-112 0 -188 43z" />
<glyph unicode="&#xb9;" horiz-adv-x="880" d="M158 670v145h225v559h-209v146h395v-705h197v-145h-608z" />
<glyph unicode="&#xba;" horiz-adv-x="864" d="M57 1200q0 145 106 239.5t269 94.5q164 0 269.5 -94.5t105.5 -239.5q0 -148 -105.5 -242t-269.5 -94q-163 0 -269 94.5t-106 241.5zM233 1200q0 -86 54.5 -138t144.5 -52t144.5 52t54.5 138q0 85 -55 137t-144 52t-144 -52t-55 -137z" />
<glyph unicode="&#xbb;" horiz-adv-x="1095" d="M61 152l285 395l-285 397h232l291 -397l-291 -395h-232zM487 152l285 395l-285 397h232l291 -397l-291 -395h-232z" />
<glyph unicode="&#xbc;" horiz-adv-x="2127" d="M158 584v145h225v559h-209v146h395v-705h197v-145h-608zM475 0l979 1434h199l-979 -1434h-199zM1282 188v119l399 543h203l-381 -516h262v158h164v-158h152v-146h-152v-188h-180v188h-467z" />
<glyph unicode="&#xbd;" horiz-adv-x="2127" d="M158 584v145h225v559h-209v146h395v-705h197v-145h-608zM475 0l979 1434h199l-979 -1434h-199zM1296 713q112 151 357 151q162 0 254 -70t92 -182q0 -66 -32.5 -123t-125.5 -139l-233 -205h426v-145h-697v115l367 319q65 57 88 93.5t23 72.5q0 49 -44.5 80t-132.5 31 q-144 0 -213 -90z" />
<glyph unicode="&#xbe;" horiz-adv-x="2127" d="M45 670l72 137q53 -39 131.5 -61.5t163.5 -22.5q99 0 149.5 31t50.5 86q0 116 -190 116h-107v119l205 213h-442v146h674v-115l-226 -232q130 -15 200.5 -80.5t70.5 -166.5q0 -115 -100.5 -193t-284.5 -78q-109 0 -210 28t-157 73zM475 0l979 1434h199l-979 -1434h-199z M1282 188v119l399 543h203l-381 -516h262v158h164v-158h152v-146h-152v-188h-180v188h-467z" />
<glyph unicode="&#xbf;" horiz-adv-x="1189" d="M125 25q0 67 19 123.5t50 96.5t68 76t74.5 68.5t68.5 65t50.5 74t19.5 89.5h260q0 -60 -19 -112.5t-49 -91.5t-66.5 -74t-73 -66.5t-66.5 -62.5t-49 -69t-19 -79q0 -77 70 -127.5t186 -50.5q217 0 334 158l197 -129q-87 -120 -226.5 -185t-326.5 -65q-227 0 -364.5 97 t-137.5 264zM440 946q0 68 46.5 114t117.5 46t117.5 -46t46.5 -114q0 -65 -46.5 -110t-117.5 -45t-117.5 45t-46.5 110z" />
<glyph unicode="&#xc0;" horiz-adv-x="1634" d="M180 0v795q0 316 172.5 487.5t464.5 171.5t464.5 -171.5t172.5 -487.5v-795h-266v369h-746v-369h-262zM412 1831h301l276 -281h-221zM442 592h746v227q0 199 -99 300.5t-274 101.5t-274 -101.5t-99 -300.5v-227z" />
<glyph unicode="&#xc1;" horiz-adv-x="1634" d="M180 0v795q0 316 172.5 487.5t464.5 171.5t464.5 -171.5t172.5 -487.5v-795h-266v369h-746v-369h-262zM442 592h746v227q0 199 -99 300.5t-274 101.5t-274 -101.5t-99 -300.5v-227zM645 1550l277 281h301l-357 -281h-221z" />
<glyph unicode="&#xc2;" horiz-adv-x="1634" d="M180 0v795q0 316 172.5 487.5t464.5 171.5t464.5 -171.5t172.5 -487.5v-795h-266v369h-746v-369h-262zM426 1550l274 281h234l274 -281h-211l-180 152l-180 -152h-211zM442 592h746v227q0 199 -99 300.5t-274 101.5t-274 -101.5t-99 -300.5v-227z" />
<glyph unicode="&#xc3;" horiz-adv-x="1634" d="M180 0v795q0 316 172.5 487.5t464.5 171.5t464.5 -171.5t172.5 -487.5v-795h-266v369h-746v-369h-262zM442 592h746v227q0 199 -99 300.5t-274 101.5t-274 -101.5t-99 -300.5v-227zM449 1563q3 126 63 200t158 74q46 0 90 -21t72 -46.5t60 -46.5t56 -21q43 0 70.5 31 t30.5 86h137q-3 -121 -63 -195t-158 -74q-46 0 -90 21t-72.5 47t-60.5 47t-56 21q-43 0 -70 -32.5t-30 -90.5h-137z" />
<glyph unicode="&#xc4;" horiz-adv-x="1634" d="M180 0v795q0 316 172.5 487.5t464.5 171.5t464.5 -171.5t172.5 -487.5v-795h-266v369h-746v-369h-262zM442 592h746v227q0 199 -99 300.5t-274 101.5t-274 -101.5t-99 -300.5v-227zM496 1696q0 55 34.5 90t87.5 35t88 -35t35 -90q0 -54 -35 -88.5t-88 -34.5t-87.5 34.5 t-34.5 88.5zM893 1696q0 55 35 90t88 35t88 -35t35 -90q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1634" d="M180 0v795q0 316 172.5 487.5t464.5 171.5t464.5 -171.5t172.5 -487.5v-795h-266v369h-746v-369h-262zM442 592h746v227q0 199 -99 300.5t-274 101.5t-274 -101.5t-99 -300.5v-227zM578 1788q0 99 68 168t169 69q102 0 171 -69t69 -168q0 -98 -69 -166t-171 -68 q-101 0 -169 68t-68 166zM686 1788q0 -56 37 -92.5t92 -36.5q57 0 94 36.5t37 92.5q0 57 -37.5 95t-93.5 38q-55 0 -92 -37.5t-37 -95.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2211" d="M164 0v797q0 307 161 472t459 165h1321v-220h-786v-380h698v-211h-698v-404h813v-219h-1075v389h-625v-389h-268zM432 606h625v594h-250q-375 0 -375 -409v-185z" />
<glyph unicode="&#xc7;" horiz-adv-x="1488" d="M90 717q0 211 99.5 379.5t275 263t393.5 94.5q175 0 321.5 -61.5t245.5 -178.5l-172 -161q-155 168 -383 168q-223 0 -367.5 -141.5t-144.5 -362.5t144.5 -362.5t367.5 -141.5q229 0 383 170l172 -164q-97 -112 -236 -173t-308 -66l-21 -80q88 -10 133 -56.5t45 -115.5 q0 -89 -76 -142t-202 -53q-113 0 -189 43l47 121q62 -33 140 -33q108 0 108 74q0 67 -108 67h-64l47 181q-141 18 -261.5 80.5t-206.5 157t-134.5 222t-48.5 271.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1341" d="M92 397q0 119 66 207t176 129q-85 44 -132.5 125t-47.5 182q0 85 38 158.5t111.5 131t192.5 91t270 33.5q136 0 264.5 -28t224.5 -78l-75 -211q-178 94 -404 94q-175 0 -264.5 -56t-89.5 -151q0 -92 65.5 -140t192.5 -48h379v-222h-393q-145 0 -225.5 -49.5t-80.5 -148.5 t97 -156t291 -57q136 0 263 36.5t214 102.5l86 -205q-96 -75 -248 -116t-328 -41q-309 0 -476 114.5t-167 302.5zM324 1831h301l276 -281h-221z" />
<glyph unicode="&#xc9;" horiz-adv-x="1341" d="M92 397q0 119 66 207t176 129q-85 44 -132.5 125t-47.5 182q0 85 38 158.5t111.5 131t192.5 91t270 33.5q136 0 264.5 -28t224.5 -78l-75 -211q-178 94 -404 94q-175 0 -264.5 -56t-89.5 -151q0 -92 65.5 -140t192.5 -48h379v-222h-393q-145 0 -225.5 -49.5t-80.5 -148.5 t97 -156t291 -57q136 0 263 36.5t214 102.5l86 -205q-96 -75 -248 -116t-328 -41q-309 0 -476 114.5t-167 302.5zM557 1550l277 281h301l-357 -281h-221z" />
<glyph unicode="&#xca;" horiz-adv-x="1341" d="M92 397q0 119 66 207t176 129q-85 44 -132.5 125t-47.5 182q0 85 38 158.5t111.5 131t192.5 91t270 33.5q136 0 264.5 -28t224.5 -78l-75 -211q-178 94 -404 94q-175 0 -264.5 -56t-89.5 -151q0 -92 65.5 -140t192.5 -48h379v-222h-393q-145 0 -225.5 -49.5t-80.5 -148.5 t97 -156t291 -57q136 0 263 36.5t214 102.5l86 -205q-96 -75 -248 -116t-328 -41q-309 0 -476 114.5t-167 302.5zM338 1550l274 281h234l274 -281h-211l-180 152l-180 -152h-211z" />
<glyph unicode="&#xcb;" horiz-adv-x="1341" d="M92 397q0 119 66 207t176 129q-85 44 -132.5 125t-47.5 182q0 85 38 158.5t111.5 131t192.5 91t270 33.5q136 0 264.5 -28t224.5 -78l-75 -211q-178 94 -404 94q-175 0 -264.5 -56t-89.5 -151q0 -92 65.5 -140t192.5 -48h379v-222h-393q-145 0 -225.5 -49.5t-80.5 -148.5 t97 -156t291 -57q136 0 263 36.5t214 102.5l86 -205q-96 -75 -248 -116t-328 -41q-309 0 -476 114.5t-167 302.5zM408 1696q0 55 34.5 90t87.5 35t88 -35t35 -90q0 -54 -35 -88.5t-88 -34.5t-87.5 34.5t-34.5 88.5zM805 1696q0 55 35 90t88 35t88 -35t35 -90 q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="960" d="M76 1831h301l276 -281h-221zM84 0v223h264v987h-264v224h793v-224h-265v-987h265v-223h-793z" />
<glyph unicode="&#xcd;" horiz-adv-x="960" d="M84 0v223h264v987h-264v224h793v-224h-265v-987h265v-223h-793zM309 1550l277 281h301l-357 -281h-221z" />
<glyph unicode="&#xce;" horiz-adv-x="960" d="M84 0v223h264v987h-264v224h793v-224h-265v-987h265v-223h-793zM90 1550l275 281h233l274 -281h-210l-181 152l-180 -152h-211z" />
<glyph unicode="&#xcf;" horiz-adv-x="960" d="M84 0v223h264v987h-264v224h793v-224h-265v-987h265v-223h-793zM160 1696q0 55 35 90t88 35t88 -35t35 -90q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5zM557 1696q0 55 35 90t88 35t88 -35t35 -90q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1712" d="M10 631v196h203v607h627q230 0 408 -89t276 -252.5t98 -375.5t-98 -375.5t-276 -252.5t-408 -89h-627v631h-203zM479 225h348q239 0 383 134.5t144 357.5t-144 357t-383 134h-348v-381h383v-196h-383v-406z" />
<glyph unicode="&#xd1;" horiz-adv-x="1632" d="M193 0v1434h258v-181q150 201 454 201q258 0 402.5 -158t144.5 -450v-846h-266v836q0 190 -91.5 287.5t-252.5 97.5q-176 0 -279.5 -103.5t-103.5 -308.5v-809h-266zM455 1563q3 126 63 200t158 74q46 0 90 -21t72 -46.5t60 -46.5t56 -21q43 0 70.5 31t30.5 86h137 q-3 -121 -63 -195t-158 -74q-46 0 -90 21t-72.5 47t-60.5 47t-56 21q-43 0 -70 -32.5t-30 -90.5h-137z" />
<glyph unicode="&#xd2;" horiz-adv-x="1724" d="M90 717q0 209 100 378t276.5 264t395.5 95t395.5 -95t276.5 -263.5t100 -378.5t-100 -378.5t-276.5 -263.5t-395.5 -95t-395.5 95t-276.5 264t-100 378zM358 717q0 -219 143.5 -361.5t360.5 -142.5t360.5 142.5t143.5 361.5t-143.5 361.5t-360.5 142.5t-360.5 -142.5 t-143.5 -361.5zM457 1831h301l276 -281h-221z" />
<glyph unicode="&#xd3;" horiz-adv-x="1724" d="M90 717q0 209 100 378t276.5 264t395.5 95t395.5 -95t276.5 -263.5t100 -378.5t-100 -378.5t-276.5 -263.5t-395.5 -95t-395.5 95t-276.5 264t-100 378zM358 717q0 -219 143.5 -361.5t360.5 -142.5t360.5 142.5t143.5 361.5t-143.5 361.5t-360.5 142.5t-360.5 -142.5 t-143.5 -361.5zM690 1550l277 281h301l-357 -281h-221z" />
<glyph unicode="&#xd4;" horiz-adv-x="1724" d="M90 717q0 209 100 378t276.5 264t395.5 95t395.5 -95t276.5 -263.5t100 -378.5t-100 -378.5t-276.5 -263.5t-395.5 -95t-395.5 95t-276.5 264t-100 378zM358 717q0 -219 143.5 -361.5t360.5 -142.5t360.5 142.5t143.5 361.5t-143.5 361.5t-360.5 142.5t-360.5 -142.5 t-143.5 -361.5zM471 1550l274 281h234l274 -281h-211l-180 152l-180 -152h-211z" />
<glyph unicode="&#xd5;" horiz-adv-x="1724" d="M90 717q0 209 100 378t276.5 264t395.5 95t395.5 -95t276.5 -263.5t100 -378.5t-100 -378.5t-276.5 -263.5t-395.5 -95t-395.5 95t-276.5 264t-100 378zM358 717q0 -219 143.5 -361.5t360.5 -142.5t360.5 142.5t143.5 361.5t-143.5 361.5t-360.5 142.5t-360.5 -142.5 t-143.5 -361.5zM494 1563q3 126 63 200t158 74q46 0 90 -21t72 -46.5t60 -46.5t56 -21q43 0 70.5 31t30.5 86h137q-3 -121 -63 -195t-158 -74q-46 0 -90 21t-72.5 47t-60.5 47t-56 21q-43 0 -70 -32.5t-30 -90.5h-137z" />
<glyph unicode="&#xd6;" horiz-adv-x="1724" d="M90 717q0 209 100 378t276.5 264t395.5 95t395.5 -95t276.5 -263.5t100 -378.5t-100 -378.5t-276.5 -263.5t-395.5 -95t-395.5 95t-276.5 264t-100 378zM358 717q0 -219 143.5 -361.5t360.5 -142.5t360.5 142.5t143.5 361.5t-143.5 361.5t-360.5 142.5t-360.5 -142.5 t-143.5 -361.5zM541 1696q0 55 35 90t88 35t87.5 -35t34.5 -90q0 -54 -34.5 -88.5t-87.5 -34.5t-88 34.5t-35 88.5zM938 1696q0 55 35 90t88 35t88 -35t35 -90q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1208" d="M203 455l258 262l-258 262l137 149l264 -266l264 266l142 -149l-260 -262l260 -262l-142 -150l-264 266l-264 -266z" />
<glyph unicode="&#xd8;" horiz-adv-x="1724" d="M90 717q0 209 100 378t276.5 264t395.5 95q200 0 373 -84l147 207h191l-207 -289q127 -102 197.5 -249t70.5 -322q0 -210 -100 -378.5t-276.5 -263.5t-395.5 -95q-206 0 -370 83l-148 -206h-190l206 288q-128 101 -199 248.5t-71 323.5zM358 717q0 -222 148 -367l586 819 q-107 52 -230 52q-217 0 -360.5 -142.5t-143.5 -361.5zM635 262q107 -49 227 -49q217 0 360.5 142.5t143.5 361.5q0 220 -147 364z" />
<glyph unicode="&#xd9;" horiz-adv-x="1624" d="M180 588v846h266v-836q0 -190 91 -287.5t251 -97.5q172 0 274.5 103.5t102.5 308.5v809h267v-1434h-256v182q-145 -202 -451 -202q-255 0 -400 158.5t-145 449.5zM397 1831h301l277 -281h-221z" />
<glyph unicode="&#xda;" horiz-adv-x="1624" d="M180 588v846h266v-836q0 -190 91 -287.5t251 -97.5q172 0 274.5 103.5t102.5 308.5v809h267v-1434h-256v182q-145 -202 -451 -202q-255 0 -400 158.5t-145 449.5zM631 1550l276 281h301l-356 -281h-221z" />
<glyph unicode="&#xdb;" horiz-adv-x="1624" d="M180 588v846h266v-836q0 -190 91 -287.5t251 -97.5q172 0 274.5 103.5t102.5 308.5v809h267v-1434h-256v182q-145 -202 -451 -202q-255 0 -400 158.5t-145 449.5zM412 1550l274 281h234l274 -281h-211l-180 152l-180 -152h-211z" />
<glyph unicode="&#xdc;" horiz-adv-x="1624" d="M180 588v846h266v-836q0 -190 91 -287.5t251 -97.5q172 0 274.5 103.5t102.5 308.5v809h267v-1434h-256v182q-145 -202 -451 -202q-255 0 -400 158.5t-145 449.5zM481 1696q0 55 35 90t88 35t88 -35t35 -90q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5zM879 1696 q0 55 34.5 90t87.5 35t88 -35t35 -90q0 -54 -35 -88.5t-88 -34.5t-87.5 34.5t-34.5 88.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1609" d="M170 952v482h266v-469q0 -188 91.5 -285.5t254.5 -97.5q175 0 278 103t103 306v443h267v-996q0 -317 -174 -487t-478 -170q-181 0 -328 52.5t-249 148.5l108 204q190 -172 465 -172q389 0 389 412v109q-146 -185 -422 -185q-272 0 -421.5 155.5t-149.5 446.5zM629 1550 l276 281h301l-356 -281h-221z" />
<glyph unicode="&#xde;" horiz-adv-x="1486" d="M193 0v1434h266v-168h323q283 0 447 -137t164 -373t-164 -373t-447 -137h-323v-246h-266zM459 471h311q173 0 263.5 74.5t90.5 210.5t-90.5 210t-263.5 74h-311v-569z" />
<glyph unicode="&#xdf;" horiz-adv-x="1400" d="M168 0v1001q0 171 72 293.5t191.5 181t273.5 58.5q228 0 361.5 -118t133.5 -300q0 -101 -45 -182t-121 -135q134 -39 212.5 -138t78.5 -243q0 -201 -139.5 -316.5t-366.5 -115.5q-138 0 -239 32l34 211q77 -26 189 -26q123 0 193.5 58.5t70.5 164.5q0 104 -78.5 161.5 t-212.5 57.5h-112v211q134 1 212 68.5t78 177.5q0 100 -65 157.5t-184 57.5q-133 0 -207 -74.5t-74 -216.5v-1026h-256z" />
<glyph unicode="&#xe0;" d="M78 547q0 252 155.5 405.5t397.5 153.5q220 0 350 -145v133h256v-1094h-244v141q-128 -155 -362 -155q-242 0 -397.5 154.5t-155.5 406.5zM295 1524h301l276 -281h-221zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94 t-91.5 -248z" />
<glyph unicode="&#xe1;" d="M78 547q0 252 155.5 405.5t397.5 153.5q220 0 350 -145v133h256v-1094h-244v141q-128 -155 -362 -155q-242 0 -397.5 154.5t-155.5 406.5zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248zM528 1243l277 281h301 l-356 -281h-222z" />
<glyph unicode="&#xe2;" d="M78 547q0 252 155.5 405.5t397.5 153.5q220 0 350 -145v133h256v-1094h-244v141q-128 -155 -362 -155q-242 0 -397.5 154.5t-155.5 406.5zM309 1243l275 281h233l275 -281h-211l-181 152l-180 -152h-211zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248 t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248z" />
<glyph unicode="&#xe3;" d="M78 547q0 252 155.5 405.5t397.5 153.5q220 0 350 -145v133h256v-1094h-244v141q-128 -155 -362 -155q-242 0 -397.5 154.5t-155.5 406.5zM332 1255q3 126 63 200.5t158 74.5q46 0 90 -21t72 -46.5t60 -46.5t56 -21q43 0 70.5 30.5t30.5 85.5h137q-3 -121 -63 -194.5 t-158 -73.5q-46 0 -90 21t-72.5 46.5t-60.5 46.5t-56 21q-43 0 -70 -32.5t-30 -90.5h-137zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248z" />
<glyph unicode="&#xe4;" d="M78 547q0 252 155.5 405.5t397.5 153.5q220 0 350 -145v133h256v-1094h-244v141q-128 -155 -362 -155q-242 0 -397.5 154.5t-155.5 406.5zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248zM379 1389 q0 55 35 89.5t88 34.5t88 -34.5t35 -89.5q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5zM776 1389q0 55 35 89.5t88 34.5t88 -34.5t35 -89.5q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5z" />
<glyph unicode="&#xe5;" d="M78 547q0 252 155.5 405.5t397.5 153.5q220 0 350 -145v133h256v-1094h-244v141q-128 -155 -362 -155q-242 0 -397.5 154.5t-155.5 406.5zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248zM461 1448 q0 99 68.5 168.5t168.5 69.5q102 0 171 -69.5t69 -168.5q0 -98 -69 -166t-171 -68q-100 0 -168.5 68t-68.5 166zM569 1448q0 -56 36.5 -92.5t92.5 -36.5q57 0 94 36.5t37 92.5q0 57 -37.5 95t-93.5 38q-55 0 -92 -37.5t-37 -95.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="2031" d="M90 324q0 139 110.5 226t344.5 87h282v18q0 114 -68.5 176t-201.5 62q-94 0 -183.5 -29t-150.5 -80l-100 187q86 66 207.5 100.5t253.5 34.5q289 0 407 -168q76 79 183.5 123.5t230.5 44.5q113 0 212 -38t173.5 -107.5t118.5 -174t45 -231.5l-842 -162q40 -89 129 -139.5 t209 -50.5q169 0 291 119l139 -158q-155 -178 -436 -178q-147 0 -272.5 51.5t-208.5 155.5q-66 -109 -180.5 -158t-250.5 -49q-205 0 -323.5 92.5t-118.5 245.5zM342 328q0 -73 56.5 -114.5t156.5 -41.5q127 0 199.5 65.5t72.5 174.5v57h-264q-221 0 -221 -141zM1083 559 l617 119q-27 101 -106 162t-191 61q-140 0 -230 -91t-90 -235v-16z" />
<glyph unicode="&#xe7;" horiz-adv-x="1189" d="M78 547q0 246 166 402.5t426 156.5q161 0 283 -65.5t184 -188.5l-197 -115q-96 152 -272 152q-144 0 -238 -93.5t-94 -248.5q0 -156 94 -249t238 -93q176 0 272 151l197 -114q-61 -120 -179 -186.5t-272 -69.5l-22 -86q88 -10 133 -56.5t45 -115.5q0 -89 -76.5 -142 t-202.5 -53q-112 0 -188 43l47 121q62 -33 139 -33q109 0 109 74q0 67 -109 67h-63l51 191q-212 35 -341.5 185.5t-129.5 365.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1271" d="M78 547q0 244 159 401.5t404 157.5q154 0 278 -64.5t199 -190.5t76 -292l-838 -164q40 -93 125.5 -142.5t206.5 -49.5q178 0 297 119l135 -158q-146 -178 -438 -178q-272 0 -438 157t-166 404zM229 1524h301l277 -281h-221zM326 559l618 119q-27 101 -108 162t-195 61 q-141 0 -228 -92.5t-87 -247.5v-2z" />
<glyph unicode="&#xe9;" horiz-adv-x="1271" d="M78 547q0 244 159 401.5t404 157.5q154 0 278 -64.5t199 -190.5t76 -292l-838 -164q40 -93 125.5 -142.5t206.5 -49.5q178 0 297 119l135 -158q-146 -178 -438 -178q-272 0 -438 157t-166 404zM326 559l618 119q-27 101 -108 162t-195 61q-141 0 -228 -92.5t-87 -247.5 v-2zM463 1243l276 281h301l-356 -281h-221z" />
<glyph unicode="&#xea;" horiz-adv-x="1271" d="M78 547q0 244 159 401.5t404 157.5q154 0 278 -64.5t199 -190.5t76 -292l-838 -164q40 -93 125.5 -142.5t206.5 -49.5q178 0 297 119l135 -158q-146 -178 -438 -178q-272 0 -438 157t-166 404zM244 1243l274 281h234l274 -281h-211l-180 152l-180 -152h-211zM326 559 l618 119q-27 101 -108 162t-195 61q-141 0 -228 -92.5t-87 -247.5v-2z" />
<glyph unicode="&#xeb;" horiz-adv-x="1271" d="M78 547q0 244 159 401.5t404 157.5q154 0 278 -64.5t199 -190.5t76 -292l-838 -164q40 -93 125.5 -142.5t206.5 -49.5q178 0 297 119l135 -158q-146 -178 -438 -178q-272 0 -438 157t-166 404zM313 1389q0 55 35 89.5t88 34.5t88 -34.5t35 -89.5q0 -54 -35 -88.5 t-88 -34.5t-88 34.5t-35 88.5zM326 559l618 119q-27 101 -108 162t-195 61q-141 0 -228 -92.5t-87 -247.5v-2zM711 1389q0 55 35 89.5t88 34.5t87.5 -34.5t34.5 -89.5q0 -54 -34.5 -88.5t-87.5 -34.5t-88 34.5t-35 88.5z" />
<glyph unicode="&#xec;" horiz-adv-x="591" d="M-109 1524h302l276 -281h-221zM168 0v1094h256v-1094h-256z" />
<glyph unicode="&#xed;" horiz-adv-x="591" d="M125 1243l276 281h301l-356 -281h-221zM168 0v1094h256v-1094h-256z" />
<glyph unicode="&#xee;" horiz-adv-x="591" d="M-43 1243l225 281h230l225 -281h-201l-139 143l-139 -143h-201zM168 0v1094h256v-1094h-256z" />
<glyph unicode="&#xef;" horiz-adv-x="591" d="M23 1389q0 53 33 87.5t85 34.5q50 0 83.5 -34.5t33.5 -87.5q0 -52 -33.5 -86.5t-83.5 -34.5q-52 0 -85 34t-33 87zM168 0v1094h256v-1094h-256zM336 1389q0 52 34 87t85 35t83.5 -34t32.5 -88q0 -53 -32.5 -87t-83.5 -34t-85 34.5t-34 86.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1327" d="M78 446q0 137 69.5 243t187.5 162t265 56q259 0 389 -178v31q0 272 -139 411l-535 -210l-61 151l416 164q-73 20 -166 20q-157 0 -289 -47l-39 209q153 45 332 45q258 0 440 -119l182 72l60 -151l-109 -43q170 -207 170 -560q0 -337 -170.5 -529.5t-470.5 -192.5 q-151 0 -271 56t-190.5 163t-70.5 247zM336 442q0 -115 81 -184.5t212 -69.5q140 0 224.5 74t84.5 180q0 110 -85.5 183t-215.5 73q-138 0 -219.5 -69.5t-81.5 -186.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1402" d="M168 0v1094h244v-142q129 154 376 154q100 0 182 -29t144 -86.5t96.5 -150t34.5 -213.5v-627h-256v594q0 144 -67.5 216.5t-192.5 72.5q-140 0 -222.5 -85t-82.5 -245v-553h-256zM338 1255q3 126 63 200.5t158 74.5q46 0 90 -21t72.5 -46.5t60.5 -46.5t56 -21 q43 0 70 30.5t30 85.5h137q-3 -121 -63 -194.5t-158 -73.5q-46 0 -90 21t-72.5 46.5t-60.5 46.5t-56 21q-43 0 -70 -32.5t-30 -90.5h-137z" />
<glyph unicode="&#xf2;" horiz-adv-x="1320" d="M78 547q0 245 164 402t417 157q255 0 419.5 -156.5t164.5 -402.5t-164.5 -403.5t-419.5 -157.5q-253 0 -417 158t-164 403zM252 1524h301l276 -281h-221zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248z" />
<glyph unicode="&#xf3;" horiz-adv-x="1320" d="M78 547q0 245 164 402t417 157q255 0 419.5 -156.5t164.5 -402.5t-164.5 -403.5t-419.5 -157.5q-253 0 -417 158t-164 403zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248zM485 1243l277 281h301l-356 -281 h-222z" />
<glyph unicode="&#xf4;" horiz-adv-x="1320" d="M78 547q0 245 164 402t417 157q255 0 419.5 -156.5t164.5 -402.5t-164.5 -403.5t-419.5 -157.5q-253 0 -417 158t-164 403zM266 1243l275 281h233l275 -281h-211l-181 152l-180 -152h-211zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94 q-140 0 -231.5 -94t-91.5 -248z" />
<glyph unicode="&#xf5;" horiz-adv-x="1320" d="M78 547q0 245 164 402t417 157q255 0 419.5 -156.5t164.5 -402.5t-164.5 -403.5t-419.5 -157.5q-253 0 -417 158t-164 403zM289 1255q3 126 63 200.5t158 74.5q46 0 90 -21t72 -46.5t60 -46.5t56 -21q43 0 70.5 30.5t30.5 85.5h137q-3 -121 -63 -194.5t-158 -73.5 q-46 0 -90 21t-72.5 46.5t-60.5 46.5t-56 21q-43 0 -70 -32.5t-30 -90.5h-137zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248z" />
<glyph unicode="&#xf6;" horiz-adv-x="1320" d="M78 547q0 245 164 402t417 157q255 0 419.5 -156.5t164.5 -402.5t-164.5 -403.5t-419.5 -157.5q-253 0 -417 158t-164 403zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248zM336 1389q0 55 35 89.5t88 34.5 t88 -34.5t35 -89.5q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5zM733 1389q0 55 35 89.5t88 34.5t88 -34.5t35 -89.5q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1208" d="M133 614v205h944v-205h-944zM463 303q0 64 41 105t100 41q62 0 103 -41t41 -105t-41 -105.5t-103 -41.5q-59 0 -100 41.5t-41 105.5zM463 1133q0 63 40.5 104t100.5 41q62 0 103 -40.5t41 -104.5t-41 -106t-103 -42q-59 0 -100 42t-41 106z" />
<glyph unicode="&#xf8;" horiz-adv-x="1320" d="M78 547q0 245 164 402t417 157q144 0 261 -51l116 176h144l-156 -236q104 -76 161.5 -191.5t57.5 -256.5q0 -246 -164.5 -403.5t-419.5 -157.5q-142 0 -260 53l-120 -184h-144l160 243q-103 76 -160 192t-57 257zM336 547q0 -151 90 -248l369 563q-61 27 -136 27 q-140 0 -231.5 -94t-91.5 -248zM524 231q59 -26 135 -26q142 0 234 94t92 248q0 153 -92 248z" />
<glyph unicode="&#xf9;" horiz-adv-x="1394" d="M160 469v625h256v-590q0 -147 66 -220t190 -73q137 0 218 85t81 245v553h256v-1094h-244v139q-61 -74 -153 -113.5t-199 -39.5q-218 0 -344.5 121.5t-126.5 361.5zM287 1524h301l276 -281h-221z" />
<glyph unicode="&#xfa;" horiz-adv-x="1394" d="M160 469v625h256v-590q0 -147 66 -220t190 -73q137 0 218 85t81 245v553h256v-1094h-244v139q-61 -74 -153 -113.5t-199 -39.5q-218 0 -344.5 121.5t-126.5 361.5zM520 1243l277 281h301l-357 -281h-221z" />
<glyph unicode="&#xfb;" horiz-adv-x="1394" d="M160 469v625h256v-590q0 -147 66 -220t190 -73q137 0 218 85t81 245v553h256v-1094h-244v139q-61 -74 -153 -113.5t-199 -39.5q-218 0 -344.5 121.5t-126.5 361.5zM301 1243l274 281h234l274 -281h-211l-180 152l-180 -152h-211z" />
<glyph unicode="&#xfc;" horiz-adv-x="1394" d="M160 469v625h256v-590q0 -147 66 -220t190 -73q137 0 218 85t81 245v553h256v-1094h-244v139q-61 -74 -153 -113.5t-199 -39.5q-218 0 -344.5 121.5t-126.5 361.5zM371 1389q0 55 35 89.5t88 34.5t87.5 -34.5t34.5 -89.5q0 -54 -34.5 -88.5t-87.5 -34.5t-88 34.5 t-35 88.5zM768 1389q0 55 35 89.5t88 34.5t88 -34.5t35 -89.5q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1394" d="M150 -256l114 193q70 -60 171 -95t210 -35q167 0 246.5 81.5t79.5 248.5v51q-61 -67 -149.5 -103t-190.5 -36q-219 0 -345 121t-126 362v562h256v-529q0 -145 66 -218t190 -73q138 0 218.5 84t80.5 244v492h256v-928q0 -578 -568 -578q-149 0 -284.5 40.5t-224.5 115.5z M520 1243l277 281h301l-357 -281h-221z" />
<glyph unicode="&#xfe;" d="M168 -397v1917h256v-555q129 141 350 141q159 0 285 -68.5t198 -196.5t72 -294q0 -252 -156.5 -406.5t-398.5 -154.5q-221 0 -350 147v-530h-256zM420 547q0 -154 92 -248t233 -94t232.5 94t91.5 248t-91.5 248t-232.5 94t-233 -94t-92 -248z" />
<glyph unicode="&#xff;" horiz-adv-x="1394" d="M150 -256l114 193q70 -60 171 -95t210 -35q167 0 246.5 81.5t79.5 248.5v51q-61 -67 -149.5 -103t-190.5 -36q-219 0 -345 121t-126 362v562h256v-529q0 -145 66 -218t190 -73q138 0 218.5 84t80.5 244v492h256v-928q0 -578 -568 -578q-149 0 -284.5 40.5t-224.5 115.5z M371 1389q0 55 35 89.5t88 34.5t87.5 -34.5t34.5 -89.5q0 -54 -34.5 -88.5t-87.5 -34.5t-88 34.5t-35 88.5zM768 1389q0 55 35 89.5t88 34.5t88 -34.5t35 -89.5q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2316" d="M90 715q0 213 98 377t276 253t408 89h1309v-224h-780v-372h694v-220h-694v-395h809v-223h-1338q-349 0 -565.5 197.5t-216.5 517.5zM358 715q0 -222 144 -356t383 -134h252v983h-252q-239 0 -383 -134.5t-144 -358.5z" />
<glyph unicode="&#x153;" horiz-adv-x="2211" d="M78 547q0 245 164 402t417 157q145 0 261.5 -55.5t189.5 -155.5q75 100 192.5 155.5t262.5 55.5q158 0 285.5 -64.5t205 -191.5t78.5 -295l-864 -162q40 -90 130 -140t214 -50q183 0 305 119l141 -158q-155 -178 -454 -178q-163 0 -289.5 56.5t-204.5 158.5 q-73 -102 -189.5 -158.5t-263.5 -56.5q-253 0 -417 158t-164 403zM336 547q0 -154 91.5 -248t231.5 -94q142 0 234 94t92 248t-92 248t-234 94q-140 0 -231.5 -94t-91.5 -248zM1241 559l635 117q-28 103 -110.5 164t-198.5 61q-145 0 -235.5 -90.5t-90.5 -235.5v-16z" />
<glyph unicode="&#x178;" horiz-adv-x="1609" d="M170 952v482h266v-469q0 -188 91.5 -285.5t254.5 -97.5q175 0 278 103t103 306v443h267v-996q0 -317 -174 -487t-478 -170q-181 0 -328 52.5t-249 148.5l108 204q190 -172 465 -172q389 0 389 412v109q-146 -185 -422 -185q-272 0 -421.5 155.5t-149.5 446.5zM479 1696 q0 55 35 90t88 35t88 -35t35 -90q0 -54 -35 -88.5t-88 -34.5t-88 34.5t-35 88.5zM877 1696q0 55 34.5 90t87.5 35t88 -35t35 -90q0 -54 -35 -88.5t-88 -34.5t-87.5 34.5t-34.5 88.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1228" d="M223 1243l275 281h233l275 -281h-211l-181 152l-180 -152h-211z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1228" d="M246 1255q3 126 63 200.5t158 74.5q46 0 90 -21t72 -46.5t60 -46.5t56 -21q43 0 70.5 30.5t30.5 85.5h137q-3 -121 -63 -194.5t-158 -73.5q-46 0 -90 21t-72.5 46.5t-60.5 46.5t-56 21q-43 0 -70 -32.5t-30 -90.5h-137z" />
<glyph unicode="&#x2000;" horiz-adv-x="1012" />
<glyph unicode="&#x2001;" horiz-adv-x="2025" />
<glyph unicode="&#x2002;" horiz-adv-x="1012" />
<glyph unicode="&#x2003;" horiz-adv-x="2025" />
<glyph unicode="&#x2004;" horiz-adv-x="675" />
<glyph unicode="&#x2005;" horiz-adv-x="506" />
<glyph unicode="&#x2006;" horiz-adv-x="337" />
<glyph unicode="&#x2007;" horiz-adv-x="337" />
<glyph unicode="&#x2008;" horiz-adv-x="253" />
<glyph unicode="&#x2009;" horiz-adv-x="405" />
<glyph unicode="&#x200a;" horiz-adv-x="112" />
<glyph unicode="&#x2010;" horiz-adv-x="788" d="M113 463v213h563v-213h-563z" />
<glyph unicode="&#x2011;" horiz-adv-x="788" d="M113 463v213h563v-213h-563z" />
<glyph unicode="&#x2012;" horiz-adv-x="788" d="M113 463v213h563v-213h-563z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 485v168h1024v-168h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 485v168h2048v-168h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="499" d="M82 1053q0 62 45 176l109 291h163l-82 -314q97 -41 97 -153q0 -74 -47 -121t-117 -47q-74 0 -121 47.5t-47 120.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="499" d="M86 1366q0 74 47.5 121t118.5 47q72 0 119 -47.5t47 -120.5q0 -62 -45 -176l-107 -291h-166l82 315q-44 18 -70 57.5t-26 94.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="499" d="M86 156q0 76 47 123t119 47t119 -47.5t47 -122.5q0 -57 -47 -176l-105 -289h-166l82 313q-44 19 -70 58.5t-26 93.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="933" d="M82 1053q0 62 45 176l109 291h163l-82 -314q97 -41 97 -153q0 -74 -47 -121t-117 -47q-74 0 -121 47.5t-47 120.5zM516 1053q0 62 45 176l109 291h164l-82 -314q96 -41 96 -153q0 -74 -47 -121t-117 -47q-74 0 -121 47.5t-47 120.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="933" d="M86 1366q0 74 47.5 121t118.5 47q72 0 119 -47.5t47 -120.5q0 -62 -45 -176l-107 -291h-166l82 315q-44 18 -70 57.5t-26 94.5zM520 1366q0 74 47.5 121t118.5 47q72 0 119 -47.5t47 -120.5q0 -62 -45 -176l-107 -291h-165l81 315q-44 18 -70 57.5t-26 94.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="933" d="M86 156q0 76 47 123t119 47t119 -47.5t47 -122.5q0 -57 -47 -176l-105 -289h-166l82 313q-44 19 -70 58.5t-26 93.5zM520 156q0 76 47 123t119 47t119 -47.5t47 -122.5q0 -57 -47 -176l-105 -289h-165l81 313q-44 19 -70 58.5t-26 93.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="686" d="M123 569q0 95 64 158.5t155 63.5t156 -63.5t65 -158.5q0 -96 -65 -159.5t-156 -63.5t-155 63t-64 160z" />
<glyph unicode="&#x2026;" horiz-adv-x="1521" d="M82 156q0 75 48.5 122.5t119.5 47.5t119.5 -47.5t48.5 -122.5q0 -73 -49 -121.5t-119 -48.5t-119 48.5t-49 121.5zM592 156q0 75 48.5 122.5t119.5 47.5t119.5 -47.5t48.5 -122.5q0 -73 -49 -121.5t-119 -48.5t-119 48.5t-49 121.5zM1104 156q0 75 48.5 122.5t119.5 47.5 t119.5 -47.5t48.5 -122.5q0 -73 -49 -121.5t-119 -48.5t-119 48.5t-49 121.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="405" />
<glyph unicode="&#x2039;" horiz-adv-x="669" d="M86 547l291 397h231l-284 -397l284 -395h-231z" />
<glyph unicode="&#x203a;" horiz-adv-x="669" d="M61 152l285 395l-285 397h232l291 -397l-291 -395h-232z" />
<glyph unicode="&#x205f;" horiz-adv-x="506" />
<glyph unicode="&#x20ac;" horiz-adv-x="1665" d="M61 504v143h207q-2 23 -2 70q0 46 2 69h-207v144h234q66 236 266.5 380t470.5 144q177 0 323 -61.5t244 -178.5l-169 -161q-155 168 -383 168q-163 0 -288 -78t-184 -213h588v-144h-626q-5 -58 -5 -69q0 -13 5 -70h626v-143h-588q59 -135 184 -213t288 -78q229 0 383 170 l169 -164q-98 -117 -243.5 -178t-323.5 -61q-270 0 -470.5 144t-266.5 380h-234z" />
<glyph unicode="&#x2122;" horiz-adv-x="2123" d="M8 1284v150h787v-150h-299v-700h-189v700h-299zM911 584v850h152l360 -545l355 545h151l2 -850h-178l-2 536l-287 -442h-86l-288 432v-526h-179z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1095" d="M0 0v1096h1096v-1096h-1096z" />
<hkern u1="&#x2a;" u2="&#xee;" k="-164" />
<hkern u1="&#x2f;" u2="&#xf0;" k="92" />
<hkern u1="&#x2f;" u2="i" k="-20" />
<hkern u1="F" u2="&#xef;" k="-20" />
<hkern u1="F" u2="&#xee;" k="-61" />
<hkern u1="T" u2="&#xef;" k="-61" />
<hkern u1="T" u2="&#xee;" k="-82" />
<hkern u1="V" u2="&#xef;" k="-51" />
<hkern u1="V" u2="&#xee;" k="-82" />
<hkern u1="V" u2="&#xec;" k="-41" />
<hkern u1="V" u2="i" k="20" />
<hkern u1="f" u2="&#xef;" k="-123" />
<hkern u1="f" u2="&#xee;" k="-102" />
<hkern u1="f" u2="&#xec;" k="-133" />
<hkern u1="i" u2="\" k="59" />
<hkern u1="j" u2="\" k="59" />
<hkern u1="q" u2="j" k="-102" />
<hkern u1="&#xa3;" u2="&#xef;" k="-20" />
<hkern u1="&#xa3;" u2="&#xee;" k="-61" />
<hkern u1="&#xaa;" u2="&#xee;" k="-164" />
<hkern u1="&#xba;" u2="&#xee;" k="-164" />
<hkern u1="&#xee;" u2="&#xba;" k="-164" />
<hkern u1="&#xee;" u2="&#xaa;" k="-164" />
<hkern u1="&#xee;" u2="&#x3f;" k="-82" />
<hkern u1="&#xee;" u2="&#x2a;" k="-164" />
<hkern u1="&#x2018;" u2="&#xec;" k="-61" />
<hkern u1="&#x201c;" u2="&#xec;" k="-61" />
<hkern g1="ampersand" 	g2="ampersand" 	k="10" />
<hkern g1="ampersand" 	g2="backslash" 	k="164" />
<hkern g1="ampersand" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="ampersand" 	g2="colon,semicolon" 	k="14" />
<hkern g1="ampersand" 	g2="degree" 	k="92" />
<hkern g1="ampersand" 	g2="exclam" 	k="25" />
<hkern g1="ampersand" 	g2="exclamdown" 	k="20" />
<hkern g1="ampersand" 	g2="four" 	k="-10" />
<hkern g1="ampersand" 	g2="one" 	k="61" />
<hkern g1="ampersand" 	g2="paragraph" 	k="102" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="question" 	k="143" />
<hkern g1="ampersand" 	g2="questiondown" 	k="10" />
<hkern g1="ampersand" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="92" />
<hkern g1="ampersand" 	g2="seven" 	k="31" />
<hkern g1="ampersand" 	g2="slash" 	k="-72" />
<hkern g1="ampersand" 	g2="three" 	k="10" />
<hkern g1="ampersand" 	g2="trademark" 	k="72" />
<hkern g1="ampersand" 	g2="two" 	k="20" />
<hkern g1="ampersand" 	g2="underscore" 	k="-61" />
<hkern g1="currency" 	g2="questiondown" 	k="10" />
<hkern g1="currency" 	g2="seven" 	k="20" />
<hkern g1="currency" 	g2="two" 	k="16" />
<hkern g1="currency" 	g2="underscore" 	k="10" />
<hkern g1="currency" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="currency" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="degree" 	g2="backslash" 	k="-113" />
<hkern g1="degree" 	g2="four" 	k="82" />
<hkern g1="degree" 	g2="one" 	k="-92" />
<hkern g1="degree" 	g2="percent" 	k="-51" />
<hkern g1="degree" 	g2="question" 	k="-41" />
<hkern g1="degree" 	g2="questiondown" 	k="123" />
<hkern g1="degree" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="degree" 	g2="seven" 	k="-82" />
<hkern g1="degree" 	g2="slash" 	k="113" />
<hkern g1="degree" 	g2="three" 	k="-35" />
<hkern g1="degree" 	g2="two" 	k="-61" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="133" />
<hkern g1="degree" 	g2="five" 	k="-10" />
<hkern g1="degree" 	g2="nine" 	k="-72" />
<hkern g1="degree" 	g2="zero,six" 	k="-6" />
<hkern g1="percent" 	g2="backslash" 	k="72" />
<hkern g1="percent" 	g2="degree" 	k="51" />
<hkern g1="percent" 	g2="four" 	k="-68" />
<hkern g1="percent" 	g2="one" 	k="41" />
<hkern g1="percent" 	g2="percent" 	k="252" />
<hkern g1="percent" 	g2="question" 	k="113" />
<hkern g1="percent" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="percent" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="percent" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="percent" 	g2="seven" 	k="31" />
<hkern g1="percent" 	g2="slash" 	k="-2" />
<hkern g1="percent" 	g2="three" 	k="-20" />
<hkern g1="percent" 	g2="two" 	k="-20" />
<hkern g1="percent" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="percent" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="percent" 	g2="five" 	k="-41" />
<hkern g1="percent" 	g2="parenright" 	k="41" />
<hkern g1="percent" 	g2="eight" 	k="-41" />
<hkern g1="section" 	g2="four" 	k="-31" />
<hkern g1="section" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="section" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="section" 	g2="seven" 	k="-10" />
<hkern g1="section" 	g2="slash" 	k="-41" />
<hkern g1="section" 	g2="underscore" 	k="-10" />
<hkern g1="section" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="section" 	g2="eight" 	k="-20" />
<hkern g1="trademark" 	g2="backslash" 	k="-82" />
<hkern g1="trademark" 	g2="exclamdown" 	k="-10" />
<hkern g1="trademark" 	g2="questiondown" 	k="20" />
<hkern g1="trademark" 	g2="seven" 	k="-31" />
<hkern g1="trademark" 	g2="slash" 	k="82" />
<hkern g1="trademark" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="trademark" 	g2="nine" 	k="-61" />
<hkern g1="yen" 	g2="backslash" 	k="-102" />
<hkern g1="yen" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="yen" 	g2="colon,semicolon" 	k="82" />
<hkern g1="yen" 	g2="exclam" 	k="-31" />
<hkern g1="yen" 	g2="exclamdown" 	k="41" />
<hkern g1="yen" 	g2="four" 	k="20" />
<hkern g1="yen" 	g2="one" 	k="-61" />
<hkern g1="yen" 	g2="questiondown" 	k="82" />
<hkern g1="yen" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="yen" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="yen" 	g2="seven" 	k="-51" />
<hkern g1="yen" 	g2="slash" 	k="61" />
<hkern g1="yen" 	g2="three" 	k="-20" />
<hkern g1="yen" 	g2="underscore" 	k="20" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="yen" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="51" />
<hkern g1="yen" 	g2="zero,six" 	k="25" />
<hkern g1="yen" 	g2="parenright" 	k="-41" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="backslash" 	g2="ampersand" 	k="-31" />
<hkern g1="backslash" 	g2="backslash" 	k="143" />
<hkern g1="backslash" 	g2="bracketright,braceright" 	k="-82" />
<hkern g1="backslash" 	g2="colon,semicolon" 	k="-102" />
<hkern g1="backslash" 	g2="degree" 	k="123" />
<hkern g1="backslash" 	g2="exclamdown" 	k="-82" />
<hkern g1="backslash" 	g2="five" 	k="-10" />
<hkern g1="backslash" 	g2="four" 	k="10" />
<hkern g1="backslash" 	g2="guillemotright,guilsinglright" 	k="-14" />
<hkern g1="backslash" 	g2="one" 	k="41" />
<hkern g1="backslash" 	g2="paragraph" 	k="61" />
<hkern g1="backslash" 	g2="parenright" 	k="-61" />
<hkern g1="backslash" 	g2="percent" 	k="20" />
<hkern g1="backslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-195" />
<hkern g1="backslash" 	g2="question" 	k="123" />
<hkern g1="backslash" 	g2="questiondown" 	k="-61" />
<hkern g1="backslash" 	g2="quoteleft,quotedblleft" 	k="102" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="102" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="backslash" 	g2="section" 	k="-61" />
<hkern g1="backslash" 	g2="seven" 	k="102" />
<hkern g1="backslash" 	g2="slash" 	k="-20" />
<hkern g1="backslash" 	g2="three" 	k="-20" />
<hkern g1="backslash" 	g2="trademark" 	k="123" />
<hkern g1="backslash" 	g2="two" 	k="-20" />
<hkern g1="backslash" 	g2="underscore" 	k="-287" />
<hkern g1="backslash" 	g2="zero,six" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="backslash" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="exclamdown" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="31" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="paragraph" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="parenright" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="question" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="seven" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="slash" 	k="-82" />
<hkern g1="bracketleft,braceleft" 	g2="three" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="trademark" 	k="-102" />
<hkern g1="bracketleft,braceleft" 	g2="two" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="underscore" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="exclam" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="12" />
<hkern g1="bracketleft,braceleft" 	g2="yen" 	k="-41" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="41" />
<hkern g1="colon,semicolon" 	g2="question" 	k="20" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="-10" />
<hkern g1="colon,semicolon" 	g2="slash" 	k="-102" />
<hkern g1="colon,semicolon" 	g2="underscore" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="yen" 	k="82" />
<hkern g1="exclam" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="exclam" 	g2="one" 	k="-20" />
<hkern g1="exclam" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-10" />
<hkern g1="exclam" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="exclam" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="exclam" 	g2="seven" 	k="-10" />
<hkern g1="exclam" 	g2="trademark" 	k="-20" />
<hkern g1="exclam" 	g2="yen" 	k="-31" />
<hkern g1="exclamdown" 	g2="backslash" 	k="113" />
<hkern g1="exclamdown" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="exclamdown" 	g2="one" 	k="41" />
<hkern g1="exclamdown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-10" />
<hkern g1="exclamdown" 	g2="question" 	k="10" />
<hkern g1="exclamdown" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="exclamdown" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="exclamdown" 	g2="slash" 	k="-82" />
<hkern g1="exclamdown" 	g2="trademark" 	k="10" />
<hkern g1="exclamdown" 	g2="underscore" 	k="-31" />
<hkern g1="exclamdown" 	g2="yen" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="113" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="five" 	k="-10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="-10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="one" 	k="10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="question" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="questiondown" 	k="-10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="slash" 	k="-14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="underscore" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="backslash" 	k="133" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="four" 	k="-10" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="one" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="paragraph" 	k="174" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="percent" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="question" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="section" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="seven" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="three" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="two" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="zero,six" 	k="-10" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="yen" 	k="51" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="currency,Euro" 	k="-10" />
<hkern g1="parenleft" 	g2="backslash" 	k="-82" />
<hkern g1="parenleft" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="parenleft" 	g2="four" 	k="41" />
<hkern g1="parenleft" 	g2="one" 	k="-20" />
<hkern g1="parenleft" 	g2="slash" 	k="-72" />
<hkern g1="parenleft" 	g2="trademark" 	k="-61" />
<hkern g1="parenleft" 	g2="underscore" 	k="-10" />
<hkern g1="parenleft" 	g2="yen" 	k="-41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="195" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="degree" 	k="133" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclamdown" 	k="-10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="guillemotright,guilsinglright" 	k="-10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="paragraph" 	k="279" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="133" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="113" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="questiondown" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="113" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="slash" 	k="-195" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="two" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="underscore" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero,six" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclam" 	k="-10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="currency,Euro" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="-20" />
<hkern g1="question" 	g2="ampersand" 	k="31" />
<hkern g1="question" 	g2="degree" 	k="-20" />
<hkern g1="question" 	g2="exclamdown" 	k="10" />
<hkern g1="question" 	g2="four" 	k="102" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="question" 	g2="question" 	k="45" />
<hkern g1="question" 	g2="questiondown" 	k="215" />
<hkern g1="question" 	g2="quoteleft,quotedblleft" 	k="-51" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="question" 	g2="slash" 	k="61" />
<hkern g1="question" 	g2="three" 	k="31" />
<hkern g1="question" 	g2="underscore" 	k="35" />
<hkern g1="question" 	g2="exclam" 	k="10" />
<hkern g1="question" 	g2="yen" 	k="31" />
<hkern g1="question" 	g2="eight" 	k="20" />
<hkern g1="question" 	g2="bracketleft" 	k="25" />
<hkern g1="question" 	g2="nine" 	k="-20" />
<hkern g1="questiondown" 	g2="ampersand" 	k="31" />
<hkern g1="questiondown" 	g2="backslash" 	k="184" />
<hkern g1="questiondown" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="questiondown" 	g2="colon,semicolon" 	k="10" />
<hkern g1="questiondown" 	g2="degree" 	k="123" />
<hkern g1="questiondown" 	g2="four" 	k="92" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="questiondown" 	g2="one" 	k="92" />
<hkern g1="questiondown" 	g2="paragraph" 	k="184" />
<hkern g1="questiondown" 	g2="percent" 	k="123" />
<hkern g1="questiondown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-10" />
<hkern g1="questiondown" 	g2="question" 	k="276" />
<hkern g1="questiondown" 	g2="questiondown" 	k="41" />
<hkern g1="questiondown" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="questiondown" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="questiondown" 	g2="section" 	k="-10" />
<hkern g1="questiondown" 	g2="seven" 	k="92" />
<hkern g1="questiondown" 	g2="slash" 	k="-82" />
<hkern g1="questiondown" 	g2="trademark" 	k="82" />
<hkern g1="questiondown" 	g2="underscore" 	k="-102" />
<hkern g1="questiondown" 	g2="zero,six" 	k="102" />
<hkern g1="questiondown" 	g2="exclam" 	k="10" />
<hkern g1="questiondown" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="82" />
<hkern g1="questiondown" 	g2="yen" 	k="102" />
<hkern g1="questiondown" 	g2="sterling" 	k="-20" />
<hkern g1="questiondown" 	g2="currency,Euro" 	k="31" />
<hkern g1="questiondown" 	g2="eight" 	k="92" />
<hkern g1="quoteleft,quotedblleft" 	g2="ampersand" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="backslash" 	k="-102" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="-10" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="72" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-72" />
<hkern g1="quoteleft,quotedblleft" 	g2="paragraph" 	k="-72" />
<hkern g1="quoteleft,quotedblleft" 	g2="percent" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="question" 	k="-72" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="section" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="slash" 	k="102" />
<hkern g1="quoteleft,quotedblleft" 	g2="three" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="trademark" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="two" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclam" 	k="-10" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="nine" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="backslash" 	k="-102" />
<hkern g1="quoteright,quotedblright" 	g2="exclamdown" 	k="-10" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="117" />
<hkern g1="quoteright,quotedblright" 	g2="one" 	k="-92" />
<hkern g1="quoteright,quotedblright" 	g2="paragraph" 	k="-72" />
<hkern g1="quoteright,quotedblright" 	g2="percent" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="question" 	k="-72" />
<hkern g1="quoteright,quotedblright" 	g2="questiondown" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="section" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-51" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="102" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="-31" />
<hkern g1="quoteright,quotedblright" 	g2="trademark" 	k="-72" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="exclam" 	k="-10" />
<hkern g1="quoteright,quotedblright" 	g2="yen" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="backslash" 	k="-123" />
<hkern g1="quotedbl,quotesingle" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="degree" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="five" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-92" />
<hkern g1="quotedbl,quotesingle" 	g2="paragraph" 	k="-82" />
<hkern g1="quotedbl,quotesingle" 	g2="percent" 	k="-51" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="quotedbl,quotesingle" 	g2="question" 	k="-61" />
<hkern g1="quotedbl,quotesingle" 	g2="questiondown" 	k="76" />
<hkern g1="quotedbl,quotesingle" 	g2="quotedbl,quotesingle" 	k="-92" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-92" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-31" />
<hkern g1="quotedbl,quotesingle" 	g2="trademark" 	k="-72" />
<hkern g1="quotedbl,quotesingle" 	g2="two" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="underscore" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="zero,six" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="exclam" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="yen" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="currency,Euro" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="nine" 	k="-82" />
<hkern g1="slash" 	g2="ampersand" 	k="72" />
<hkern g1="slash" 	g2="backslash" 	k="-41" />
<hkern g1="slash" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="31" />
<hkern g1="slash" 	g2="degree" 	k="-92" />
<hkern g1="slash" 	g2="exclamdown" 	k="133" />
<hkern g1="slash" 	g2="four" 	k="133" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="92" />
<hkern g1="slash" 	g2="one" 	k="-61" />
<hkern g1="slash" 	g2="parenright" 	k="-72" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="195" />
<hkern g1="slash" 	g2="questiondown" 	k="154" />
<hkern g1="slash" 	g2="quoteleft,quotedblleft" 	k="-92" />
<hkern g1="slash" 	g2="quoteright,quotedblright" 	k="-102" />
<hkern g1="slash" 	g2="quotedbl,quotesingle" 	k="-123" />
<hkern g1="slash" 	g2="section" 	k="41" />
<hkern g1="slash" 	g2="seven" 	k="-20" />
<hkern g1="slash" 	g2="slash" 	k="143" />
<hkern g1="slash" 	g2="trademark" 	k="-92" />
<hkern g1="slash" 	g2="underscore" 	k="246" />
<hkern g1="slash" 	g2="zero,six" 	k="41" />
<hkern g1="slash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="143" />
<hkern g1="slash" 	g2="yen" 	k="-82" />
<hkern g1="slash" 	g2="sterling" 	k="20" />
<hkern g1="slash" 	g2="eight" 	k="51" />
<hkern g1="underscore" 	g2="backslash" 	k="266" />
<hkern g1="underscore" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="underscore" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="underscore" 	g2="exclamdown" 	k="-31" />
<hkern g1="underscore" 	g2="five" 	k="-20" />
<hkern g1="underscore" 	g2="four" 	k="51" />
<hkern g1="underscore" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="underscore" 	g2="one" 	k="16" />
<hkern g1="underscore" 	g2="paragraph" 	k="127" />
<hkern g1="underscore" 	g2="parenright" 	k="-10" />
<hkern g1="underscore" 	g2="percent" 	k="51" />
<hkern g1="underscore" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="underscore" 	g2="question" 	k="31" />
<hkern g1="underscore" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="underscore" 	g2="section" 	k="-51" />
<hkern g1="underscore" 	g2="slash" 	k="-256" />
<hkern g1="underscore" 	g2="three" 	k="-51" />
<hkern g1="underscore" 	g2="trademark" 	k="102" />
<hkern g1="underscore" 	g2="two" 	k="-61" />
<hkern g1="underscore" 	g2="zero,six" 	k="41" />
<hkern g1="underscore" 	g2="yen" 	k="20" />
<hkern g1="underscore" 	g2="sterling" 	k="-31" />
<hkern g1="underscore" 	g2="currency,Euro" 	k="10" />
<hkern g1="underscore" 	g2="nine" 	k="-20" />
<hkern g1="eight" 	g2="AE" 	k="10" />
<hkern g1="eight" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="6" />
<hkern g1="eight" 	g2="J" 	k="76" />
<hkern g1="eight" 	g2="T" 	k="10" />
<hkern g1="eight" 	g2="V" 	k="35" />
<hkern g1="eight" 	g2="X" 	k="45" />
<hkern g1="eight" 	g2="Z" 	k="6" />
<hkern g1="eight" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-10" />
<hkern g1="eight" 	g2="backslash" 	k="61" />
<hkern g1="eight" 	g2="j" 	k="16" />
<hkern g1="eight" 	g2="percent" 	k="20" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="eight" 	g2="question" 	k="20" />
<hkern g1="eight" 	g2="questiondown" 	k="41" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="-6" />
<hkern g1="eight" 	g2="section" 	k="-20" />
<hkern g1="eight" 	g2="v" 	k="-4" />
<hkern g1="eight" 	g2="x" 	k="16" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="five" 	g2="J" 	k="92" />
<hkern g1="five" 	g2="V" 	k="14" />
<hkern g1="five" 	g2="X" 	k="25" />
<hkern g1="five" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="five" 	g2="backslash" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="20" />
<hkern g1="five" 	g2="v" 	k="20" />
<hkern g1="five" 	g2="x" 	k="20" />
<hkern g1="five" 	g2="degree" 	k="20" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="seven" 	k="27" />
<hkern g1="five" 	g2="three" 	k="10" />
<hkern g1="five" 	g2="two" 	k="10" />
<hkern g1="four" 	g2="J" 	k="10" />
<hkern g1="four" 	g2="T" 	k="106" />
<hkern g1="four" 	g2="V" 	k="113" />
<hkern g1="four" 	g2="X" 	k="20" />
<hkern g1="four" 	g2="Z" 	k="10" />
<hkern g1="four" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="percent" 	k="61" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="four" 	g2="question" 	k="113" />
<hkern g1="four" 	g2="questiondown" 	k="-20" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="four" 	g2="section" 	k="-47" />
<hkern g1="four" 	g2="v" 	k="41" />
<hkern g1="four" 	g2="x" 	k="31" />
<hkern g1="four" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="degree" 	k="61" />
<hkern g1="four" 	g2="five" 	k="6" />
<hkern g1="four" 	g2="seven" 	k="92" />
<hkern g1="four" 	g2="three" 	k="20" />
<hkern g1="four" 	g2="two" 	k="6" />
<hkern g1="four" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="four" 	g2="dollar,S" 	k="10" />
<hkern g1="four" 	g2="ae" 	k="-57" />
<hkern g1="four" 	g2="slash" 	k="-51" />
<hkern g1="four" 	g2="underscore" 	k="-41" />
<hkern g1="four" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="-41" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="four" 	g2="ampersand" 	k="-41" />
<hkern g1="four" 	g2="colon,semicolon" 	k="-14" />
<hkern g1="four" 	g2="currency,Euro" 	k="-20" />
<hkern g1="four" 	g2="eight" 	k="-20" />
<hkern g1="four" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="four" 	g2="sterling" 	k="-20" />
<hkern g1="four" 	g2="nine" 	k="10" />
<hkern g1="four" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-31" />
<hkern g1="four" 	g2="one" 	k="51" />
<hkern g1="four" 	g2="paragraph" 	k="41" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="four" 	g2="uniFB02" 	k="-4" />
<hkern g1="four" 	g2="trademark" 	k="41" />
<hkern g1="seven" 	g2="AE" 	k="127" />
<hkern g1="seven" 	g2="J" 	k="31" />
<hkern g1="seven" 	g2="T" 	k="-20" />
<hkern g1="seven" 	g2="V" 	k="-10" />
<hkern g1="seven" 	g2="X" 	k="6" />
<hkern g1="seven" 	g2="Z" 	k="10" />
<hkern g1="seven" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-51" />
<hkern g1="seven" 	g2="backslash" 	k="-20" />
<hkern g1="seven" 	g2="j" 	k="43" />
<hkern g1="seven" 	g2="percent" 	k="-10" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="seven" 	g2="question" 	k="-31" />
<hkern g1="seven" 	g2="questiondown" 	k="123" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-61" />
<hkern g1="seven" 	g2="section" 	k="20" />
<hkern g1="seven" 	g2="x" 	k="31" />
<hkern g1="seven" 	g2="yen" 	k="-31" />
<hkern g1="seven" 	g2="degree" 	k="-16" />
<hkern g1="seven" 	g2="five" 	k="37" />
<hkern g1="seven" 	g2="three" 	k="16" />
<hkern g1="seven" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="seven" 	g2="dollar,S" 	k="10" />
<hkern g1="seven" 	g2="ae" 	k="78" />
<hkern g1="seven" 	g2="slash" 	k="82" />
<hkern g1="seven" 	g2="underscore" 	k="61" />
<hkern g1="seven" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="6" />
<hkern g1="seven" 	g2="ampersand" 	k="61" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="41" />
<hkern g1="seven" 	g2="currency,Euro" 	k="41" />
<hkern g1="seven" 	g2="eight" 	k="37" />
<hkern g1="seven" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="102" />
<hkern g1="seven" 	g2="sterling" 	k="27" />
<hkern g1="seven" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="seven" 	g2="one" 	k="-20" />
<hkern g1="seven" 	g2="paragraph" 	k="-51" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="seven" 	g2="uniFB02" 	k="20" />
<hkern g1="seven" 	g2="trademark" 	k="-82" />
<hkern g1="seven" 	g2="exclamdown" 	k="51" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="seven" 	g2="s" 	k="61" />
<hkern g1="seven" 	g2="z" 	k="51" />
<hkern g1="seven" 	g2="zero,six" 	k="41" />
<hkern g1="seven" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="seven" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="seven" 	g2="four" 	k="133" />
<hkern g1="seven" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="45" />
<hkern g1="seven" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="51" />
<hkern g1="six" 	g2="AE" 	k="-10" />
<hkern g1="six" 	g2="J" 	k="51" />
<hkern g1="six" 	g2="T" 	k="20" />
<hkern g1="six" 	g2="V" 	k="20" />
<hkern g1="six" 	g2="X" 	k="10" />
<hkern g1="six" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="six" 	g2="j" 	k="16" />
<hkern g1="six" 	g2="percent" 	k="41" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="six" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="six" 	g2="section" 	k="-41" />
<hkern g1="six" 	g2="v" 	k="27" />
<hkern g1="six" 	g2="x" 	k="31" />
<hkern g1="six" 	g2="degree" 	k="35" />
<hkern g1="six" 	g2="seven" 	k="10" />
<hkern g1="six" 	g2="slash" 	k="-20" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="six" 	g2="ampersand" 	k="-20" />
<hkern g1="six" 	g2="currency,Euro" 	k="-20" />
<hkern g1="six" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="six" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-16" />
<hkern g1="three" 	g2="AE" 	k="-6" />
<hkern g1="three" 	g2="J" 	k="55" />
<hkern g1="three" 	g2="V" 	k="20" />
<hkern g1="three" 	g2="X" 	k="20" />
<hkern g1="three" 	g2="j" 	k="12" />
<hkern g1="three" 	g2="percent" 	k="20" />
<hkern g1="three" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="three" 	g2="v" 	k="14" />
<hkern g1="three" 	g2="x" 	k="31" />
<hkern g1="three" 	g2="degree" 	k="31" />
<hkern g1="three" 	g2="five" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="20" />
<hkern g1="three" 	g2="three" 	k="10" />
<hkern g1="three" 	g2="two" 	k="10" />
<hkern g1="two" 	g2="AE" 	k="-10" />
<hkern g1="two" 	g2="J" 	k="-10" />
<hkern g1="two" 	g2="V" 	k="25" />
<hkern g1="two" 	g2="backslash" 	k="10" />
<hkern g1="two" 	g2="percent" 	k="-20" />
<hkern g1="two" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="two" 	g2="v" 	k="-6" />
<hkern g1="two" 	g2="x" 	k="10" />
<hkern g1="two" 	g2="seven" 	k="6" />
<hkern g1="two" 	g2="slash" 	k="-20" />
<hkern g1="two" 	g2="underscore" 	k="-61" />
<hkern g1="two" 	g2="ampersand" 	k="10" />
<hkern g1="two" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="10" />
<hkern g1="two" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="two" 	g2="z" 	k="10" />
<hkern g1="two" 	g2="zero,six" 	k="6" />
<hkern g1="two" 	g2="four" 	k="31" />
<hkern g1="two" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="6" />
<hkern g1="zero,nine" 	g2="AE" 	k="20" />
<hkern g1="zero,nine" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="zero,nine" 	g2="J" 	k="102" />
<hkern g1="zero,nine" 	g2="T" 	k="31" />
<hkern g1="zero,nine" 	g2="V" 	k="51" />
<hkern g1="zero,nine" 	g2="X" 	k="72" />
<hkern g1="zero,nine" 	g2="Z" 	k="35" />
<hkern g1="zero,nine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-10" />
<hkern g1="zero,nine" 	g2="backslash" 	k="41" />
<hkern g1="zero,nine" 	g2="j" 	k="16" />
<hkern g1="zero,nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="zero,nine" 	g2="question" 	k="41" />
<hkern g1="zero,nine" 	g2="questiondown" 	k="61" />
<hkern g1="zero,nine" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="zero,nine" 	g2="v" 	k="-10" />
<hkern g1="zero,nine" 	g2="x" 	k="20" />
<hkern g1="zero,nine" 	g2="yen" 	k="25" />
<hkern g1="zero,nine" 	g2="degree" 	k="-6" />
<hkern g1="zero,nine" 	g2="seven" 	k="10" />
<hkern g1="zero,nine" 	g2="three" 	k="20" />
<hkern g1="zero,nine" 	g2="two" 	k="10" />
<hkern g1="zero,nine" 	g2="slash" 	k="41" />
<hkern g1="zero,nine" 	g2="underscore" 	k="41" />
<hkern g1="zero,nine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="zero,nine" 	g2="one" 	k="10" />
<hkern g1="zero,nine" 	g2="uniFB02" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,ordfeminine,ordmasculine" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="12" />
<hkern g1="B" 	g2="J" 	k="61" />
<hkern g1="B" 	g2="T" 	k="10" />
<hkern g1="B" 	g2="V" 	k="10" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-10" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="B" 	g2="trademark" 	k="10" />
<hkern g1="B" 	g2="underscore" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="AE" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="J" 	k="51" />
<hkern g1="C,Ccedilla,Euro" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="V" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="X" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="C,Ccedilla,Euro" 	g2="backslash" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="colon,semicolon" 	k="14" />
<hkern g1="C,Ccedilla,Euro" 	g2="degree" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="eight" 	k="27" />
<hkern g1="C,Ccedilla,Euro" 	g2="exclamdown" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="four" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="nine" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="one" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="paragraph" 	k="-10" />
<hkern g1="C,Ccedilla,Euro" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-27" />
<hkern g1="C,Ccedilla,Euro" 	g2="questiondown" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteright,quotedblright" 	k="-25" />
<hkern g1="C,Ccedilla,Euro" 	g2="s" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="three" 	k="6" />
<hkern g1="C,Ccedilla,Euro" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="underscore" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="x" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="z" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="zero,six" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="dollar,S" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="ae" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="ampersand" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="section" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="two" 	k="10" />
<hkern g1="AE,OE" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="AE,OE" 	g2="V" 	k="20" />
<hkern g1="AE,OE" 	g2="X" 	k="16" />
<hkern g1="AE,OE" 	g2="colon,semicolon" 	k="10" />
<hkern g1="AE,OE" 	g2="eight" 	k="10" />
<hkern g1="AE,OE" 	g2="exclamdown" 	k="10" />
<hkern g1="AE,OE" 	g2="four" 	k="49" />
<hkern g1="AE,OE" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="AE,OE" 	g2="nine" 	k="6" />
<hkern g1="AE,OE" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="AE,OE" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="AE,OE" 	g2="question" 	k="20" />
<hkern g1="AE,OE" 	g2="s" 	k="10" />
<hkern g1="AE,OE" 	g2="seven" 	k="10" />
<hkern g1="AE,OE" 	g2="uniFB02" 	k="20" />
<hkern g1="AE,OE" 	g2="three" 	k="20" />
<hkern g1="AE,OE" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="10" />
<hkern g1="AE,OE" 	g2="underscore" 	k="-20" />
<hkern g1="AE,OE" 	g2="v" 	k="14" />
<hkern g1="AE,OE" 	g2="x" 	k="20" />
<hkern g1="AE,OE" 	g2="z" 	k="14" />
<hkern g1="AE,OE" 	g2="zero,six" 	k="25" />
<hkern g1="AE,OE" 	g2="ae" 	k="10" />
<hkern g1="AE,OE" 	g2="five" 	k="10" />
<hkern g1="AE,OE" 	g2="section" 	k="10" />
<hkern g1="AE,OE" 	g2="two" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="J" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="T" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="X" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="eight" 	k="27" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="four" 	k="61" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="nine" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="question" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="questiondown" 	k="-10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="s" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="slash" 	k="-20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="uniFB02" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="three" 	k="-10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="16" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="underscore" 	k="-82" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="v" 	k="27" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="x" 	k="27" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="zero,six" 	k="37" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="ae" 	k="6" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="parenright" 	k="14" />
<hkern g1="F,sterling" 	g2="AE" 	k="92" />
<hkern g1="F,sterling" 	g2="J" 	k="72" />
<hkern g1="F,sterling" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="F,sterling" 	g2="X" 	k="20" />
<hkern g1="F,sterling" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="F,sterling" 	g2="backslash" 	k="-31" />
<hkern g1="F,sterling" 	g2="colon,semicolon" 	k="61" />
<hkern g1="F,sterling" 	g2="eight" 	k="68" />
<hkern g1="F,sterling" 	g2="exclamdown" 	k="51" />
<hkern g1="F,sterling" 	g2="four" 	k="61" />
<hkern g1="F,sterling" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="F,sterling" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="F,sterling" 	g2="nine" 	k="41" />
<hkern g1="F,sterling" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="F,sterling" 	g2="one" 	k="-31" />
<hkern g1="F,sterling" 	g2="paragraph" 	k="-20" />
<hkern g1="F,sterling" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="F,sterling" 	g2="questiondown" 	k="41" />
<hkern g1="F,sterling" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="F,sterling" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="F,sterling" 	g2="s" 	k="82" />
<hkern g1="F,sterling" 	g2="seven" 	k="-10" />
<hkern g1="F,sterling" 	g2="slash" 	k="-25" />
<hkern g1="F,sterling" 	g2="uniFB02" 	k="41" />
<hkern g1="F,sterling" 	g2="three" 	k="20" />
<hkern g1="F,sterling" 	g2="trademark" 	k="-61" />
<hkern g1="F,sterling" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="51" />
<hkern g1="F,sterling" 	g2="underscore" 	k="-31" />
<hkern g1="F,sterling" 	g2="v" 	k="41" />
<hkern g1="F,sterling" 	g2="x" 	k="51" />
<hkern g1="F,sterling" 	g2="z" 	k="82" />
<hkern g1="F,sterling" 	g2="zero,six" 	k="72" />
<hkern g1="F,sterling" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="F,sterling" 	g2="dollar,S" 	k="20" />
<hkern g1="F,sterling" 	g2="ae" 	k="82" />
<hkern g1="F,sterling" 	g2="ampersand" 	k="72" />
<hkern g1="F,sterling" 	g2="five" 	k="31" />
<hkern g1="F,sterling" 	g2="section" 	k="20" />
<hkern g1="F,sterling" 	g2="two" 	k="20" />
<hkern g1="F,sterling" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="F,sterling" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="F,sterling" 	g2="j" 	k="51" />
<hkern g1="F,sterling" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="41" />
<hkern g1="G" 	g2="J" 	k="20" />
<hkern g1="G" 	g2="backslash" 	k="31" />
<hkern g1="G" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="H,U,paragraph,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="10" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="eight" 	k="6" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="four" 	k="20" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="nine" 	k="6" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="trademark" 	k="-41" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="zero,six" 	k="20" />
<hkern g1="J" 	g2="J" 	k="41" />
<hkern g1="J" 	g2="underscore" 	k="20" />
<hkern g1="K" 	g2="AE" 	k="61" />
<hkern g1="K" 	g2="J" 	k="20" />
<hkern g1="K" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="K" 	g2="T" 	k="41" />
<hkern g1="K" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="K" 	g2="V" 	k="82" />
<hkern g1="K" 	g2="X" 	k="82" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="K" 	g2="degree" 	k="20" />
<hkern g1="K" 	g2="eight" 	k="45" />
<hkern g1="K" 	g2="four" 	k="92" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="K" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="72" />
<hkern g1="K" 	g2="nine" 	k="20" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="K" 	g2="one" 	k="-41" />
<hkern g1="K" 	g2="paragraph" 	k="20" />
<hkern g1="K" 	g2="question" 	k="82" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="K" 	g2="s" 	k="31" />
<hkern g1="K" 	g2="slash" 	k="-10" />
<hkern g1="K" 	g2="uniFB02" 	k="51" />
<hkern g1="K" 	g2="three" 	k="10" />
<hkern g1="K" 	g2="trademark" 	k="-41" />
<hkern g1="K" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="37" />
<hkern g1="K" 	g2="underscore" 	k="-133" />
<hkern g1="K" 	g2="v" 	k="76" />
<hkern g1="K" 	g2="x" 	k="88" />
<hkern g1="K" 	g2="z" 	k="51" />
<hkern g1="K" 	g2="zero,six" 	k="45" />
<hkern g1="K" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="K" 	g2="dollar,S" 	k="31" />
<hkern g1="K" 	g2="ae" 	k="27" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="two" 	k="-4" />
<hkern g1="K" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="K" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="K" 	g2="j" 	k="6" />
<hkern g1="K" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="K" 	g2="bracketleft" 	k="41" />
<hkern g1="L" 	g2="AE" 	k="-10" />
<hkern g1="L" 	g2="J" 	k="-61" />
<hkern g1="L" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="L" 	g2="T" 	k="133" />
<hkern g1="L" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="L" 	g2="V" 	k="102" />
<hkern g1="L" 	g2="X" 	k="20" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="L" 	g2="asterisk,ordfeminine,ordmasculine" 	k="143" />
<hkern g1="L" 	g2="backslash" 	k="143" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="L" 	g2="degree" 	k="133" />
<hkern g1="L" 	g2="eight" 	k="10" />
<hkern g1="L" 	g2="four" 	k="49" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-10" />
<hkern g1="L" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="10" />
<hkern g1="L" 	g2="nine" 	k="20" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="L" 	g2="one" 	k="72" />
<hkern g1="L" 	g2="paragraph" 	k="164" />
<hkern g1="L" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="L" 	g2="question" 	k="184" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="92" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="96" />
<hkern g1="L" 	g2="seven" 	k="102" />
<hkern g1="L" 	g2="slash" 	k="-41" />
<hkern g1="L" 	g2="uniFB02" 	k="10" />
<hkern g1="L" 	g2="three" 	k="-10" />
<hkern g1="L" 	g2="trademark" 	k="143" />
<hkern g1="L" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="10" />
<hkern g1="L" 	g2="underscore" 	k="-113" />
<hkern g1="L" 	g2="v" 	k="78" />
<hkern g1="L" 	g2="x" 	k="10" />
<hkern g1="L" 	g2="zero,six" 	k="41" />
<hkern g1="L" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="L" 	g2="dollar,S" 	k="10" />
<hkern g1="L" 	g2="ae" 	k="-10" />
<hkern g1="L" 	g2="ampersand" 	k="-10" />
<hkern g1="M,N,Ntilde" 	g2="backslash" 	k="20" />
<hkern g1="M,N,Ntilde" 	g2="one" 	k="6" />
<hkern g1="M,N,Ntilde" 	g2="question" 	k="10" />
<hkern g1="M,N,Ntilde" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="M,N,Ntilde" 	g2="trademark" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="45" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="51" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="degree" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="questiondown" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="37" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="uniFB02" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="25" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="j" 	k="-164" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="16" />
<hkern g1="P" 	g2="AE" 	k="92" />
<hkern g1="P" 	g2="J" 	k="92" />
<hkern g1="P" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="P" 	g2="V" 	k="35" />
<hkern g1="P" 	g2="X" 	k="51" />
<hkern g1="P" 	g2="backslash" 	k="41" />
<hkern g1="P" 	g2="degree" 	k="-41" />
<hkern g1="P" 	g2="eight" 	k="14" />
<hkern g1="P" 	g2="four" 	k="61" />
<hkern g1="P" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="10" />
<hkern g1="P" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="57" />
<hkern g1="P" 	g2="questiondown" 	k="102" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="slash" 	k="92" />
<hkern g1="P" 	g2="uniFB02" 	k="-10" />
<hkern g1="P" 	g2="three" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="72" />
<hkern g1="P" 	g2="v" 	k="10" />
<hkern g1="P" 	g2="x" 	k="68" />
<hkern g1="P" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="P" 	g2="ae" 	k="31" />
<hkern g1="P" 	g2="ampersand" 	k="61" />
<hkern g1="P" 	g2="five" 	k="20" />
<hkern g1="P" 	g2="two" 	k="10" />
<hkern g1="P" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="45" />
<hkern g1="P" 	g2="parenright" 	k="14" />
<hkern g1="P" 	g2="bracketright,braceright" 	k="14" />
<hkern g1="P" 	g2="j" 	k="20" />
<hkern g1="P" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="31" />
<hkern g1="P" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="P" 	g2="Z" 	k="31" />
<hkern g1="P" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="20" />
<hkern g1="R" 	g2="J" 	k="61" />
<hkern g1="R" 	g2="X" 	k="41" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="degree" 	k="-31" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="4" />
<hkern g1="R" 	g2="question" 	k="10" />
<hkern g1="R" 	g2="questiondown" 	k="20" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="R" 	g2="uniFB02" 	k="-10" />
<hkern g1="R" 	g2="three" 	k="10" />
<hkern g1="R" 	g2="underscore" 	k="-31" />
<hkern g1="R" 	g2="ae" 	k="10" />
<hkern g1="dollar,S" 	g2="J" 	k="61" />
<hkern g1="dollar,S" 	g2="T" 	k="16" />
<hkern g1="dollar,S" 	g2="X" 	k="27" />
<hkern g1="dollar,S" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="20" />
<hkern g1="dollar,S" 	g2="colon,semicolon" 	k="10" />
<hkern g1="dollar,S" 	g2="degree" 	k="31" />
<hkern g1="dollar,S" 	g2="exclamdown" 	k="4" />
<hkern g1="dollar,S" 	g2="four" 	k="-20" />
<hkern g1="dollar,S" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-31" />
<hkern g1="dollar,S" 	g2="nine" 	k="20" />
<hkern g1="dollar,S" 	g2="one" 	k="20" />
<hkern g1="dollar,S" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="questiondown" 	k="20" />
<hkern g1="dollar,S" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="dollar,S" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="dollar,S" 	g2="seven" 	k="20" />
<hkern g1="dollar,S" 	g2="three" 	k="10" />
<hkern g1="dollar,S" 	g2="underscore" 	k="41" />
<hkern g1="dollar,S" 	g2="v" 	k="20" />
<hkern g1="dollar,S" 	g2="x" 	k="20" />
<hkern g1="T" 	g2="AE" 	k="82" />
<hkern g1="T" 	g2="J" 	k="10" />
<hkern g1="T" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="V" 	k="-31" />
<hkern g1="T" 	g2="X" 	k="31" />
<hkern g1="T" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-82" />
<hkern g1="T" 	g2="backslash" 	k="-41" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="degree" 	k="-61" />
<hkern g1="T" 	g2="eight" 	k="10" />
<hkern g1="T" 	g2="exclamdown" 	k="41" />
<hkern g1="T" 	g2="four" 	k="164" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="T" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="72" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="one" 	k="-82" />
<hkern g1="T" 	g2="paragraph" 	k="-82" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="66" />
<hkern g1="T" 	g2="question" 	k="-61" />
<hkern g1="T" 	g2="questiondown" 	k="102" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-72" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="T" 	g2="s" 	k="72" />
<hkern g1="T" 	g2="seven" 	k="-61" />
<hkern g1="T" 	g2="slash" 	k="41" />
<hkern g1="T" 	g2="three" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-102" />
<hkern g1="T" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="37" />
<hkern g1="T" 	g2="underscore" 	k="10" />
<hkern g1="T" 	g2="x" 	k="6" />
<hkern g1="T" 	g2="z" 	k="41" />
<hkern g1="T" 	g2="zero,six" 	k="31" />
<hkern g1="T" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="T" 	g2="ae" 	k="61" />
<hkern g1="T" 	g2="ampersand" 	k="61" />
<hkern g1="T" 	g2="five" 	k="-10" />
<hkern g1="T" 	g2="two" 	k="-41" />
<hkern g1="T" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="T" 	g2="bracketright,braceright" 	k="-31" />
<hkern g1="T" 	g2="j" 	k="20" />
<hkern g1="T" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="T" 	g2="exclam" 	k="-41" />
<hkern g1="T" 	g2="parenleft" 	k="20" />
<hkern g1="Thorn" 	g2="AE" 	k="61" />
<hkern g1="Thorn" 	g2="J" 	k="133" />
<hkern g1="Thorn" 	g2="T" 	k="31" />
<hkern g1="Thorn" 	g2="V" 	k="20" />
<hkern g1="Thorn" 	g2="X" 	k="61" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="one" 	k="41" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="47" />
<hkern g1="Thorn" 	g2="question" 	k="61" />
<hkern g1="Thorn" 	g2="questiondown" 	k="51" />
<hkern g1="Thorn" 	g2="slash" 	k="61" />
<hkern g1="Thorn" 	g2="uniFB02" 	k="-10" />
<hkern g1="Thorn" 	g2="three" 	k="41" />
<hkern g1="Thorn" 	g2="trademark" 	k="10" />
<hkern g1="Thorn" 	g2="underscore" 	k="61" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="ae" 	k="20" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="two" 	k="51" />
<hkern g1="Thorn" 	g2="j" 	k="20" />
<hkern g1="Thorn" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="31" />
<hkern g1="Thorn" 	g2="Z" 	k="37" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="AE" 	k="31" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="J" 	k="51" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="X" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="questiondown" 	k="10" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="slash" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="underscore" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="x" 	k="10" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="j" 	k="10" />
<hkern g1="V" 	g2="AE" 	k="76" />
<hkern g1="V" 	g2="J" 	k="51" />
<hkern g1="V" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="45" />
<hkern g1="V" 	g2="T" 	k="-31" />
<hkern g1="V" 	g2="X" 	k="51" />
<hkern g1="V" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-68" />
<hkern g1="V" 	g2="backslash" 	k="-61" />
<hkern g1="V" 	g2="colon,semicolon" 	k="51" />
<hkern g1="V" 	g2="degree" 	k="-51" />
<hkern g1="V" 	g2="eight" 	k="35" />
<hkern g1="V" 	g2="exclamdown" 	k="61" />
<hkern g1="V" 	g2="four" 	k="154" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="V" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="51" />
<hkern g1="V" 	g2="nine" 	k="14" />
<hkern g1="V" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="V" 	g2="one" 	k="-61" />
<hkern g1="V" 	g2="paragraph" 	k="-41" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="76" />
<hkern g1="V" 	g2="questiondown" 	k="143" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="V" 	g2="s" 	k="123" />
<hkern g1="V" 	g2="seven" 	k="-20" />
<hkern g1="V" 	g2="slash" 	k="82" />
<hkern g1="V" 	g2="uniFB02" 	k="10" />
<hkern g1="V" 	g2="three" 	k="-10" />
<hkern g1="V" 	g2="trademark" 	k="-82" />
<hkern g1="V" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="66" />
<hkern g1="V" 	g2="underscore" 	k="20" />
<hkern g1="V" 	g2="v" 	k="35" />
<hkern g1="V" 	g2="x" 	k="68" />
<hkern g1="V" 	g2="z" 	k="61" />
<hkern g1="V" 	g2="zero,six" 	k="51" />
<hkern g1="V" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="45" />
<hkern g1="V" 	g2="dollar,S" 	k="31" />
<hkern g1="V" 	g2="ae" 	k="113" />
<hkern g1="V" 	g2="ampersand" 	k="51" />
<hkern g1="V" 	g2="five" 	k="20" />
<hkern g1="V" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="V" 	g2="parenright" 	k="-20" />
<hkern g1="V" 	g2="bracketright,braceright" 	k="-51" />
<hkern g1="V" 	g2="j" 	k="41" />
<hkern g1="V" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="72" />
<hkern g1="V" 	g2="Z" 	k="20" />
<hkern g1="V" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="V" 	g2="exclam" 	k="-20" />
<hkern g1="X" 	g2="AE" 	k="72" />
<hkern g1="X" 	g2="J" 	k="20" />
<hkern g1="X" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="X" 	g2="T" 	k="31" />
<hkern g1="X" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="X" 	g2="V" 	k="51" />
<hkern g1="X" 	g2="X" 	k="31" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="X" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="X" 	g2="colon,semicolon" 	k="14" />
<hkern g1="X" 	g2="degree" 	k="41" />
<hkern g1="X" 	g2="eight" 	k="45" />
<hkern g1="X" 	g2="exclamdown" 	k="20" />
<hkern g1="X" 	g2="four" 	k="113" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="X" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="X" 	g2="nine" 	k="35" />
<hkern g1="X" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="X" 	g2="one" 	k="-20" />
<hkern g1="X" 	g2="question" 	k="61" />
<hkern g1="X" 	g2="questiondown" 	k="-10" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="-8" />
<hkern g1="X" 	g2="s" 	k="41" />
<hkern g1="X" 	g2="seven" 	k="20" />
<hkern g1="X" 	g2="slash" 	k="-31" />
<hkern g1="X" 	g2="uniFB02" 	k="51" />
<hkern g1="X" 	g2="trademark" 	k="-61" />
<hkern g1="X" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="55" />
<hkern g1="X" 	g2="underscore" 	k="-92" />
<hkern g1="X" 	g2="v" 	k="61" />
<hkern g1="X" 	g2="x" 	k="82" />
<hkern g1="X" 	g2="z" 	k="31" />
<hkern g1="X" 	g2="zero,six" 	k="72" />
<hkern g1="X" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="31" />
<hkern g1="X" 	g2="dollar,S" 	k="27" />
<hkern g1="X" 	g2="ae" 	k="35" />
<hkern g1="X" 	g2="ampersand" 	k="41" />
<hkern g1="X" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="X" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="X" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="X" 	g2="Z" 	k="20" />
<hkern g1="X" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="47" />
<hkern g1="X" 	g2="parenleft" 	k="14" />
<hkern g1="Z" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="Z" 	g2="T" 	k="-20" />
<hkern g1="Z" 	g2="X" 	k="20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="Z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="Z" 	g2="eight" 	k="6" />
<hkern g1="Z" 	g2="exclamdown" 	k="-10" />
<hkern g1="Z" 	g2="four" 	k="49" />
<hkern g1="Z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="Z" 	g2="one" 	k="-41" />
<hkern g1="Z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="Z" 	g2="question" 	k="-10" />
<hkern g1="Z" 	g2="slash" 	k="-10" />
<hkern g1="Z" 	g2="uniFB02" 	k="10" />
<hkern g1="Z" 	g2="trademark" 	k="-61" />
<hkern g1="Z" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="10" />
<hkern g1="Z" 	g2="underscore" 	k="-61" />
<hkern g1="Z" 	g2="v" 	k="20" />
<hkern g1="Z" 	g2="zero,six" 	k="35" />
<hkern g1="Z" 	g2="ae" 	k="-4" />
<hkern g1="Z" 	g2="ampersand" 	k="20" />
<hkern g1="Z" 	g2="five" 	k="6" />
<hkern g1="Z" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="20" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="76" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="133" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="one" 	k="10" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="question" 	k="-41" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="dollar,S" 	k="25" />
<hkern g1="z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="z" 	g2="backslash" 	k="123" />
<hkern g1="z" 	g2="four" 	k="20" />
<hkern g1="z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="27" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="z" 	g2="one" 	k="20" />
<hkern g1="z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-10" />
<hkern g1="z" 	g2="uniFB02" 	k="-4" />
<hkern g1="z" 	g2="underscore" 	k="-41" />
<hkern g1="z" 	g2="v" 	k="20" />
<hkern g1="z" 	g2="x" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="10" />
<hkern g1="z" 	g2="two" 	k="10" />
<hkern g1="ampersand" 	g2="AE" 	k="-20" />
<hkern g1="ampersand" 	g2="T" 	k="61" />
<hkern g1="ampersand" 	g2="V" 	k="41" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="ampersand" 	g2="Z" 	k="-20" />
<hkern g1="ampersand" 	g2="ae" 	k="-20" />
<hkern g1="ampersand" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="ampersand" 	g2="uniFB02" 	k="-20" />
<hkern g1="ampersand" 	g2="v" 	k="31" />
<hkern g1="ampersand" 	g2="x" 	k="10" />
<hkern g1="currency" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-10" />
<hkern g1="degree" 	g2="AE" 	k="195" />
<hkern g1="degree" 	g2="T" 	k="-61" />
<hkern g1="degree" 	g2="V" 	k="-51" />
<hkern g1="degree" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="degree" 	g2="uniFB02" 	k="-41" />
<hkern g1="degree" 	g2="v" 	k="-72" />
<hkern g1="degree" 	g2="x" 	k="-20" />
<hkern g1="degree" 	g2="J" 	k="20" />
<hkern g1="degree" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="degree" 	g2="dollar,S" 	k="-41" />
<hkern g1="degree" 	g2="X" 	k="41" />
<hkern g1="degree" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="degree" 	g2="s" 	k="10" />
<hkern g1="percent" 	g2="ae" 	k="-20" />
<hkern g1="percent" 	g2="asterisk,ordfeminine,ordmasculine" 	k="102" />
<hkern g1="percent" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-51" />
<hkern g1="section" 	g2="ae" 	k="-10" />
<hkern g1="section" 	g2="uniFB02" 	k="-10" />
<hkern g1="section" 	g2="v" 	k="-10" />
<hkern g1="section" 	g2="x" 	k="-10" />
<hkern g1="section" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-10" />
<hkern g1="section" 	g2="s" 	k="-10" />
<hkern g1="section" 	g2="j" 	k="-10" />
<hkern g1="section" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="-10" />
<hkern g1="section" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-10" />
<hkern g1="section" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="-10" />
<hkern g1="section" 	g2="z" 	k="-10" />
<hkern g1="trademark" 	g2="AE" 	k="41" />
<hkern g1="trademark" 	g2="ae" 	k="-51" />
<hkern g1="trademark" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="trademark" 	g2="uniFB02" 	k="-72" />
<hkern g1="trademark" 	g2="v" 	k="-20" />
<hkern g1="trademark" 	g2="J" 	k="20" />
<hkern g1="trademark" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-51" />
<hkern g1="trademark" 	g2="s" 	k="-51" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="AE" 	k="195" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="dollar,S" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="T" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="V" 	k="-68" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="X" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Z" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="ampersand" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="currency,Euro" 	k="-10" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="degree" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="exclam" 	k="-14" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="four" 	k="27" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="guillemotright,guilsinglright" 	k="-31" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="nine" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="one" 	k="-51" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="paragraph" 	k="-72" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="percent" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="72" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="question" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="questiondown" 	k="164" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="seven" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="slash" 	k="276" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="uniFB02" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="trademark" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="two" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="underscore" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="v" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="x" 	k="-31" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="z" 	k="-31" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="zero,six" 	k="-10" />
<hkern g1="backslash" 	g2="AE" 	k="-61" />
<hkern g1="backslash" 	g2="J" 	k="-102" />
<hkern g1="backslash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="backslash" 	g2="T" 	k="41" />
<hkern g1="backslash" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="backslash" 	g2="V" 	k="82" />
<hkern g1="backslash" 	g2="X" 	k="-41" />
<hkern g1="backslash" 	g2="asterisk,ordfeminine,ordmasculine" 	k="276" />
<hkern g1="backslash" 	g2="j" 	k="-246" />
<hkern g1="backslash" 	g2="v" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="J" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="T" 	k="-31" />
<hkern g1="bracketleft,braceleft" 	g2="V" 	k="-51" />
<hkern g1="bracketleft,braceleft" 	g2="X" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-205" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="25" />
<hkern g1="bracketleft,braceleft" 	g2="ae" 	k="14" />
<hkern g1="bracketright" 	g2="J" 	k="20" />
<hkern g1="bracketright" 	g2="T" 	k="25" />
<hkern g1="bracketright" 	g2="X" 	k="31" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-10" />
<hkern g1="colon,semicolon" 	g2="T" 	k="20" />
<hkern g1="colon,semicolon" 	g2="V" 	k="51" />
<hkern g1="colon,semicolon" 	g2="X" 	k="14" />
<hkern g1="colon,semicolon" 	g2="v" 	k="-10" />
<hkern g1="colon,semicolon" 	g2="uniFB02" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="x" 	k="20" />
<hkern g1="exclam" 	g2="J" 	k="20" />
<hkern g1="exclam" 	g2="T" 	k="-41" />
<hkern g1="exclam" 	g2="V" 	k="-20" />
<hkern g1="exclam" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-14" />
<hkern g1="exclamdown" 	g2="AE" 	k="-20" />
<hkern g1="exclamdown" 	g2="J" 	k="-41" />
<hkern g1="exclamdown" 	g2="T" 	k="41" />
<hkern g1="exclamdown" 	g2="V" 	k="61" />
<hkern g1="exclamdown" 	g2="X" 	k="20" />
<hkern g1="exclamdown" 	g2="j" 	k="-143" />
<hkern g1="exclamdown" 	g2="uniFB02" 	k="-16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="AE" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="uniFB02" 	k="-10" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="AE" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="J" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-10" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="T" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="V" 	k="51" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="X" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="v" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="uniFB02" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="x" 	k="51" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="dollar,S" 	k="31" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="z" 	k="27" />
<hkern g1="parenleft" 	g2="J" 	k="14" />
<hkern g1="parenleft" 	g2="j" 	k="-197" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="AE" 	k="-68" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="J" 	k="-10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="66" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="76" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="72" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="68" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="ae" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uniFB02" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="dollar,S" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="question" 	g2="AE" 	k="92" />
<hkern g1="question" 	g2="J" 	k="92" />
<hkern g1="question" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="question" 	g2="V" 	k="31" />
<hkern g1="question" 	g2="X" 	k="41" />
<hkern g1="question" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="question" 	g2="v" 	k="-20" />
<hkern g1="question" 	g2="Z" 	k="20" />
<hkern g1="question" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="question" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="questiondown" 	g2="AE" 	k="-41" />
<hkern g1="questiondown" 	g2="J" 	k="-123" />
<hkern g1="questiondown" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="113" />
<hkern g1="questiondown" 	g2="T" 	k="123" />
<hkern g1="questiondown" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="72" />
<hkern g1="questiondown" 	g2="V" 	k="143" />
<hkern g1="questiondown" 	g2="X" 	k="-20" />
<hkern g1="questiondown" 	g2="asterisk,ordfeminine,ordmasculine" 	k="174" />
<hkern g1="questiondown" 	g2="j" 	k="-143" />
<hkern g1="questiondown" 	g2="v" 	k="102" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="questiondown" 	g2="ae" 	k="31" />
<hkern g1="questiondown" 	g2="uniFB02" 	k="20" />
<hkern g1="questiondown" 	g2="x" 	k="-20" />
<hkern g1="questiondown" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="72" />
<hkern g1="questiondown" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="102" />
<hkern g1="questiondown" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="51" />
<hkern g1="questiondown" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="questiondown" 	g2="s" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="72" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-68" />
<hkern g1="quoteleft,quotedblleft" 	g2="ae" 	k="16" />
<hkern g1="quoteleft,quotedblleft" 	g2="uniFB02" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="72" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-72" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="-10" />
<hkern g1="quoteright,quotedblright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="v" 	k="-31" />
<hkern g1="quoteright,quotedblright" 	g2="ae" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="uniFB02" 	k="-10" />
<hkern g1="quoteright,quotedblright" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="88" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="slash" 	g2="AE" 	k="82" />
<hkern g1="slash" 	g2="J" 	k="20" />
<hkern g1="slash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="T" 	k="-61" />
<hkern g1="slash" 	g2="V" 	k="-61" />
<hkern g1="slash" 	g2="j" 	k="-10" />
<hkern g1="slash" 	g2="v" 	k="61" />
<hkern g1="slash" 	g2="ae" 	k="133" />
<hkern g1="slash" 	g2="uniFB02" 	k="51" />
<hkern g1="slash" 	g2="x" 	k="113" />
<hkern g1="slash" 	g2="dollar,S" 	k="41" />
<hkern g1="slash" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="133" />
<hkern g1="slash" 	g2="z" 	k="133" />
<hkern g1="slash" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="133" />
<hkern g1="slash" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="slash" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="slash" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="133" />
<hkern g1="slash" 	g2="s" 	k="133" />
<hkern g1="underscore" 	g2="AE" 	k="-113" />
<hkern g1="underscore" 	g2="J" 	k="-174" />
<hkern g1="underscore" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="underscore" 	g2="T" 	k="10" />
<hkern g1="underscore" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="underscore" 	g2="V" 	k="20" />
<hkern g1="underscore" 	g2="X" 	k="-92" />
<hkern g1="underscore" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="underscore" 	g2="j" 	k="-225" />
<hkern g1="underscore" 	g2="v" 	k="31" />
<hkern g1="underscore" 	g2="Y,Yacute,Ydieresis" 	k="-10" />
<hkern g1="underscore" 	g2="ae" 	k="20" />
<hkern g1="underscore" 	g2="uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="x" 	k="-72" />
<hkern g1="underscore" 	g2="Z" 	k="-41" />
<hkern g1="underscore" 	g2="dollar,S" 	k="-41" />
<hkern g1="underscore" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="underscore" 	g2="z" 	k="-41" />
<hkern g1="underscore" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="51" />
<hkern g1="underscore" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="underscore" 	g2="s" 	k="-20" />
<hkern g1="underscore" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="-61" />
<hkern g1="c,cent,ccedilla" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="c,cent,ccedilla" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="V" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-31" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="92" />
<hkern g1="c,cent,ccedilla" 	g2="bracketright,braceright" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="bracketleft" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-14" />
<hkern g1="c,cent,ccedilla" 	g2="degree" 	k="-35" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="four" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="nine" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="47" />
<hkern g1="c,cent,ccedilla" 	g2="parenright" 	k="45" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="51" />
<hkern g1="c,cent,ccedilla" 	g2="questiondown" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="c,cent,ccedilla" 	g2="section" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="seven" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="uniFB02" 	k="-14" />
<hkern g1="c,cent,ccedilla" 	g2="three" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="27" />
<hkern g1="c,cent,ccedilla" 	g2="two" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="v" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="x" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="z" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="zero,six" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="133" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="degree" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="nine" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="one" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="92" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="questiondown" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="seven" 	k="57" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="three" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="two" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="76" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="ae" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="paragraph" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="percent" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="underscore" 	k="14" />
<hkern g1="eth" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="eth" 	g2="backslash" 	k="20" />
<hkern g1="eth" 	g2="degree" 	k="-41" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="37" />
<hkern g1="eth" 	g2="question" 	k="10" />
<hkern g1="eth" 	g2="questiondown" 	k="10" />
<hkern g1="eth" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="eth" 	g2="three" 	k="10" />
<hkern g1="eth" 	g2="trademark" 	k="-51" />
<hkern g1="eth" 	g2="zero,six" 	k="-10" />
<hkern g1="eth" 	g2="underscore" 	k="41" />
<hkern g1="f" 	g2="ampersand" 	k="31" />
<hkern g1="f" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-92" />
<hkern g1="f" 	g2="backslash" 	k="-129" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-92" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="f" 	g2="degree" 	k="-72" />
<hkern g1="f" 	g2="eight" 	k="-20" />
<hkern g1="f" 	g2="four" 	k="51" />
<hkern g1="f" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="f" 	g2="nine" 	k="-55" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="f" 	g2="one" 	k="-92" />
<hkern g1="f" 	g2="parenright" 	k="-72" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="f" 	g2="question" 	k="-51" />
<hkern g1="f" 	g2="questiondown" 	k="31" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-131" />
<hkern g1="f" 	g2="seven" 	k="-82" />
<hkern g1="f" 	g2="three" 	k="-41" />
<hkern g1="f" 	g2="trademark" 	k="-143" />
<hkern g1="f" 	g2="two" 	k="-31" />
<hkern g1="f" 	g2="v" 	k="-20" />
<hkern g1="f" 	g2="zero,six" 	k="-20" />
<hkern g1="f" 	g2="ae" 	k="20" />
<hkern g1="f" 	g2="paragraph" 	k="-82" />
<hkern g1="f" 	g2="percent" 	k="-31" />
<hkern g1="f" 	g2="underscore" 	k="41" />
<hkern g1="f" 	g2="exclam" 	k="-10" />
<hkern g1="f" 	g2="five" 	k="-20" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="f" 	g2="j" 	k="-92" />
<hkern g1="f" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="f" 	g2="slash" 	k="61" />
<hkern g1="k" 	g2="V" 	k="14" />
<hkern g1="k" 	g2="ampersand" 	k="20" />
<hkern g1="k" 	g2="backslash" 	k="82" />
<hkern g1="k" 	g2="bracketleft" 	k="14" />
<hkern g1="k" 	g2="eight" 	k="20" />
<hkern g1="k" 	g2="four" 	k="61" />
<hkern g1="k" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="51" />
<hkern g1="k" 	g2="nine" 	k="-20" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="k" 	g2="one" 	k="-20" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="k" 	g2="question" 	k="-20" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-8" />
<hkern g1="k" 	g2="three" 	k="-20" />
<hkern g1="k" 	g2="two" 	k="-41" />
<hkern g1="k" 	g2="v" 	k="61" />
<hkern g1="k" 	g2="x" 	k="41" />
<hkern g1="k" 	g2="z" 	k="25" />
<hkern g1="k" 	g2="zero,six" 	k="20" />
<hkern g1="k" 	g2="X" 	k="10" />
<hkern g1="k" 	g2="ae" 	k="20" />
<hkern g1="k" 	g2="underscore" 	k="-123" />
<hkern g1="k" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="10" />
<hkern g1="k" 	g2="s" 	k="35" />
<hkern g1="k" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="d,uniFB02" 	g2="uniFB02" 	k="-53" />
<hkern g1="l" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="l" 	g2="backslash" 	k="92" />
<hkern g1="l" 	g2="colon,semicolon" 	k="-31" />
<hkern g1="l" 	g2="degree" 	k="41" />
<hkern g1="l" 	g2="eight" 	k="-10" />
<hkern g1="l" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="l" 	g2="one" 	k="41" />
<hkern g1="l" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-51" />
<hkern g1="l" 	g2="question" 	k="41" />
<hkern g1="l" 	g2="questiondown" 	k="-10" />
<hkern g1="l" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="l" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="l" 	g2="seven" 	k="31" />
<hkern g1="l" 	g2="uniFB02" 	k="10" />
<hkern g1="l" 	g2="three" 	k="-20" />
<hkern g1="l" 	g2="two" 	k="-31" />
<hkern g1="l" 	g2="v" 	k="51" />
<hkern g1="l" 	g2="x" 	k="-10" />
<hkern g1="l" 	g2="paragraph" 	k="20" />
<hkern g1="l" 	g2="underscore" 	k="-111" />
<hkern g1="l" 	g2="five" 	k="-6" />
<hkern g1="l" 	g2="slash" 	k="-41" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk,ordfeminine,ordmasculine" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="133" />
<hkern g1="h,m,n,ntilde" 	g2="degree" 	k="35" />
<hkern g1="h,m,n,ntilde" 	g2="nine" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-10" />
<hkern g1="h,m,n,ntilde" 	g2="one" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="92" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="seven" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="two" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="paragraph" 	k="41" />
<hkern g1="h,m,n,ntilde" 	g2="percent" 	k="57" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V" 	k="82" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk,ordfeminine,ordmasculine" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="133" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="degree" 	k="35" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="nine" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="one" 	k="51" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="92" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="questiondown" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="seven" 	k="51" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="three" 	k="47" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="trademark" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="two" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="37" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="16" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="ae" 	k="14" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="paragraph" 	k="47" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="percent" 	k="51" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="underscore" 	k="51" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="AE" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="J" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="113" />
<hkern g1="r" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="r" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="45" />
<hkern g1="r" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="r" 	g2="V" 	k="51" />
<hkern g1="r" 	g2="ampersand" 	k="61" />
<hkern g1="r" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="r" 	g2="backslash" 	k="41" />
<hkern g1="r" 	g2="bracketright,braceright" 	k="20" />
<hkern g1="r" 	g2="bracketleft" 	k="25" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="r" 	g2="degree" 	k="-68" />
<hkern g1="r" 	g2="eight" 	k="-4" />
<hkern g1="r" 	g2="four" 	k="51" />
<hkern g1="r" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="16" />
<hkern g1="r" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="10" />
<hkern g1="r" 	g2="nine" 	k="-20" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="r" 	g2="parenright" 	k="14" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="r" 	g2="question" 	k="-20" />
<hkern g1="r" 	g2="questiondown" 	k="72" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-72" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-72" />
<hkern g1="r" 	g2="section" 	k="-20" />
<hkern g1="r" 	g2="seven" 	k="-45" />
<hkern g1="r" 	g2="uniFB02" 	k="-31" />
<hkern g1="r" 	g2="three" 	k="10" />
<hkern g1="r" 	g2="two" 	k="-10" />
<hkern g1="r" 	g2="v" 	k="-31" />
<hkern g1="r" 	g2="x" 	k="10" />
<hkern g1="r" 	g2="zero,six" 	k="-10" />
<hkern g1="r" 	g2="X" 	k="82" />
<hkern g1="r" 	g2="ae" 	k="16" />
<hkern g1="r" 	g2="paragraph" 	k="-61" />
<hkern g1="r" 	g2="underscore" 	k="31" />
<hkern g1="r" 	g2="five" 	k="-4" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="-10" />
<hkern g1="r" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="10" />
<hkern g1="r" 	g2="slash" 	k="82" />
<hkern g1="r" 	g2="s" 	k="6" />
<hkern g1="r" 	g2="AE" 	k="86" />
<hkern g1="r" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="20" />
<hkern g1="r" 	g2="dollar,S" 	k="25" />
<hkern g1="r" 	g2="parenleft" 	k="20" />
<hkern g1="s" 	g2="backslash" 	k="123" />
<hkern g1="s" 	g2="degree" 	k="27" />
<hkern g1="s" 	g2="nine" 	k="10" />
<hkern g1="s" 	g2="one" 	k="10" />
<hkern g1="s" 	g2="question" 	k="31" />
<hkern g1="s" 	g2="seven" 	k="51" />
<hkern g1="s" 	g2="three" 	k="20" />
<hkern g1="s" 	g2="trademark" 	k="31" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="s" 	g2="v" 	k="20" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="paragraph" 	k="47" />
<hkern g1="t" 	g2="ampersand" 	k="16" />
<hkern g1="t" 	g2="asterisk,ordfeminine,ordmasculine" 	k="31" />
<hkern g1="t" 	g2="backslash" 	k="133" />
<hkern g1="t" 	g2="degree" 	k="10" />
<hkern g1="t" 	g2="eight" 	k="14" />
<hkern g1="t" 	g2="four" 	k="51" />
<hkern g1="t" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="t" 	g2="nine" 	k="20" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="t" 	g2="one" 	k="35" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="question" 	k="31" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="t" 	g2="section" 	k="14" />
<hkern g1="t" 	g2="seven" 	k="41" />
<hkern g1="t" 	g2="uniFB02" 	k="27" />
<hkern g1="t" 	g2="trademark" 	k="51" />
<hkern g1="t" 	g2="two" 	k="10" />
<hkern g1="t" 	g2="v" 	k="20" />
<hkern g1="t" 	g2="zero,six" 	k="31" />
<hkern g1="t" 	g2="paragraph" 	k="41" />
<hkern g1="t" 	g2="underscore" 	k="-61" />
<hkern g1="t" 	g2="five" 	k="10" />
<hkern g1="v" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="v" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="v" 	g2="ampersand" 	k="51" />
<hkern g1="v" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-82" />
<hkern g1="v" 	g2="backslash" 	k="41" />
<hkern g1="v" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="v" 	g2="degree" 	k="-72" />
<hkern g1="v" 	g2="eight" 	k="-10" />
<hkern g1="v" 	g2="four" 	k="61" />
<hkern g1="v" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="31" />
<hkern g1="v" 	g2="nine" 	k="-61" />
<hkern g1="v" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="v" 	g2="one" 	k="-31" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="68" />
<hkern g1="v" 	g2="question" 	k="-72" />
<hkern g1="v" 	g2="questiondown" 	k="72" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-68" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-31" />
<hkern g1="v" 	g2="seven" 	k="-31" />
<hkern g1="v" 	g2="uniFB02" 	k="-31" />
<hkern g1="v" 	g2="two" 	k="-31" />
<hkern g1="v" 	g2="v" 	k="20" />
<hkern g1="v" 	g2="x" 	k="61" />
<hkern g1="v" 	g2="z" 	k="20" />
<hkern g1="v" 	g2="zero,six" 	k="-10" />
<hkern g1="v" 	g2="X" 	k="61" />
<hkern g1="v" 	g2="ae" 	k="35" />
<hkern g1="v" 	g2="paragraph" 	k="-61" />
<hkern g1="v" 	g2="underscore" 	k="31" />
<hkern g1="v" 	g2="five" 	k="-10" />
<hkern g1="v" 	g2="j" 	k="31" />
<hkern g1="v" 	g2="slash" 	k="68" />
<hkern g1="v" 	g2="s" 	k="14" />
<hkern g1="v" 	g2="AE" 	k="55" />
<hkern g1="v" 	g2="dollar,S" 	k="10" />
<hkern g1="w" 	g2="backslash" 	k="102" />
<hkern g1="w" 	g2="one" 	k="10" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="w" 	g2="three" 	k="20" />
<hkern g1="w" 	g2="two" 	k="10" />
<hkern g1="w" 	g2="underscore" 	k="51" />
<hkern g1="x" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="x" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="x" 	g2="V" 	k="45" />
<hkern g1="x" 	g2="ampersand" 	k="41" />
<hkern g1="x" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-31" />
<hkern g1="x" 	g2="backslash" 	k="92" />
<hkern g1="x" 	g2="colon,semicolon" 	k="20" />
<hkern g1="x" 	g2="degree" 	k="-20" />
<hkern g1="x" 	g2="eight" 	k="10" />
<hkern g1="x" 	g2="four" 	k="72" />
<hkern g1="x" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="51" />
<hkern g1="x" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="x" 	g2="one" 	k="-20" />
<hkern g1="x" 	g2="question" 	k="-31" />
<hkern g1="x" 	g2="seven" 	k="10" />
<hkern g1="x" 	g2="two" 	k="-10" />
<hkern g1="x" 	g2="v" 	k="61" />
<hkern g1="x" 	g2="z" 	k="20" />
<hkern g1="x" 	g2="zero,six" 	k="20" />
<hkern g1="x" 	g2="ae" 	k="20" />
<hkern g1="x" 	g2="paragraph" 	k="-10" />
<hkern g1="x" 	g2="underscore" 	k="-72" />
<hkern g1="x" 	g2="j" 	k="-33" />
<hkern g1="x" 	g2="slash" 	k="-20" />
<hkern g1="x" 	g2="s" 	k="20" />
<hkern g1="x" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="x" 	g2="dollar,S" 	k="14" />
</font>
</defs></svg> 