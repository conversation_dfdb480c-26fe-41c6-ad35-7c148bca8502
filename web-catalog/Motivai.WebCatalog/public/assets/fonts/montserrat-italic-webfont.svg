<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="montserratitalic" horiz-adv-x="1388" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="536" />
<glyph unicode="&#xfb01;" horiz-adv-x="1409" d="M119 954l22 123h195l26 135q31 154 124.5 236t244.5 82q141 0 217 -70l-63 -106q-59 53 -154 53t-150.5 -51t-78.5 -160l-25 -119h330l-25 -123h-325l-191 -954h-145l190 954h-192zM954 0l213 1077h146l-213 -1077h-146zM1204 1409q0 46 32 78.5t81 32.5q42 0 71 -28 t29 -65q0 -49 -32.5 -81.5t-81.5 -32.5q-42 0 -70.5 28.5t-28.5 67.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1245" d="M119 954l22 123h195l26 135q31 154 124.5 236t244.5 82q141 0 217 -70l-63 -106q-59 53 -154 53t-150.5 -51t-78.5 -160l-25 -119h330l-25 -123h-325l-191 -954h-145l190 954h-192zM791 0l303 1520h145l-303 -1520h-145z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="536" />
<glyph unicode=" "  horiz-adv-x="536" />
<glyph unicode="&#x09;" horiz-adv-x="536" />
<glyph unicode="&#xa0;" horiz-adv-x="536" />
<glyph unicode="!" horiz-adv-x="532" d="M70 90q0 47 32 79t78 32q44 0 74.5 -29t30.5 -70q0 -47 -32.5 -79.5t-78.5 -32.5q-44 0 -74 30t-30 70zM188 430l170 1004h172l-229 -1004h-113z" />
<glyph unicode="&#x22;" horiz-adv-x="763" d="M225 893l97 541h129l-117 -541h-109zM575 893l97 541h129l-117 -541h-109z" />
<glyph unicode="#" horiz-adv-x="1425" d="M33 389l22 113h301l138 430h-287l20 112h303l125 390h115l-125 -390h416l125 390h117l-125 -390h288l-22 -112h-303l-137 -430h290l-22 -113h-305l-125 -389h-115l125 389h-418l-125 -389h-114l124 389h-286zM471 502h416l139 430h-418z" />
<glyph unicode="$" horiz-adv-x="1259" d="M25 170l73 119q65 -69 174 -115t232 -55l106 540q-47 14 -80 25.5t-75 30t-71 37t-59 45.5t-48 57.5t-29.5 71.5t-11.5 88q0 191 147 306t385 126l47 233h100l-47 -235q233 -13 385 -121l-63 -123q-146 106 -348 115l-109 -547q52 -15 84.5 -25.5t79 -29t75.5 -36t62 -44 t52 -56t31.5 -70t12.5 -87.5q0 -197 -151.5 -310.5t-400.5 -121.5l-48 -234h-100l47 236q-140 10 -263.5 59.5t-188.5 120.5zM383 1028q0 -47 19 -84t57 -64t79 -45.5t101 -37.5l102 518q-164 -12 -261 -88.5t-97 -198.5zM604 119q172 8 274.5 81.5t102.5 198.5q0 39 -15 72 t-38.5 56t-61.5 43.5t-73.5 34t-85.5 28.5z" />
<glyph unicode="%" horiz-adv-x="1697" d="M182 973q0 124 45 230t134 172.5t204 66.5q128 0 203.5 -81.5t75.5 -223.5q0 -125 -45 -230.5t-134.5 -172t-205.5 -66.5q-128 0 -202.5 81.5t-74.5 223.5zM190 0l1260 1434h127l-1258 -1434h-129zM285 981q0 -107 47.5 -165t134.5 -58q85 0 148.5 57.5t93.5 140 t30 172.5q0 107 -48 165.5t-134 58.5q-64 0 -117 -33.5t-86 -87.5t-51 -119t-18 -131zM926 295q0 124 45.5 230t135 172.5t204.5 66.5q128 0 202 -81.5t74 -223.5q0 -125 -44.5 -230.5t-133.5 -172t-205 -66.5q-128 0 -203 81.5t-75 223.5zM1028 303q0 -107 48.5 -165 t135.5 -58q85 0 149 58t94 140.5t30 172.5q0 106 -48.5 164.5t-133.5 58.5t-150 -58t-95 -140.5t-30 -172.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1370" d="M59 315q0 143 106 256t343 230q-117 158 -117 286q0 163 110.5 260t286.5 97q141 0 227.5 -68t86.5 -184q0 -128 -93 -221.5t-309 -198.5l314 -399q90 116 153 278l115 -37q-74 -196 -190 -340l157 -202l-98 -95l-156 199q-209 -188 -497 -188q-201 0 -320 88t-119 239z M205 338q0 -104 80 -166.5t223 -62.5q238 0 414 161l-344 443q-206 -101 -289.5 -185t-83.5 -190zM526 1104q0 -105 107 -244q198 94 270 162t72 160q0 67 -50.5 110t-140.5 43q-117 0 -187.5 -63.5t-70.5 -167.5z" />
<glyph unicode="'" horiz-adv-x="413" d="M225 893l97 541h129l-117 -541h-109z" />
<glyph unicode="(" horiz-adv-x="673" d="M176 145q0 374 125 746t336 629h149q-217 -272 -344 -645t-127 -738q0 -266 86 -534h-131q-43 115 -68.5 259.5t-25.5 282.5z" />
<glyph unicode=")" horiz-adv-x="673" d="M-4 -397q217 272 344 644.5t127 737.5q0 267 -86 535h131q43 -115 68.5 -260t25.5 -283q0 -374 -125 -745.5t-336 -628.5h-149z" />
<glyph unicode="*" horiz-adv-x="790" d="M137 1004l289 147l-225 141l57 88l229 -153l56 293h96l-61 -293l286 151l37 -88l-285 -143l222 -141l-58 -86l-231 153l-57 -295h-97l60 289l-279 -147z" />
<glyph unicode="+" horiz-adv-x="1177" d="M166 655l24 121h381l76 375h129l-76 -375h383l-24 -121h-383l-74 -372h-129l74 372h-381z" />
<glyph unicode="," horiz-adv-x="434" d="M-53 -299l131 301q-26 12 -42 37.5t-16 56.5q0 50 34 83.5t81 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -43 -39 -108l-156 -297h-98z" />
<glyph unicode="-" horiz-adv-x="782" d="M115 492l24 126h537l-25 -126h-536z" />
<glyph unicode="." horiz-adv-x="434" d="M20 96q0 50 34 83.5t81 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -50 -33.5 -83t-79.5 -33q-44 0 -75.5 31t-31.5 75z" />
<glyph unicode="/" horiz-adv-x="686" d="M-203 -205l1057 1929h141l-1056 -1929h-142z" />
<glyph unicode="0" horiz-adv-x="1355" d="M121 506q0 146 31.5 284.5t94 257t148 207.5t200 140t243.5 51q220 0 343.5 -137t123.5 -381q0 -146 -31.5 -284.5t-94 -257t-148 -207.5t-200 -140t-243.5 -51q-220 0 -343.5 137t-123.5 381zM270 508q0 -186 86.5 -285.5t243.5 -99.5q103 0 193.5 48t155.5 128.5 t112 184.5t70.5 217t23.5 225q0 186 -86.5 285.5t-243.5 99.5q-103 0 -193.5 -48t-155.5 -128.5t-112 -184.5t-70.5 -217t-23.5 -225z" />
<glyph unicode="1" horiz-adv-x="739" d="M170 1303l27 131h487l-287 -1434h-149l260 1303h-338z" />
<glyph unicode="2" horiz-adv-x="1163" d="M-29 0l23 104l717 582q151 121 208.5 209.5t57.5 187.5q0 109 -77 170.5t-232 61.5q-113 0 -217 -43t-179 -121l-102 88q88 97 225 152t293 55q211 0 325.5 -93.5t114.5 -250.5q0 -128 -69.5 -244t-245.5 -258l-586 -469h779l-27 -131h-1008z" />
<glyph unicode="3" horiz-adv-x="1155" d="M-45 174l82 115q59 -75 168.5 -120.5t249.5 -45.5q200 0 314 88t114 238q0 247 -346 247h-93l23 109l491 498h-727l27 131h920l-21 -105l-506 -512q185 -15 284 -108.5t99 -251.5q0 -208 -162.5 -338.5t-425.5 -130.5q-163 0 -292.5 50t-198.5 136z" />
<glyph unicode="4" horiz-adv-x="1353" d="M51 377l21 106l938 951h174l-910 -926h615l67 332h142l-66 -332h287l-25 -131h-288l-76 -377h-148l76 377h-807z" />
<glyph unicode="5" horiz-adv-x="1159" d="M-16 174l84 115q58 -75 167 -120.5t250 -45.5q202 0 315 88t113 244q0 260 -399 260h-332l217 719h783l-27 -131h-649l-141 -457h174q263 0 393.5 -99.5t130.5 -275.5q0 -220 -160.5 -351.5t-427.5 -131.5q-163 0 -292.5 50t-198.5 136z" />
<glyph unicode="6" horiz-adv-x="1247" d="M123 555q0 126 26 248t86.5 239.5t149 206t224.5 143t302 54.5q223 0 344 -80l-69 -119q-107 70 -283 70q-123 0 -225 -38.5t-171 -103t-118.5 -151.5t-75 -179t-32.5 -192q69 104 181 154.5t245 50.5q130 0 233 -46t165 -139t62 -218q0 -214 -144.5 -340.5 t-373.5 -126.5q-239 0 -382.5 146t-143.5 421zM291 408q0 -118 94.5 -206.5t261.5 -88.5q168 0 269.5 88.5t101.5 242.5q0 137 -94 213t-248 76q-171 0 -278 -93t-107 -232z" />
<glyph unicode="7" horiz-adv-x="1206" d="M166 1034l78 400h1054l-20 -105l-860 -1329h-168l844 1303h-729l-54 -269h-145z" />
<glyph unicode="8" horiz-adv-x="1306" d="M70 367q0 153 92 256t262 145q-168 86 -168 272q0 187 144 296.5t382 109.5q219 0 351.5 -94t132.5 -254q0 -133 -77 -227.5t-220 -135.5q103 -46 157 -125t54 -184q0 -201 -159 -319.5t-423 -118.5q-247 0 -387.5 102t-140.5 277zM219 377q0 -123 101.5 -193.5 t281.5 -70.5q198 0 313 84t115 227q0 122 -102.5 193t-282.5 71q-197 0 -311.5 -84t-114.5 -227zM406 1042q0 -110 88 -173.5t247 -63.5q173 0 274 74.5t101 199.5q0 112 -92 177t-248 65q-169 0 -269.5 -75t-100.5 -204z" />
<glyph unicode="9" horiz-adv-x="1247" d="M63 68l70 118q106 -69 283 -69q122 0 224 38.5t171 102.5t119 151t75.5 180t32.5 193q-69 -105 -181 -156t-245 -51q-129 0 -232.5 46.5t-165.5 139.5t-62 218q0 214 144.5 340.5t373.5 126.5q239 0 382.5 -146t143.5 -421q0 -126 -26 -248t-86.5 -239.5t-149 -206 t-225 -143t-303.5 -54.5q-222 0 -343 80zM301 989q0 -137 94 -213t248 -76q170 0 277.5 93.5t107.5 232.5q0 118 -94.5 206.5t-261.5 88.5q-168 0 -269.5 -89t-101.5 -243z" />
<glyph unicode=":" horiz-adv-x="434" d="M20 96q0 50 34 83.5t81 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -50 -33.5 -83t-79.5 -33q-44 0 -75.5 31t-31.5 75zM195 969q0 50 33.5 83t80.5 33q45 0 75 -30.5t30 -75.5q0 -50 -34 -83.5t-79 -33.5q-44 0 -75 31.5t-31 75.5z" />
<glyph unicode=";" horiz-adv-x="434" d="M-53 -299l131 301q-26 12 -42 37.5t-16 56.5q0 50 34 83.5t81 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -43 -39 -108l-156 -297h-98zM195 969q0 50 33.5 83t80.5 33q45 0 75 -30.5t30 -75.5q0 -50 -34 -83.5t-79 -33.5q-44 0 -75 31.5t-31 75.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1177" d="M164 649l26 135l963 359l-29 -137l-811 -297l697 -295l-25 -121z" />
<glyph unicode="=" horiz-adv-x="1177" d="M121 420l24 121h891l-24 -121h-891zM215 891l25 123h890l-26 -123h-889z" />
<glyph unicode="&#x3e;" horiz-adv-x="1177" d="M96 293l29 135l809 295l-692 297l24 123l817 -359l-26 -135z" />
<glyph unicode="?" horiz-adv-x="1161" d="M162 1239q185 207 532 207q197 0 309.5 -84.5t112.5 -233.5q0 -79 -24 -143.5t-64 -109.5t-88.5 -84t-100 -76t-96.5 -75.5t-81 -92t-50 -117.5h-151q14 74 49.5 135.5t80.5 104.5t96.5 81.5t100.5 74.5t89 74.5t64 90.5t24 115q0 100 -75.5 155.5t-215.5 55.5 q-261 0 -416 -166zM362 90q0 47 32.5 79t78.5 32q43 0 72.5 -29t29.5 -70q0 -47 -32 -79.5t-78 -32.5q-43 0 -73 30t-30 70z" />
<glyph unicode="@" horiz-adv-x="2115" d="M86 352q0 178 50 343t146.5 301t230 237t309 157t374.5 56q392 0 610.5 -202.5t218.5 -541.5q0 -186 -58 -349.5t-161 -263t-225 -99.5q-95 0 -151.5 47t-57.5 129q-158 -176 -414 -176q-200 0 -323 118.5t-123 317.5q0 173 74 314.5t208 224t302 82.5q147 0 248 -60 t147 -172l45 223h133l-156 -778q-6 -27 -6 -51q0 -103 107 -103q84 0 156 88t111.5 224t39.5 282q0 296 -190 468.5t-547 172.5q-219 0 -405 -77.5t-311.5 -211t-196 -313t-70.5 -381.5q0 -148 46.5 -269.5t135 -209t223.5 -136t305 -48.5q227 0 398 88l22 -102 q-187 -91 -432 -91q-188 0 -341.5 55.5t-256.5 156t-159 241t-56 309.5zM647 434q0 -156 89.5 -240.5t250.5 -84.5q199 0 328 141t129 354q0 155 -91.5 239.5t-254.5 84.5q-196 0 -323.5 -139t-127.5 -355z" />
<glyph unicode="A" horiz-adv-x="1468" d="M-102 0l942 1434h149l369 -1434h-152l-96 383h-801l-248 -383h-163zM387 506h694l-194 774z" />
<glyph unicode="B" horiz-adv-x="1544" d="M127 0l287 1434h612q208 0 324 -78.5t116 -227.5q0 -146 -79.5 -246.5t-210.5 -142.5q110 -30 169.5 -104.5t59.5 -190.5q0 -215 -163 -329.5t-449 -114.5h-666zM303 125h502q217 0 333.5 81.5t116.5 235.5q0 118 -82.5 170t-250.5 52h-512zM434 788h457q190 0 305 81.5 t115 232.5q0 104 -81.5 155.5t-232.5 51.5h-458z" />
<glyph unicode="C" horiz-adv-x="1472" d="M131 584q0 176 61.5 333t172.5 274.5t276.5 186t362.5 68.5q172 0 304.5 -54t204.5 -155l-106 -94q-136 168 -416 168q-159 0 -294 -57.5t-225 -156t-140.5 -229t-50.5 -276.5q0 -216 137.5 -342.5t394.5 -126.5q282 0 455 170l88 -100q-94 -99 -239.5 -152t-315.5 -53 q-315 0 -492.5 163t-177.5 433z" />
<glyph unicode="D" horiz-adv-x="1691" d="M127 0l287 1434h544q320 0 498 -158t178 -426q0 -187 -64 -344.5t-179 -269t-282 -174t-366 -62.5h-616zM303 131h445q168 0 306.5 53.5t232 148t145 225.5t51.5 286q0 213 -138 336t-403 123h-405z" />
<glyph unicode="E" horiz-adv-x="1370" d="M127 0l287 1434h983l-27 -131h-833l-103 -510h744l-25 -129h-743l-107 -533h862l-26 -131h-1012z" />
<glyph unicode="F" horiz-adv-x="1296" d="M127 0l287 1434h983l-27 -131h-833l-113 -564h743l-26 -131h-744l-121 -608h-149z" />
<glyph unicode="G" horiz-adv-x="1583" d="M131 584q0 176 61.5 333t173 274.5t277.5 186t363 68.5q180 0 316 -54t216 -155l-106 -92q-141 166 -433 166q-160 0 -296 -57t-228 -154.5t-143 -228.5t-51 -279q0 -216 138 -342.5t398 -126.5q208 0 367 113l96 481h145l-110 -551q-99 -84 -233.5 -131t-280.5 -47 q-313 0 -491.5 162.5t-178.5 433.5z" />
<glyph unicode="H" horiz-adv-x="1665" d="M127 0l287 1434h149l-127 -637h897l127 637h150l-287 -1434h-149l131 659h-897l-132 -659h-149z" />
<glyph unicode="I" horiz-adv-x="618" d="M127 0l287 1434h149l-287 -1434h-149z" />
<glyph unicode="J" horiz-adv-x="1026" d="M-76 195l107 96q41 -81 115 -126.5t173 -45.5q129 0 206 72t110 237l174 875h-522l26 131h672l-203 -1006q-46 -236 -159.5 -338t-307.5 -102q-137 0 -240.5 55t-150.5 152z" />
<glyph unicode="K" horiz-adv-x="1456" d="M127 0l287 1434h149l-172 -856l1000 856h198l-774 -666l522 -768h-170l-462 676l-353 -301l-76 -375h-149z" />
<glyph unicode="L" horiz-adv-x="1206" d="M127 0l287 1434h149l-260 -1303h803l-27 -131h-952z" />
<glyph unicode="M" horiz-adv-x="1955" d="M127 0l287 1434h123l413 -1065l819 1065h134l-287 -1434h-146l228 1137l-742 -955h-71l-383 973l-230 -1155h-145z" />
<glyph unicode="N" horiz-adv-x="1665" d="M127 0l287 1434h123l686 -1188l237 1188h150l-287 -1434h-123l-686 1188l-238 -1188h-149z" />
<glyph unicode="O" horiz-adv-x="1718" d="M131 584q0 176 61.5 333t171.5 274.5t274 186t357 68.5q313 0 489.5 -163t176.5 -433q0 -176 -62 -333.5t-172.5 -274.5t-274.5 -185.5t-357 -68.5q-313 0 -488.5 163t-175.5 433zM281 592q0 -216 137 -342.5t391 -126.5q207 0 368.5 99t246.5 262.5t85 359.5 q0 214 -137 340.5t-391 126.5q-207 0 -368.5 -99t-246.5 -262t-85 -358z" />
<glyph unicode="P" horiz-adv-x="1470" d="M127 0l287 1434h514q243 0 377.5 -107t134.5 -299q0 -274 -177.5 -424.5t-488.5 -150.5h-405l-93 -453h-149zM393 586h383q247 0 379.5 109t132.5 319q0 141 -96.5 215t-276.5 74h-376z" />
<glyph unicode="Q" horiz-adv-x="1718" d="M131 584q0 176 61.5 333t171.5 274.5t274 186t357 68.5q313 0 489.5 -163t176.5 -433q0 -161 -52 -307.5t-146.5 -260.5t-234.5 -190t-308 -96q114 -160 288 -160q167 0 277 125l65 -92q-63 -75 -153.5 -114.5t-192.5 -39.5q-278 0 -473 275q-284 17 -442 177.5 t-158 416.5zM281 592q0 -216 137 -342.5t391 -126.5q207 0 368.5 99t246.5 262.5t85 359.5q0 214 -137 340.5t-391 126.5q-207 0 -368.5 -99t-246.5 -262t-85 -358z" />
<glyph unicode="R" horiz-adv-x="1480" d="M127 0l287 1434h516q244 0 378 -107t134 -299q0 -219 -115.5 -360t-325.5 -189l271 -479h-164l-258 457q-23 -2 -74 -2h-407l-90 -455h-152zM393 586h387q245 0 377.5 109t132.5 319q0 141 -95.5 215t-274.5 74h-381z" />
<glyph unicode="S" horiz-adv-x="1259" d="M25 170l73 119q72 -77 198.5 -124.5t262.5 -47.5q191 0 306.5 74.5t115.5 207.5q0 53 -25.5 93t-68.5 66t-99 46.5t-117.5 38.5t-123.5 37.5t-118 47.5t-99 64.5t-68.5 93t-25.5 128.5q0 199 159 315.5t414 116.5q129 0 245.5 -32t198.5 -91l-63 -123q-160 117 -389 117 q-189 0 -303.5 -78t-114.5 -211q0 -67 41 -115t106.5 -76t145.5 -51t159.5 -50t145.5 -63.5t107.5 -100.5t41.5 -152q0 -135 -75.5 -233.5t-207 -148.5t-302.5 -50q-158 0 -302.5 51.5t-217.5 130.5z" />
<glyph unicode="T" horiz-adv-x="1175" d="M160 1303l26 131h1162l-29 -131h-504l-260 -1303h-149l260 1303h-506z" />
<glyph unicode="U" horiz-adv-x="1622" d="M221 453q0 78 17 159l163 822h150l-164 -818q-16 -85 -16 -145q0 -171 98 -259.5t283 -88.5q211 0 337 120t177 373l164 818h147l-164 -828q-125 -618 -674 -618q-246 0 -382 123t-136 342z" />
<glyph unicode="V" horiz-adv-x="1429" d="M184 1434h156l301 -1244l803 1244h160l-922 -1434h-147z" />
<glyph unicode="W" horiz-adv-x="2275" d="M252 1434h149l170 -1235l674 1235h141l175 -1237l669 1237h152l-770 -1434h-160l-176 1217l-668 -1217h-159z" />
<glyph unicode="X" horiz-adv-x="1343" d="M-72 0l701 745l-379 689h160l319 -582l549 582h172l-655 -701l403 -733h-160l-344 629l-585 -629h-181z" />
<glyph unicode="Y" horiz-adv-x="1300" d="M180 1434h152l332 -807l651 807h162l-764 -951l-97 -483h-149l98 494z" />
<glyph unicode="Z" horiz-adv-x="1333" d="M-16 0l20 104l1180 1199h-924l27 131h1136l-20 -105l-1182 -1198h965l-27 -131h-1175z" />
<glyph unicode="[" horiz-adv-x="653" d="M45 -397l383 1917h375l-25 -123h-229l-332 -1671h227l-24 -123h-375z" />
<glyph unicode="\" horiz-adv-x="686" d="M164 1724h127l297 -1929h-127z" />
<glyph unicode="]" horiz-adv-x="651" d="M-141 -397l24 123h230l331 1671h-229l25 123h376l-383 -1917h-374z" />
<glyph unicode="^" horiz-adv-x="1179" d="M123 293l526 848h123l189 -848h-115l-154 721l-444 -721h-125z" />
<glyph unicode="_" horiz-adv-x="1024" d="M-127 -98l21 98h1024l-21 -98h-1024z" />
<glyph unicode="`" horiz-adv-x="1228" d="M463 1497h192l234 -254h-146z" />
<glyph unicode="a" d="M86 446q0 136 46 255t126.5 203t193.5 132.5t242 48.5q153 0 258.5 -60t151.5 -173l45 225h145l-215 -1077h-139l35 170q-162 -180 -420 -180q-210 0 -339.5 122.5t-129.5 333.5zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5 t-261.5 88.5q-202 0 -334.5 -143t-132.5 -362z" />
<glyph unicode="b" d="M96 0l303 1520h146l-121 -605q160 170 410 170q210 0 340.5 -122.5t130.5 -331.5q0 -183 -79 -329.5t-218.5 -229t-313.5 -82.5q-156 0 -261.5 63.5t-149.5 182.5l-47 -236h-140zM338 453q0 -158 92.5 -246t261.5 -88q201 0 333 142.5t132 361.5q0 159 -93 247t-261 88 q-202 0 -333.5 -142t-131.5 -363z" />
<glyph unicode="c" horiz-adv-x="1153" d="M86 446q0 183 79.5 328.5t223 228t324.5 82.5q304 0 424 -215l-123 -71q-92 159 -314 159q-203 0 -336 -143t-133 -362q0 -159 94 -246.5t265 -87.5q104 0 193 40t147 115l104 -79q-72 -96 -191.5 -150.5t-258.5 -54.5q-229 0 -363.5 124t-134.5 332z" />
<glyph unicode="d" d="M86 446q0 136 46 255t126.5 203t193.5 132.5t242 48.5q153 0 258 -60t152 -173l133 668h145l-303 -1520h-139l35 170q-162 -180 -420 -180q-210 0 -339.5 122.5t-129.5 333.5zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5 t-261.5 88.5q-202 0 -334.5 -143t-132.5 -362z" />
<glyph unicode="e" horiz-adv-x="1236" d="M86 449q0 181 77 326.5t213 227.5t306 82q216 0 342.5 -121.5t126.5 -334.5q0 -59 -14 -131h-906q-2 -14 -2 -43q0 -163 93.5 -249.5t277.5 -86.5q107 0 199.5 36t152.5 99l66 -104q-75 -75 -189.5 -117.5t-240.5 -42.5q-233 0 -367.5 123.5t-134.5 335.5zM246 606h768 q2 14 2 39q0 151 -92 234.5t-246 83.5q-161 0 -276.5 -97t-155.5 -260z" />
<glyph unicode="f" horiz-adv-x="694" d="M119 954l22 123h195l26 135q31 154 124.5 236t244.5 82q141 0 217 -70l-63 -106q-59 53 -154 53t-150.5 -51t-78.5 -160l-25 -119h330l-25 -123h-325l-191 -954h-145l190 954h-192z" />
<glyph unicode="g" horiz-adv-x="1404" d="M14 -246l86 111q66 -67 171.5 -105.5t232.5 -38.5q203 0 315.5 94.5t151.5 290.5l24 119q-163 -184 -430 -184q-215 0 -345 117t-130 317q0 171 78.5 310.5t219.5 219.5t317 80q153 0 263.5 -62.5t155.5 -182.5l45 237h140l-189 -946q-56 -283 -208 -411t-418 -128 q-307 0 -480 162zM238 483q0 -148 93.5 -230.5t262.5 -82.5q203 0 336 135t133 340q0 148 -94.5 230.5t-261.5 82.5q-204 0 -336.5 -134.5t-132.5 -340.5z" />
<glyph unicode="h" horiz-adv-x="1386" d="M96 0l303 1520h146l-121 -605q148 170 412 170q182 0 283.5 -92.5t101.5 -257.5q0 -53 -13 -123l-123 -612h-145l123 614q10 58 10 99q0 117 -70 179t-208 62q-174 0 -287.5 -95t-151.5 -281l-114 -578h-146z" />
<glyph unicode="i" horiz-adv-x="550" d="M96 0l213 1077h146l-213 -1077h-146zM346 1409q0 46 32 78.5t81 32.5q42 0 71 -28t29 -65q0 -49 -32.5 -81.5t-82.5 -32.5q-42 0 -70 28.5t-28 67.5z" />
<glyph unicode="j" horiz-adv-x="561" d="M-356 -340l67 113q62 -56 162 -56q89 0 141 49.5t74 155.5l231 1155h146l-232 -1159q-65 -326 -362 -326q-143 0 -227 68zM354 1409q0 46 32 78.5t81 32.5q42 0 71 -28t29 -65q0 -49 -32.5 -81.5t-81.5 -32.5q-42 0 -70.5 28.5t-28.5 67.5z" />
<glyph unicode="k" horiz-adv-x="1208" d="M96 0l303 1520h146l-205 -1022l741 579h207l-569 -444l387 -633h-170l-332 545l-301 -234l-61 -311h-146z" />
<glyph unicode="l" horiz-adv-x="550" d="M96 0l303 1520h146l-303 -1520h-146z" />
<glyph unicode="m" horiz-adv-x="2172" d="M96 0l213 1077h140l-35 -172q147 180 407 180q136 0 227 -56t126 -159q164 215 454 215q182 0 281.5 -91t99.5 -255q0 -48 -14 -127l-123 -612h-146l123 614q11 51 11 101q0 115 -67.5 177t-199.5 62q-164 0 -272.5 -91t-144.5 -269l-119 -594h-146l123 614q10 58 10 99 q0 117 -66.5 179t-199.5 62q-166 0 -275 -95.5t-147 -280.5l-114 -578h-146z" />
<glyph unicode="n" horiz-adv-x="1386" d="M96 0l213 1077h140l-35 -172q152 180 422 180q182 0 283.5 -92.5t101.5 -257.5q0 -53 -13 -123l-123 -612h-145l123 614q10 58 10 99q0 117 -70 179t-208 62q-174 0 -287.5 -95t-151.5 -281l-114 -578h-146z" />
<glyph unicode="o" horiz-adv-x="1284" d="M86 446q0 181 79.5 327t222.5 229t321 83q225 0 357 -122.5t132 -331.5q0 -182 -79.5 -328.5t-222.5 -229.5t-321 -83q-224 0 -356.5 124t-132.5 332zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5t-261.5 88.5q-202 0 -334.5 -143 t-132.5 -362z" />
<glyph unicode="p" d="M16 -397l293 1474h140l-35 -172q160 180 420 180q210 0 340.5 -122.5t130.5 -331.5q0 -183 -79 -329.5t-218.5 -229t-313.5 -82.5q-153 0 -258 60.5t-151 174.5l-123 -622h-146zM338 453q0 -158 92.5 -246t261.5 -88q201 0 333 142.5t132 361.5q0 159 -93 247t-261 88 q-202 0 -333.5 -142t-131.5 -363z" />
<glyph unicode="q" d="M86 446q0 136 46 255t126.5 203t193.5 132.5t242 48.5q156 0 262 -62.5t152 -180.5l47 235h139l-295 -1474h-145l111 559q-158 -172 -410 -172q-210 0 -339.5 122.5t-129.5 333.5zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5 t-261.5 88.5q-202 0 -334.5 -143t-132.5 -362z" />
<glyph unicode="r" horiz-adv-x="821" d="M96 0l213 1077h140l-37 -196q72 105 179.5 154.5t262.5 49.5l-27 -141q-7 0 -20 1t-21 1q-174 0 -285.5 -100t-150.5 -299l-108 -547h-146z" />
<glyph unicode="s" horiz-adv-x="1001" d="M-18 121l69 115q62 -54 162.5 -86.5t210.5 -32.5q154 0 236.5 50.5t82.5 141.5q0 49 -33.5 81.5t-87.5 49t-119 30t-130 31t-119 44.5t-87.5 79t-33.5 126q0 153 127 244t340 91q103 0 203.5 -27.5t161.5 -72.5l-62 -117q-61 46 -144.5 69.5t-170.5 23.5 q-147 0 -229.5 -54t-82.5 -143q0 -50 33.5 -83.5t87.5 -50.5t119.5 -30t130.5 -30.5t119 -43.5t87.5 -76.5t33.5 -123.5q0 -158 -129.5 -247t-347.5 -89q-130 0 -247 36.5t-181 94.5z" />
<glyph unicode="t" horiz-adv-x="831" d="M119 954l22 123h193l47 236h145l-47 -236h328l-25 -123h-327l-125 -628q-8 -50 -8 -68q0 -143 155 -143q93 0 166 55l35 -111q-92 -69 -240 -69q-122 0 -192 65.5t-70 180.5q0 44 8 83l125 635h-190z" />
<glyph unicode="u" horiz-adv-x="1378" d="M164 342q0 56 12 121l123 614h145l-122 -614q-11 -64 -11 -96q0 -119 69.5 -182.5t205.5 -63.5q172 0 285 96t151 281l115 579h145l-215 -1077h-139l35 172q-151 -182 -416 -182q-181 0 -282 93t-101 259z" />
<glyph unicode="v" horiz-adv-x="1110" d="M109 1077h145l223 -919l588 919h152l-693 -1077h-149z" />
<glyph unicode="w" horiz-adv-x="1800" d="M117 1077h133l174 -913l520 913h123l180 -913l512 913h142l-609 -1077h-141l-180 881l-504 -881h-139z" />
<glyph unicode="x" horiz-adv-x="1093" d="M-72 0l543 557l-299 520h154l241 -422l406 422h168l-514 -530l317 -547h-156l-256 449l-432 -449h-172z" />
<glyph unicode="y" horiz-adv-x="1110" d="M-215 -311l78 106q75 -78 190 -78q71 0 129 39t119 135l76 115l-268 1071h147l223 -917l588 917h150l-787 -1216q-99 -152 -184.5 -210.5t-204.5 -58.5q-76 0 -146.5 26t-109.5 71z" />
<glyph unicode="z" horiz-adv-x="1046" d="M-23 0l19 96l840 858h-654l25 123h850l-21 -96l-839 -860h682l-25 -121h-877z" />
<glyph unicode="{" horiz-adv-x="686" d="M115 500l24 123h72q47 0 74 24.5t37 79.5l108 537q27 132 105.5 194t224.5 62h76l-25 -123h-53q-155 0 -185 -148l-104 -520q-15 -78 -44.5 -116.5t-86.5 -53.5q70 -28 70 -110q0 -21 -7 -56l-98 -487q-6 -30 -6 -58q0 -122 125 -122h55l-24 -123h-76q-107 0 -168.5 54.5 t-61.5 156.5q0 34 9 77l100 504q4 24 4 35q0 70 -74 70h-71z" />
<glyph unicode="|" horiz-adv-x="604" d="M47 -397l383 1917h135l-383 -1917h-135z" />
<glyph unicode="}" horiz-adv-x="686" d="M-143 -397l24 123h56q155 0 184 147l104 520q17 80 45.5 117.5t83.5 52.5q-67 28 -67 111q0 25 6 55l98 488q6 30 6 57q0 123 -125 123h-55l23 123h77q107 0 168.5 -54.5t61.5 -156.5q0 -39 -8 -78l-101 -504q-4 -24 -4 -35q0 -69 74 -69h72l-25 -123h-72 q-47 0 -73.5 -25t-36.5 -80l-109 -536q-27 -132 -105.5 -194t-224.5 -62h-77z" />
<glyph unicode="~" horiz-adv-x="1177" d="M133 569q27 149 107.5 229.5t187.5 80.5q58 0 106 -21.5t80.5 -52.5t62 -61.5t65.5 -52t77 -21.5q73 0 125.5 53t71.5 143h100q-27 -149 -107 -229t-188 -80q-58 0 -105.5 21.5t-80 51.5t-62 60.5t-65.5 52t-76 21.5q-74 0 -126 -52t-73 -143h-100z" />
<glyph unicode="&#xa1;" horiz-adv-x="532" d="M8 -315l223 960h113l-164 -960h-172zM246 973q0 47 32.5 79.5t79.5 32.5q44 0 73.5 -29t29.5 -71q0 -46 -32.5 -78.5t-78.5 -32.5q-44 0 -74 29.5t-30 69.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="1153" d="M86 446q0 179 76.5 323t215 227.5t314.5 88.5l47 238h101l-47 -242q238 -24 344 -211l-123 -71q-76 134 -248 155l-166 -835q101 2 187.5 41.5t142.5 113.5l104 -79q-72 -96 -191.5 -150.5t-258.5 -54.5h-9l-47 -236h-100l49 244q-183 28 -287 147t-104 301zM231 453 q0 -136 70 -221t201 -107l166 831q-191 -12 -314 -152.5t-123 -350.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1304" d="M-47 0l27 131h231l106 535h-233l22 102h232l35 172q51 253 221.5 379.5t421.5 126.5q271 0 407 -113l-71 -121q-124 99 -344 99q-205 0 -326 -93t-162 -292l-31 -158h566l-21 -102h-565l-109 -535h803l-28 -131h-1182z" />
<glyph unicode="&#xa4;" horiz-adv-x="1433" d="M90 92l215 215q-127 149 -127 344q0 193 125 344l-213 213l86 92l217 -217q152 117 344 117q193 0 346 -115l217 215l86 -92l-212 -211q129 -148 129 -346q0 -195 -132 -346l215 -213l-86 -92l-221 219q-148 -113 -342 -113q-190 0 -340 115l-221 -221zM299 651 q0 -174 128.5 -298t309.5 -124q183 0 315 124.5t132 297.5q0 114 -60.5 212.5t-163.5 156t-223 57.5q-181 0 -309.5 -125.5t-128.5 -300.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1423" d="M129 266l21 90h450l39 193h-453l21 90h409l-436 795h156l405 -748l699 748h159l-739 -795h397l-18 -90h-448l-39 -193h448l-18 -90h-449l-53 -266h-152l54 266h-453z" />
<glyph unicode="&#xa6;" horiz-adv-x="604" d="M47 -397l143 716h136l-144 -716h-135zM287 803l143 717h135l-143 -717h-135z" />
<glyph unicode="&#xa7;" horiz-adv-x="1003" d="M-63 -80l65 109q138 -125 340 -125q150 0 238 62.5t88 158.5q0 52 -32 88.5t-82.5 57t-112 38t-123 38.5t-112 50t-82.5 81.5t-32 125.5q0 100 63.5 181t170.5 124q-86 69 -86 189q0 154 134 250t349 96q105 0 208.5 -28t164.5 -77l-64 -108q-125 94 -319 94 q-159 0 -250.5 -63t-91.5 -156q0 -47 24.5 -81t65 -53t92.5 -36t105.5 -29.5t105.5 -35t92.5 -51t65 -78.5t24.5 -117q0 -102 -63 -182t-168 -121q86 -69 86 -189q0 -155 -131.5 -251.5t-345.5 -96.5q-118 0 -219 36t-168 99zM221 616q0 -38 17 -68t42 -48.5t66 -35.5 t77 -27t87.5 -25t85.5 -27q104 26 162.5 86.5t58.5 140.5q0 39 -17 70t-41.5 50t-65.5 36.5t-76 27t-86.5 24t-84.5 26.5q-106 -27 -165.5 -89t-59.5 -141z" />
<glyph unicode="&#xa8;" horiz-adv-x="1228" d="M510 1368q0 42 27 70t65 28q36 0 60 -23t24 -59q0 -40 -27 -67t-65 -27q-36 0 -60 22t-24 56zM877 1368q0 42 26.5 70t65.5 28q35 0 59.5 -23t24.5 -59q0 -40 -28 -67t-67 -27q-34 0 -57.5 22.5t-23.5 55.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1656" d="M141 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM219 717q0 -179 83.5 -327t231.5 -234t328 -86 q135 0 255 51t206 137.5t136 207t50 255.5t-49 254t-134 204.5t-204.5 135t-255.5 49.5q-182 0 -331 -86t-232.5 -234t-83.5 -327zM455 717q0 187 120.5 306.5t307.5 119.5q102 0 187 -42t136 -116l-88 -65q-80 116 -237 116q-134 0 -221 -88t-87 -231t87 -231.5t221 -88.5 q157 0 237 117l88 -63q-51 -75 -136 -117.5t-187 -42.5q-187 0 -307.5 119.5t-120.5 306.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="821" d="M188 1038q0 98 78 152.5t211 54.5h234l6 29q4 28 4 41q0 63 -44 95t-124 32q-143 0 -240 -72l-32 74q54 39 131.5 61.5t156.5 22.5q124 0 192 -50t68 -147q0 -17 -8 -67l-76 -383h-102l19 92q-92 -99 -246 -99q-106 0 -167 46t-61 118zM291 1049q0 -48 38 -73.5 t111 -25.5q150 0 234 113l22 111h-219q-89 0 -137.5 -32.5t-48.5 -92.5z" />
<glyph unicode="&#xab;" horiz-adv-x="976" d="M102 539l373 389h141l-368 -393l207 -383h-133zM467 539l373 389h141l-369 -393l207 -383h-133z" />
<glyph unicode="&#xac;" horiz-adv-x="1179" d="M166 657l24 121h893l-96 -489h-129l74 368h-766z" />
<glyph unicode="&#xad;" horiz-adv-x="782" d="M115 492l24 126h537l-25 -126h-536z" />
<glyph unicode="&#xae;" horiz-adv-x="1656" d="M141 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM219 717q0 -179 83.5 -327t231.5 -234t328 -86 q135 0 255 51t206 137.5t136 207t50 255.5t-49 254t-134 204.5t-204.5 135t-255.5 49.5q-182 0 -331 -86t-232.5 -234t-83.5 -327zM582 303v827h321q151 0 238.5 -74t87.5 -200q0 -96 -47.5 -162t-134.5 -96l192 -295h-111l-180 277q-14 -2 -45 -2h-215v-275h-106zM686 668 h209q109 0 169 49.5t60 138.5q0 88 -60 136t-169 48h-209v-372z" />
<glyph unicode="&#xaf;" horiz-adv-x="1228" d="M455 1321l18 96h633l-21 -96h-630z" />
<glyph unicode="&#xb0;" horiz-adv-x="858" d="M219 1120q0 135 95.5 229.5t232.5 94.5q136 0 230.5 -94.5t94.5 -229.5q0 -136 -94 -230.5t-231 -94.5t-232.5 94.5t-95.5 230.5zM309 1120q0 -101 68.5 -170t169.5 -69q100 0 167.5 69t67.5 170q0 100 -68 169t-167 69q-101 0 -169.5 -68.5t-68.5 -169.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="1177" d="M35 0l24 123h891l-24 -123h-891zM178 721l25 123h381l73 366h129l-73 -366h381l-25 -123h-381l-74 -367h-129l74 367h-381z" />
<glyph unicode="&#xb2;" horiz-adv-x="880" d="M123 670l14 71l453 328q105 76 142.5 127t37.5 107q0 62 -52.5 96.5t-148.5 34.5q-162 0 -268 -95l-65 62q121 127 346 127q140 0 219.5 -59t79.5 -158q0 -74 -44.5 -139t-172.5 -158l-347 -252h496l-18 -92h-672z" />
<glyph unicode="&#xb3;" horiz-adv-x="880" d="M117 772l53 82q48 -44 124.5 -71t168.5 -27q128 0 200 47t72 135q0 137 -200 137h-97l15 76l301 276h-492l19 93h643l-15 -72l-313 -289q120 -7 185 -64.5t65 -150.5q0 -131 -106.5 -206.5t-284.5 -75.5q-110 0 -201.5 30.5t-136.5 79.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="1228" d="M616 1243l312 254h198l-358 -254h-152z" />
<glyph unicode="&#xb5;" d="M16 -397l295 1474h146l-123 -614q-10 -58 -10 -98q0 -117 69.5 -180.5t204.5 -63.5q171 0 283.5 96t150.5 281l115 579h145l-213 -1077h-139l35 172q-76 -91 -176.5 -136.5t-212.5 -45.5q-232 0 -318 143l-106 -530h-146z" />
<glyph unicode="&#xb6;" horiz-adv-x="1294" d="M164 1112q0 200 141.5 304t376.5 104h573l-344 -1725h-123l322 1610h-352l-322 -1610h-123l203 1014q-157 3 -254.5 82.5t-97.5 220.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="516" d="M152 549q0 50 33.5 83.5t80.5 33.5q45 0 75 -30t30 -75q0 -51 -33.5 -85t-79.5 -34q-44 0 -75 31.5t-31 75.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1228" d="M215 -403l45 77q53 -39 137 -39q66 0 103 30t37 79q0 39 -29 60.5t-84 21.5h-45l86 186h88l-53 -116q69 -9 106 -47.5t37 -98.5q0 -90 -71 -145.5t-185 -55.5q-107 0 -172 48z" />
<glyph unicode="&#xb9;" horiz-adv-x="880" d="M197 670l16 90h250l133 672h-231l18 88h342l-152 -760h224l-19 -90h-581z" />
<glyph unicode="&#xba;" horiz-adv-x="849" d="M199 1157q0 160 114.5 265.5t288.5 105.5q139 0 225 -77.5t86 -203.5q0 -161 -113 -267t-286 -106q-141 0 -228 78.5t-87 204.5zM305 1161q0 -89 60 -141.5t157 -52.5q125 0 206 78t81 196q0 90 -59.5 143.5t-157.5 53.5q-126 0 -206.5 -80t-80.5 -197z" />
<glyph unicode="&#xbb;" horiz-adv-x="978" d="M-4 152l371 393l-207 383h133l219 -389l-375 -387h-141zM360 152l371 393l-207 383h133l220 -389l-375 -387h-142z" />
<glyph unicode="&#xbc;" horiz-adv-x="2107" d="M180 584l17 90h249l134 672h-232l19 88h342l-152 -760h223l-18 -90h-582zM397 0l1256 1434h129l-1258 -1434h-127zM1217 217l16 74l526 559h129l-514 -543h346l37 187h101l-39 -187h184l-21 -90h-180l-45 -217h-106l43 217h-477z" />
<glyph unicode="&#xbd;" horiz-adv-x="2107" d="M180 584l17 90h249l134 672h-232l19 88h342l-152 -760h223l-18 -90h-582zM397 0l1256 1434h129l-1258 -1434h-127zM1217 0l14 72l452 327q106 76 143.5 127.5t37.5 106.5q0 62 -52.5 96.5t-148.5 34.5q-163 0 -268 -94l-66 61q121 127 346 127q140 0 219.5 -59t79.5 -158 q0 -74 -44.5 -139t-172.5 -158l-346 -252h496l-19 -92h-671z" />
<glyph unicode="&#xbe;" horiz-adv-x="2107" d="M100 686l54 82q48 -45 124 -71.5t168 -26.5q128 0 200.5 47t72.5 135q0 137 -201 137h-96l14 76l301 276h-491l18 93h643l-14 -72l-313 -289q120 -7 184.5 -64t64.5 -151q0 -131 -106.5 -207t-284.5 -76q-110 0 -201 31t-137 80zM397 0l1256 1434h129l-1258 -1434h-127z M1217 217l16 74l526 559h129l-514 -543h346l37 187h101l-39 -187h184l-21 -90h-180l-45 -217h-106l43 217h-477z" />
<glyph unicode="&#xbf;" horiz-adv-x="1161" d="M53 -14q0 73 24 134.5t63.5 105.5t88 83t99.5 75t96 72t80 85t49 104h152q-12 -67 -46.5 -123.5t-79.5 -97t-97 -77.5t-101 -72t-89 -73t-64.5 -88.5t-24.5 -109.5q0 -95 76.5 -149t214.5 -54q259 0 417 166l97 -88q-188 -207 -535 -207q-195 0 -307.5 84t-112.5 230z M588 975q0 46 32 78t78 32q44 0 73.5 -29t29.5 -69q0 -47 -32 -80t-77 -33q-44 0 -74 29.5t-30 71.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1468" d="M-102 0l942 1434h149l369 -1434h-152l-96 383h-801l-248 -383h-163zM387 506h694l-194 774zM645 1804h193l233 -254h-145z" />
<glyph unicode="&#xc1;" horiz-adv-x="1468" d="M-102 0l942 1434h149l369 -1434h-152l-96 383h-801l-248 -383h-163zM387 506h694l-194 774zM799 1550l311 254h199l-359 -254h-151z" />
<glyph unicode="&#xc2;" horiz-adv-x="1468" d="M-102 0l942 1434h149l369 -1434h-152l-96 383h-801l-248 -383h-163zM387 506h694l-194 774zM600 1550l293 254h147l236 -254h-131l-184 170l-222 -170h-139z" />
<glyph unicode="&#xc3;" horiz-adv-x="1468" d="M-102 0l942 1434h149l369 -1434h-152l-96 383h-801l-248 -383h-163zM387 506h694l-194 774zM623 1571q17 106 71.5 165.5t134.5 59.5q43 0 82.5 -21.5t65 -48t57.5 -48t62 -21.5q45 0 77 34.5t41 92.5h89q-17 -104 -71.5 -162.5t-135.5 -58.5q-35 0 -67 14.5t-56.5 35 t-47 40.5t-48 34.5t-50.5 14.5q-45 0 -76 -36t-40 -95h-88z" />
<glyph unicode="&#xc4;" horiz-adv-x="1468" d="M-102 0l942 1434h149l369 -1434h-152l-96 383h-801l-248 -383h-163zM387 506h694l-194 774zM692 1675q0 42 27.5 70.5t64.5 28.5q36 0 60 -23t24 -59q0 -40 -27 -67.5t-65 -27.5q-36 0 -60 22t-24 56zM1059 1675q0 42 26.5 70.5t65.5 28.5q35 0 59.5 -23t24.5 -59 q0 -41 -27.5 -68t-66.5 -27q-34 0 -58 22.5t-24 55.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1468" d="M-102 0l942 1434h149l369 -1434h-152l-96 383h-801l-248 -383h-163zM387 506h694l-194 774zM772 1784q0 89 61 152t152 63q90 0 151.5 -63t61.5 -152q0 -87 -61 -148t-152 -61t-152 61t-61 148zM846 1784q0 -59 39 -98t100 -39t100 39t39 98q0 60 -39.5 101.5t-99.5 41.5 t-99.5 -40.5t-39.5 -102.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2115" d="M-104 0l1153 1434h1093l-26 -131h-834l-102 -510h745l-29 -129h-741l-106 -533h862l-27 -131h-1010l76 383h-577l-307 -383h-170zM471 506h504l160 799h-25z" />
<glyph unicode="&#xc7;" horiz-adv-x="1472" d="M131 584q0 176 61.5 333t172.5 274.5t276.5 186t362.5 68.5q172 0 304.5 -54t204.5 -155l-106 -94q-136 168 -416 168q-159 0 -294 -57.5t-225 -156t-140.5 -229t-50.5 -276.5q0 -216 137.5 -342.5t394.5 -126.5q282 0 455 170l88 -100q-94 -99 -239.5 -152t-315.5 -53 h-25l-43 -92q69 -9 106.5 -47.5t37.5 -98.5q0 -90 -71 -145.5t-185 -55.5q-107 0 -172 48l45 77q53 -39 137 -39q66 0 102.5 30t36.5 79q0 39 -29 60.5t-84 21.5h-45l78 168q-265 29 -412 187t-147 403z" />
<glyph unicode="&#xc8;" horiz-adv-x="1370" d="M127 0l287 1434h983l-27 -131h-833l-103 -510h744l-25 -129h-743l-107 -533h862l-26 -131h-1012zM637 1804h192l234 -254h-145z" />
<glyph unicode="&#xc9;" horiz-adv-x="1370" d="M127 0l287 1434h983l-27 -131h-833l-103 -510h744l-25 -129h-743l-107 -533h862l-26 -131h-1012zM791 1550l311 254h198l-358 -254h-151z" />
<glyph unicode="&#xca;" horiz-adv-x="1370" d="M127 0l287 1434h983l-27 -131h-833l-103 -510h744l-25 -129h-743l-107 -533h862l-26 -131h-1012zM592 1550l293 254h147l236 -254h-131l-185 170l-221 -170h-139z" />
<glyph unicode="&#xcb;" horiz-adv-x="1370" d="M127 0l287 1434h983l-27 -131h-833l-103 -510h744l-25 -129h-743l-107 -533h862l-26 -131h-1012zM684 1675q0 42 27.5 70.5t64.5 28.5q36 0 60 -23t24 -59q0 -40 -27 -67.5t-65 -27.5q-36 0 -60 22t-24 56zM1051 1675q0 42 26.5 70.5t65.5 28.5q35 0 59.5 -23t24.5 -59 q0 -41 -27.5 -68t-66.5 -27q-34 0 -58 22.5t-24 55.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="618" d="M127 0l287 1434h149l-287 -1434h-149zM219 1804h193l233 -254h-145z" />
<glyph unicode="&#xcd;" horiz-adv-x="618" d="M127 0l287 1434h149l-287 -1434h-149zM373 1550l311 254h199l-359 -254h-151z" />
<glyph unicode="&#xce;" horiz-adv-x="618" d="M127 0l287 1434h149l-287 -1434h-149zM174 1550l293 254h147l236 -254h-131l-184 170l-222 -170h-139z" />
<glyph unicode="&#xcf;" horiz-adv-x="618" d="M127 0l287 1434h149l-287 -1434h-149zM266 1675q0 42 27.5 70.5t64.5 28.5q36 0 60 -23t24 -59q0 -40 -27 -67.5t-65 -27.5q-36 0 -60 22t-24 56zM633 1675q0 42 26.5 70.5t65.5 28.5q35 0 59.5 -23t24.5 -59q0 -41 -27.5 -68t-66.5 -27q-34 0 -58 22.5t-24 55.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1701" d="M51 670l25 121h219l129 643h545q320 0 498 -158t178 -426q0 -187 -64 -344.5t-179 -269t-282 -174t-366 -62.5h-617l133 670h-219zM313 131h445q168 0 306.5 53.5t232 148t145 225.5t51.5 286q0 213 -138 336t-403 123h-405l-103 -512h424l-22 -121h-426z" />
<glyph unicode="&#xd1;" horiz-adv-x="1665" d="M127 0l287 1434h123l686 -1188l237 1188h150l-287 -1434h-123l-686 1188l-238 -1188h-149zM721 1571q17 106 72 165.5t135 59.5q35 0 67 -14.5t56 -35t46.5 -40.5t47 -34.5t49.5 -14.5q45 0 77.5 35t41.5 92h88q-17 -104 -71.5 -162.5t-135.5 -58.5q-35 0 -67 14.5 t-56 35t-46.5 40.5t-48 34.5t-50.5 14.5q-45 0 -76.5 -36t-40.5 -95h-88z" />
<glyph unicode="&#xd2;" horiz-adv-x="1718" d="M131 584q0 176 61.5 333t171.5 274.5t274 186t357 68.5q313 0 489.5 -163t176.5 -433q0 -176 -62 -333.5t-172.5 -274.5t-274.5 -185.5t-357 -68.5q-313 0 -488.5 163t-175.5 433zM281 592q0 -216 137 -342.5t391 -126.5q207 0 368.5 99t246.5 262.5t85 359.5 q0 214 -137 340.5t-391 126.5q-207 0 -368.5 -99t-246.5 -262t-85 -358zM770 1804h193l233 -254h-145z" />
<glyph unicode="&#xd3;" horiz-adv-x="1718" d="M131 584q0 176 61.5 333t171.5 274.5t274 186t357 68.5q313 0 489.5 -163t176.5 -433q0 -176 -62 -333.5t-172.5 -274.5t-274.5 -185.5t-357 -68.5q-313 0 -488.5 163t-175.5 433zM281 592q0 -216 137 -342.5t391 -126.5q207 0 368.5 99t246.5 262.5t85 359.5 q0 214 -137 340.5t-391 126.5q-207 0 -368.5 -99t-246.5 -262t-85 -358zM924 1550l311 254h199l-359 -254h-151z" />
<glyph unicode="&#xd4;" horiz-adv-x="1718" d="M131 584q0 176 61.5 333t171.5 274.5t274 186t357 68.5q313 0 489.5 -163t176.5 -433q0 -176 -62 -333.5t-172.5 -274.5t-274.5 -185.5t-357 -68.5q-313 0 -488.5 163t-175.5 433zM281 592q0 -216 137 -342.5t391 -126.5q207 0 368.5 99t246.5 262.5t85 359.5 q0 214 -137 340.5t-391 126.5q-207 0 -368.5 -99t-246.5 -262t-85 -358zM725 1550l293 254h147l236 -254h-131l-185 170l-221 -170h-139z" />
<glyph unicode="&#xd5;" horiz-adv-x="1718" d="M131 584q0 176 61.5 333t171.5 274.5t274 186t357 68.5q313 0 489.5 -163t176.5 -433q0 -176 -62 -333.5t-172.5 -274.5t-274.5 -185.5t-357 -68.5q-313 0 -488.5 163t-175.5 433zM281 592q0 -216 137 -342.5t391 -126.5q207 0 368.5 99t246.5 262.5t85 359.5 q0 214 -137 340.5t-391 126.5q-207 0 -368.5 -99t-246.5 -262t-85 -358zM748 1571q17 106 71.5 165.5t134.5 59.5q43 0 82.5 -21.5t65 -48t57.5 -48t62 -21.5q45 0 77 34.5t41 92.5h88q-17 -104 -71 -162.5t-135 -58.5q-35 0 -67 14.5t-56.5 35t-47 40.5t-48 34.5 t-50.5 14.5q-45 0 -76 -36t-40 -95h-88z" />
<glyph unicode="&#xd6;" horiz-adv-x="1718" d="M131 584q0 176 61.5 333t171.5 274.5t274 186t357 68.5q313 0 489.5 -163t176.5 -433q0 -176 -62 -333.5t-172.5 -274.5t-274.5 -185.5t-357 -68.5q-313 0 -488.5 163t-175.5 433zM281 592q0 -216 137 -342.5t391 -126.5q207 0 368.5 99t246.5 262.5t85 359.5 q0 214 -137 340.5t-391 126.5q-207 0 -368.5 -99t-246.5 -262t-85 -358zM817 1675q0 42 27.5 70.5t64.5 28.5q36 0 60 -23t24 -59q0 -40 -27.5 -67.5t-64.5 -27.5q-36 0 -60 22t-24 56zM1184 1675q0 42 26.5 70.5t65.5 28.5q35 0 59.5 -23t24.5 -59q0 -41 -27.5 -68 t-66.5 -27q-34 0 -58 22.5t-24 55.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1177" d="M240 446l301 275l-236 272l90 84l236 -274l303 274l76 -90l-301 -274l233 -273l-88 -84l-236 275l-303 -275z" />
<glyph unicode="&#xd8;" horiz-adv-x="1718" d="M37 -143l270 293q-176 159 -176 434q0 176 61.5 333t171.5 274.5t274 186t357 68.5q246 0 410 -105l217 236h127l-268 -291q180 -160 180 -436q0 -176 -62 -333.5t-172.5 -274.5t-274.5 -185.5t-357 -68.5q-250 0 -412 104l-217 -235h-129zM281 592q0 -211 127 -332 l897 973q-125 78 -324 78q-207 0 -368.5 -99t-246.5 -262t-85 -358zM483 201q134 -78 326 -78q207 0 368.5 99t246.5 262.5t85 359.5q0 212 -129 332z" />
<glyph unicode="&#xd9;" horiz-adv-x="1622" d="M221 453q0 78 17 159l163 822h150l-164 -818q-16 -85 -16 -145q0 -171 98 -259.5t283 -88.5q211 0 337 120t177 373l164 818h147l-164 -828q-125 -618 -674 -618q-246 0 -382 123t-136 342zM721 1804h192l234 -254h-146z" />
<glyph unicode="&#xda;" horiz-adv-x="1622" d="M221 453q0 78 17 159l163 822h150l-164 -818q-16 -85 -16 -145q0 -171 98 -259.5t283 -88.5q211 0 337 120t177 373l164 818h147l-164 -828q-125 -618 -674 -618q-246 0 -382 123t-136 342zM874 1550l312 254h198l-358 -254h-152z" />
<glyph unicode="&#xdb;" horiz-adv-x="1622" d="M221 453q0 78 17 159l163 822h150l-164 -818q-16 -85 -16 -145q0 -171 98 -259.5t283 -88.5q211 0 337 120t177 373l164 818h147l-164 -828q-125 -618 -674 -618q-246 0 -382 123t-136 342zM676 1550l293 254h147l236 -254h-131l-185 170l-221 -170h-139z" />
<glyph unicode="&#xdc;" horiz-adv-x="1622" d="M221 453q0 78 17 159l163 822h150l-164 -818q-16 -85 -16 -145q0 -171 98 -259.5t283 -88.5q211 0 337 120t177 373l164 818h147l-164 -828q-125 -618 -674 -618q-246 0 -382 123t-136 342zM768 1675q0 42 27.5 70.5t64.5 28.5q36 0 60 -23t24 -59q0 -40 -27 -67.5 t-65 -27.5q-36 0 -60 22t-24 56zM1135 1675q0 42 26.5 70.5t65.5 28.5q35 0 59.5 -23t24.5 -59q0 -41 -27.5 -68t-66.5 -27q-34 0 -58 22.5t-24 55.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1300" d="M180 1434h152l332 -807l651 807h162l-764 -951l-97 -483h-149l98 494zM715 1550l311 254h199l-359 -254h-151z" />
<glyph unicode="&#xde;" horiz-adv-x="1470" d="M127 0l287 1434h149l-37 -187h369q242 0 375 -106t133 -301q0 -273 -178 -423.5t-488 -150.5h-407l-54 -266h-149zM356 397h385q245 0 377.5 110t132.5 320q0 140 -95.5 213.5t-274.5 73.5h-381z" />
<glyph unicode="&#xdf;" horiz-adv-x="1366" d="M94 0l203 1024q52 257 203.5 381.5t376.5 124.5q183 0 290 -90.5t107 -245.5q0 -135 -73 -243t-197 -163q116 -33 181.5 -113t65.5 -200q0 -220 -154.5 -352.5t-398.5 -132.5q-152 0 -237 37l41 123q82 -33 196 -33q183 0 294.5 94.5t111.5 257.5q0 116 -82 174.5 t-225 58.5h-115l25 125q190 2 308 97.5t118 257.5q0 104 -69 162.5t-198 58.5q-164 0 -273.5 -90t-146.5 -279l-206 -1034h-146z" />
<glyph unicode="&#xe0;" d="M86 446q0 136 46 255t126.5 203t193.5 132.5t242 48.5q153 0 258.5 -60t151.5 -173l45 225h145l-215 -1077h-139l35 170q-162 -180 -420 -180q-210 0 -339.5 122.5t-129.5 333.5zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5 t-261.5 88.5q-202 0 -334.5 -143t-132.5 -362zM522 1497h193l233 -254h-145z" />
<glyph unicode="&#xe1;" d="M86 446q0 136 46 255t126.5 203t193.5 132.5t242 48.5q153 0 258.5 -60t151.5 -173l45 225h145l-215 -1077h-139l35 170q-162 -180 -420 -180q-210 0 -339.5 122.5t-129.5 333.5zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5 t-261.5 88.5q-202 0 -334.5 -143t-132.5 -362zM676 1243l311 254h199l-359 -254h-151z" />
<glyph unicode="&#xe2;" d="M86 446q0 136 46 255t126.5 203t193.5 132.5t242 48.5q153 0 258.5 -60t151.5 -173l45 225h145l-215 -1077h-139l35 170q-162 -180 -420 -180q-210 0 -339.5 122.5t-129.5 333.5zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5 t-261.5 88.5q-202 0 -334.5 -143t-132.5 -362zM477 1243l293 254h148l235 -254h-131l-184 170l-222 -170h-139z" />
<glyph unicode="&#xe3;" d="M86 446q0 136 46 255t126.5 203t193.5 132.5t242 48.5q153 0 258.5 -60t151.5 -173l45 225h145l-215 -1077h-139l35 170q-162 -180 -420 -180q-210 0 -339.5 122.5t-129.5 333.5zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5 t-261.5 88.5q-202 0 -334.5 -143t-132.5 -362zM500 1264q17 106 72 165.5t135 59.5q35 0 67 -14.5t56 -35t46.5 -40.5t47 -34.5t49.5 -14.5q45 0 77.5 35t41.5 92h88q-17 -104 -71.5 -163t-135.5 -59q-43 0 -82.5 22t-64.5 48t-58 48t-63 22q-45 0 -76.5 -36t-40.5 -95h-88z " />
<glyph unicode="&#xe4;" d="M86 446q0 136 46 255t126.5 203t193.5 132.5t242 48.5q153 0 258.5 -60t151.5 -173l45 225h145l-215 -1077h-139l35 170q-162 -180 -420 -180q-210 0 -339.5 122.5t-129.5 333.5zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5 t-261.5 88.5q-202 0 -334.5 -143t-132.5 -362zM569 1368q0 42 27.5 70t65.5 28q36 0 59.5 -23t23.5 -59q0 -40 -27 -67t-65 -27q-36 0 -60 22t-24 56zM936 1368q0 42 26.5 70t65.5 28q35 0 59.5 -23t24.5 -59q0 -41 -27.5 -67.5t-66.5 -26.5q-34 0 -58 22.5t-24 55.5z" />
<glyph unicode="&#xe5;" d="M86 446q0 136 46 255t126.5 203t193.5 132.5t242 48.5q153 0 258.5 -60t151.5 -173l45 225h145l-215 -1077h-139l35 170q-162 -180 -420 -180q-210 0 -339.5 122.5t-129.5 333.5zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5 t-261.5 88.5q-202 0 -334.5 -143t-132.5 -362zM639 1427q0 89 61 152t152 63q90 0 151.5 -63t61.5 -152q0 -87 -61 -147.5t-152 -60.5t-152 60.5t-61 147.5zM713 1427q0 -59 39 -98t100 -39t100 39t39 98q0 61 -39.5 102.5t-99.5 41.5t-99.5 -41t-39.5 -103z" />
<glyph unicode="&#xe6;" horiz-adv-x="2021" d="M66 274q0 155 118 244.5t338 89.5h361l10 56q8 41 8 84q0 210 -264 210q-103 0 -202 -33.5t-169 -90.5l-49 114q86 65 204 101t236 36q305 0 363 -208q83 100 199 154t245 54q216 0 344.5 -121.5t128.5 -334.5q0 -67 -16 -129h-911q-2 -14 -2 -45q0 -336 372 -336 q222 0 351 135l67 -104q-78 -75 -191.5 -117.5t-238.5 -42.5q-163 0 -281.5 65.5t-171.5 194.5q-70 -134 -193 -197t-273 -63q-181 0 -282 76t-101 208zM209 283q0 -86 64 -132.5t190 -46.5q141 0 245 80t134 228l18 88h-348q-144 0 -223.5 -57.5t-79.5 -159.5zM1026 608 h770q12 168 -82 261.5t-256 93.5q-159 0 -275.5 -96.5t-156.5 -258.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1153" d="M86 446q0 183 79.5 328.5t223 228t324.5 82.5q304 0 424 -215l-123 -71q-92 159 -314 159q-203 0 -336 -143t-133 -362q0 -159 94 -246.5t265 -87.5q104 0 193 40t147 115l104 -79q-68 -92 -180 -145.5t-244 -59.5l-45 -96q69 -9 106.5 -47.5t37.5 -98.5 q0 -90 -71 -145.5t-185 -55.5q-108 0 -172 47l45 78q53 -39 137 -39q66 0 102.5 30t36.5 79q0 39 -29 60.5t-84 21.5h-45l78 168q-202 17 -319 138.5t-117 315.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1236" d="M86 449q0 181 77 326.5t213 227.5t306 82q216 0 342.5 -121.5t126.5 -334.5q0 -59 -14 -131h-906q-2 -14 -2 -43q0 -163 93.5 -249.5t277.5 -86.5q107 0 199.5 36t152.5 99l66 -104q-75 -75 -189.5 -117.5t-240.5 -42.5q-233 0 -367.5 123.5t-134.5 335.5zM246 606h768 q2 14 2 39q0 151 -92 234.5t-246 83.5q-161 0 -276.5 -97t-155.5 -260zM457 1495h192l234 -254h-146z" />
<glyph unicode="&#xe9;" horiz-adv-x="1236" d="M86 449q0 181 77 326.5t213 227.5t306 82q216 0 342.5 -121.5t126.5 -334.5q0 -59 -14 -131h-906q-2 -14 -2 -43q0 -163 93.5 -249.5t277.5 -86.5q107 0 199.5 36t152.5 99l66 -104q-75 -75 -189.5 -117.5t-240.5 -42.5q-233 0 -367.5 123.5t-134.5 335.5zM246 606h768 q2 14 2 39q0 151 -92 234.5t-246 83.5q-161 0 -276.5 -97t-155.5 -260zM610 1241l312 254h198l-358 -254h-152z" />
<glyph unicode="&#xea;" horiz-adv-x="1236" d="M86 449q0 181 77 326.5t213 227.5t306 82q216 0 342.5 -121.5t126.5 -334.5q0 -59 -14 -131h-906q-2 -14 -2 -43q0 -163 93.5 -249.5t277.5 -86.5q107 0 199.5 36t152.5 99l66 -104q-75 -75 -189.5 -117.5t-240.5 -42.5q-233 0 -367.5 123.5t-134.5 335.5zM246 606h768 q2 14 2 39q0 151 -92 234.5t-246 83.5q-161 0 -276.5 -97t-155.5 -260zM412 1241l293 254h147l235 -254h-131l-184 170l-221 -170h-139z" />
<glyph unicode="&#xeb;" horiz-adv-x="1236" d="M86 449q0 181 77 326.5t213 227.5t306 82q216 0 342.5 -121.5t126.5 -334.5q0 -59 -14 -131h-906q-2 -14 -2 -43q0 -163 93.5 -249.5t277.5 -86.5q107 0 199.5 36t152.5 99l66 -104q-75 -75 -189.5 -117.5t-240.5 -42.5q-233 0 -367.5 123.5t-134.5 335.5zM246 606h768 q2 14 2 39q0 151 -92 234.5t-246 83.5q-161 0 -276.5 -97t-155.5 -260zM504 1366q0 42 27 70t65 28q36 0 60 -23t24 -59q0 -40 -27 -67t-65 -27q-36 0 -60 22t-24 56zM870 1366q0 42 27 70t66 28q35 0 59.5 -23t24.5 -59q0 -40 -28 -67t-67 -27q-34 0 -58 22.5t-24 55.5z " />
<glyph unicode="&#xec;" horiz-adv-x="550" d="M96 0l213 1077h146l-213 -1077h-146zM125 1497h192l234 -254h-145z" />
<glyph unicode="&#xed;" horiz-adv-x="550" d="M96 0l213 1077h146l-213 -1077h-146zM279 1243l311 254h198l-358 -254h-151z" />
<glyph unicode="&#xee;" horiz-adv-x="550" d="M96 0l213 1077h146l-213 -1077h-146zM131 1243l246 254h141l187 -254h-127l-138 166l-176 -166h-133z" />
<glyph unicode="&#xef;" horiz-adv-x="550" d="M96 0l213 1077h146l-213 -1077h-146zM221 1368q0 41 27 69.5t65 28.5q33 0 55.5 -24t22.5 -58q0 -41 -25.5 -67.5t-64.5 -26.5q-34 0 -57 22t-23 56zM494 1368q0 41 27 69.5t65 28.5q33 0 55.5 -23.5t22.5 -58.5q0 -40 -26.5 -67t-64.5 -27q-34 0 -56.5 22t-22.5 56z" />
<glyph unicode="&#xf0;" horiz-adv-x="1296" d="M74 383q0 228 161 369t414 141q161 0 272.5 -75t143.5 -204q41 139 41 299q0 213 -115 322l-567 -223l-37 100l494 195q-88 39 -222 39q-168 0 -284 -43l-4 124q134 43 299 43q225 0 370 -100l215 84l39 -100l-164 -64q123 -145 123 -375q0 -139 -29.5 -272.5t-90 -252 t-145 -208t-202.5 -142t-254 -52.5q-205 0 -331.5 108.5t-126.5 286.5zM217 393q0 -131 86.5 -205.5t241.5 -74.5q111 0 210.5 48t161.5 135t62 191q0 131 -87 207t-239 76q-194 0 -315 -104.5t-121 -272.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1386" d="M96 0l213 1077h140l-35 -172q152 180 422 180q182 0 283.5 -92.5t101.5 -257.5q0 -53 -13 -123l-123 -612h-145l123 614q10 58 10 99q0 117 -70 179t-208 62q-174 0 -287.5 -95t-151.5 -281l-114 -578h-146zM522 1264q17 106 72 165.5t135 59.5q35 0 67 -14.5t56 -35 t46.5 -40.5t47 -34.5t49.5 -14.5q45 0 77.5 35t41.5 92h88q-17 -104 -71.5 -163t-135.5 -59q-43 0 -82.5 22t-64.5 48t-58 48t-63 22q-45 0 -76.5 -36t-40.5 -95h-88z" />
<glyph unicode="&#xf2;" horiz-adv-x="1284" d="M86 446q0 181 79.5 327t222.5 229t321 83q225 0 357 -122.5t132 -331.5q0 -182 -79.5 -328.5t-222.5 -229.5t-321 -83q-224 0 -356.5 124t-132.5 332zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5t-261.5 88.5q-202 0 -334.5 -143 t-132.5 -362zM487 1497h193l233 -254h-145z" />
<glyph unicode="&#xf3;" horiz-adv-x="1284" d="M86 446q0 181 79.5 327t222.5 229t321 83q225 0 357 -122.5t132 -331.5q0 -182 -79.5 -328.5t-222.5 -229.5t-321 -83q-224 0 -356.5 124t-132.5 332zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5t-261.5 88.5q-202 0 -334.5 -143 t-132.5 -362zM641 1243l311 254h199l-358 -254h-152z" />
<glyph unicode="&#xf4;" horiz-adv-x="1284" d="M86 446q0 181 79.5 327t222.5 229t321 83q225 0 357 -122.5t132 -331.5q0 -182 -79.5 -328.5t-222.5 -229.5t-321 -83q-224 0 -356.5 124t-132.5 332zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5t-261.5 88.5q-202 0 -334.5 -143 t-132.5 -362zM442 1243l293 254h148l235 -254h-131l-184 170l-221 -170h-140z" />
<glyph unicode="&#xf5;" horiz-adv-x="1284" d="M86 446q0 181 79.5 327t222.5 229t321 83q225 0 357 -122.5t132 -331.5q0 -182 -79.5 -328.5t-222.5 -229.5t-321 -83q-224 0 -356.5 124t-132.5 332zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5t-261.5 88.5q-202 0 -334.5 -143 t-132.5 -362zM465 1264q17 106 72 165.5t135 59.5q35 0 67 -14.5t56 -35t46.5 -40.5t47 -34.5t49.5 -14.5q45 0 77.5 35t41.5 92h88q-17 -104 -71.5 -163t-135.5 -59q-43 0 -82.5 22t-64.5 48t-58 48t-63 22q-45 0 -76.5 -36t-40.5 -95h-88z" />
<glyph unicode="&#xf6;" horiz-adv-x="1284" d="M86 446q0 181 79.5 327t222.5 229t321 83q225 0 357 -122.5t132 -331.5q0 -182 -79.5 -328.5t-222.5 -229.5t-321 -83q-224 0 -356.5 124t-132.5 332zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5t-261.5 88.5q-202 0 -334.5 -143 t-132.5 -362zM535 1368q0 42 27 70t65 28q36 0 60 -23t24 -59q0 -40 -27.5 -67t-65.5 -27q-35 0 -59 22t-24 56zM901 1368q0 42 26.5 70t65.5 28q35 0 59.5 -23t24.5 -59q0 -41 -27.5 -67.5t-66.5 -26.5q-34 0 -58 22.5t-24 55.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1177" d="M166 655l24 121h893l-24 -121h-893zM449 319q0 45 30 75t72 30q39 0 65.5 -25.5t26.5 -66.5q0 -44 -30.5 -75.5t-71.5 -31.5q-38 0 -65 27t-27 67zM604 1104q0 44 30.5 74t72.5 30q39 0 65.5 -26.5t26.5 -65.5q0 -45 -30 -75.5t-73 -30.5q-38 0 -65 27t-27 67z" />
<glyph unicode="&#xf8;" horiz-adv-x="1284" d="M-2 -143l221 258q-133 121 -133 331q0 181 79.5 327t222.5 229t321 83q168 0 286 -71l176 205h105l-217 -250q139 -124 139 -338q0 -182 -79.5 -328.5t-222.5 -229.5t-321 -83q-171 0 -292 76l-181 -209h-104zM231 453q0 -148 80 -234l594 690q-82 49 -207 49 q-202 0 -334.5 -143t-132.5 -362zM373 170q85 -51 213 -51q202 0 334.5 142.5t132.5 361.5q0 152 -84 239z" />
<glyph unicode="&#xf9;" horiz-adv-x="1378" d="M164 342q0 56 12 121l123 614h145l-122 -614q-11 -64 -11 -96q0 -119 69.5 -182.5t205.5 -63.5q172 0 285 96t151 281l115 579h145l-215 -1077h-139l35 172q-151 -182 -416 -182q-181 0 -282 93t-101 259zM532 1497h193l233 -254h-145z" />
<glyph unicode="&#xfa;" horiz-adv-x="1378" d="M164 342q0 56 12 121l123 614h145l-122 -614q-11 -64 -11 -96q0 -119 69.5 -182.5t205.5 -63.5q172 0 285 96t151 281l115 579h145l-215 -1077h-139l35 172q-151 -182 -416 -182q-181 0 -282 93t-101 259zM686 1243l311 254h199l-358 -254h-152z" />
<glyph unicode="&#xfb;" horiz-adv-x="1378" d="M164 342q0 56 12 121l123 614h145l-122 -614q-11 -64 -11 -96q0 -119 69.5 -182.5t205.5 -63.5q172 0 285 96t151 281l115 579h145l-215 -1077h-139l35 172q-151 -182 -416 -182q-181 0 -282 93t-101 259zM487 1243l293 254h148l235 -254h-131l-184 170l-221 -170h-140z " />
<glyph unicode="&#xfc;" horiz-adv-x="1378" d="M164 342q0 56 12 121l123 614h145l-122 -614q-11 -64 -11 -96q0 -119 69.5 -182.5t205.5 -63.5q172 0 285 96t151 281l115 579h145l-215 -1077h-139l35 172q-151 -182 -416 -182q-181 0 -282 93t-101 259zM580 1368q0 42 27 70t65 28q36 0 60 -23t24 -59q0 -40 -27 -67 t-65 -27q-36 0 -60 22t-24 56zM946 1368q0 42 26.5 70t65.5 28q35 0 59.5 -23t24.5 -59q0 -41 -27.5 -67.5t-66.5 -26.5q-34 0 -58 22.5t-24 55.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1110" d="M-215 -311l78 106q75 -78 190 -78q71 0 129 39t119 135l76 115l-268 1071h147l223 -917l588 917h150l-787 -1216q-99 -152 -184.5 -210.5t-204.5 -58.5q-76 0 -146.5 26t-109.5 71zM528 1243l312 254h198l-358 -254h-152z" />
<glyph unicode="&#xfe;" d="M16 -397l383 1917h146l-121 -605q160 170 410 170q210 0 340.5 -122.5t130.5 -331.5q0 -183 -79 -329.5t-218.5 -229t-313.5 -82.5q-154 0 -258.5 60.5t-150.5 176.5l-123 -624h-146zM338 453q0 -158 92.5 -246t261.5 -88q201 0 333 142.5t132 361.5q0 159 -93 247 t-261 88q-202 0 -333.5 -142t-131.5 -363z" />
<glyph unicode="&#xff;" horiz-adv-x="1110" d="M-215 -311l78 106q75 -78 190 -78q71 0 129 39t119 135l76 115l-268 1071h147l223 -917l588 917h150l-787 -1216q-99 -152 -184.5 -210.5t-204.5 -58.5q-76 0 -146.5 26t-109.5 71zM422 1368q0 42 27 70t65 28q36 0 60 -23t24 -59q0 -40 -27 -67t-65 -27q-36 0 -60 22 t-24 56zM788 1368q0 42 27 70t66 28q35 0 59.5 -23t24.5 -59q0 -40 -28 -67t-67 -27q-34 0 -58 22.5t-24 55.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2295" d="M131 584q0 187 63.5 344.5t178.5 269t282 174t367 62.5h1300l-26 -131h-834l-102 -510h743l-24 -129h-744l-106 -533h862l-27 -131h-1257q-320 0 -498 158t-178 426zM281 592q0 -215 138 -338t402 -123h258l234 1172h-295q-168 0 -307 -53.5t-233 -148t-145.5 -225.5 t-51.5 -284z" />
<glyph unicode="&#x153;" horiz-adv-x="2189" d="M86 446q0 182 77.5 327.5t217.5 228.5t315 83q166 0 282.5 -71.5t166.5 -200.5q77 128 206 200t285 72q214 0 340.5 -121.5t126.5 -334.5q0 -74 -14 -131h-907q-2 -14 -2 -43q0 -163 94 -249.5t278 -86.5q107 0 200 36t153 99l65 -104q-75 -75 -189 -117.5t-241 -42.5 q-176 0 -295.5 72t-167.5 204q-81 -130 -214 -203t-298 -73q-217 0 -348 124.5t-131 331.5zM231 453q0 -158 93 -246t262 -88q202 0 334.5 142.5t132.5 361.5q0 158 -93.5 246.5t-261.5 88.5q-202 0 -334.5 -143t-132.5 -362zM1196 606h770q2 14 2 39q0 151 -92 234.5 t-246 83.5q-161 0 -277.5 -97t-156.5 -260z" />
<glyph unicode="&#x178;" horiz-adv-x="1300" d="M180 1434h152l332 -807l651 807h162l-764 -951l-97 -483h-149l98 494zM608 1675q0 42 27.5 70.5t64.5 28.5q36 0 60 -23t24 -59q0 -40 -27 -67.5t-65 -27.5q-36 0 -60 22t-24 56zM975 1675q0 42 26.5 70.5t65.5 28.5q35 0 59.5 -23t24.5 -59q0 -41 -27.5 -68t-66.5 -27 q-34 0 -58 22.5t-24 55.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1228" d="M418 1243l293 254h147l236 -254h-131l-185 170l-221 -170h-139z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1228" d="M440 1264q17 106 72 165.5t135 59.5q35 0 67 -14.5t56 -35t46.5 -40.5t47 -34.5t49.5 -14.5q45 0 77.5 35t41.5 92h88q-17 -104 -71.5 -163t-135.5 -59q-43 0 -82.5 22t-64.5 48t-58 48t-63 22q-45 0 -76.5 -36t-40.5 -95h-88z" />
<glyph unicode="&#x2000;" horiz-adv-x="999" />
<glyph unicode="&#x2001;" horiz-adv-x="1999" />
<glyph unicode="&#x2002;" horiz-adv-x="999" />
<glyph unicode="&#x2003;" horiz-adv-x="1999" />
<glyph unicode="&#x2004;" horiz-adv-x="666" />
<glyph unicode="&#x2005;" horiz-adv-x="499" />
<glyph unicode="&#x2006;" horiz-adv-x="333" />
<glyph unicode="&#x2007;" horiz-adv-x="333" />
<glyph unicode="&#x2008;" horiz-adv-x="249" />
<glyph unicode="&#x2009;" horiz-adv-x="399" />
<glyph unicode="&#x200a;" horiz-adv-x="111" />
<glyph unicode="&#x2010;" horiz-adv-x="782" d="M115 492l24 126h537l-25 -126h-536z" />
<glyph unicode="&#x2011;" horiz-adv-x="782" d="M115 492l24 126h537l-25 -126h-536z" />
<glyph unicode="&#x2012;" horiz-adv-x="782" d="M115 492l24 126h537l-25 -126h-536z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M-6 504l20 104h1024l-20 -104h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M-6 504l20 104h2048l-20 -104h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="434" d="M223 1114q0 42 37 109l158 297h98l-131 -299q26 -12 41.5 -38t15.5 -57q0 -50 -33.5 -83t-80.5 -33q-44 0 -74.5 30t-30.5 74z" />
<glyph unicode="&#x2019;" horiz-adv-x="434" d="M211 1020l129 299q-26 12 -41.5 37.5t-15.5 56.5q0 50 33.5 83.5t80.5 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -41 -37 -108l-156 -295h-98z" />
<glyph unicode="&#x201a;" horiz-adv-x="434" d="M-53 -299l131 301q-26 12 -42 37.5t-16 56.5q0 50 34 83.5t81 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -43 -39 -108l-156 -297h-98z" />
<glyph unicode="&#x201c;" horiz-adv-x="782" d="M223 1114q0 42 37 109l158 297h98l-131 -299q26 -12 41.5 -38t15.5 -57q0 -50 -33.5 -83t-80.5 -33q-44 0 -74.5 30t-30.5 74zM571 1114q0 42 37 109l158 297h98l-131 -299q26 -12 42 -38t16 -57q0 -50 -34 -83t-81 -33q-44 0 -74.5 30t-30.5 74z" />
<glyph unicode="&#x201d;" horiz-adv-x="782" d="M211 1020l129 299q-26 12 -41.5 37.5t-15.5 56.5q0 50 33.5 83.5t80.5 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -41 -37 -108l-156 -295h-98zM559 1020l129 299q-26 12 -41.5 37.5t-15.5 56.5q0 50 33.5 83.5t80.5 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -41 -37 -108l-156 -295 h-98z" />
<glyph unicode="&#x201e;" horiz-adv-x="782" d="M-53 -299l131 301q-26 12 -42 37.5t-16 56.5q0 50 34 83.5t81 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -43 -39 -108l-156 -297h-98zM295 -299l131 301q-26 12 -41.5 37.5t-15.5 56.5q0 50 33.5 83.5t80.5 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -43 -39 -108l-156 -297h-98z " />
<glyph unicode="&#x2022;" horiz-adv-x="604" d="M154 549q0 70 46.5 118t114.5 48q60 0 103 -42.5t43 -103.5q0 -70 -48 -118t-116 -48q-60 0 -101.5 42.5t-41.5 103.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1325" d="M20 96q0 50 34 83.5t81 33.5q44 0 74.5 -30.5t30.5 -76.5q0 -50 -33.5 -83t-79.5 -33q-44 0 -75.5 31t-31.5 75zM465 96q0 50 34 83.5t81 33.5q44 0 74 -30.5t30 -76.5q0 -50 -33.5 -83t-79.5 -33q-44 0 -75 31t-31 75zM911 96q0 50 34 83.5t81 33.5q44 0 74 -30.5 t30 -76.5q0 -50 -33.5 -83t-78.5 -33q-44 0 -75.5 31t-31.5 75z" />
<glyph unicode="&#x202f;" horiz-adv-x="399" />
<glyph unicode="&#x2039;" horiz-adv-x="612" d="M102 539l373 389h141l-368 -393l207 -383h-133z" />
<glyph unicode="&#x203a;" horiz-adv-x="614" d="M-4 152l371 393l-207 383h133l219 -389l-375 -387h-141z" />
<glyph unicode="&#x205f;" horiz-adv-x="499" />
<glyph unicode="&#x20ac;" horiz-adv-x="1644" d="M59 530l19 91h223q6 106 29 192h-213l18 90h221q91 244 307 393.5t513 149.5q173 0 305.5 -54t204.5 -155l-107 -94q-136 168 -416 168q-227 0 -396 -111t-251 -297h717l-19 -90h-731q-27 -92 -30 -192h723l-19 -91h-702q21 -189 156.5 -298t371.5 -109q284 0 457 170 l88 -100q-96 -99 -240.5 -152t-314.5 -53q-300 0 -476 146.5t-194 395.5h-244z" />
<glyph unicode="&#x2122;" horiz-adv-x="2091" d="M168 1341l18 93h752l-18 -93h-320l-151 -757h-113l151 757h-319zM899 584l170 850h90l273 -615l507 615h97l-170 -850h-109l129 643l-448 -539h-56l-245 553l-131 -657h-107z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1075" d="M0 0v1075h1075v-1075h-1075z" />
<hkern u1="&#x2f;" u2="&#xf0;" k="98" />
<hkern u1="&#x2f;" u2="i" k="-10" />
<hkern u1="F" u2="&#xee;" k="-10" />
<hkern u1="Q" u2="&#x7d;" k="-10" />
<hkern u1="Q" u2="_" k="-82" />
<hkern u1="Q" u2="]" k="-10" />
<hkern u1="Q" u2="&#x2f;" k="-76" />
<hkern u1="T" u2="&#xef;" k="-31" />
<hkern u1="T" u2="&#xee;" k="-61" />
<hkern u1="T" u2="&#xec;" k="-35" />
<hkern u1="V" u2="&#xef;" k="-31" />
<hkern u1="V" u2="&#xee;" k="-66" />
<hkern u1="V" u2="&#xec;" k="-35" />
<hkern u1="V" u2="i" k="37" />
<hkern u1="W" u2="&#xef;" k="-31" />
<hkern u1="W" u2="&#xee;" k="-66" />
<hkern u1="W" u2="&#xec;" k="-35" />
<hkern u1="W" u2="i" k="37" />
<hkern u1="Y" u2="&#xef;" k="-35" />
<hkern u1="Y" u2="&#xee;" k="-51" />
<hkern u1="Y" u2="&#xec;" k="-61" />
<hkern u1="Y" u2="i" k="59" />
<hkern u1="_" u2="y" k="-41" />
<hkern u1="f" u2="&#xef;" k="-123" />
<hkern u1="f" u2="&#xee;" k="-92" />
<hkern u1="f" u2="&#xec;" k="-139" />
<hkern u1="i" u2="\" k="102" />
<hkern u1="j" u2="\" k="102" />
<hkern u1="q" u2="j" k="-102" />
<hkern u1="&#xa3;" u2="&#xef;" k="-10" />
<hkern u1="&#xa3;" u2="&#xee;" k="-51" />
<hkern u1="&#xbf;" u2="y" k="45" />
<hkern u1="&#xdd;" u2="&#xef;" k="-35" />
<hkern u1="&#xdd;" u2="&#xee;" k="-51" />
<hkern u1="&#xdd;" u2="&#xec;" k="-61" />
<hkern u1="&#xdd;" u2="i" k="59" />
<hkern u1="&#xee;" u2="&#xba;" k="-164" />
<hkern u1="&#xee;" u2="&#xaa;" k="-164" />
<hkern u1="&#xee;" u2="&#x3f;" k="-47" />
<hkern u1="&#xee;" u2="&#x2a;" k="-164" />
<hkern u1="&#x178;" u2="&#xef;" k="-35" />
<hkern u1="&#x178;" u2="&#xee;" k="-51" />
<hkern u1="&#x178;" u2="&#xec;" k="-61" />
<hkern u1="&#x178;" u2="i" k="59" />
<hkern g1="ampersand" 	g2="ampersand" 	k="16" />
<hkern g1="ampersand" 	g2="backslash" 	k="164" />
<hkern g1="ampersand" 	g2="bracketright,braceright" 	k="10" />
<hkern g1="ampersand" 	g2="colon,semicolon" 	k="12" />
<hkern g1="ampersand" 	g2="degree" 	k="76" />
<hkern g1="ampersand" 	g2="exclam" 	k="23" />
<hkern g1="ampersand" 	g2="exclamdown" 	k="20" />
<hkern g1="ampersand" 	g2="four" 	k="-4" />
<hkern g1="ampersand" 	g2="one" 	k="61" />
<hkern g1="ampersand" 	g2="paragraph" 	k="82" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-10" />
<hkern g1="ampersand" 	g2="question" 	k="160" />
<hkern g1="ampersand" 	g2="questiondown" 	k="4" />
<hkern g1="ampersand" 	g2="quoteleft,quotedblleft" 	k="72" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="109" />
<hkern g1="ampersand" 	g2="seven" 	k="37" />
<hkern g1="ampersand" 	g2="slash" 	k="-88" />
<hkern g1="ampersand" 	g2="three" 	k="4" />
<hkern g1="ampersand" 	g2="trademark" 	k="66" />
<hkern g1="ampersand" 	g2="two" 	k="10" />
<hkern g1="ampersand" 	g2="underscore" 	k="-92" />
<hkern g1="currency" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="currency" 	g2="questiondown" 	k="16" />
<hkern g1="currency" 	g2="seven" 	k="31" />
<hkern g1="currency" 	g2="two" 	k="18" />
<hkern g1="currency" 	g2="underscore" 	k="16" />
<hkern g1="currency" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-16" />
<hkern g1="degree" 	g2="backslash" 	k="-106" />
<hkern g1="degree" 	g2="four" 	k="82" />
<hkern g1="degree" 	g2="one" 	k="-86" />
<hkern g1="degree" 	g2="percent" 	k="-45" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="127" />
<hkern g1="degree" 	g2="question" 	k="-41" />
<hkern g1="degree" 	g2="questiondown" 	k="133" />
<hkern g1="degree" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="degree" 	g2="seven" 	k="-92" />
<hkern g1="degree" 	g2="slash" 	k="119" />
<hkern g1="degree" 	g2="three" 	k="-33" />
<hkern g1="degree" 	g2="two" 	k="-61" />
<hkern g1="degree" 	g2="five" 	k="-16" />
<hkern g1="degree" 	g2="nine" 	k="-78" />
<hkern g1="degree" 	g2="zero,six" 	k="-8" />
<hkern g1="percent" 	g2="backslash" 	k="78" />
<hkern g1="percent" 	g2="degree" 	k="57" />
<hkern g1="percent" 	g2="four" 	k="-70" />
<hkern g1="percent" 	g2="one" 	k="41" />
<hkern g1="percent" 	g2="percent" 	k="244" />
<hkern g1="percent" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="percent" 	g2="question" 	k="119" />
<hkern g1="percent" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="percent" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="percent" 	g2="quotedbl,quotesingle" 	k="57" />
<hkern g1="percent" 	g2="seven" 	k="37" />
<hkern g1="percent" 	g2="slash" 	k="-2" />
<hkern g1="percent" 	g2="three" 	k="-20" />
<hkern g1="percent" 	g2="two" 	k="-20" />
<hkern g1="percent" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="percent" 	g2="five" 	k="-41" />
<hkern g1="percent" 	g2="parenright" 	k="41" />
<hkern g1="percent" 	g2="eight" 	k="-41" />
<hkern g1="section" 	g2="four" 	k="-37" />
<hkern g1="section" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="section" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="section" 	g2="seven" 	k="-16" />
<hkern g1="section" 	g2="slash" 	k="-51" />
<hkern g1="section" 	g2="underscore" 	k="-16" />
<hkern g1="section" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="section" 	g2="eight" 	k="-20" />
<hkern g1="trademark" 	g2="backslash" 	k="-82" />
<hkern g1="trademark" 	g2="exclamdown" 	k="-4" />
<hkern g1="trademark" 	g2="questiondown" 	k="31" />
<hkern g1="trademark" 	g2="seven" 	k="-25" />
<hkern g1="trademark" 	g2="slash" 	k="92" />
<hkern g1="trademark" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="trademark" 	g2="nine" 	k="-61" />
<hkern g1="yen" 	g2="backslash" 	k="-102" />
<hkern g1="yen" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="yen" 	g2="colon,semicolon" 	k="82" />
<hkern g1="yen" 	g2="exclam" 	k="-14" />
<hkern g1="yen" 	g2="exclamdown" 	k="41" />
<hkern g1="yen" 	g2="four" 	k="20" />
<hkern g1="yen" 	g2="one" 	k="-61" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="yen" 	g2="questiondown" 	k="82" />
<hkern g1="yen" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="yen" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="yen" 	g2="seven" 	k="-57" />
<hkern g1="yen" 	g2="slash" 	k="61" />
<hkern g1="yen" 	g2="three" 	k="-20" />
<hkern g1="yen" 	g2="underscore" 	k="20" />
<hkern g1="yen" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="45" />
<hkern g1="yen" 	g2="zero,six" 	k="23" />
<hkern g1="yen" 	g2="parenright" 	k="-20" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="backslash" 	g2="ampersand" 	k="-37" />
<hkern g1="backslash" 	g2="backslash" 	k="133" />
<hkern g1="backslash" 	g2="bracketright,braceright" 	k="-72" />
<hkern g1="backslash" 	g2="colon,semicolon" 	k="-92" />
<hkern g1="backslash" 	g2="degree" 	k="123" />
<hkern g1="backslash" 	g2="exclamdown" 	k="-82" />
<hkern g1="backslash" 	g2="five" 	k="-4" />
<hkern g1="backslash" 	g2="four" 	k="16" />
<hkern g1="backslash" 	g2="guillemotright,guilsinglright" 	k="-23" />
<hkern g1="backslash" 	g2="one" 	k="41" />
<hkern g1="backslash" 	g2="paragraph" 	k="61" />
<hkern g1="backslash" 	g2="parenright" 	k="-61" />
<hkern g1="backslash" 	g2="percent" 	k="20" />
<hkern g1="backslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-211" />
<hkern g1="backslash" 	g2="question" 	k="123" />
<hkern g1="backslash" 	g2="questiondown" 	k="-61" />
<hkern g1="backslash" 	g2="quoteleft,quotedblleft" 	k="113" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="113" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="backslash" 	g2="section" 	k="-61" />
<hkern g1="backslash" 	g2="seven" 	k="102" />
<hkern g1="backslash" 	g2="slash" 	k="-31" />
<hkern g1="backslash" 	g2="three" 	k="-20" />
<hkern g1="backslash" 	g2="trademark" 	k="123" />
<hkern g1="backslash" 	g2="two" 	k="-20" />
<hkern g1="backslash" 	g2="underscore" 	k="-287" />
<hkern g1="backslash" 	g2="zero,six" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="backslash" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="exclamdown" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="25" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="-31" />
<hkern g1="bracketleft,braceleft" 	g2="paragraph" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="parenright" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="question" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="bracketleft,braceleft" 	g2="seven" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="slash" 	k="-72" />
<hkern g1="bracketleft,braceleft" 	g2="three" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="trademark" 	k="-92" />
<hkern g1="bracketleft,braceleft" 	g2="two" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="underscore" 	k="-51" />
<hkern g1="bracketleft,braceleft" 	g2="exclam" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="bracketleft,braceleft" 	g2="yen" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="35" />
<hkern g1="colon,semicolon" 	g2="question" 	k="20" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="-16" />
<hkern g1="colon,semicolon" 	g2="slash" 	k="-92" />
<hkern g1="colon,semicolon" 	g2="underscore" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="yen" 	k="82" />
<hkern g1="exclam" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="exclam" 	g2="one" 	k="-20" />
<hkern g1="exclam" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="exclam" 	g2="quoteleft,quotedblleft" 	k="-4" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="-4" />
<hkern g1="exclam" 	g2="quotedbl,quotesingle" 	k="-4" />
<hkern g1="exclam" 	g2="seven" 	k="-16" />
<hkern g1="exclam" 	g2="trademark" 	k="-10" />
<hkern g1="exclam" 	g2="yen" 	k="-14" />
<hkern g1="exclamdown" 	g2="backslash" 	k="113" />
<hkern g1="exclamdown" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="exclamdown" 	g2="one" 	k="41" />
<hkern g1="exclamdown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="exclamdown" 	g2="question" 	k="4" />
<hkern g1="exclamdown" 	g2="quoteleft,quotedblleft" 	k="-4" />
<hkern g1="exclamdown" 	g2="quoteright,quotedblright" 	k="-4" />
<hkern g1="exclamdown" 	g2="slash" 	k="-82" />
<hkern g1="exclamdown" 	g2="trademark" 	k="4" />
<hkern g1="exclamdown" 	g2="underscore" 	k="-14" />
<hkern g1="exclamdown" 	g2="yen" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="119" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="five" 	k="-16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="-16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="one" 	k="4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="question" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="questiondown" 	k="-16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="slash" 	k="-23" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="underscore" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="backslash" 	k="133" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="four" 	k="-16" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="one" 	k="35" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="paragraph" 	k="180" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="percent" 	k="78" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="12" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="question" 	k="82" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="section" 	k="-20" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="seven" 	k="20" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="three" 	k="41" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="two" 	k="25" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="zero,six" 	k="-16" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-4" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="yen" 	k="45" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="currency,Euro" 	k="-16" />
<hkern g1="parenleft" 	g2="backslash" 	k="-72" />
<hkern g1="parenleft" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="parenleft" 	g2="four" 	k="41" />
<hkern g1="parenleft" 	g2="one" 	k="-20" />
<hkern g1="parenleft" 	g2="slash" 	k="-66" />
<hkern g1="parenleft" 	g2="trademark" 	k="-51" />
<hkern g1="parenleft" 	g2="underscore" 	k="-4" />
<hkern g1="parenleft" 	g2="yen" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="201" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="degree" 	k="127" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclamdown" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="27" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="guillemotright,guilsinglright" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="paragraph" 	k="250" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="127" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="106" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="questiondown" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="113" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="slash" 	k="-211" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="two" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="underscore" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero,six" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclam" 	k="-4" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="currency,Euro" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="-20" />
<hkern g1="question" 	g2="ampersand" 	k="47" />
<hkern g1="question" 	g2="degree" 	k="-20" />
<hkern g1="question" 	g2="exclamdown" 	k="16" />
<hkern g1="question" 	g2="four" 	k="102" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="question" 	g2="question" 	k="66" />
<hkern g1="question" 	g2="questiondown" 	k="225" />
<hkern g1="question" 	g2="quoteleft,quotedblleft" 	k="-45" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="-45" />
<hkern g1="question" 	g2="slash" 	k="61" />
<hkern g1="question" 	g2="three" 	k="31" />
<hkern g1="question" 	g2="underscore" 	k="43" />
<hkern g1="question" 	g2="exclam" 	k="16" />
<hkern g1="question" 	g2="yen" 	k="31" />
<hkern g1="question" 	g2="eight" 	k="20" />
<hkern g1="question" 	g2="bracketleft" 	k="39" />
<hkern g1="question" 	g2="nine" 	k="-20" />
<hkern g1="questiondown" 	g2="ampersand" 	k="37" />
<hkern g1="questiondown" 	g2="backslash" 	k="201" />
<hkern g1="questiondown" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="questiondown" 	g2="colon,semicolon" 	k="4" />
<hkern g1="questiondown" 	g2="degree" 	k="113" />
<hkern g1="questiondown" 	g2="four" 	k="86" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="questiondown" 	g2="one" 	k="86" />
<hkern g1="questiondown" 	g2="paragraph" 	k="178" />
<hkern g1="questiondown" 	g2="percent" 	k="123" />
<hkern g1="questiondown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="questiondown" 	g2="question" 	k="270" />
<hkern g1="questiondown" 	g2="questiondown" 	k="61" />
<hkern g1="questiondown" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="questiondown" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="questiondown" 	g2="section" 	k="-4" />
<hkern g1="questiondown" 	g2="seven" 	k="86" />
<hkern g1="questiondown" 	g2="slash" 	k="-82" />
<hkern g1="questiondown" 	g2="trademark" 	k="72" />
<hkern g1="questiondown" 	g2="underscore" 	k="-102" />
<hkern g1="questiondown" 	g2="zero,six" 	k="92" />
<hkern g1="questiondown" 	g2="exclam" 	k="4" />
<hkern g1="questiondown" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="82" />
<hkern g1="questiondown" 	g2="yen" 	k="102" />
<hkern g1="questiondown" 	g2="sterling" 	k="-10" />
<hkern g1="questiondown" 	g2="currency,Euro" 	k="47" />
<hkern g1="questiondown" 	g2="eight" 	k="86" />
<hkern g1="quoteleft,quotedblleft" 	g2="ampersand" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="backslash" 	k="-113" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="-4" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="78" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-78" />
<hkern g1="quoteleft,quotedblleft" 	g2="paragraph" 	k="-78" />
<hkern g1="quoteleft,quotedblleft" 	g2="percent" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="quoteleft,quotedblleft" 	g2="question" 	k="-78" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteleft,quotedblleft" 	k="-51" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="quoteleft,quotedblleft" 	g2="section" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-92" />
<hkern g1="quoteleft,quotedblleft" 	g2="slash" 	k="113" />
<hkern g1="quoteleft,quotedblleft" 	g2="three" 	k="-47" />
<hkern g1="quoteleft,quotedblleft" 	g2="trademark" 	k="-47" />
<hkern g1="quoteleft,quotedblleft" 	g2="two" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclam" 	k="-4" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="nine" 	k="-72" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="31" />
<hkern g1="quoteright,quotedblright" 	g2="backslash" 	k="-113" />
<hkern g1="quoteright,quotedblright" 	g2="exclamdown" 	k="-4" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="115" />
<hkern g1="quoteright,quotedblright" 	g2="one" 	k="-98" />
<hkern g1="quoteright,quotedblright" 	g2="paragraph" 	k="-78" />
<hkern g1="quoteright,quotedblright" 	g2="percent" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="quoteright,quotedblright" 	g2="question" 	k="-78" />
<hkern g1="quoteright,quotedblright" 	g2="questiondown" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="quoteleft,quotedblleft" 	k="-51" />
<hkern g1="quoteright,quotedblright" 	g2="quoteright,quotedblright" 	k="-51" />
<hkern g1="quoteright,quotedblright" 	g2="section" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-57" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="113" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="-37" />
<hkern g1="quoteright,quotedblright" 	g2="trademark" 	k="-66" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="exclam" 	k="-4" />
<hkern g1="quoteright,quotedblright" 	g2="yen" 	k="-10" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="51" />
<hkern g1="quotedbl,quotesingle" 	g2="backslash" 	k="-123" />
<hkern g1="quotedbl,quotesingle" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="degree" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="five" 	k="-16" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-98" />
<hkern g1="quotedbl,quotesingle" 	g2="paragraph" 	k="-102" />
<hkern g1="quotedbl,quotesingle" 	g2="percent" 	k="-57" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="quotedbl,quotesingle" 	g2="question" 	k="-72" />
<hkern g1="quotedbl,quotesingle" 	g2="questiondown" 	k="100" />
<hkern g1="quotedbl,quotesingle" 	g2="quotedbl,quotesingle" 	k="-92" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-109" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-37" />
<hkern g1="quotedbl,quotesingle" 	g2="trademark" 	k="-78" />
<hkern g1="quotedbl,quotesingle" 	g2="two" 	k="-51" />
<hkern g1="quotedbl,quotesingle" 	g2="underscore" 	k="16" />
<hkern g1="quotedbl,quotesingle" 	g2="zero,six" 	k="-16" />
<hkern g1="quotedbl,quotesingle" 	g2="exclam" 	k="-4" />
<hkern g1="quotedbl,quotesingle" 	g2="yen" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="currency,Euro" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="-8" />
<hkern g1="quotedbl,quotesingle" 	g2="nine" 	k="-92" />
<hkern g1="slash" 	g2="ampersand" 	k="78" />
<hkern g1="slash" 	g2="backslash" 	k="-61" />
<hkern g1="slash" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="31" />
<hkern g1="slash" 	g2="degree" 	k="-98" />
<hkern g1="slash" 	g2="exclamdown" 	k="133" />
<hkern g1="slash" 	g2="four" 	k="139" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="109" />
<hkern g1="slash" 	g2="one" 	k="-61" />
<hkern g1="slash" 	g2="parenright" 	k="-66" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="201" />
<hkern g1="slash" 	g2="questiondown" 	k="154" />
<hkern g1="slash" 	g2="quoteleft,quotedblleft" 	k="-109" />
<hkern g1="slash" 	g2="quoteright,quotedblright" 	k="-113" />
<hkern g1="slash" 	g2="quotedbl,quotesingle" 	k="-123" />
<hkern g1="slash" 	g2="section" 	k="41" />
<hkern g1="slash" 	g2="seven" 	k="-31" />
<hkern g1="slash" 	g2="slash" 	k="133" />
<hkern g1="slash" 	g2="trademark" 	k="-98" />
<hkern g1="slash" 	g2="underscore" 	k="256" />
<hkern g1="slash" 	g2="zero,six" 	k="41" />
<hkern g1="slash" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="137" />
<hkern g1="slash" 	g2="yen" 	k="-82" />
<hkern g1="slash" 	g2="sterling" 	k="20" />
<hkern g1="slash" 	g2="eight" 	k="45" />
<hkern g1="underscore" 	g2="backslash" 	k="266" />
<hkern g1="underscore" 	g2="bracketright,braceright" 	k="-51" />
<hkern g1="underscore" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="underscore" 	g2="exclamdown" 	k="-14" />
<hkern g1="underscore" 	g2="five" 	k="-20" />
<hkern g1="underscore" 	g2="four" 	k="57" />
<hkern g1="underscore" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="underscore" 	g2="one" 	k="18" />
<hkern g1="underscore" 	g2="paragraph" 	k="162" />
<hkern g1="underscore" 	g2="parenright" 	k="-4" />
<hkern g1="underscore" 	g2="percent" 	k="57" />
<hkern g1="underscore" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="underscore" 	g2="question" 	k="20" />
<hkern g1="underscore" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="underscore" 	g2="section" 	k="-57" />
<hkern g1="underscore" 	g2="slash" 	k="-272" />
<hkern g1="underscore" 	g2="three" 	k="-57" />
<hkern g1="underscore" 	g2="trademark" 	k="102" />
<hkern g1="underscore" 	g2="two" 	k="-72" />
<hkern g1="underscore" 	g2="zero,six" 	k="41" />
<hkern g1="underscore" 	g2="yen" 	k="20" />
<hkern g1="underscore" 	g2="sterling" 	k="-37" />
<hkern g1="underscore" 	g2="currency,Euro" 	k="16" />
<hkern g1="underscore" 	g2="nine" 	k="-20" />
<hkern g1="eight" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="10" />
<hkern g1="eight" 	g2="J" 	k="8" />
<hkern g1="eight" 	g2="T" 	k="20" />
<hkern g1="eight" 	g2="V,W" 	k="33" />
<hkern g1="eight" 	g2="X" 	k="43" />
<hkern g1="eight" 	g2="Y,Yacute,Ydieresis" 	k="43" />
<hkern g1="eight" 	g2="Z" 	k="8" />
<hkern g1="eight" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-16" />
<hkern g1="eight" 	g2="backslash" 	k="61" />
<hkern g1="eight" 	g2="j" 	k="16" />
<hkern g1="eight" 	g2="percent" 	k="20" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="eight" 	g2="question" 	k="20" />
<hkern g1="eight" 	g2="questiondown" 	k="41" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="-8" />
<hkern g1="eight" 	g2="section" 	k="-20" />
<hkern g1="eight" 	g2="v,w,y,yacute,ydieresis" 	k="-12" />
<hkern g1="eight" 	g2="x" 	k="8" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="five" 	g2="J" 	k="20" />
<hkern g1="five" 	g2="T" 	k="20" />
<hkern g1="five" 	g2="V,W" 	k="12" />
<hkern g1="five" 	g2="X" 	k="23" />
<hkern g1="five" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="five" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="five" 	g2="backslash" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="20" />
<hkern g1="five" 	g2="v,w,y,yacute,ydieresis" 	k="14" />
<hkern g1="five" 	g2="x" 	k="14" />
<hkern g1="five" 	g2="degree" 	k="31" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="seven" 	k="29" />
<hkern g1="five" 	g2="three" 	k="10" />
<hkern g1="five" 	g2="two" 	k="10" />
<hkern g1="four" 	g2="J" 	k="-4" />
<hkern g1="four" 	g2="T" 	k="104" />
<hkern g1="four" 	g2="V,W" 	k="106" />
<hkern g1="four" 	g2="X" 	k="31" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="176" />
<hkern g1="four" 	g2="Z" 	k="16" />
<hkern g1="four" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="percent" 	k="61" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="four" 	g2="question" 	k="106" />
<hkern g1="four" 	g2="questiondown" 	k="-20" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="45" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="four" 	g2="section" 	k="-39" />
<hkern g1="four" 	g2="v,w,y,yacute,ydieresis" 	k="31" />
<hkern g1="four" 	g2="x" 	k="25" />
<hkern g1="four" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="degree" 	k="61" />
<hkern g1="four" 	g2="five" 	k="8" />
<hkern g1="four" 	g2="seven" 	k="102" />
<hkern g1="four" 	g2="three" 	k="31" />
<hkern g1="four" 	g2="two" 	k="8" />
<hkern g1="four" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="four" 	g2="dollar,S" 	k="16" />
<hkern g1="four" 	g2="slash" 	k="-57" />
<hkern g1="four" 	g2="underscore" 	k="-41" />
<hkern g1="four" 	g2="ampersand" 	k="-41" />
<hkern g1="four" 	g2="colon,semicolon" 	k="-12" />
<hkern g1="four" 	g2="currency,Euro" 	k="-20" />
<hkern g1="four" 	g2="eight" 	k="-20" />
<hkern g1="four" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="four" 	g2="sterling" 	k="-20" />
<hkern g1="four" 	g2="nine" 	k="16" />
<hkern g1="four" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-25" />
<hkern g1="four" 	g2="one" 	k="51" />
<hkern g1="four" 	g2="paragraph" 	k="31" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="four" 	g2="f,t,uniFB01,uniFB02" 	k="-2" />
<hkern g1="four" 	g2="trademark" 	k="41" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="135" />
<hkern g1="seven" 	g2="J" 	k="57" />
<hkern g1="seven" 	g2="V,W" 	k="-10" />
<hkern g1="seven" 	g2="X" 	k="8" />
<hkern g1="seven" 	g2="Y,Yacute,Ydieresis" 	k="-14" />
<hkern g1="seven" 	g2="Z" 	k="16" />
<hkern g1="seven" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-57" />
<hkern g1="seven" 	g2="backslash" 	k="-20" />
<hkern g1="seven" 	g2="j" 	k="43" />
<hkern g1="seven" 	g2="percent" 	k="-4" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="109" />
<hkern g1="seven" 	g2="question" 	k="-31" />
<hkern g1="seven" 	g2="questiondown" 	k="133" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-61" />
<hkern g1="seven" 	g2="section" 	k="20" />
<hkern g1="seven" 	g2="v,w,y,yacute,ydieresis" 	k="10" />
<hkern g1="seven" 	g2="x" 	k="47" />
<hkern g1="seven" 	g2="yen" 	k="-25" />
<hkern g1="seven" 	g2="degree" 	k="-8" />
<hkern g1="seven" 	g2="five" 	k="39" />
<hkern g1="seven" 	g2="three" 	k="18" />
<hkern g1="seven" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="39" />
<hkern g1="seven" 	g2="dollar,S" 	k="16" />
<hkern g1="seven" 	g2="slash" 	k="88" />
<hkern g1="seven" 	g2="underscore" 	k="61" />
<hkern g1="seven" 	g2="ampersand" 	k="72" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="41" />
<hkern g1="seven" 	g2="currency,Euro" 	k="41" />
<hkern g1="seven" 	g2="eight" 	k="39" />
<hkern g1="seven" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="102" />
<hkern g1="seven" 	g2="sterling" 	k="29" />
<hkern g1="seven" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="seven" 	g2="one" 	k="-20" />
<hkern g1="seven" 	g2="paragraph" 	k="-57" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="seven" 	g2="f,t,uniFB01,uniFB02" 	k="31" />
<hkern g1="seven" 	g2="trademark" 	k="-82" />
<hkern g1="seven" 	g2="exclamdown" 	k="57" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="seven" 	g2="s" 	k="61" />
<hkern g1="seven" 	g2="z" 	k="61" />
<hkern g1="seven" 	g2="zero,six" 	k="41" />
<hkern g1="seven" 	g2="bracketright,braceright" 	k="6" />
<hkern g1="seven" 	g2="four" 	k="133" />
<hkern g1="seven" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="53" />
<hkern g1="seven" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="57" />
<hkern g1="six" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-16" />
<hkern g1="six" 	g2="T" 	k="20" />
<hkern g1="six" 	g2="V,W" 	k="20" />
<hkern g1="six" 	g2="X" 	k="16" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="six" 	g2="asterisk,ordfeminine,ordmasculine" 	k="31" />
<hkern g1="six" 	g2="j" 	k="16" />
<hkern g1="six" 	g2="percent" 	k="41" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="six" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="six" 	g2="section" 	k="-41" />
<hkern g1="six" 	g2="v,w,y,yacute,ydieresis" 	k="18" />
<hkern g1="six" 	g2="x" 	k="31" />
<hkern g1="six" 	g2="degree" 	k="43" />
<hkern g1="six" 	g2="seven" 	k="16" />
<hkern g1="six" 	g2="slash" 	k="-20" />
<hkern g1="six" 	g2="ampersand" 	k="-20" />
<hkern g1="six" 	g2="currency,Euro" 	k="-20" />
<hkern g1="six" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="six" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-18" />
<hkern g1="three" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-8" />
<hkern g1="three" 	g2="T" 	k="12" />
<hkern g1="three" 	g2="V,W" 	k="20" />
<hkern g1="three" 	g2="X" 	k="20" />
<hkern g1="three" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="three" 	g2="j" 	k="12" />
<hkern g1="three" 	g2="percent" 	k="20" />
<hkern g1="three" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="three" 	g2="v,w,y,yacute,ydieresis" 	k="12" />
<hkern g1="three" 	g2="x" 	k="25" />
<hkern g1="three" 	g2="degree" 	k="31" />
<hkern g1="three" 	g2="five" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="27" />
<hkern g1="three" 	g2="three" 	k="10" />
<hkern g1="three" 	g2="two" 	k="10" />
<hkern g1="two" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-4" />
<hkern g1="two" 	g2="J" 	k="-25" />
<hkern g1="two" 	g2="T" 	k="16" />
<hkern g1="two" 	g2="V,W" 	k="23" />
<hkern g1="two" 	g2="Y,Yacute,Ydieresis" 	k="33" />
<hkern g1="two" 	g2="backslash" 	k="16" />
<hkern g1="two" 	g2="percent" 	k="-20" />
<hkern g1="two" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="two" 	g2="v,w,y,yacute,ydieresis" 	k="-8" />
<hkern g1="two" 	g2="x" 	k="16" />
<hkern g1="two" 	g2="seven" 	k="8" />
<hkern g1="two" 	g2="slash" 	k="-20" />
<hkern g1="two" 	g2="underscore" 	k="-61" />
<hkern g1="two" 	g2="ampersand" 	k="16" />
<hkern g1="two" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="16" />
<hkern g1="two" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="two" 	g2="z" 	k="16" />
<hkern g1="two" 	g2="zero,six" 	k="8" />
<hkern g1="two" 	g2="four" 	k="47" />
<hkern g1="two" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="8" />
<hkern g1="zero,nine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="14" />
<hkern g1="zero,nine" 	g2="J" 	k="45" />
<hkern g1="zero,nine" 	g2="T" 	k="41" />
<hkern g1="zero,nine" 	g2="V,W" 	k="51" />
<hkern g1="zero,nine" 	g2="X" 	k="66" />
<hkern g1="zero,nine" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="zero,nine" 	g2="Z" 	k="43" />
<hkern g1="zero,nine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-16" />
<hkern g1="zero,nine" 	g2="backslash" 	k="41" />
<hkern g1="zero,nine" 	g2="j" 	k="16" />
<hkern g1="zero,nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="23" />
<hkern g1="zero,nine" 	g2="question" 	k="41" />
<hkern g1="zero,nine" 	g2="questiondown" 	k="61" />
<hkern g1="zero,nine" 	g2="quotedbl,quotesingle" 	k="-16" />
<hkern g1="zero,nine" 	g2="v,w,y,yacute,ydieresis" 	k="-16" />
<hkern g1="zero,nine" 	g2="x" 	k="20" />
<hkern g1="zero,nine" 	g2="yen" 	k="23" />
<hkern g1="zero,nine" 	g2="degree" 	k="-8" />
<hkern g1="zero,nine" 	g2="seven" 	k="16" />
<hkern g1="zero,nine" 	g2="three" 	k="20" />
<hkern g1="zero,nine" 	g2="two" 	k="4" />
<hkern g1="zero,nine" 	g2="slash" 	k="41" />
<hkern g1="zero,nine" 	g2="underscore" 	k="41" />
<hkern g1="zero,nine" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-16" />
<hkern g1="zero,nine" 	g2="one" 	k="4" />
<hkern g1="zero,nine" 	g2="f,t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V,W" 	k="74" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="66" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="84" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,ordfeminine,ordmasculine" 	k="188" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="degree" 	k="188" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="eight" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="exclamdown" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="four" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotright,guilsinglright" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="nine" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="84" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="paragraph" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-70" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="questiondown" 	k="-41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="66" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="66" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="90" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="seven" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,t,uniFB01,uniFB02" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="three" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="147" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="underscore" 	k="-119" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v,w,y,yacute,ydieresis" 	k="53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="66" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="z" 	k="2" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="zero,six" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-18" />
<hkern g1="B" 	g2="T" 	k="20" />
<hkern g1="B" 	g2="V,W" 	k="10" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="4" />
<hkern g1="B" 	g2="trademark" 	k="4" />
<hkern g1="B" 	g2="underscore" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="T" 	k="18" />
<hkern g1="C,Ccedilla,Euro" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="4" />
<hkern g1="C,Ccedilla,Euro" 	g2="V,W" 	k="16" />
<hkern g1="C,Ccedilla,Euro" 	g2="X" 	k="37" />
<hkern g1="C,Ccedilla,Euro" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="C,Ccedilla,Euro" 	g2="backslash" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="colon,semicolon" 	k="12" />
<hkern g1="C,Ccedilla,Euro" 	g2="degree" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="eight" 	k="29" />
<hkern g1="C,Ccedilla,Euro" 	g2="exclamdown" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="four" 	k="61" />
<hkern g1="C,Ccedilla,Euro" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="C,Ccedilla,Euro" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="4" />
<hkern g1="C,Ccedilla,Euro" 	g2="nine" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="C,Ccedilla,Euro" 	g2="one" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="paragraph" 	k="-16" />
<hkern g1="C,Ccedilla,Euro" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-29" />
<hkern g1="C,Ccedilla,Euro" 	g2="questiondown" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteleft,quotedblleft" 	k="-6" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteright,quotedblright" 	k="-33" />
<hkern g1="C,Ccedilla,Euro" 	g2="s" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="three" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="37" />
<hkern g1="C,Ccedilla,Euro" 	g2="underscore" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="x" 	k="37" />
<hkern g1="C,Ccedilla,Euro" 	g2="z" 	k="37" />
<hkern g1="C,Ccedilla,Euro" 	g2="zero,six" 	k="47" />
<hkern g1="C,Ccedilla,Euro" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="37" />
<hkern g1="C,Ccedilla,Euro" 	g2="dollar,S" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="ampersand" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="section" 	k="16" />
<hkern g1="C,Ccedilla,Euro" 	g2="two" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="V,W" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="X" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="colon,semicolon" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="eight" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="exclamdown" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="four" 	k="78" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="nine" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="question" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="s" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="seven" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="f,t,uniFB01,uniFB02" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="three" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="underscore" 	k="-31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v,w,y,yacute,ydieresis" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="z" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="zero,six" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="five" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="section" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="two" 	k="16" />
<hkern g1="F" 	g2="J" 	k="31" />
<hkern g1="F" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="F" 	g2="T" 	k="-10" />
<hkern g1="F" 	g2="X" 	k="37" />
<hkern g1="F" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="F" 	g2="backslash" 	k="-20" />
<hkern g1="F" 	g2="degree" 	k="-61" />
<hkern g1="F" 	g2="eight" 	k="37" />
<hkern g1="F" 	g2="exclamdown" 	k="20" />
<hkern g1="F" 	g2="four" 	k="102" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="29" />
<hkern g1="F" 	g2="nine" 	k="-12" />
<hkern g1="F" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="F" 	g2="one" 	k="-31" />
<hkern g1="F" 	g2="paragraph" 	k="-20" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="F" 	g2="question" 	k="25" />
<hkern g1="F" 	g2="questiondown" 	k="98" />
<hkern g1="F" 	g2="quoteleft,quotedblleft" 	k="-37" />
<hkern g1="F" 	g2="quoteright,quotedblright" 	k="-57" />
<hkern g1="F" 	g2="quotedbl,quotesingle" 	k="-82" />
<hkern g1="F" 	g2="s" 	k="20" />
<hkern g1="F" 	g2="seven" 	k="-20" />
<hkern g1="F" 	g2="slash" 	k="88" />
<hkern g1="F" 	g2="trademark" 	k="-61" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="37" />
<hkern g1="F" 	g2="underscore" 	k="41" />
<hkern g1="F" 	g2="v,w,y,yacute,ydieresis" 	k="-10" />
<hkern g1="F" 	g2="x" 	k="43" />
<hkern g1="F" 	g2="z" 	k="37" />
<hkern g1="F" 	g2="zero,six" 	k="29" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="68" />
<hkern g1="F" 	g2="dollar,S" 	k="8" />
<hkern g1="F" 	g2="ampersand" 	k="61" />
<hkern g1="F" 	g2="five" 	k="37" />
<hkern g1="F" 	g2="section" 	k="16" />
<hkern g1="F" 	g2="two" 	k="2" />
<hkern g1="F" 	g2="parenright" 	k="16" />
<hkern g1="F" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="F" 	g2="exclam" 	k="-10" />
<hkern g1="F" 	g2="j" 	k="31" />
<hkern g1="F" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="sterling" 	g2="J" 	k="41" />
<hkern g1="sterling" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="sterling" 	g2="X" 	k="20" />
<hkern g1="sterling" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="sterling" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="sterling" 	g2="backslash" 	k="-37" />
<hkern g1="sterling" 	g2="colon,semicolon" 	k="61" />
<hkern g1="sterling" 	g2="eight" 	k="70" />
<hkern g1="sterling" 	g2="exclamdown" 	k="51" />
<hkern g1="sterling" 	g2="four" 	k="78" />
<hkern g1="sterling" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="sterling" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="sterling" 	g2="nine" 	k="41" />
<hkern g1="sterling" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="sterling" 	g2="one" 	k="-37" />
<hkern g1="sterling" 	g2="paragraph" 	k="-31" />
<hkern g1="sterling" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="4" />
<hkern g1="sterling" 	g2="questiondown" 	k="47" />
<hkern g1="sterling" 	g2="quoteleft,quotedblleft" 	k="4" />
<hkern g1="sterling" 	g2="quoteright,quotedblright" 	k="-4" />
<hkern g1="sterling" 	g2="s" 	k="82" />
<hkern g1="sterling" 	g2="seven" 	k="-16" />
<hkern g1="sterling" 	g2="slash" 	k="-23" />
<hkern g1="sterling" 	g2="f,t,uniFB01,uniFB02" 	k="41" />
<hkern g1="sterling" 	g2="three" 	k="20" />
<hkern g1="sterling" 	g2="trademark" 	k="-72" />
<hkern g1="sterling" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="57" />
<hkern g1="sterling" 	g2="underscore" 	k="-37" />
<hkern g1="sterling" 	g2="v,w,y,yacute,ydieresis" 	k="41" />
<hkern g1="sterling" 	g2="x" 	k="51" />
<hkern g1="sterling" 	g2="z" 	k="82" />
<hkern g1="sterling" 	g2="zero,six" 	k="78" />
<hkern g1="sterling" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="92" />
<hkern g1="sterling" 	g2="dollar,S" 	k="20" />
<hkern g1="sterling" 	g2="ampersand" 	k="82" />
<hkern g1="sterling" 	g2="five" 	k="25" />
<hkern g1="sterling" 	g2="section" 	k="20" />
<hkern g1="sterling" 	g2="two" 	k="20" />
<hkern g1="sterling" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="sterling" 	g2="j" 	k="51" />
<hkern g1="sterling" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="41" />
<hkern g1="G" 	g2="backslash" 	k="37" />
<hkern g1="G" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="H,I,M,N,paragraph,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="V,W" 	k="16" />
<hkern g1="K" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="K" 	g2="T" 	k="82" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="K" 	g2="V,W" 	k="82" />
<hkern g1="K" 	g2="X" 	k="76" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="96" />
<hkern g1="K" 	g2="degree" 	k="20" />
<hkern g1="K" 	g2="eight" 	k="43" />
<hkern g1="K" 	g2="four" 	k="98" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="K" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="66" />
<hkern g1="K" 	g2="nine" 	k="20" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="K" 	g2="one" 	k="-41" />
<hkern g1="K" 	g2="paragraph" 	k="10" />
<hkern g1="K" 	g2="question" 	k="82" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="K" 	g2="s" 	k="37" />
<hkern g1="K" 	g2="slash" 	k="-16" />
<hkern g1="K" 	g2="f,t,uniFB01,uniFB02" 	k="57" />
<hkern g1="K" 	g2="three" 	k="16" />
<hkern g1="K" 	g2="trademark" 	k="-41" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="39" />
<hkern g1="K" 	g2="underscore" 	k="-139" />
<hkern g1="K" 	g2="v,w,y,yacute,ydieresis" 	k="74" />
<hkern g1="K" 	g2="x" 	k="80" />
<hkern g1="K" 	g2="z" 	k="45" />
<hkern g1="K" 	g2="zero,six" 	k="43" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="55" />
<hkern g1="K" 	g2="dollar,S" 	k="31" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="two" 	k="-2" />
<hkern g1="K" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="K" 	g2="j" 	k="8" />
<hkern g1="K" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="K" 	g2="bracketleft" 	k="61" />
<hkern g1="L" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="L" 	g2="T" 	k="133" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="27" />
<hkern g1="L" 	g2="V,W" 	k="109" />
<hkern g1="L" 	g2="X" 	k="20" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="152" />
<hkern g1="L" 	g2="asterisk,ordfeminine,ordmasculine" 	k="143" />
<hkern g1="L" 	g2="backslash" 	k="143" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="L" 	g2="degree" 	k="133" />
<hkern g1="L" 	g2="eight" 	k="16" />
<hkern g1="L" 	g2="four" 	k="78" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-16" />
<hkern g1="L" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="27" />
<hkern g1="L" 	g2="nine" 	k="20" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="L" 	g2="one" 	k="78" />
<hkern g1="L" 	g2="paragraph" 	k="154" />
<hkern g1="L" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="L" 	g2="question" 	k="195" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="86" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="94" />
<hkern g1="L" 	g2="seven" 	k="102" />
<hkern g1="L" 	g2="slash" 	k="-51" />
<hkern g1="L" 	g2="f,t,uniFB01,uniFB02" 	k="10" />
<hkern g1="L" 	g2="three" 	k="-4" />
<hkern g1="L" 	g2="trademark" 	k="133" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="L" 	g2="underscore" 	k="-129" />
<hkern g1="L" 	g2="v,w,y,yacute,ydieresis" 	k="80" />
<hkern g1="L" 	g2="x" 	k="16" />
<hkern g1="L" 	g2="zero,six" 	k="51" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-4" />
<hkern g1="L" 	g2="dollar,S" 	k="16" />
<hkern g1="L" 	g2="ampersand" 	k="-4" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V,W" 	k="43" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="51" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="degree" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-16" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="questiondown" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="39" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="f,t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="23" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="25" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="-31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="j" 	k="-164" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="18" />
<hkern g1="P" 	g2="J" 	k="70" />
<hkern g1="P" 	g2="T" 	k="68" />
<hkern g1="P" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="P" 	g2="V,W" 	k="33" />
<hkern g1="P" 	g2="X" 	k="51" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="P" 	g2="backslash" 	k="41" />
<hkern g1="P" 	g2="degree" 	k="-41" />
<hkern g1="P" 	g2="eight" 	k="12" />
<hkern g1="P" 	g2="four" 	k="61" />
<hkern g1="P" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="4" />
<hkern g1="P" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="59" />
<hkern g1="P" 	g2="questiondown" 	k="109" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="slash" 	k="98" />
<hkern g1="P" 	g2="f,t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="P" 	g2="three" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="66" />
<hkern g1="P" 	g2="v,w,y,yacute,ydieresis" 	k="16" />
<hkern g1="P" 	g2="x" 	k="70" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="92" />
<hkern g1="P" 	g2="ampersand" 	k="68" />
<hkern g1="P" 	g2="five" 	k="20" />
<hkern g1="P" 	g2="two" 	k="10" />
<hkern g1="P" 	g2="parenright" 	k="23" />
<hkern g1="P" 	g2="bracketright,braceright" 	k="23" />
<hkern g1="P" 	g2="j" 	k="20" />
<hkern g1="P" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="47" />
<hkern g1="P" 	g2="Z" 	k="37" />
<hkern g1="P" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="20" />
<hkern g1="R" 	g2="J" 	k="20" />
<hkern g1="R" 	g2="T" 	k="20" />
<hkern g1="R" 	g2="X" 	k="41" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="degree" 	k="-31" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="2" />
<hkern g1="R" 	g2="question" 	k="10" />
<hkern g1="R" 	g2="questiondown" 	k="20" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="R" 	g2="f,t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="R" 	g2="three" 	k="16" />
<hkern g1="R" 	g2="underscore" 	k="-37" />
<hkern g1="dollar,S" 	g2="T" 	k="20" />
<hkern g1="dollar,S" 	g2="X" 	k="29" />
<hkern g1="dollar,S" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="dollar,S" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="20" />
<hkern g1="dollar,S" 	g2="colon,semicolon" 	k="10" />
<hkern g1="dollar,S" 	g2="degree" 	k="31" />
<hkern g1="dollar,S" 	g2="exclamdown" 	k="2" />
<hkern g1="dollar,S" 	g2="four" 	k="-20" />
<hkern g1="dollar,S" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-37" />
<hkern g1="dollar,S" 	g2="nine" 	k="20" />
<hkern g1="dollar,S" 	g2="one" 	k="20" />
<hkern g1="dollar,S" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="questiondown" 	k="20" />
<hkern g1="dollar,S" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="dollar,S" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="dollar,S" 	g2="seven" 	k="20" />
<hkern g1="dollar,S" 	g2="three" 	k="16" />
<hkern g1="dollar,S" 	g2="underscore" 	k="41" />
<hkern g1="dollar,S" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="dollar,S" 	g2="x" 	k="20" />
<hkern g1="T" 	g2="J" 	k="20" />
<hkern g1="T" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="T" 	k="-20" />
<hkern g1="T" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="29" />
<hkern g1="T" 	g2="V,W" 	k="10" />
<hkern g1="T" 	g2="X" 	k="61" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="T" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="T" 	g2="backslash" 	k="-61" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="degree" 	k="-82" />
<hkern g1="T" 	g2="eight" 	k="20" />
<hkern g1="T" 	g2="exclamdown" 	k="41" />
<hkern g1="T" 	g2="four" 	k="180" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="T" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="66" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="one" 	k="-82" />
<hkern g1="T" 	g2="paragraph" 	k="-61" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="63" />
<hkern g1="T" 	g2="question" 	k="-14" />
<hkern g1="T" 	g2="questiondown" 	k="123" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-57" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-57" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-10" />
<hkern g1="T" 	g2="s" 	k="80" />
<hkern g1="T" 	g2="seven" 	k="-41" />
<hkern g1="T" 	g2="slash" 	k="41" />
<hkern g1="T" 	g2="f,t,uniFB01,uniFB02" 	k="-4" />
<hkern g1="T" 	g2="three" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-102" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="29" />
<hkern g1="T" 	g2="v,w,y,yacute,ydieresis" 	k="70" />
<hkern g1="T" 	g2="x" 	k="70" />
<hkern g1="T" 	g2="z" 	k="20" />
<hkern g1="T" 	g2="zero,six" 	k="41" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="72" />
<hkern g1="T" 	g2="dollar,S" 	k="23" />
<hkern g1="T" 	g2="ampersand" 	k="72" />
<hkern g1="T" 	g2="five" 	k="12" />
<hkern g1="T" 	g2="two" 	k="-31" />
<hkern g1="T" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="T" 	g2="exclam" 	k="-41" />
<hkern g1="T" 	g2="j" 	k="-45" />
<hkern g1="T" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="29" />
<hkern g1="T" 	g2="parenleft" 	k="39" />
<hkern g1="Thorn" 	g2="J" 	k="82" />
<hkern g1="Thorn" 	g2="T" 	k="51" />
<hkern g1="Thorn" 	g2="V,W" 	k="20" />
<hkern g1="Thorn" 	g2="X" 	k="61" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="one" 	k="41" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="49" />
<hkern g1="Thorn" 	g2="question" 	k="61" />
<hkern g1="Thorn" 	g2="questiondown" 	k="51" />
<hkern g1="Thorn" 	g2="slash" 	k="61" />
<hkern g1="Thorn" 	g2="f,t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="Thorn" 	g2="three" 	k="41" />
<hkern g1="Thorn" 	g2="trademark" 	k="4" />
<hkern g1="Thorn" 	g2="underscore" 	k="61" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="61" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="two" 	k="51" />
<hkern g1="Thorn" 	g2="j" 	k="20" />
<hkern g1="Thorn" 	g2="Z" 	k="39" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="T" 	k="29" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="18" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="questiondown" 	k="16" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="underscore" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="16" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="31" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="j" 	k="10" />
<hkern g1="V,W" 	g2="J" 	k="31" />
<hkern g1="V,W" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="V,W" 	g2="T" 	k="10" />
<hkern g1="V,W" 	g2="X" 	k="45" />
<hkern g1="V,W" 	g2="Y,Yacute,Ydieresis" 	k="39" />
<hkern g1="V,W" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-70" />
<hkern g1="V,W" 	g2="backslash" 	k="-61" />
<hkern g1="V,W" 	g2="colon,semicolon" 	k="45" />
<hkern g1="V,W" 	g2="degree" 	k="-57" />
<hkern g1="V,W" 	g2="eight" 	k="33" />
<hkern g1="V,W" 	g2="exclamdown" 	k="61" />
<hkern g1="V,W" 	g2="four" 	k="147" />
<hkern g1="V,W" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="V,W" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="45" />
<hkern g1="V,W" 	g2="nine" 	k="12" />
<hkern g1="V,W" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="V,W" 	g2="one" 	k="-61" />
<hkern g1="V,W" 	g2="paragraph" 	k="-51" />
<hkern g1="V,W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="V,W" 	g2="questiondown" 	k="150" />
<hkern g1="V,W" 	g2="quoteleft,quotedblleft" 	k="-47" />
<hkern g1="V,W" 	g2="quoteright,quotedblright" 	k="-72" />
<hkern g1="V,W" 	g2="s" 	k="117" />
<hkern g1="V,W" 	g2="seven" 	k="-20" />
<hkern g1="V,W" 	g2="slash" 	k="82" />
<hkern g1="V,W" 	g2="f,t,uniFB01,uniFB02" 	k="4" />
<hkern g1="V,W" 	g2="three" 	k="-4" />
<hkern g1="V,W" 	g2="trademark" 	k="-82" />
<hkern g1="V,W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="63" />
<hkern g1="V,W" 	g2="underscore" 	k="20" />
<hkern g1="V,W" 	g2="v,w,y,yacute,ydieresis" 	k="33" />
<hkern g1="V,W" 	g2="x" 	k="70" />
<hkern g1="V,W" 	g2="z" 	k="55" />
<hkern g1="V,W" 	g2="zero,six" 	k="51" />
<hkern g1="V,W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="74" />
<hkern g1="V,W" 	g2="dollar,S" 	k="31" />
<hkern g1="V,W" 	g2="ampersand" 	k="57" />
<hkern g1="V,W" 	g2="five" 	k="14" />
<hkern g1="V,W" 	g2="parenright" 	k="-10" />
<hkern g1="V,W" 	g2="bracketright,braceright" 	k="-45" />
<hkern g1="V,W" 	g2="exclam" 	k="-20" />
<hkern g1="V,W" 	g2="j" 	k="41" />
<hkern g1="V,W" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="72" />
<hkern g1="V,W" 	g2="Z" 	k="20" />
<hkern g1="V,W" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="X" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="X" 	g2="T" 	k="61" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="X" 	g2="V,W" 	k="45" />
<hkern g1="X" 	g2="X" 	k="47" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="X" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="X" 	g2="colon,semicolon" 	k="12" />
<hkern g1="X" 	g2="degree" 	k="41" />
<hkern g1="X" 	g2="eight" 	k="43" />
<hkern g1="X" 	g2="exclamdown" 	k="20" />
<hkern g1="X" 	g2="four" 	k="106" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="X" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="35" />
<hkern g1="X" 	g2="nine" 	k="33" />
<hkern g1="X" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="X" 	g2="one" 	k="-20" />
<hkern g1="X" 	g2="question" 	k="61" />
<hkern g1="X" 	g2="questiondown" 	k="-4" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="-6" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="-16" />
<hkern g1="X" 	g2="s" 	k="41" />
<hkern g1="X" 	g2="seven" 	k="20" />
<hkern g1="X" 	g2="slash" 	k="-37" />
<hkern g1="X" 	g2="f,t,uniFB01,uniFB02" 	k="45" />
<hkern g1="X" 	g2="trademark" 	k="-61" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="53" />
<hkern g1="X" 	g2="underscore" 	k="-98" />
<hkern g1="X" 	g2="v,w,y,yacute,ydieresis" 	k="51" />
<hkern g1="X" 	g2="x" 	k="72" />
<hkern g1="X" 	g2="z" 	k="25" />
<hkern g1="X" 	g2="zero,six" 	k="66" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="66" />
<hkern g1="X" 	g2="dollar,S" 	k="29" />
<hkern g1="X" 	g2="ampersand" 	k="41" />
<hkern g1="X" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="X" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="X" 	g2="Z" 	k="20" />
<hkern g1="X" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="49" />
<hkern g1="X" 	g2="parenleft" 	k="23" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V,W" 	k="39" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="X" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="4" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="degree" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eight" 	k="43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="exclamdown" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="four" 	k="188" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="63" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="nine" 	k="33" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="one" 	k="-66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="paragraph" 	k="-51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="questiondown" 	k="170" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="-31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="137" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="seven" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,t,uniFB01,uniFB02" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="three" 	k="-10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="underscore" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,w,y,yacute,ydieresis" 	k="84" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="zero,six" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="84" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="dollar,S" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="five" 	k="35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="section" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="two" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright,braceright" 	k="-51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="exclam" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="80" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenleft" 	k="31" />
<hkern g1="Z" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="Z" 	g2="X" 	k="20" />
<hkern g1="Z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="Z" 	g2="eight" 	k="8" />
<hkern g1="Z" 	g2="exclamdown" 	k="-4" />
<hkern g1="Z" 	g2="four" 	k="78" />
<hkern g1="Z" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="31" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="Z" 	g2="one" 	k="-41" />
<hkern g1="Z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="Z" 	g2="question" 	k="-4" />
<hkern g1="Z" 	g2="slash" 	k="-16" />
<hkern g1="Z" 	g2="f,t,uniFB01,uniFB02" 	k="16" />
<hkern g1="Z" 	g2="trademark" 	k="-61" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="Z" 	g2="underscore" 	k="-61" />
<hkern g1="Z" 	g2="v,w,y,yacute,ydieresis" 	k="31" />
<hkern g1="Z" 	g2="zero,six" 	k="43" />
<hkern g1="Z" 	g2="ampersand" 	k="31" />
<hkern g1="Z" 	g2="five" 	k="8" />
<hkern g1="Z" 	g2="bracketright,braceright" 	k="-10" />
<hkern g1="a,g,i,j,q,u,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="188" />
<hkern g1="a,g,i,j,q,u,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="a,g,i,j,q,u,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="V,W" 	k="10" />
<hkern g1="a,g,i,j,q,u,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="90" />
<hkern g1="a,g,i,j,q,u,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="109" />
<hkern g1="a,g,i,j,q,u,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="139" />
<hkern g1="a,g,i,j,q,u,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="one" 	k="16" />
<hkern g1="a,g,i,j,q,u,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="question" 	k="-55" />
<hkern g1="a,g,i,j,q,u,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="dollar,S" 	k="39" />
<hkern g1="z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="z" 	g2="backslash" 	k="133" />
<hkern g1="z" 	g2="four" 	k="31" />
<hkern g1="z" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="29" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="z" 	g2="one" 	k="31" />
<hkern g1="z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="z" 	g2="f,t,uniFB01,uniFB02" 	k="-2" />
<hkern g1="z" 	g2="underscore" 	k="-51" />
<hkern g1="z" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="z" 	g2="x" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="16" />
<hkern g1="z" 	g2="two" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="c,cent,ccedilla" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-25" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="98" />
<hkern g1="c,cent,ccedilla" 	g2="degree" 	k="-33" />
<hkern g1="c,cent,ccedilla" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="57" />
<hkern g1="c,cent,ccedilla" 	g2="quoteleft,quotedblleft" 	k="-35" />
<hkern g1="c,cent,ccedilla" 	g2="quoteright,quotedblright" 	k="-45" />
<hkern g1="c,cent,ccedilla" 	g2="f,t,uniFB01,uniFB02" 	k="-12" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="29" />
<hkern g1="c,cent,ccedilla" 	g2="v,w,y,yacute,ydieresis" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="49" />
<hkern g1="c,cent,ccedilla" 	g2="seven" 	k="31" />
<hkern g1="c,cent,ccedilla" 	g2="two" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="x" 	k="37" />
<hkern g1="c,cent,ccedilla" 	g2="zero,six" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="T" 	k="195" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="51" />
<hkern g1="c,cent,ccedilla" 	g2="bracketright,braceright" 	k="23" />
<hkern g1="c,cent,ccedilla" 	g2="bracketleft" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-12" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="four" 	k="47" />
<hkern g1="c,cent,ccedilla" 	g2="b,h,k,l,germandbls,thorn" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="nine" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="parenright" 	k="43" />
<hkern g1="c,cent,ccedilla" 	g2="questiondown" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="section" 	k="23" />
<hkern g1="c,cent,ccedilla" 	g2="three" 	k="37" />
<hkern g1="c,cent,ccedilla" 	g2="z" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="V,W" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="139" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="degree" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="paragraph" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="percent" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="98" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="underscore" 	k="12" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="one" 	k="57" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="seven" 	k="59" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="two" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="201" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="nine" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="questiondown" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="three" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V,W" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="74" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="51" />
<hkern g1="eth" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="eth" 	g2="backslash" 	k="20" />
<hkern g1="eth" 	g2="degree" 	k="-41" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="39" />
<hkern g1="eth" 	g2="question" 	k="16" />
<hkern g1="eth" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="eth" 	g2="trademark" 	k="-57" />
<hkern g1="eth" 	g2="underscore" 	k="41" />
<hkern g1="eth" 	g2="zero,six" 	k="-16" />
<hkern g1="eth" 	g2="questiondown" 	k="16" />
<hkern g1="eth" 	g2="three" 	k="16" />
<hkern g1="f" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-109" />
<hkern g1="f" 	g2="backslash" 	k="-131" />
<hkern g1="f" 	g2="degree" 	k="-78" />
<hkern g1="f" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="f" 	g2="paragraph" 	k="-92" />
<hkern g1="f" 	g2="percent" 	k="-37" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="f" 	g2="question" 	k="-57" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-113" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-160" />
<hkern g1="f" 	g2="slash" 	k="61" />
<hkern g1="f" 	g2="trademark" 	k="-164" />
<hkern g1="f" 	g2="underscore" 	k="31" />
<hkern g1="f" 	g2="v,w,y,yacute,ydieresis" 	k="-20" />
<hkern g1="f" 	g2="one" 	k="-109" />
<hkern g1="f" 	g2="seven" 	k="-92" />
<hkern g1="f" 	g2="two" 	k="-37" />
<hkern g1="f" 	g2="zero,six" 	k="-31" />
<hkern g1="f" 	g2="ampersand" 	k="25" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-98" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-51" />
<hkern g1="f" 	g2="eight" 	k="-31" />
<hkern g1="f" 	g2="four" 	k="57" />
<hkern g1="f" 	g2="nine" 	k="-63" />
<hkern g1="f" 	g2="parenright" 	k="-55" />
<hkern g1="f" 	g2="questiondown" 	k="25" />
<hkern g1="f" 	g2="three" 	k="-51" />
<hkern g1="f" 	g2="exclam" 	k="-4" />
<hkern g1="f" 	g2="five" 	k="-31" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="f" 	g2="j" 	k="-86" />
<hkern g1="f" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="k" 	g2="backslash" 	k="82" />
<hkern g1="k" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="45" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="43" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="k" 	g2="question" 	k="-10" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-4" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="k" 	g2="underscore" 	k="-133" />
<hkern g1="k" 	g2="v,w,y,yacute,ydieresis" 	k="55" />
<hkern g1="k" 	g2="one" 	k="-10" />
<hkern g1="k" 	g2="seven" 	k="10" />
<hkern g1="k" 	g2="two" 	k="-31" />
<hkern g1="k" 	g2="x" 	k="41" />
<hkern g1="k" 	g2="zero,six" 	k="20" />
<hkern g1="k" 	g2="T" 	k="63" />
<hkern g1="k" 	g2="ampersand" 	k="10" />
<hkern g1="k" 	g2="bracketleft" 	k="23" />
<hkern g1="k" 	g2="eight" 	k="14" />
<hkern g1="k" 	g2="four" 	k="61" />
<hkern g1="k" 	g2="nine" 	k="-20" />
<hkern g1="k" 	g2="three" 	k="-20" />
<hkern g1="k" 	g2="z" 	k="23" />
<hkern g1="k" 	g2="V,W" 	k="23" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="k" 	g2="X" 	k="16" />
<hkern g1="k" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="16" />
<hkern g1="k" 	g2="s" 	k="33" />
<hkern g1="d,l,uniFB02" 	g2="f,t,uniFB01,uniFB02" 	k="-4" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk,ordfeminine,ordmasculine" 	k="57" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="139" />
<hkern g1="h,m,n,ntilde" 	g2="degree" 	k="43" />
<hkern g1="h,m,n,ntilde" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-4" />
<hkern g1="h,m,n,ntilde" 	g2="paragraph" 	k="51" />
<hkern g1="h,m,n,ntilde" 	g2="percent" 	k="59" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="98" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="57" />
<hkern g1="h,m,n,ntilde" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="one" 	k="47" />
<hkern g1="h,m,n,ntilde" 	g2="seven" 	k="47" />
<hkern g1="h,m,n,ntilde" 	g2="two" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="nine" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="14" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk,ordfeminine,ordmasculine" 	k="37" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="139" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="degree" 	k="33" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="paragraph" 	k="49" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="percent" 	k="57" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="98" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="trademark" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="underscore" 	k="57" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v,w,y,yacute,ydieresis" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="one" 	k="57" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="seven" 	k="57" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="two" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="39" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="172" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="nine" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="questiondown" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="three" 	k="49" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="18" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="47" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V,W" 	k="82" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="14" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="78" />
<hkern g1="r" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="r" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="r" 	g2="backslash" 	k="41" />
<hkern g1="r" 	g2="degree" 	k="-59" />
<hkern g1="r" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="23" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="r" 	g2="paragraph" 	k="-61" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="r" 	g2="question" 	k="-10" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-66" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-66" />
<hkern g1="r" 	g2="slash" 	k="82" />
<hkern g1="r" 	g2="f,t,uniFB01,uniFB02" 	k="-31" />
<hkern g1="r" 	g2="underscore" 	k="25" />
<hkern g1="r" 	g2="v,w,y,yacute,ydieresis" 	k="-31" />
<hkern g1="r" 	g2="one" 	k="20" />
<hkern g1="r" 	g2="seven" 	k="-43" />
<hkern g1="r" 	g2="two" 	k="-4" />
<hkern g1="r" 	g2="x" 	k="16" />
<hkern g1="r" 	g2="zero,six" 	k="-4" />
<hkern g1="r" 	g2="T" 	k="92" />
<hkern g1="r" 	g2="ampersand" 	k="72" />
<hkern g1="r" 	g2="bracketright,braceright" 	k="31" />
<hkern g1="r" 	g2="bracketleft" 	k="39" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-4" />
<hkern g1="r" 	g2="eight" 	k="-2" />
<hkern g1="r" 	g2="four" 	k="61" />
<hkern g1="r" 	g2="b,h,k,l,germandbls,thorn" 	k="16" />
<hkern g1="r" 	g2="nine" 	k="-10" />
<hkern g1="r" 	g2="parenright" 	k="23" />
<hkern g1="r" 	g2="questiondown" 	k="78" />
<hkern g1="r" 	g2="section" 	k="-20" />
<hkern g1="r" 	g2="three" 	k="16" />
<hkern g1="r" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="47" />
<hkern g1="r" 	g2="V,W" 	k="57" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="84" />
<hkern g1="r" 	g2="X" 	k="88" />
<hkern g1="r" 	g2="five" 	k="-2" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="-4" />
<hkern g1="r" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="16" />
<hkern g1="r" 	g2="s" 	k="8" />
<hkern g1="r" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="r" 	g2="dollar,S" 	k="39" />
<hkern g1="r" 	g2="parenleft" 	k="31" />
<hkern g1="s" 	g2="backslash" 	k="123" />
<hkern g1="s" 	g2="degree" 	k="29" />
<hkern g1="s" 	g2="paragraph" 	k="49" />
<hkern g1="s" 	g2="question" 	k="37" />
<hkern g1="s" 	g2="trademark" 	k="37" />
<hkern g1="s" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="s" 	g2="one" 	k="16" />
<hkern g1="s" 	g2="seven" 	k="57" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="nine" 	k="10" />
<hkern g1="s" 	g2="three" 	k="20" />
<hkern g1="t" 	g2="asterisk,ordfeminine,ordmasculine" 	k="37" />
<hkern g1="t" 	g2="backslash" 	k="139" />
<hkern g1="t" 	g2="degree" 	k="10" />
<hkern g1="t" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="t" 	g2="paragraph" 	k="41" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="question" 	k="37" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="t" 	g2="f,t,uniFB01,uniFB02" 	k="29" />
<hkern g1="t" 	g2="trademark" 	k="57" />
<hkern g1="t" 	g2="underscore" 	k="-72" />
<hkern g1="t" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="t" 	g2="one" 	k="43" />
<hkern g1="t" 	g2="seven" 	k="51" />
<hkern g1="t" 	g2="two" 	k="16" />
<hkern g1="t" 	g2="zero,six" 	k="37" />
<hkern g1="t" 	g2="ampersand" 	k="18" />
<hkern g1="t" 	g2="eight" 	k="12" />
<hkern g1="t" 	g2="four" 	k="68" />
<hkern g1="t" 	g2="nine" 	k="31" />
<hkern g1="t" 	g2="section" 	k="23" />
<hkern g1="t" 	g2="five" 	k="16" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="53" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-92" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="backslash" 	k="41" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="degree" 	k="-78" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="25" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="paragraph" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="70" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="question" 	k="-66" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="quoteleft,quotedblleft" 	k="-70" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-37" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="slash" 	k="59" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="f,t,uniFB01,uniFB02" 	k="-31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="underscore" 	k="25" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="v,w,y,yacute,ydieresis" 	k="10" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="one" 	k="-14" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="seven" 	k="-31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="two" 	k="-14" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="x" 	k="61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="zero,six" 	k="-16" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="T" 	k="102" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="ampersand" 	k="45" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="colon,semicolon" 	k="-16" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="eight" 	k="-16" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="four" 	k="51" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="nine" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="questiondown" 	k="61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="three" 	k="10" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="z" 	k="20" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="57" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="X" 	k="61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="five" 	k="-16" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="j" 	k="31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="s" 	k="12" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="dollar,S" 	k="16" />
<hkern g1="x" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="x" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-25" />
<hkern g1="x" 	g2="backslash" 	k="98" />
<hkern g1="x" 	g2="degree" 	k="-20" />
<hkern g1="x" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="45" />
<hkern g1="x" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="x" 	g2="paragraph" 	k="-4" />
<hkern g1="x" 	g2="question" 	k="-14" />
<hkern g1="x" 	g2="slash" 	k="-20" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="x" 	g2="underscore" 	k="-78" />
<hkern g1="x" 	g2="v,w,y,yacute,ydieresis" 	k="61" />
<hkern g1="x" 	g2="one" 	k="-10" />
<hkern g1="x" 	g2="seven" 	k="16" />
<hkern g1="x" 	g2="two" 	k="6" />
<hkern g1="x" 	g2="zero,six" 	k="20" />
<hkern g1="x" 	g2="T" 	k="100" />
<hkern g1="x" 	g2="ampersand" 	k="41" />
<hkern g1="x" 	g2="colon,semicolon" 	k="20" />
<hkern g1="x" 	g2="eight" 	k="4" />
<hkern g1="x" 	g2="four" 	k="66" />
<hkern g1="x" 	g2="z" 	k="20" />
<hkern g1="x" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="x" 	g2="V,W" 	k="43" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="x" 	g2="j" 	k="4" />
<hkern g1="x" 	g2="s" 	k="20" />
<hkern g1="x" 	g2="dollar,S" 	k="23" />
<hkern g1="ampersand" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-31" />
<hkern g1="ampersand" 	g2="J" 	k="-72" />
<hkern g1="ampersand" 	g2="T" 	k="57" />
<hkern g1="ampersand" 	g2="V,W" 	k="41" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="ampersand" 	g2="Z" 	k="-31" />
<hkern g1="ampersand" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="ampersand" 	g2="f,t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="ampersand" 	g2="v,w,y,yacute,ydieresis" 	k="25" />
<hkern g1="ampersand" 	g2="x" 	k="4" />
<hkern g1="currency" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-16" />
<hkern g1="degree" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="188" />
<hkern g1="degree" 	g2="J" 	k="20" />
<hkern g1="degree" 	g2="T" 	k="-82" />
<hkern g1="degree" 	g2="V,W" 	k="-57" />
<hkern g1="degree" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="degree" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="degree" 	g2="f,t,uniFB01,uniFB02" 	k="-41" />
<hkern g1="degree" 	g2="v,w,y,yacute,ydieresis" 	k="-78" />
<hkern g1="degree" 	g2="x" 	k="-20" />
<hkern g1="degree" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="degree" 	g2="dollar,S" 	k="-41" />
<hkern g1="degree" 	g2="X" 	k="41" />
<hkern g1="degree" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="degree" 	g2="s" 	k="4" />
<hkern g1="percent" 	g2="asterisk,ordfeminine,ordmasculine" 	k="102" />
<hkern g1="percent" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-45" />
<hkern g1="section" 	g2="J" 	k="-16" />
<hkern g1="section" 	g2="f,t,uniFB01,uniFB02" 	k="-16" />
<hkern g1="section" 	g2="v,w,y,yacute,ydieresis" 	k="-16" />
<hkern g1="section" 	g2="x" 	k="-16" />
<hkern g1="section" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-16" />
<hkern g1="section" 	g2="s" 	k="-16" />
<hkern g1="section" 	g2="j" 	k="-16" />
<hkern g1="section" 	g2="b,h,k,l,germandbls,thorn" 	k="-16" />
<hkern g1="section" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-16" />
<hkern g1="section" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="-16" />
<hkern g1="section" 	g2="z" 	k="-16" />
<hkern g1="trademark" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="41" />
<hkern g1="trademark" 	g2="J" 	k="41" />
<hkern g1="trademark" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="trademark" 	g2="f,t,uniFB01,uniFB02" 	k="-66" />
<hkern g1="trademark" 	g2="v,w,y,yacute,ydieresis" 	k="-31" />
<hkern g1="trademark" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-57" />
<hkern g1="trademark" 	g2="s" 	k="-57" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="188" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="J" 	k="23" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="dollar,S" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="T" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="V,W" 	k="-70" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="X" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Z" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="ampersand" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="bracketright,braceright" 	k="-4" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="currency,Euro" 	k="-16" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="degree" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="exclam" 	k="-12" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="four" 	k="29" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="guillemotright,guilsinglright" 	k="-25" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="plus,hyphen,braceleft,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="nine" 	k="-72" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="one" 	k="-57" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="paragraph" 	k="-78" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="percent" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="66" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="question" 	k="-72" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="questiondown" 	k="174" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="seven" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="slash" 	k="287" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="f,t,uniFB01,uniFB02" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="three" 	k="-6" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="trademark" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="two" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="underscore" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="v,w,y,yacute,ydieresis" 	k="-92" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="x" 	k="-25" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="z" 	k="-37" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="zero,six" 	k="-16" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-61" />
<hkern g1="backslash" 	g2="J" 	k="-61" />
<hkern g1="backslash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="backslash" 	g2="T" 	k="41" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="backslash" 	g2="V,W" 	k="82" />
<hkern g1="backslash" 	g2="X" 	k="-41" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="backslash" 	g2="asterisk,ordfeminine,ordmasculine" 	k="287" />
<hkern g1="backslash" 	g2="j" 	k="-246" />
<hkern g1="backslash" 	g2="v,w,y,yacute,ydieresis" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="T" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="V,W" 	k="-45" />
<hkern g1="bracketleft,braceleft" 	g2="X" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="-51" />
<hkern g1="bracketleft,braceleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-4" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-205" />
<hkern g1="bracketright" 	g2="T" 	k="39" />
<hkern g1="bracketright" 	g2="X" 	k="47" />
<hkern g1="bracketright" 	g2="Y,Yacute,Ydieresis" 	k="23" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-78" />
<hkern g1="colon,semicolon" 	g2="T" 	k="20" />
<hkern g1="colon,semicolon" 	g2="V,W" 	k="45" />
<hkern g1="colon,semicolon" 	g2="X" 	k="12" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="colon,semicolon" 	g2="v,w,y,yacute,ydieresis" 	k="-16" />
<hkern g1="colon,semicolon" 	g2="f,t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="colon,semicolon" 	g2="x" 	k="20" />
<hkern g1="exclam" 	g2="J" 	k="-49" />
<hkern g1="exclam" 	g2="T" 	k="-41" />
<hkern g1="exclam" 	g2="V,W" 	k="-20" />
<hkern g1="exclam" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="exclam" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-12" />
<hkern g1="exclamdown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-20" />
<hkern g1="exclamdown" 	g2="J" 	k="-20" />
<hkern g1="exclamdown" 	g2="T" 	k="41" />
<hkern g1="exclamdown" 	g2="V,W" 	k="61" />
<hkern g1="exclamdown" 	g2="X" 	k="20" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="exclamdown" 	g2="j" 	k="-133" />
<hkern g1="exclamdown" 	g2="f,t,uniFB01,uniFB02" 	k="-8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="-61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V,W" 	k="25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="10" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="f,t,uniFB01,uniFB02" 	k="-4" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="31" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="J" 	k="41" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-16" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="T" 	k="66" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="V,W" 	k="45" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="X" 	k="35" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="63" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="v,w,y,yacute,ydieresis" 	k="25" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="f,t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="x" 	k="45" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="dollar,S" 	k="37" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="plus,hyphen,braceright,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="z" 	k="29" />
<hkern g1="parenleft" 	g2="T" 	k="31" />
<hkern g1="parenleft" 	g2="j" 	k="-168" />
<hkern g1="parenright" 	g2="T" 	k="78" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="J" 	k="-119" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="63" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V,W" 	k="74" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="66" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v,w,y,yacute,ydieresis" 	k="70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="f,t,uniFB01,uniFB02" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-37" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="dollar,S" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="question" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="86" />
<hkern g1="question" 	g2="J" 	k="55" />
<hkern g1="question" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="question" 	g2="V,W" 	k="25" />
<hkern g1="question" 	g2="X" 	k="35" />
<hkern g1="question" 	g2="Y,Yacute,Ydieresis" 	k="25" />
<hkern g1="question" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-31" />
<hkern g1="question" 	g2="v,w,y,yacute,ydieresis" 	k="-20" />
<hkern g1="question" 	g2="Z" 	k="20" />
<hkern g1="question" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-20" />
<hkern g1="questiondown" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="106" />
<hkern g1="questiondown" 	g2="T" 	k="117" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="72" />
<hkern g1="questiondown" 	g2="V,W" 	k="137" />
<hkern g1="questiondown" 	g2="X" 	k="-10" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="158" />
<hkern g1="questiondown" 	g2="asterisk,ordfeminine,ordmasculine" 	k="168" />
<hkern g1="questiondown" 	g2="j" 	k="-143" />
<hkern g1="questiondown" 	g2="v,w,y,yacute,ydieresis" 	k="92" />
<hkern g1="questiondown" 	g2="f,t,uniFB01,uniFB02" 	k="31" />
<hkern g1="questiondown" 	g2="x" 	k="-10" />
<hkern g1="questiondown" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="questiondown" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="102" />
<hkern g1="questiondown" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="31" />
<hkern g1="questiondown" 	g2="s" 	k="16" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="66" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="4" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-57" />
<hkern g1="quoteleft,quotedblleft" 	g2="V,W" 	k="-47" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="-6" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="v,w,y,yacute,ydieresis" 	k="-70" />
<hkern g1="quoteleft,quotedblleft" 	g2="f,t,uniFB01,uniFB02" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="quoteleft,quotedblleft" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="66" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="2" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-57" />
<hkern g1="quoteright,quotedblright" 	g2="V,W" 	k="-72" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="-16" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-57" />
<hkern g1="quoteright,quotedblright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="v,w,y,yacute,ydieresis" 	k="-37" />
<hkern g1="quoteright,quotedblright" 	g2="f,t,uniFB01,uniFB02" 	k="-4" />
<hkern g1="quoteright,quotedblright" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="57" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="90" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="82" />
<hkern g1="slash" 	g2="J" 	k="20" />
<hkern g1="slash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="T" 	k="-61" />
<hkern g1="slash" 	g2="V,W" 	k="-61" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-61" />
<hkern g1="slash" 	g2="j" 	k="-4" />
<hkern g1="slash" 	g2="v,w,y,yacute,ydieresis" 	k="61" />
<hkern g1="slash" 	g2="f,t,uniFB01,uniFB02" 	k="57" />
<hkern g1="slash" 	g2="x" 	k="129" />
<hkern g1="slash" 	g2="dollar,S" 	k="41" />
<hkern g1="slash" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="139" />
<hkern g1="slash" 	g2="z" 	k="139" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="139" />
<hkern g1="slash" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="139" />
<hkern g1="slash" 	g2="s" 	k="139" />
<hkern g1="underscore" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-119" />
<hkern g1="underscore" 	g2="J" 	k="-174" />
<hkern g1="underscore" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="underscore" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="underscore" 	g2="V,W" 	k="20" />
<hkern g1="underscore" 	g2="X" 	k="-98" />
<hkern g1="underscore" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="underscore" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="underscore" 	g2="j" 	k="-215" />
<hkern g1="underscore" 	g2="v,w,y,yacute,ydieresis" 	k="25" />
<hkern g1="underscore" 	g2="f,t,uniFB01,uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="x" 	k="-78" />
<hkern g1="underscore" 	g2="Z" 	k="-41" />
<hkern g1="underscore" 	g2="dollar,S" 	k="-41" />
<hkern g1="underscore" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="57" />
<hkern g1="underscore" 	g2="z" 	k="-61" />
<hkern g1="underscore" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="57" />
<hkern g1="underscore" 	g2="s" 	k="-20" />
<hkern g1="underscore" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="-61" />
</font>
</defs></svg> 