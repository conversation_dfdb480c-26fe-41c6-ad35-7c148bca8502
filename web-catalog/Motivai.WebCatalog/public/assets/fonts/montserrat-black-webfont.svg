<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="montserratblack" horiz-adv-x="1269" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="614" />
<glyph unicode="&#xfb01;" horiz-adv-x="1703" d="M0 721v338h154v6q0 219 131.5 347t367.5 128q174 0 275 -55l-111 -322q-60 27 -104 27q-52 0 -81.5 -32t-29.5 -97v-2h244v-338h-230v-721h-462v721h-154zM1092 1446q0 102 76.5 168.5t201.5 66.5q126 0 202.5 -63t76.5 -164q0 -107 -76.5 -175.5t-202.5 -68.5 q-125 0 -201.5 67t-76.5 169zM1139 0v1120h463v-1120h-463z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1540" d="M0 721v338h154v6q0 219 131.5 347t367.5 128q174 0 275 -55l-111 -322q-60 27 -104 27q-52 0 -81.5 -32t-29.5 -97v-2h244v-338h-230v-721h-462v721h-154zM975 0v1520h463v-1520h-463z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="614" />
<glyph unicode=" "  horiz-adv-x="614" />
<glyph unicode="&#x09;" horiz-adv-x="614" />
<glyph unicode="&#xa0;" horiz-adv-x="614" />
<glyph unicode="!" horiz-adv-x="643" d="M45 1434h553l-92 -885h-369zM51 225q0 104 76 173t195 69t194.5 -69t75.5 -173q0 -103 -76 -174t-194 -71t-194.5 71t-76.5 174z" />
<glyph unicode="&#x22;" horiz-adv-x="1001" d="M66 1434h389l-31 -615h-328zM547 1434h389l-31 -615h-327z" />
<glyph unicode="#" horiz-adv-x="1515" d="M27 281v307h286l31 258h-248v307h287l35 281h327l-34 -281h202l35 281h328l-35 -281h248v-307h-287l-31 -258h248v-307h-286l-35 -281h-328l35 281h-203l-35 -281h-327l34 281h-247zM641 588h203l30 258h-202z" />
<glyph unicode="$" horiz-adv-x="1345" d="M27 115l155 352q102 -60 228.5 -95.5t244.5 -35.5q187 0 187 78q0 28 -27 49t-73 34t-105.5 25t-125 27.5t-131.5 35t-125.5 53.5t-105.5 78t-73 112.5t-27 154.5q0 180 130 309.5t374 163.5v223h289v-219q244 -22 415 -119l-145 -350q-210 107 -399 107q-187 0 -187 -90 q0 -30 34 -51.5t90 -33.5t127 -28t145.5 -32t145.5 -49t127 -77t90 -117.5t34 -168.5q0 -172 -123.5 -300.5t-353.5 -168.5v-228h-289v217q-152 9 -292.5 47.5t-233.5 96.5z" />
<glyph unicode="%" horiz-adv-x="1878" d="M33 1055q0 186 107 292.5t274 106.5t274 -106.5t107 -292.5q0 -187 -106.5 -293.5t-274.5 -106.5t-274.5 106.5t-106.5 293.5zM276 0l979 1434h347l-979 -1434h-347zM324 1055q0 -95 25.5 -136.5t64.5 -41.5t64.5 41.5t25.5 136.5t-25.5 136.5t-64.5 41.5t-64.5 -41.5 t-25.5 -136.5zM1083 379q0 186 107 292.5t274 106.5t274 -106.5t107 -292.5q0 -187 -106.5 -293t-274.5 -106t-274.5 106t-106.5 293zM1374 379q0 -95 25.5 -136.5t64.5 -41.5t64.5 41.5t25.5 136.5t-25.5 136.5t-64.5 41.5t-64.5 -41.5t-25.5 -136.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1591" d="M53 383q0 134 68 234t211 180q-123 132 -123 274q0 173 142 280t374 107q215 0 345 -98.5t130 -263.5q0 -109 -61 -194.5t-187 -156.5l152 -129q36 79 55 187l371 -111q-41 -179 -135 -323l135 -115l-248 -281l-152 131q-213 -137 -479 -137q-170 0 -306.5 53.5t-214 149 t-77.5 213.5zM506 434q0 -53 48 -88t132 -35t162 35l-262 225q-80 -58 -80 -137zM629 1077q0 -27 14.5 -51t56.5 -63q113 61 113 122q0 33 -22 53.5t-64 20.5q-46 0 -72 -23t-26 -59z" />
<glyph unicode="'" horiz-adv-x="520" d="M66 1434h389l-31 -615h-328z" />
<glyph unicode="(" horiz-adv-x="780" d="M96 561q0 281 70.5 527.5t202.5 431.5h442q-136 -243 -199 -476t-63 -483t62.5 -482.5t199.5 -475.5h-442q-132 185 -202.5 431t-70.5 527z" />
<glyph unicode=")" horiz-adv-x="780" d="M-31 -397q137 243 199.5 475.5t62.5 482.5t-63 483t-199 476h443q132 -185 202 -431.5t70 -527.5t-70 -527t-202 -431h-443z" />
<glyph unicode="*" horiz-adv-x="968" d="M12 985l197 105l-197 104l140 229l200 -123l-6 220h275l-7 -220l201 123l141 -229l-196 -104l196 -105l-141 -229l-201 123l7 -220h-275l6 220l-200 -123z" />
<glyph unicode="+" d="M113 543v348h338v328h368v-328h338v-348h-338v-328h-368v328h-338z" />
<glyph unicode="," horiz-adv-x="622" d="M41 246q0 118 75.5 190t194.5 72t195 -72t76 -190q0 -55 -14.5 -108t-65.5 -161l-144 -305h-286l102 346q-63 30 -98 89t-35 139z" />
<glyph unicode="-" horiz-adv-x="798" d="M92 418v348h615v-348h-615z" />
<glyph unicode="." horiz-adv-x="622" d="M41 246q0 117 76.5 189.5t193.5 72.5t194 -72.5t77 -189.5q0 -116 -77.5 -191t-193.5 -75t-193 75t-77 191z" />
<glyph unicode="/" horiz-adv-x="901" d="M-82 -205l676 1929h410l-676 -1929h-410z" />
<glyph unicode="0" horiz-adv-x="1417" d="M59 717q0 349 180 549t470 200q142 0 261.5 -51t205.5 -145t134 -236t48 -317t-48 -317t-134 -236.5t-205.5 -145.5t-261.5 -51t-261.5 51t-205.5 145.5t-134.5 236.5t-48.5 317zM545 717q0 -363 164 -363q163 0 163 363q0 362 -163 362q-164 0 -164 -362z" />
<glyph unicode="1" horiz-adv-x="856" d="M10 1069v365h729v-1434h-483v1069h-246z" />
<glyph unicode="2" horiz-adv-x="1245" d="M-27 1135q81 154 239 242.5t382 88.5q257 0 418.5 -123t161.5 -325q0 -119 -50 -222.5t-196 -234.5l-205 -186h483v-375h-1149v297l518 479q67 63 90 108.5t23 88.5q0 53 -35.5 82.5t-103.5 29.5q-66 0 -122.5 -35t-88.5 -98z" />
<glyph unicode="3" horiz-adv-x="1257" d="M-12 96l164 357q179 -105 385 -105q85 0 135.5 28.5t50.5 82.5q0 98 -170 98h-191v297l211 215h-512v365h1069v-297l-266 -271q166 -41 255 -148t89 -259q0 -76 -23.5 -146t-75.5 -133.5t-127 -110.5t-185.5 -74.5t-243.5 -27.5q-153 0 -303 33t-262 96z" />
<glyph unicode="4" horiz-adv-x="1458" d="M47 246v307l608 881h496l-541 -813h195v219h455v-219h190v-375h-190v-246h-469v246h-744z" />
<glyph unicode="5" horiz-adv-x="1267" d="M10 96l164 357q179 -105 383 -105q84 0 136 29.5t52 83.5q0 50 -47 76t-170 26h-415l71 871h953v-365h-555l-11 -141h74q154 0 269.5 -35t183.5 -98.5t100.5 -144.5t32.5 -179q0 -79 -23.5 -151t-75.5 -137t-127.5 -112.5t-185.5 -75.5t-244 -28q-153 0 -303 33t-262 96z " />
<glyph unicode="6" horiz-adv-x="1353" d="M59 672q0 247 100.5 427.5t279 273.5t415.5 93q129 0 246 -29t198 -83l-174 -338q-108 76 -260 76q-124 0 -205 -62.5t-106 -181.5q113 80 285 80q132 0 243 -55t179.5 -161.5t68.5 -242.5q0 -150 -78 -265.5t-209.5 -176t-293.5 -60.5q-155 0 -281 44.5t-217 131.5 t-141 221.5t-50 307.5zM567 446q0 -66 41 -105.5t107 -39.5q65 0 106 39t41 106t-41 107.5t-106 40.5t-106.5 -40.5t-41.5 -107.5z" />
<glyph unicode="7" horiz-adv-x="1320" d="M43 850v584h1227v-297l-477 -1137h-529l455 1059h-277v-209h-399z" />
<glyph unicode="8" horiz-adv-x="1388" d="M51 426q0 212 193 330q-142 113 -142 282q0 128 75.5 225.5t209.5 150t307 52.5t307 -52.5t209.5 -150t75.5 -225.5q0 -170 -141 -282q92 -56 142 -140t50 -190q0 -208 -178.5 -333.5t-464.5 -125.5t-464.5 125.5t-178.5 333.5zM537 442q0 -68 43.5 -108.5t113.5 -40.5 t114 40.5t44 108.5t-44 109t-114 41t-113.5 -41t-43.5 -109zM580 1018q0 -57 31.5 -90t82.5 -33t83 33t32 90t-32 90t-83 33t-82.5 -33t-31.5 -90z" />
<glyph unicode="9" horiz-adv-x="1353" d="M25 965q0 150 78 265t209.5 175.5t293.5 60.5q208 0 360.5 -77t240 -237t87.5 -390q0 -185 -57.5 -335.5t-162 -251t-251 -154.5t-323.5 -54q-128 0 -246 29.5t-199 83.5l174 338q108 -76 260 -76q124 0 206 62.5t106 181.5q-113 -80 -285 -80q-132 0 -243 55 t-179.5 161.5t-68.5 242.5zM492 987q0 -67 41 -107t106 -40t106 40t41 107t-41 106.5t-106 39.5t-106 -39.5t-41 -106.5z" />
<glyph unicode=":" horiz-adv-x="622" d="M41 246q0 117 76.5 189.5t193.5 72.5t194 -72.5t77 -189.5q0 -116 -77.5 -191t-193.5 -75t-193 75t-77 191zM41 879q0 117 76.5 189.5t193.5 72.5t194 -72.5t77 -189.5q0 -116 -77.5 -191.5t-193.5 -75.5t-193 75.5t-77 191.5z" />
<glyph unicode=";" horiz-adv-x="622" d="M41 246q0 118 75.5 190t194.5 72t195 -72t76 -190q0 -55 -14.5 -108t-65.5 -161l-144 -305h-286l102 346q-63 30 -98 89t-35 139zM41 879q0 117 76.5 189.5t193.5 72.5t194 -72.5t77 -189.5q0 -116 -77.5 -191.5t-193.5 -75.5t-193 75.5t-77 191.5z" />
<glyph unicode="&#x3c;" d="M113 535v364l1044 379v-348l-620 -213l620 -213v-348z" />
<glyph unicode="=" d="M113 276v349h1044v-349h-1044zM113 809v348h1044v-348h-1044z" />
<glyph unicode="&#x3e;" d="M113 156v348l620 213l-620 213v348l1044 -379v-364z" />
<glyph unicode="?" horiz-adv-x="1241" d="M-27 1135q82 155 242 243t397 88q250 0 406 -102.5t156 -288.5q0 -87 -34 -158.5t-82 -117.5t-96 -85.5t-82 -80.5t-34 -84h-422q0 69 27 128.5t66 100.5t78 76.5t66 71t27 69.5q0 46 -39 71.5t-98 25.5q-66 0 -123.5 -37.5t-89.5 -102.5zM365 225q0 104 75.5 173 t194.5 69t194.5 -69t75.5 -173q0 -103 -76 -174t-194 -71t-194 71t-76 174z" />
<glyph unicode="@" horiz-adv-x="2123" d="M59 518q0 197 72.5 370t202.5 300t323 200.5t422 73.5q217 0 402 -69.5t312.5 -189t199 -282.5t71.5 -346q0 -143 -38 -257t-104.5 -186.5t-153 -111t-185.5 -38.5q-238 0 -323 178q-90 -178 -312 -178q-83 0 -161 36t-139 100.5t-97.5 162t-36.5 211.5q0 115 36 211.5 t96 160.5t138.5 99.5t163.5 35.5q163 0 252 -100v82h393v-586q0 -112 80 -112q105 0 105 286q0 280 -192.5 456.5t-506.5 176.5q-155 0 -286.5 -52.5t-222.5 -143.5t-142 -217t-51 -271q0 -149 50 -276.5t140 -218t220 -142t284 -51.5q215 0 414 90l86 -252 q-93 -44 -228.5 -71t-271.5 -27q-227 0 -417.5 73t-320.5 199.5t-202 301t-72 374.5zM915 492q0 -91 40.5 -140t105.5 -49t106 49t41 140q0 89 -41 137.5t-106 48.5t-105.5 -48.5t-40.5 -137.5z" />
<glyph unicode="A" horiz-adv-x="1650" d="M-39 0l627 1434h475l627 -1434h-500l-96 250h-545l-96 -250h-492zM682 598h279l-140 360z" />
<glyph unicode="B" horiz-adv-x="1583" d="M117 0v1434h766q286 0 432.5 -102.5t146.5 -272.5q0 -101 -50.5 -180.5t-147.5 -128.5q127 -46 196.5 -137.5t69.5 -217.5q0 -189 -156 -292t-450 -103h-807zM592 338h291q159 0 159 113q0 112 -159 112h-291v-225zM592 883h225q158 0 158 106q0 107 -158 107h-225v-213z " />
<glyph unicode="C" horiz-adv-x="1523" d="M59 717q0 217 103 388t285.5 266t410.5 95q209 0 372.5 -73.5t270.5 -212.5l-305 -273q-133 168 -313 168q-152 0 -244 -97.5t-92 -260.5t92.5 -261t243.5 -98q180 0 313 168l305 -272q-107 -139 -270.5 -213t-372.5 -74q-171 0 -319 55.5t-253.5 154t-166 238.5 t-60.5 302z" />
<glyph unicode="D" horiz-adv-x="1691" d="M117 0v1434h706q239 0 421.5 -86t285 -249t102.5 -382t-102.5 -382t-285 -249t-421.5 -86h-706zM600 377h203q155 0 248.5 89.5t93.5 250.5t-93.5 250.5t-248.5 89.5h-203v-680z" />
<glyph unicode="E" horiz-adv-x="1378" d="M117 0v1434h1165v-365h-690v-168h606v-348h-606v-188h717v-365h-1192z" />
<glyph unicode="F" horiz-adv-x="1318" d="M117 0v1434h1165v-365h-682v-246h598v-364h-598v-459h-483z" />
<glyph unicode="G" horiz-adv-x="1574" d="M59 717q0 217 103.5 388t288.5 266t419 95q211 0 379 -71t277 -205l-309 -272q-136 157 -322 157q-160 0 -254 -96.5t-94 -261.5q0 -162 93.5 -260.5t248.5 -98.5q93 0 176 35v363h420v-606q-128 -87 -295.5 -135t-329.5 -48q-172 0 -320.5 55.5t-254 154t-166 238 t-60.5 302.5z" />
<glyph unicode="H" horiz-adv-x="1646" d="M117 0v1434h483v-508h447v508h483v-1434h-483v528h-447v-528h-483z" />
<glyph unicode="I" horiz-adv-x="716" d="M117 0v1434h483v-1434h-483z" />
<glyph unicode="J" horiz-adv-x="1175" d="M-49 180l260 307q114 -141 227 -141q75 0 115.5 45.5t40.5 132.5v545h-488v365h965v-881q0 -293 -150.5 -439.5t-441.5 -146.5q-168 0 -303.5 54.5t-224.5 158.5z" />
<glyph unicode="K" horiz-adv-x="1566" d="M117 0v1434h475v-521l471 521h526l-573 -631l602 -803h-559l-359 475l-108 -121v-354h-475z" />
<glyph unicode="L" horiz-adv-x="1263" d="M117 0v1434h483v-1059h647v-375h-1130z" />
<glyph unicode="M" horiz-adv-x="1953" d="M117 0v1434h397l467 -764l455 764h397l4 -1434h-440l-4 653l-312 -524h-213l-311 502v-631h-440z" />
<glyph unicode="N" horiz-adv-x="1646" d="M117 0v1434h397l545 -652v652h471v-1434h-397l-545 651v-651h-471z" />
<glyph unicode="O" horiz-adv-x="1736" d="M59 717q0 160 61 299.5t168 238t257 155t323 56.5t323 -56.5t257 -155t168 -238t61 -299.5t-61 -299.5t-168 -238.5t-257 -155.5t-323 -56.5t-323 56.5t-257 155.5t-168 238.5t-61 299.5zM547 717q0 -163 92 -261t229 -98t229.5 98t92.5 261t-92 260.5t-230 97.5 q-137 0 -229 -97.5t-92 -260.5z" />
<glyph unicode="P" horiz-adv-x="1521" d="M117 0v1434h690q300 0 476.5 -146t176.5 -395t-176.5 -395t-476.5 -146h-207v-352h-483zM600 725h176q98 0 147.5 44t49.5 124t-49.5 124t-147.5 44h-176v-336z" />
<glyph unicode="Q" horiz-adv-x="1736" d="M59 717q0 160 61 299.5t168 238t257 155t323 56.5t323 -56.5t257 -155t168 -238t61 -299.5q0 -235 -125 -416.5t-338 -267.5q57 -60 134 -60q105 0 198 93l209 -246q-153 -182 -418 -182q-191 0 -333.5 74t-335.5 276q-273 58 -441 256.5t-168 472.5zM547 717 q0 -163 92 -261t229 -98t229.5 98t92.5 261t-92 260.5t-230 97.5q-137 0 -229 -97.5t-92 -260.5z" />
<glyph unicode="R" horiz-adv-x="1525" d="M117 0v1434h690q300 0 476.5 -146t176.5 -395q0 -154 -70 -269t-200 -182l301 -442h-516l-244 360h-131v-360h-483zM600 725h176q98 0 147.5 44t49.5 124t-49.5 124t-147.5 44h-176v-336z" />
<glyph unicode="S" horiz-adv-x="1345" d="M27 115l155 352q102 -60 228.5 -95.5t244.5 -35.5q187 0 187 78q0 28 -27 49t-73 34t-105.5 25t-125 27.5t-131.5 35t-125.5 53.5t-105.5 78t-73 112.5t-27 154.5q0 101 42.5 188t124 153t210.5 104t291 38q317 0 540 -125l-145 -350q-210 107 -399 107q-187 0 -187 -90 q0 -30 34 -51.5t90 -33.5t127 -28t145.5 -32t145.5 -49t127 -77t90 -117.5t34 -168.5q0 -100 -42.5 -186.5t-124 -153.5t-210.5 -105.5t-291 -38.5q-178 0 -346.5 40.5t-277.5 107.5z" />
<glyph unicode="T" horiz-adv-x="1339" d="M8 1059v375h1323v-375h-420v-1059h-483v1059h-420z" />
<glyph unicode="U" horiz-adv-x="1605" d="M104 645v789h484v-775q0 -301 219 -301t219 301v775h475v-789q0 -322 -184 -500t-514 -178q-331 0 -515 178t-184 500z" />
<glyph unicode="V" horiz-adv-x="1609" d="M-39 1434h520l342 -838l351 838h475l-607 -1434h-475z" />
<glyph unicode="W" horiz-adv-x="2469" d="M20 1434h498l250 -816l264 816h445l249 -826l265 826h458l-458 -1434h-518l-232 766l-244 -766h-518z" />
<glyph unicode="X" horiz-adv-x="1560" d="M-16 0l518 723l-500 711h543l243 -361l238 361h520l-497 -693l528 -741h-553l-248 383l-241 -383h-551z" />
<glyph unicode="Y" horiz-adv-x="1454" d="M-55 1434h510l293 -496l294 496h467l-540 -914v-520h-484v526z" />
<glyph unicode="Z" horiz-adv-x="1406" d="M66 0v297l655 762h-635v375h1264v-297l-656 -762h688v-375h-1316z" />
<glyph unicode="[" horiz-adv-x="839" d="M117 -397v1917h702v-349h-239v-1220h239v-348h-702z" />
<glyph unicode="\" horiz-adv-x="901" d="M-102 1724h409l676 -1929h-410z" />
<glyph unicode="]" horiz-adv-x="839" d="M20 -49h240v1220h-240v349h703v-1917h-703v348z" />
<glyph unicode="^" d="M74 285l379 864h364l379 -864h-348l-213 514l-213 -514h-348z" />
<glyph unicode="_" horiz-adv-x="1024" d="M0 0h1024v-246h-1024v246z" />
<glyph unicode="`" horiz-adv-x="1228" d="M119 1571h440l270 -328h-307z" />
<glyph unicode="a" horiz-adv-x="1308" d="M47 328q0 165 127.5 251.5t384.5 86.5h186q-21 131 -210 131q-76 0 -154 -23.5t-133 -64.5l-148 305q96 60 233 93.5t271 33.5q606 0 606 -539v-602h-430v147q-78 -167 -317 -167q-198 0 -307 98.5t-109 249.5zM492 348q0 -39 29.5 -64.5t80.5 -25.5q50 0 89 27t57 80v77 h-115q-141 0 -141 -94z" />
<glyph unicode="b" horiz-adv-x="1433" d="M102 0v1520h463v-476q100 97 285 97q110 0 208 -40.5t172 -114t117.5 -183.5t43.5 -242t-43.5 -242.5t-117.5 -184t-172 -114t-208 -40.5q-207 0 -307 112v-92h-441zM557 561q0 -105 51 -163t131 -58t131.5 58t51.5 163t-51.5 162t-131.5 57t-131 -57t-51 -162z" />
<glyph unicode="c" horiz-adv-x="1259" d="M43 561q0 168 84.5 300.5t235.5 206t342 73.5q203 0 348.5 -88t201.5 -244l-358 -176q-66 147 -195 147q-82 0 -136 -57.5t-54 -161.5q0 -106 54 -163.5t136 -57.5q129 0 195 147l358 -176q-56 -156 -201 -243.5t-349 -87.5q-191 0 -342 73.5t-235.5 206t-84.5 301.5z " />
<glyph unicode="d" horiz-adv-x="1443" d="M53 561q0 132 43.5 242t117.5 183.5t172 114t208 40.5q185 0 285 -97v476h462v-1520h-440v92q-100 -112 -307 -112q-110 0 -208 40.5t-172 114t-117.5 184t-43.5 242.5zM522 561q0 -105 51.5 -163t131.5 -58t131 58t51 163t-51 162t-131 57t-131.5 -57t-51.5 -162z" />
<glyph unicode="e" horiz-adv-x="1337" d="M43 561q0 167 83 300t228 206.5t324 73.5q126 0 236.5 -38t195.5 -109t134.5 -183t49.5 -250q0 -9 -6 -108h-778q50 -131 225 -131q73 0 124 18.5t110 62.5l241 -243q-165 -180 -489 -180q-305 0 -491.5 163.5t-186.5 417.5zM502 676h360q-13 70 -61 109.5t-119 39.5 q-72 0 -119 -39t-61 -110z" />
<glyph unicode="f" horiz-adv-x="872" d="M0 721v338h154v6q0 219 131.5 347t367.5 128q174 0 275 -55l-111 -322q-60 27 -104 27q-52 0 -81.5 -32t-29.5 -97v-2h244v-338h-230v-721h-462v721h-154z" />
<glyph unicode="g" horiz-adv-x="1456" d="M43 602q0 161 75.5 285.5t197.5 189t266 64.5q227 0 331 -148v127h441v-897q0 -320 -177.5 -480.5t-504.5 -160.5q-351 0 -561 139l155 316q65 -50 165.5 -80.5t193.5 -30.5q139 0 202.5 59t63.5 175v20q-104 -119 -309 -119q-144 0 -266 65t-197.5 190t-75.5 286z M512 602q0 -80 55 -130t138 -50t136.5 49.5t53.5 130.5t-53.5 129.5t-136.5 48.5t-138 -49t-55 -129z" />
<glyph unicode="h" horiz-adv-x="1437" d="M102 0v1520h463v-490q129 111 316 111q99 0 181 -29.5t144.5 -89t97.5 -157.5t35 -226v-639h-462v559q0 193 -138 193q-77 0 -125.5 -54.5t-48.5 -171.5v-526h-463z" />
<glyph unicode="i" horiz-adv-x="667" d="M55 1446q0 102 77 168.5t202 66.5q126 0 202 -63t76 -164q0 -107 -76 -175.5t-202 -68.5q-125 0 -202 67t-77 169zM102 0v1120h463v-1120h-463z" />
<glyph unicode="j" horiz-adv-x="684" d="M-193 -362l111 321q52 -27 105 -27q44 0 70 32t26 97v1059h463v-1063q0 -219 -130 -347t-360 -128q-176 0 -285 56zM72 1446q0 102 76.5 168.5t201.5 66.5q126 0 202.5 -63t76.5 -164q0 -107 -76.5 -175.5t-202.5 -68.5q-125 0 -201.5 67t-76.5 169z" />
<glyph unicode="k" horiz-adv-x="1460" d="M102 0v1520h463v-725l322 325h547l-463 -485l499 -635h-559l-272 344l-74 -80v-264h-463z" />
<glyph unicode="l" horiz-adv-x="667" d="M102 0v1520h463v-1520h-463z" />
<glyph unicode="m" horiz-adv-x="2129" d="M102 0v1120h441v-104q125 125 317 125q237 0 350 -170q68 82 164 126t211 44q202 0 324.5 -125t122.5 -377v-639h-463v559q0 193 -125 193q-66 0 -106 -49.5t-40 -155.5v-547h-462v559q0 193 -125 193q-66 0 -106 -49.5t-40 -155.5v-547h-463z" />
<glyph unicode="n" horiz-adv-x="1437" d="M102 0v1120h441v-110q131 131 338 131q99 0 181 -29.5t144.5 -89t97.5 -157.5t35 -226v-639h-462v559q0 193 -138 193q-77 0 -125.5 -54.5t-48.5 -171.5v-526h-463z" />
<glyph unicode="o" horiz-adv-x="1388" d="M43 561q0 168 83.5 300.5t232 206t335.5 73.5q188 0 337 -73.5t232 -206t83 -300.5t-83 -300.5t-232 -206.5t-337 -74q-187 0 -336 74t-232 206.5t-83 300.5zM512 561q0 -105 51 -163t131 -58t131.5 58t51.5 163t-51.5 162t-131.5 57t-131 -57t-51 -162z" />
<glyph unicode="p" horiz-adv-x="1433" d="M102 -397v1517h441v-92q101 113 307 113q110 0 208 -41t172 -114.5t117.5 -184t43.5 -242.5t-43.5 -242t-117.5 -183t-172 -113.5t-208 -40.5q-186 0 -285 96v-473h-463zM557 559q0 -105 51 -162t131 -57t131.5 57t51.5 162t-51.5 163t-131.5 58t-131 -58t-51 -163z" />
<glyph unicode="q" horiz-adv-x="1433" d="M43 559q0 132 43.5 242.5t117.5 184t172 114.5t208 41q206 0 307 -113v92h440v-1517h-463v473q-99 -96 -284 -96q-110 0 -208 40.5t-172 113.5t-117.5 183t-43.5 242zM512 559q0 -105 51 -162t131 -57t131.5 57t51.5 162t-51.5 163t-131.5 58t-131 -58t-51 -163z" />
<glyph unicode="r" horiz-adv-x="933" d="M102 0v1120h441v-121q124 142 362 142v-410q-55 8 -102 8q-238 0 -238 -241v-498h-463z" />
<glyph unicode="s" horiz-adv-x="1157" d="M33 90l127 305q79 -47 184.5 -73.5t206.5 -26.5q80 0 111.5 13t31.5 40q0 18 -22 29.5t-59.5 17.5t-86 10.5t-101.5 13t-106.5 21.5t-102 38t-86 60t-59.5 91.5t-22 128.5q0 166 151 274.5t423 108.5q127 0 248 -23.5t208 -68.5l-127 -306q-150 82 -323 82 q-152 0 -152 -53q0 -21 27.5 -33.5t73 -17.5t103 -13.5t118 -18t118 -34t103 -59.5t73 -96t27.5 -142q0 -162 -150.5 -270t-428.5 -108q-144 0 -282.5 30t-225.5 80z" />
<glyph unicode="t" horiz-adv-x="942" d="M0 721v338h154v313h462v-313h230v-338h-230v-283q0 -51 27 -79.5t70 -28.5q58 0 104 30l111 -321q-104 -59 -285 -59q-236 0 -362.5 113t-126.5 341v287h-154z" />
<glyph unicode="u" horiz-adv-x="1429" d="M98 502v618h463v-538q0 -115 37.5 -164t108.5 -49q67 0 112 54.5t45 170.5v526h463v-1120h-440v106q-123 -126 -318 -126q-103 0 -187 31t-148.5 92.5t-100 163.5t-35.5 235z" />
<glyph unicode="v" horiz-adv-x="1318" d="M-31 1120h475l226 -600l241 600h439l-451 -1120h-479z" />
<glyph unicode="w" horiz-adv-x="2000" d="M-12 1120h438l192 -592l201 592h393l195 -598l201 598h405l-389 -1120h-450l-170 518l-177 -518h-450z" />
<glyph unicode="x" horiz-adv-x="1320" d="M-18 0l424 555l-412 565h522l160 -237l168 237h485l-411 -545l421 -575h-530l-154 240l-172 -240h-501z" />
<glyph unicode="y" horiz-adv-x="1318" d="M-31 1120h475l232 -600l235 600h439l-461 -1143q-88 -219 -219 -307t-322 -88q-93 0 -188.5 28t-151.5 73l154 317q76 -59 164 -59q75 0 114 45z" />
<glyph unicode="z" horiz-adv-x="1167" d="M61 0v266l463 516h-446v338h1030v-266l-463 -516h485v-338h-1069z" />
<glyph unicode="{" horiz-adv-x="897" d="M92 387v348h68q40 0 64 23t24 67v258q0 210 121 323.5t356 113.5h152v-349h-21q-71 0 -108 -36.5t-37 -104.5v-194q0 -121 -39.5 -184.5t-126.5 -90.5q88 -27 127 -90t39 -184v-195q0 -68 37 -104.5t108 -36.5h21v-348h-152q-235 0 -356 113t-121 323v258q0 44 -24 67 t-64 23h-68z" />
<glyph unicode="|" horiz-adv-x="655" d="M117 -397v1917h422v-1917h-422z" />
<glyph unicode="}" horiz-adv-x="897" d="M20 -49h21q71 0 108 36.5t37 104.5v195q0 121 39 184t127 90q-87 27 -126.5 90.5t-39.5 184.5v194q0 68 -37 104.5t-108 36.5h-21v349h152q235 0 356 -113.5t121 -323.5v-258q0 -44 24 -67t64 -23h68v-348h-68q-40 0 -64 -23t-24 -67v-258q0 -210 -121 -323t-356 -113 h-152v348z" />
<glyph unicode="~" d="M82 506q3 211 99 331.5t251 120.5q63 0 123 -24t97 -52.5t78 -52.5t71 -24q45 0 73 34t31 95h283q-3 -211 -99 -332t-251 -121q-63 0 -123 24t-97 53t-78 53t-71 24q-45 0 -73 -34t-31 -95h-283z" />
<glyph unicode="&#xa1;" horiz-adv-x="643" d="M45 -315l92 886h369l92 -886h-553zM51 895q0 103 76.5 174.5t194.5 71.5t194 -71.5t76 -174.5q0 -104 -75.5 -173t-194.5 -69t-195 69t-76 173z" />
<glyph unicode="&#xa2;" horiz-adv-x="1259" d="M43 561q0 215 136 367.5t360 195.5v242h288v-233q159 -25 270.5 -109.5t157.5 -214.5l-358 -176q-66 147 -195 147q-82 0 -136 -57.5t-54 -161.5q0 -106 54 -163.5t136 -57.5q129 0 195 147l358 -176q-46 -130 -157.5 -214t-270.5 -109v-234h-288v242 q-225 43 -360.5 195.5t-135.5 369.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1421" d="M61 0v365h185v217h-185v270h185q10 296 200.5 455t530.5 159q260 0 426 -88l-139 -364q-113 61 -246 61q-275 0 -289 -223h430v-270h-430v-217h623v-365h-1291z" />
<glyph unicode="&#xa4;" horiz-adv-x="1433" d="M37 227l209 209q-51 105 -51 226q0 110 45 208l-203 203l256 256l199 -199q107 52 223 52q118 0 225 -52l199 199l256 -256l-203 -203q43 -100 43 -208q0 -119 -51 -224l211 -211l-256 -256l-215 215q-98 -45 -209 -45q-109 0 -209 43l-213 -213zM553 662 q0 -68 47.5 -115t114.5 -47q68 0 116 47.5t48 114.5t-48 115t-116 48q-67 0 -114.5 -47.5t-47.5 -115.5z" />
<glyph unicode="&#xa5;" horiz-adv-x="1576" d="M-55 1434h516l348 -527l350 527h473l-518 -799h244v-229h-328v-54h328v-229h-328v-123h-483v123h-328v229h328v54h-328v229h246z" />
<glyph unicode="&#xa6;" horiz-adv-x="655" d="M117 319h422v-716h-422v716zM117 803v717h422v-717h-422z" />
<glyph unicode="&#xa7;" horiz-adv-x="1124" d="M33 -115l127 305q77 -46 179 -73t183 -27q58 0 85.5 19t27.5 49t-33 48.5t-85.5 28.5t-116 20.5t-127 31.5t-116 53.5t-85.5 93.5t-33 146q0 160 113 264q-74 81 -74 211q0 177 145 288t428 111q105 0 237 -23.5t210 -68.5l-127 -305q-150 82 -340 82q-148 0 -148 -68 q0 -25 33 -41t85 -26t115.5 -21.5t127 -35.5t115.5 -60t85 -101.5t33 -154.5q0 -144 -112 -244q75 -78 75 -213q0 -176 -133 -287.5t-381 -111.5q-134 0 -270.5 30.5t-222.5 79.5zM438 618q0 -37 44 -55t138 -36q5 -1 7 -1q51 36 51 86q0 20 -13 35t-44.5 26t-56 16.5 t-75.5 15.5q-51 -36 -51 -87z" />
<glyph unicode="&#xa8;" horiz-adv-x="1228" d="M203 1407q0 81 52.5 131.5t131.5 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-131.5 50t-52.5 130zM657 1407q0 81 53 131.5t132 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-132 50t-53 130z" />
<glyph unicode="&#xa9;" horiz-adv-x="1564" d="M59 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM252 717q0 -152 67.5 -276t188.5 -195.5t272 -71.5t273.5 72 t191 197t68.5 278q0 233 -148.5 386t-380.5 153q-231 0 -381.5 -155.5t-150.5 -387.5zM371 717q0 178 124 291.5t318 113.5q136 0 234 -61.5t135 -169.5l-262 -133q-38 82 -109 82q-43 0 -73.5 -32.5t-30.5 -90.5t30.5 -90.5t73.5 -32.5q71 0 109 82l262 -133 q-37 -108 -135 -170t-234 -62q-194 0 -318 114t-124 292z" />
<glyph unicode="&#xaa;" horiz-adv-x="864" d="M35 1057q0 200 330 200h108q-16 68 -133 68q-43 0 -91.5 -13.5t-82.5 -37.5l-98 190q63 36 151 56t174 20q398 0 398 -326v-354h-291v86q-49 -98 -201 -98q-127 0 -195.5 59t-68.5 150zM330 1069q0 -21 17 -34t46 -13q65 0 84 57v39h-63q-84 0 -84 -49z" />
<glyph unicode="&#xab;" horiz-adv-x="1316" d="M51 561l283 410h409l-272 -410l272 -409h-409zM594 561l283 410h409l-272 -410l272 -409h-409z" />
<glyph unicode="&#xac;" d="M113 543v348h1044v-696h-369v348h-675z" />
<glyph unicode="&#xad;" horiz-adv-x="798" d="M92 418v348h615v-348h-615z" />
<glyph unicode="&#xae;" horiz-adv-x="1564" d="M59 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM252 717q0 -152 67.5 -276t188.5 -195.5t272 -71.5 q229 0 381 158h-250l-127 196h-47v-196h-295v770h367q170 0 266 -77.5t96 -209.5q0 -161 -122 -233l141 -220q123 151 123 359q0 233 -148.5 386t-380.5 153q-231 0 -381.5 -155.5t-150.5 -387.5zM737 731h54q41 0 63.5 22.5t22.5 61.5t-22.5 61.5t-63.5 22.5h-54v-168z" />
<glyph unicode="&#xaf;" horiz-adv-x="1228" d="M184 1286v242h860v-242h-860z" />
<glyph unicode="&#xb0;" horiz-adv-x="851" d="M41 1085q0 156 113 263.5t272 107.5t272 -107.5t113 -263.5t-113 -263t-272 -107t-272 107t-113 263zM270 1085q0 -66 44.5 -111.5t111.5 -45.5t111.5 45.5t44.5 111.5q0 67 -44.5 112.5t-111.5 45.5t-111.5 -45.5t-44.5 -112.5z" />
<glyph unicode="&#xb1;" d="M113 0v348h1044v-348h-1044zM113 793v329h338v312h368v-312h338v-329h-338v-312h-368v312h-338z" />
<glyph unicode="&#xb2;" horiz-adv-x="880" d="M14 1337q47 93 149.5 148t254.5 55q170 0 273.5 -73t103.5 -197q0 -66 -34.5 -127t-129.5 -144l-113 -96h303v-233h-749v186l325 279q74 62 74 110q0 26 -20.5 42t-61.5 16q-94 0 -137 -78z" />
<glyph unicode="&#xb3;" horiz-adv-x="880" d="M18 735l107 223q133 -71 283 -71q118 0 118 57q0 49 -98 49h-147v187l129 112h-344v228h733v-187l-170 -145q107 -24 164 -88.5t57 -155.5q0 -56 -25.5 -107.5t-75.5 -94t-136 -68t-197 -25.5t-219.5 23.5t-178.5 62.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="1228" d="M399 1243l271 328h440l-403 -328h-308z" />
<glyph unicode="&#xb5;" horiz-adv-x="1433" d="M102 -397v1517h463v-538q0 -115 37.5 -164t108.5 -49q67 0 112 54.5t45 170.5v526h463v-1120h-399v104q-81 -124 -236 -124q-70 0 -131 28v-405h-463z" />
<glyph unicode="&#xb6;" horiz-adv-x="1495" d="M16 1100q0 193 145.5 306.5t387.5 113.5h829v-1725h-372v1407h-164v-1407h-373v887q-195 6 -324 120t-129 298z" />
<glyph unicode="&#xb7;" horiz-adv-x="704" d="M82 594q0 121 74.5 192.5t195.5 71.5t196 -72t75 -192q0 -121 -75 -194.5t-196 -73.5t-195.5 73.5t-74.5 194.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1228" d="M330 -449l57 170q62 -24 123 -24q98 0 98 49q0 41 -84 41h-84l56 233h237l-28 -126q90 -11 132 -57t42 -107q0 -102 -95.5 -162t-253.5 -60q-53 0 -109.5 12t-90.5 31z" />
<glyph unicode="&#xb9;" horiz-adv-x="880" d="M131 670v227h180v395h-159v228h487v-623h152v-227h-660z" />
<glyph unicode="&#xba;" horiz-adv-x="897" d="M33 1194q0 152 117 249t299 97q183 0 299 -97t116 -249t-116 -249t-299 -97q-182 0 -299 97t-117 249zM342 1194q0 -58 30.5 -89.5t76.5 -31.5t76 31.5t30 89.5q0 59 -29.5 90t-76.5 31t-77 -31t-30 -90z" />
<glyph unicode="&#xbb;" horiz-adv-x="1316" d="M31 152l272 409l-272 410h409l283 -410l-283 -409h-409zM573 152l273 409l-273 410h410l283 -410l-283 -409h-410z" />
<glyph unicode="&#xbc;" horiz-adv-x="2164" d="M131 584v227h180v395h-159v228h487v-623h152v-227h-660zM420 0l979 1434h346l-979 -1434h-346zM1282 139v193l358 518h342l-331 -477h116v112h275v-112h102v-234h-102v-139h-311v139h-449z" />
<glyph unicode="&#xbd;" horiz-adv-x="2164" d="M131 584v227h180v395h-159v228h487v-623h152v-227h-660zM420 0l979 1434h346l-979 -1434h-346zM1298 668q47 92 149.5 147t254.5 55q170 0 273.5 -73t103.5 -197q0 -66 -34.5 -126t-129.5 -144l-113 -97h303v-233h-749v186l325 279q74 62 74 110q0 26 -20.5 42t-61.5 16 q-94 0 -137 -78z" />
<glyph unicode="&#xbe;" horiz-adv-x="2164" d="M18 649l107 223q133 -71 283 -71q118 0 118 57q0 49 -98 49h-147v187l129 112h-344v228h733v-187l-170 -145q107 -24 164 -88.5t57 -155.5q0 -56 -25.5 -107.5t-75.5 -94t-136 -68t-197 -25.5t-219.5 23.5t-178.5 62.5zM420 0l979 1434h346l-979 -1434h-346zM1282 139 v193l358 518h342l-331 -477h116v112h275v-112h102v-234h-102v-139h-311v139h-449z" />
<glyph unicode="&#xbf;" horiz-adv-x="1241" d="M68 45q0 64 18 119t47 95t63.5 74.5t69.5 63t64 55.5t47 57t18 62h422q0 -69 -27 -128.5t-66 -100.5t-78 -76.5t-66 -71t-27 -69.5q0 -46 38.5 -71t98.5 -25q66 0 123.5 37.5t89.5 101.5l365 -182q-82 -155 -242 -243.5t-397 -88.5q-250 0 -405.5 102.5t-155.5 288.5z M336 895q0 103 76 174.5t194 71.5t194.5 -71.5t76.5 -174.5q0 -104 -76 -173t-195 -69t-194.5 69t-75.5 173z" />
<glyph unicode="&#xc0;" horiz-adv-x="1650" d="M-39 0l627 1434h475l627 -1434h-500l-96 250h-545l-96 -250h-492zM330 1878h440l270 -328h-307zM682 598h279l-140 360z" />
<glyph unicode="&#xc1;" horiz-adv-x="1650" d="M-39 0l627 1434h475l627 -1434h-500l-96 250h-545l-96 -250h-492zM610 1550l271 328h440l-403 -328h-308zM682 598h279l-140 360z" />
<glyph unicode="&#xc2;" horiz-adv-x="1650" d="M-39 0l627 1434h475l627 -1434h-500l-96 250h-545l-96 -250h-492zM367 1550l262 328h393l262 -328h-287l-172 158l-172 -158h-286zM682 598h279l-140 360z" />
<glyph unicode="&#xc3;" horiz-adv-x="1650" d="M-39 0l627 1434h475l627 -1434h-500l-96 250h-545l-96 -250h-492zM377 1550q3 163 78 259t204 96q51 0 100.5 -19t81 -41.5t65 -41.5t57.5 -19q40 0 64.5 25.5t27.5 68.5h219q-3 -159 -78.5 -253.5t-204.5 -94.5q-51 0 -100.5 19t-80.5 41.5t-64.5 41.5t-57.5 19 q-39 0 -64 -27.5t-28 -73.5h-219zM682 598h279l-140 360z" />
<glyph unicode="&#xc4;" horiz-adv-x="1650" d="M-39 0l627 1434h475l627 -1434h-500l-96 250h-545l-96 -250h-492zM414 1714q0 81 52.5 131.5t131.5 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-131.5 50t-52.5 130zM682 598h279l-140 360zM868 1714q0 81 53 131.5t132 50.5t131.5 -50.5t52.5 -131.5 q0 -80 -52.5 -130t-131.5 -50t-132 50t-53 130z" />
<glyph unicode="&#xc5;" horiz-adv-x="1650" d="M-39 0l627 1434h475l627 -1434h-500l-96 250h-545l-96 -250h-492zM539 1796q0 116 82.5 195.5t203.5 79.5t204 -79.5t83 -195.5t-83 -195t-204 -79t-203.5 79t-82.5 195zM682 598h279l-140 360zM715 1796q0 -49 31.5 -79.5t78.5 -30.5t79 30.5t32 79.5t-32 80t-79 31 t-78.5 -31t-31.5 -80z" />
<glyph unicode="&#xc6;" horiz-adv-x="2291" d="M-39 0l756 1434h1478v-365h-690v-168h606v-348h-606v-188h717v-365h-1192v250h-452l-125 -250h-492zM750 598h280v471h-47z" />
<glyph unicode="&#xc7;" horiz-adv-x="1523" d="M59 717q0 217 103 388t285.5 266t410.5 95q209 0 372.5 -73.5t270.5 -212.5l-305 -273q-133 168 -313 168q-152 0 -244 -97.5t-92 -260.5t92.5 -261t243.5 -98q180 0 313 168l305 -272q-101 -131 -251.5 -204.5t-340.5 -80.5l-16 -75q90 -11 132 -57t42 -107 q0 -102 -95 -162t-253 -60q-53 0 -110 12t-91 31l57 170q62 -24 123 -24q99 0 99 49q0 41 -84 41h-84l47 197q-277 55 -447 253.5t-170 479.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="1378" d="M117 0v1434h1165v-365h-690v-168h606v-348h-606v-188h717v-365h-1192zM205 1878h440l270 -328h-307z" />
<glyph unicode="&#xc9;" horiz-adv-x="1378" d="M117 0v1434h1165v-365h-690v-168h606v-348h-606v-188h717v-365h-1192zM485 1550l271 328h440l-403 -328h-308z" />
<glyph unicode="&#xca;" horiz-adv-x="1378" d="M117 0v1434h1165v-365h-690v-168h606v-348h-606v-188h717v-365h-1192zM242 1550l262 328h393l262 -328h-287l-172 158l-172 -158h-286z" />
<glyph unicode="&#xcb;" horiz-adv-x="1378" d="M117 0v1434h1165v-365h-690v-168h606v-348h-606v-188h717v-365h-1192zM289 1714q0 81 52.5 131.5t131.5 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-131.5 50t-52.5 130zM743 1714q0 81 53 131.5t132 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130 t-131.5 -50t-132 50t-53 130z" />
<glyph unicode="&#xcc;" horiz-adv-x="716" d="M-137 1878h440l270 -328h-307zM117 0v1434h483v-1434h-483z" />
<glyph unicode="&#xcd;" horiz-adv-x="716" d="M117 0v1434h483v-1434h-483zM143 1550l271 328h440l-403 -328h-308z" />
<glyph unicode="&#xce;" horiz-adv-x="716" d="M-100 1550l262 328h393l262 -328h-287l-172 158l-172 -158h-286zM117 0v1434h483v-1434h-483z" />
<glyph unicode="&#xcf;" horiz-adv-x="716" d="M-53 1714q0 81 52.5 131.5t131.5 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-131.5 50t-52.5 130zM117 0v1434h483v-1434h-483zM401 1714q0 81 53 131.5t132 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-132 50t-53 130z" />
<glyph unicode="&#xd0;" horiz-adv-x="1732" d="M-18 588v278h176v568h706q239 0 421.5 -86t285 -249t102.5 -382t-102.5 -382t-285 -249t-421.5 -86h-706v588h-176zM641 377h203q155 0 248.5 89.5t93.5 250.5t-93.5 250.5t-248.5 89.5h-203v-191h303v-278h-303v-211z" />
<glyph unicode="&#xd1;" horiz-adv-x="1646" d="M117 0v1434h397l545 -652v652h471v-1434h-397l-545 651v-651h-471zM375 1550q3 163 78 259t204 96q51 0 100.5 -19t81 -41.5t65 -41.5t57.5 -19q40 0 64.5 25.5t27.5 68.5h219q-3 -159 -78.5 -253.5t-204.5 -94.5q-51 0 -100.5 19t-80.5 41.5t-64.5 41.5t-57.5 19 q-39 0 -64 -27.5t-28 -73.5h-219z" />
<glyph unicode="&#xd2;" horiz-adv-x="1736" d="M59 717q0 160 61 299.5t168 238t257 155t323 56.5t323 -56.5t257 -155t168 -238t61 -299.5t-61 -299.5t-168 -238.5t-257 -155.5t-323 -56.5t-323 56.5t-257 155.5t-168 238.5t-61 299.5zM373 1878h440l270 -328h-307zM547 717q0 -163 92 -261t229 -98t229.5 98t92.5 261 t-92 260.5t-230 97.5q-137 0 -229 -97.5t-92 -260.5z" />
<glyph unicode="&#xd3;" horiz-adv-x="1736" d="M59 717q0 160 61 299.5t168 238t257 155t323 56.5t323 -56.5t257 -155t168 -238t61 -299.5t-61 -299.5t-168 -238.5t-257 -155.5t-323 -56.5t-323 56.5t-257 155.5t-168 238.5t-61 299.5zM547 717q0 -163 92 -261t229 -98t229.5 98t92.5 261t-92 260.5t-230 97.5 q-137 0 -229 -97.5t-92 -260.5zM653 1550l271 328h440l-403 -328h-308z" />
<glyph unicode="&#xd4;" horiz-adv-x="1736" d="M59 717q0 160 61 299.5t168 238t257 155t323 56.5t323 -56.5t257 -155t168 -238t61 -299.5t-61 -299.5t-168 -238.5t-257 -155.5t-323 -56.5t-323 56.5t-257 155.5t-168 238.5t-61 299.5zM410 1550l262 328h393l262 -328h-287l-172 158l-172 -158h-286zM547 717 q0 -163 92 -261t229 -98t229.5 98t92.5 261t-92 260.5t-230 97.5q-137 0 -229 -97.5t-92 -260.5z" />
<glyph unicode="&#xd5;" horiz-adv-x="1736" d="M59 717q0 160 61 299.5t168 238t257 155t323 56.5t323 -56.5t257 -155t168 -238t61 -299.5t-61 -299.5t-168 -238.5t-257 -155.5t-323 -56.5t-323 56.5t-257 155.5t-168 238.5t-61 299.5zM420 1550q3 163 78 259t204 96q51 0 100.5 -19t81 -41.5t65 -41.5t57.5 -19 q40 0 64.5 25.5t27.5 68.5h219q-3 -159 -78.5 -253.5t-204.5 -94.5q-51 0 -100.5 19t-80.5 41.5t-64.5 41.5t-57.5 19q-39 0 -64 -27.5t-28 -73.5h-219zM547 717q0 -163 92 -261t229 -98t229.5 98t92.5 261t-92 260.5t-230 97.5q-137 0 -229 -97.5t-92 -260.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1736" d="M59 717q0 160 61 299.5t168 238t257 155t323 56.5t323 -56.5t257 -155t168 -238t61 -299.5t-61 -299.5t-168 -238.5t-257 -155.5t-323 -56.5t-323 56.5t-257 155.5t-168 238.5t-61 299.5zM457 1714q0 81 52.5 131.5t131.5 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130 t-131.5 -50t-131.5 50t-52.5 130zM547 717q0 -163 92 -261t229 -98t229.5 98t92.5 261t-92 260.5t-230 97.5q-137 0 -229 -97.5t-92 -260.5zM911 1714q0 81 53 131.5t132 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-132 50t-53 130z" />
<glyph unicode="&#xd7;" d="M143 471l246 246l-246 246l246 245l246 -245l246 245l245 -245l-245 -246l245 -246l-245 -246l-246 246l-246 -246z" />
<glyph unicode="&#xd8;" horiz-adv-x="1736" d="M59 717q0 160 61 299.5t168 238t257 155t323 56.5q189 0 351 -67l127 178h266l-205 -289q128 -102 199 -248.5t71 -322.5q0 -160 -61 -299.5t-168 -238.5t-257 -155.5t-323 -56.5q-186 0 -350 68l-127 -178h-266l205 288q-128 102 -199.5 249t-71.5 323zM547 717 q0 -119 47 -199l383 539q-53 18 -109 18q-137 0 -229 -97.5t-92 -260.5zM760 377q55 -19 108 -19q137 0 229.5 98t92.5 261q0 118 -47 198z" />
<glyph unicode="&#xd9;" horiz-adv-x="1605" d="M104 645v789h484v-775q0 -301 219 -301t219 301v775h475v-789q0 -322 -184 -500t-514 -178q-331 0 -515 178t-184 500zM311 1878h441l270 -328h-307z" />
<glyph unicode="&#xda;" horiz-adv-x="1605" d="M104 645v789h484v-775q0 -301 219 -301t219 301v775h475v-789q0 -322 -184 -500t-514 -178q-331 0 -515 178t-184 500zM592 1550l270 328h441l-404 -328h-307z" />
<glyph unicode="&#xdb;" horiz-adv-x="1605" d="M104 645v789h484v-775q0 -301 219 -301t219 301v775h475v-789q0 -322 -184 -500t-514 -178q-331 0 -515 178t-184 500zM348 1550l262 328h394l262 -328h-287l-172 158l-172 -158h-287z" />
<glyph unicode="&#xdc;" horiz-adv-x="1605" d="M104 645v789h484v-775q0 -301 219 -301t219 301v775h475v-789q0 -322 -184 -500t-514 -178q-331 0 -515 178t-184 500zM395 1714q0 81 53 131.5t132 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-132 50t-53 130zM850 1714q0 81 52.5 131.5t131.5 50.5 t132 -50.5t53 -131.5q0 -80 -53 -130t-132 -50t-131.5 50t-52.5 130z" />
<glyph unicode="&#xdd;" horiz-adv-x="1454" d="M-55 1434h510l293 -496l294 496h467l-540 -914v-520h-484v526zM512 1550l270 328h441l-404 -328h-307z" />
<glyph unicode="&#xde;" horiz-adv-x="1521" d="M117 0v1436h483v-140h207q300 0 476.5 -145.5t176.5 -394.5t-176.5 -395t-476.5 -146h-207v-215h-483zM600 588h176q98 0 147.5 44t49.5 124t-49.5 124t-147.5 44h-176v-336z" />
<glyph unicode="&#xdf;" horiz-adv-x="1462" d="M102 0v967q0 137 48.5 247.5t132.5 181t193.5 107.5t234.5 37q271 0 425 -126.5t154 -321.5q0 -163 -131 -281q127 -50 193.5 -145t66.5 -230q0 -214 -145.5 -335t-392.5 -121q-162 0 -250 32l45 353q67 -25 135 -25q62 0 100.5 28.5t38.5 80.5q0 106 -155 106h-107v348 q71 3 114.5 42t43.5 102q0 62 -36.5 99.5t-96.5 37.5q-68 0 -108 -40.5t-40 -113.5v-1030h-463z" />
<glyph unicode="&#xe0;" horiz-adv-x="1308" d="M47 328q0 165 127.5 251.5t384.5 86.5h186q-21 131 -210 131q-76 0 -154 -23.5t-133 -64.5l-148 305q96 60 233 93.5t271 33.5q606 0 606 -539v-602h-430v147q-78 -167 -317 -167q-198 0 -307 98.5t-109 249.5zM162 1571h440l270 -328h-307zM492 348q0 -39 29.5 -64.5 t80.5 -25.5q50 0 89 27t57 80v77h-115q-141 0 -141 -94z" />
<glyph unicode="&#xe1;" horiz-adv-x="1308" d="M47 328q0 165 127.5 251.5t384.5 86.5h186q-21 131 -210 131q-76 0 -154 -23.5t-133 -64.5l-148 305q96 60 233 93.5t271 33.5q606 0 606 -539v-602h-430v147q-78 -167 -317 -167q-198 0 -307 98.5t-109 249.5zM442 1243l271 328h440l-403 -328h-308zM492 348 q0 -39 29.5 -64.5t80.5 -25.5q50 0 89 27t57 80v77h-115q-141 0 -141 -94z" />
<glyph unicode="&#xe2;" horiz-adv-x="1308" d="M47 328q0 165 127.5 251.5t384.5 86.5h186q-21 131 -210 131q-76 0 -154 -23.5t-133 -64.5l-148 305q96 60 233 93.5t271 33.5q606 0 606 -539v-602h-430v147q-78 -167 -317 -167q-198 0 -307 98.5t-109 249.5zM199 1243l262 328h393l262 -328h-287l-172 158l-172 -158 h-286zM492 348q0 -39 29.5 -64.5t80.5 -25.5q50 0 89 27t57 80v77h-115q-141 0 -141 -94z" />
<glyph unicode="&#xe3;" horiz-adv-x="1308" d="M47 328q0 165 127.5 251.5t384.5 86.5h186q-21 131 -210 131q-76 0 -154 -23.5t-133 -64.5l-148 305q96 60 233 93.5t271 33.5q606 0 606 -539v-602h-430v147q-78 -167 -317 -167q-198 0 -307 98.5t-109 249.5zM209 1243q3 163 78.5 258.5t204.5 95.5q51 0 100.5 -18.5 t80.5 -41.5t64.5 -41.5t57.5 -18.5q40 0 64.5 25.5t27.5 68.5h219q-3 -159 -78.5 -253.5t-204.5 -94.5q-51 0 -100.5 18.5t-80.5 41.5t-64.5 41.5t-57.5 18.5q-40 0 -64.5 -27t-27.5 -73h-219zM492 348q0 -39 29.5 -64.5t80.5 -25.5q50 0 89 27t57 80v77h-115 q-141 0 -141 -94z" />
<glyph unicode="&#xe4;" horiz-adv-x="1308" d="M47 328q0 165 127.5 251.5t384.5 86.5h186q-21 131 -210 131q-76 0 -154 -23.5t-133 -64.5l-148 305q96 60 233 93.5t271 33.5q606 0 606 -539v-602h-430v147q-78 -167 -317 -167q-198 0 -307 98.5t-109 249.5zM246 1407q0 81 52.5 131.5t131.5 50.5t131.5 -50.5 t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-131.5 50t-52.5 130zM492 348q0 -39 29.5 -64.5t80.5 -25.5q50 0 89 27t57 80v77h-115q-141 0 -141 -94zM700 1407q0 81 53 131.5t132 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-132 50t-53 130z" />
<glyph unicode="&#xe5;" horiz-adv-x="1308" d="M47 328q0 165 127.5 251.5t384.5 86.5h186q-21 131 -210 131q-76 0 -154 -23.5t-133 -64.5l-148 305q96 60 233 93.5t271 33.5q606 0 606 -539v-602h-430v147q-78 -167 -317 -167q-198 0 -307 98.5t-109 249.5zM371 1483q0 116 82.5 195t203.5 79t204 -79t83 -195 t-83 -195.5t-204 -79.5t-203.5 79.5t-82.5 195.5zM492 348q0 -39 29.5 -64.5t80.5 -25.5q50 0 89 27t57 80v77h-115q-141 0 -141 -94zM547 1483q0 -49 31.5 -80t78.5 -31t79 31t32 80t-32 79.5t-79 30.5t-78.5 -30.5t-31.5 -79.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="2054" d="M47 344q0 156 128.5 242t383.5 86h184q-26 125 -206 125q-80 0 -157 -23.5t-132 -64.5l-148 305q96 60 231 93.5t271 33.5q262 0 406 -123q172 123 395 123q119 0 228 -38.5t194 -109t135.5 -182t50.5 -246.5q0 -50 -8 -116h-776q48 -127 221 -127q66 0 120.5 19.5 t114.5 61.5l242 -243q-171 -180 -483 -180q-302 0 -477 167q-72 -91 -174.5 -129t-237.5 -38q-236 0 -371 99t-135 265zM492 354q0 -44 29 -70t85 -26q67 0 104.5 40.5t37.5 111.5v39h-115q-141 0 -141 -95zM1214 672h355q-13 72 -59.5 112.5t-114.5 40.5 q-69 0 -117.5 -40.5t-63.5 -112.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="1259" d="M43 561q0 168 84.5 300.5t235.5 206t342 73.5q203 0 348.5 -88t201.5 -244l-358 -176q-66 147 -195 147q-82 0 -136 -57.5t-54 -161.5q0 -106 54 -163.5t136 -57.5q129 0 195 147l358 -176q-53 -145 -183 -232t-314 -97l-21 -88q90 -11 132 -57t42 -107q0 -102 -95 -162 t-253 -60q-53 0 -110 12t-91 31l58 170q62 -24 123 -24q98 0 98 49q0 41 -84 41h-84l49 211q-218 48 -348.5 199.5t-130.5 363.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="1337" d="M43 561q0 167 83 300t228 206.5t324 73.5q126 0 236.5 -38t195.5 -109t134.5 -183t49.5 -250q0 -9 -6 -108h-778q50 -131 225 -131q73 0 124 18.5t110 62.5l241 -243q-165 -180 -489 -180q-305 0 -491.5 163.5t-186.5 417.5zM174 1571h440l271 -328h-307zM502 676h360 q-13 70 -61 109.5t-119 39.5q-72 0 -119 -39t-61 -110z" />
<glyph unicode="&#xe9;" horiz-adv-x="1337" d="M43 561q0 167 83 300t228 206.5t324 73.5q126 0 236.5 -38t195.5 -109t134.5 -183t49.5 -250q0 -9 -6 -108h-778q50 -131 225 -131q73 0 124 18.5t110 62.5l241 -243q-165 -180 -489 -180q-305 0 -491.5 163.5t-186.5 417.5zM455 1243l270 328h440l-403 -328h-307z M502 676h360q-13 70 -61 109.5t-119 39.5q-72 0 -119 -39t-61 -110z" />
<glyph unicode="&#xea;" horiz-adv-x="1337" d="M43 561q0 167 83 300t228 206.5t324 73.5q126 0 236.5 -38t195.5 -109t134.5 -183t49.5 -250q0 -9 -6 -108h-778q50 -131 225 -131q73 0 124 18.5t110 62.5l241 -243q-165 -180 -489 -180q-305 0 -491.5 163.5t-186.5 417.5zM211 1243l262 328h393l262 -328h-286 l-172 158l-172 -158h-287zM502 676h360q-13 70 -61 109.5t-119 39.5q-72 0 -119 -39t-61 -110z" />
<glyph unicode="&#xeb;" horiz-adv-x="1337" d="M43 561q0 167 83 300t228 206.5t324 73.5q126 0 236.5 -38t195.5 -109t134.5 -183t49.5 -250q0 -9 -6 -108h-778q50 -131 225 -131q73 0 124 18.5t110 62.5l241 -243q-165 -180 -489 -180q-305 0 -491.5 163.5t-186.5 417.5zM258 1407q0 81 52.5 131.5t131.5 50.5 t132 -50.5t53 -131.5q0 -80 -53 -130t-132 -50t-131.5 50t-52.5 130zM502 676h360q-13 70 -61 109.5t-119 39.5q-72 0 -119 -39t-61 -110zM713 1407q0 81 52.5 131.5t131.5 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-131.5 50t-52.5 130z" />
<glyph unicode="&#xec;" horiz-adv-x="667" d="M-162 1571h441l270 -328h-307zM102 0v1120h463v-1120h-463z" />
<glyph unicode="&#xed;" horiz-adv-x="667" d="M102 0v1120h463v-1120h-463zM119 1243l270 328h440l-403 -328h-307z" />
<glyph unicode="&#xee;" horiz-adv-x="667" d="M-74 1243l215 328h385l215 -328h-286l-121 137l-121 -137h-287zM102 0v1120h463v-1120h-463z" />
<glyph unicode="&#xef;" horiz-adv-x="667" d="M-27 1407q0 81 48.5 131.5t121.5 50.5q72 0 120 -50.5t48 -131.5q0 -80 -48 -130t-120 -50q-73 0 -121.5 50t-48.5 130zM102 0v1120h463v-1120h-463zM356 1407q0 81 48.5 131.5t121.5 50.5q72 0 120 -50.5t48 -131.5q0 -80 -48 -130t-120 -50q-73 0 -121.5 50t-48.5 130z " />
<glyph unicode="&#xf0;" horiz-adv-x="1384" d="M43 469q0 134 69 240t184 162.5t253 56.5q187 0 301 -88q-6 162 -80 254l-475 -172l-72 186l299 109q-50 4 -76 4q-135 0 -247 -37l-43 334q157 34 319 34q339 0 555 -151l191 69l71 -186l-104 -39q153 -210 153 -553q0 -340 -192 -532.5t-514 -192.5q-166 0 -300 61.5 t-213 177t-79 263.5zM514 446q0 -66 45 -105.5t113 -39.5t112.5 39.5t44.5 105.5t-45 107t-112 41t-112.5 -41t-45.5 -107z" />
<glyph unicode="&#xf1;" horiz-adv-x="1437" d="M102 0v1120h441v-110q131 131 338 131q99 0 181 -29.5t144.5 -89t97.5 -157.5t35 -226v-639h-462v559q0 193 -138 193q-77 0 -125.5 -54.5t-48.5 -171.5v-526h-463zM279 1243q3 163 78 258.5t204 95.5q51 0 100.5 -18.5t80.5 -41.5t64.5 -41.5t57.5 -18.5q40 0 64.5 25.5 t27.5 68.5h220q-3 -159 -78.5 -253.5t-204.5 -94.5q-51 0 -100.5 18.5t-80.5 41.5t-64.5 41.5t-57.5 18.5q-40 0 -64.5 -27t-27.5 -73h-219z" />
<glyph unicode="&#xf2;" horiz-adv-x="1388" d="M43 561q0 168 83.5 300.5t232 206t335.5 73.5q188 0 337 -73.5t232 -206t83 -300.5t-83 -300.5t-232 -206.5t-337 -74q-187 0 -336 74t-232 206.5t-83 300.5zM193 1571h440l270 -328h-307zM512 561q0 -105 51 -163t131 -58t131.5 58t51.5 163t-51.5 162t-131.5 57 t-131 -57t-51 -162z" />
<glyph unicode="&#xf3;" horiz-adv-x="1388" d="M43 561q0 168 83.5 300.5t232 206t335.5 73.5q188 0 337 -73.5t232 -206t83 -300.5t-83 -300.5t-232 -206.5t-337 -74q-187 0 -336 74t-232 206.5t-83 300.5zM473 1243l270 328h441l-404 -328h-307zM512 561q0 -105 51 -163t131 -58t131.5 58t51.5 163t-51.5 162 t-131.5 57t-131 -57t-51 -162z" />
<glyph unicode="&#xf4;" horiz-adv-x="1388" d="M43 561q0 168 83.5 300.5t232 206t335.5 73.5q188 0 337 -73.5t232 -206t83 -300.5t-83 -300.5t-232 -206.5t-337 -74q-187 0 -336 74t-232 206.5t-83 300.5zM229 1243l263 328h393l262 -328h-287l-172 158l-172 -158h-287zM512 561q0 -105 51 -163t131 -58t131.5 58 t51.5 163t-51.5 162t-131.5 57t-131 -57t-51 -162z" />
<glyph unicode="&#xf5;" horiz-adv-x="1388" d="M43 561q0 168 83.5 300.5t232 206t335.5 73.5q188 0 337 -73.5t232 -206t83 -300.5t-83 -300.5t-232 -206.5t-337 -74q-187 0 -336 74t-232 206.5t-83 300.5zM240 1243q3 163 78 258.5t204 95.5q51 0 100.5 -18.5t80.5 -41.5t64.5 -41.5t57.5 -18.5q40 0 65 25.5t28 68.5 h219q-3 -159 -78.5 -253.5t-204.5 -94.5q-51 0 -100.5 18.5t-80.5 41.5t-64.5 41.5t-57.5 18.5q-40 0 -64.5 -27t-27.5 -73h-219zM512 561q0 -105 51 -163t131 -58t131.5 58t51.5 163t-51.5 162t-131.5 57t-131 -57t-51 -162z" />
<glyph unicode="&#xf6;" horiz-adv-x="1388" d="M43 561q0 168 83.5 300.5t232 206t335.5 73.5q188 0 337 -73.5t232 -206t83 -300.5t-83 -300.5t-232 -206.5t-337 -74q-187 0 -336 74t-232 206.5t-83 300.5zM276 1407q0 81 53 131.5t132 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-132 50t-53 130z M512 561q0 -105 51 -163t131 -58t131.5 58t51.5 163t-51.5 162t-131.5 57t-131 -57t-51 -162zM731 1407q0 81 52.5 131.5t131.5 50.5t132 -50.5t53 -131.5q0 -80 -53 -130t-132 -50t-131.5 50t-52.5 130z" />
<glyph unicode="&#xf7;" d="M113 543v348h1044v-348h-1044zM406 266q0 98 65.5 159.5t163.5 61.5t163.5 -61.5t65.5 -159.5t-65.5 -161.5t-163.5 -63.5t-163.5 63.5t-65.5 161.5zM406 1171q0 98 65.5 160t163.5 62t163.5 -62t65.5 -160t-65.5 -161.5t-163.5 -63.5t-163.5 63.5t-65.5 161.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1388" d="M43 561q0 168 83.5 300.5t232 206t335.5 73.5q133 0 252 -39l96 147h195l-141 -219q119 -78 184.5 -199t65.5 -270q0 -168 -83 -300.5t-232 -206.5t-337 -74q-141 0 -254 40l-106 -163h-195l154 235q-119 78 -184.5 199t-65.5 270zM512 561q0 -54 16 -104l207 319 q-24 4 -41 4q-80 0 -131 -57t-51 -162zM651 344q24 -4 43 -4q80 0 131.5 58t51.5 163q0 59 -19 105z" />
<glyph unicode="&#xf9;" horiz-adv-x="1429" d="M98 502v618h463v-538q0 -115 37.5 -164t108.5 -49q67 0 112 54.5t45 170.5v526h463v-1120h-440v106q-123 -126 -318 -126q-103 0 -187 31t-148.5 92.5t-100 163.5t-35.5 235zM217 1571h440l271 -328h-307z" />
<glyph unicode="&#xfa;" horiz-adv-x="1429" d="M98 502v618h463v-538q0 -115 37.5 -164t108.5 -49q67 0 112 54.5t45 170.5v526h463v-1120h-440v106q-123 -126 -318 -126q-103 0 -187 31t-148.5 92.5t-100 163.5t-35.5 235zM498 1243l270 328h440l-403 -328h-307z" />
<glyph unicode="&#xfb;" horiz-adv-x="1429" d="M98 502v618h463v-538q0 -115 37.5 -164t108.5 -49q67 0 112 54.5t45 170.5v526h463v-1120h-440v106q-123 -126 -318 -126q-103 0 -187 31t-148.5 92.5t-100 163.5t-35.5 235zM254 1243l262 328h393l262 -328h-286l-172 158l-172 -158h-287z" />
<glyph unicode="&#xfc;" horiz-adv-x="1429" d="M98 502v618h463v-538q0 -115 37.5 -164t108.5 -49q67 0 112 54.5t45 170.5v526h463v-1120h-440v106q-123 -126 -318 -126q-103 0 -187 31t-148.5 92.5t-100 163.5t-35.5 235zM301 1407q0 81 52.5 131.5t131.5 50.5t132 -50.5t53 -131.5q0 -80 -53 -130t-132 -50 t-131.5 50t-52.5 130zM756 1407q0 81 52.5 131.5t131.5 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-131.5 50t-52.5 130z" />
<glyph unicode="&#xfd;" horiz-adv-x="1318" d="M-31 1120h475l232 -600l235 600h439l-461 -1143q-88 -219 -219 -307t-322 -88q-93 0 -188.5 28t-151.5 73l154 317q76 -59 164 -59q75 0 114 45zM426 1243l270 328h441l-404 -328h-307z" />
<glyph unicode="&#xfe;" horiz-adv-x="1433" d="M102 -397v1917h463v-469q100 90 285 90q110 0 208 -41t172 -114.5t117.5 -184t43.5 -242.5t-43.5 -242t-117.5 -183t-172 -113.5t-208 -40.5q-186 0 -285 96v-473h-463zM557 559q0 -105 51 -162t131 -57t131.5 57t51.5 162t-51.5 163t-131.5 58t-131 -58t-51 -163z" />
<glyph unicode="&#xff;" horiz-adv-x="1318" d="M-31 1120h475l232 -600l235 600h439l-461 -1143q-88 -219 -219 -307t-322 -88q-93 0 -188.5 28t-151.5 73l154 317q76 -59 164 -59q75 0 114 45zM229 1407q0 81 53 131.5t132 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-132 50t-53 130zM684 1407 q0 81 52.5 131.5t131.5 50.5t132 -50.5t53 -131.5q0 -80 -53 -130t-132 -50t-131.5 50t-52.5 130z" />
<glyph unicode="&#x152;" horiz-adv-x="2353" d="M59 717q0 219 102.5 382t285 249t421.5 86h1389v-365h-690v-168h606v-348h-606v-188h717v-365h-1416q-239 0 -421.5 86t-285 249t-102.5 382zM547 717q0 -161 93.5 -250.5t248.5 -89.5h203v680h-203q-155 0 -248.5 -89.5t-93.5 -250.5z" />
<glyph unicode="&#x153;" horiz-adv-x="2174" d="M43 561q0 167 83 300t229.5 206.5t330.5 73.5q256 0 416 -144q166 144 411 144q124 0 234.5 -38t197 -108.5t137 -181t50.5 -246.5q0 -48 -8 -114h-785q47 -131 230 -131q66 0 118 19t111 62l248 -243q-84 -88 -210.5 -134t-279.5 -46q-276 0 -450 147 q-163 -147 -420 -147q-184 0 -330.5 74t-229.5 206.5t-83 300.5zM512 561q0 -105 51 -163t131 -58t131.5 58t51.5 163t-51.5 162t-131.5 57t-131 -57t-51 -162zM1331 676h361q-12 71 -59 110t-120 39t-120.5 -39t-61.5 -110z" />
<glyph unicode="&#x178;" horiz-adv-x="1454" d="M-55 1434h510l293 -496l294 496h467l-540 -914v-520h-484v526zM315 1714q0 81 53 131.5t132 50.5t131.5 -50.5t52.5 -131.5q0 -80 -52.5 -130t-131.5 -50t-132 50t-53 130zM770 1714q0 81 52.5 131.5t131.5 50.5t132 -50.5t53 -131.5q0 -80 -53 -130t-132 -50t-131.5 50 t-52.5 130z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1228" d="M156 1243l262 328h393l262 -328h-287l-172 158l-172 -158h-286z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1228" d="M166 1243q3 163 78.5 258.5t204.5 95.5q51 0 100.5 -18.5t80.5 -41.5t64.5 -41.5t57.5 -18.5q40 0 64.5 25.5t27.5 68.5h219q-3 -159 -78.5 -253.5t-204.5 -94.5q-51 0 -100.5 18.5t-80.5 41.5t-64.5 41.5t-57.5 18.5q-40 0 -64.5 -27t-27.5 -73h-219z" />
<glyph unicode="&#x2000;" horiz-adv-x="1035" />
<glyph unicode="&#x2001;" horiz-adv-x="2071" />
<glyph unicode="&#x2002;" horiz-adv-x="1035" />
<glyph unicode="&#x2003;" horiz-adv-x="2071" />
<glyph unicode="&#x2004;" horiz-adv-x="690" />
<glyph unicode="&#x2005;" horiz-adv-x="517" />
<glyph unicode="&#x2006;" horiz-adv-x="345" />
<glyph unicode="&#x2007;" horiz-adv-x="345" />
<glyph unicode="&#x2008;" horiz-adv-x="258" />
<glyph unicode="&#x2009;" horiz-adv-x="414" />
<glyph unicode="&#x200a;" horiz-adv-x="115" />
<glyph unicode="&#x2010;" horiz-adv-x="798" d="M92 418v348h615v-348h-615z" />
<glyph unicode="&#x2011;" horiz-adv-x="798" d="M92 418v348h615v-348h-615z" />
<glyph unicode="&#x2012;" horiz-adv-x="798" d="M92 418v348h615v-348h-615z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 459v266h1024v-266h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 459v266h2048v-266h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="622" d="M41 946q0 54 14.5 107t65.5 161l143 306h287l-102 -346q63 -30 98 -89t35 -139q0 -118 -76 -190t-195 -72t-194.5 72t-75.5 190z" />
<glyph unicode="&#x2019;" horiz-adv-x="622" d="M41 1278q0 118 75.5 190t194.5 72t195 -72t76 -190q0 -54 -14.5 -107t-65.5 -161l-144 -305h-286l102 346q-63 30 -98 88.5t-35 138.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="622" d="M41 246q0 118 75.5 190t194.5 72t195 -72t76 -190q0 -55 -14.5 -108t-65.5 -161l-144 -305h-286l102 346q-63 30 -98 89t-35 139z" />
<glyph unicode="&#x201c;" horiz-adv-x="1216" d="M41 946q0 54 14.5 107t65.5 161l143 306h287l-102 -346q63 -30 98 -89t35 -139q0 -118 -76 -190t-195 -72t-194.5 72t-75.5 190zM635 946q0 54 14.5 107t65.5 161l143 306h287l-103 -346q63 -30 98.5 -89t35.5 -139q0 -118 -76 -190t-195 -72t-194.5 72t-75.5 190z" />
<glyph unicode="&#x201d;" horiz-adv-x="1216" d="M41 1278q0 118 75.5 190t194.5 72t195 -72t76 -190q0 -54 -14.5 -107t-65.5 -161l-144 -305h-286l102 346q-63 30 -98 88.5t-35 138.5zM635 1278q0 118 75.5 190t194.5 72t195 -72t76 -190q0 -54 -14.5 -107t-65.5 -161l-144 -305h-286l102 346q-63 30 -98 88.5 t-35 138.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="1216" d="M41 246q0 118 75.5 190t194.5 72t195 -72t76 -190q0 -55 -14.5 -108t-65.5 -161l-144 -305h-286l102 346q-63 30 -98 89t-35 139zM635 246q0 118 75.5 190t194.5 72t195 -72t76 -190q0 -55 -14.5 -108t-65.5 -161l-144 -305h-286l102 346q-63 30 -98 89t-35 139z" />
<glyph unicode="&#x2022;" horiz-adv-x="839" d="M82 584q0 145 97 240.5t239 95.5q143 0 241.5 -95.5t98.5 -240.5t-99 -242.5t-241 -97.5t-239 97.5t-97 242.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1888" d="M41 246q0 117 76.5 189.5t193.5 72.5t194 -72.5t77 -189.5q0 -116 -77.5 -191t-193.5 -75t-193 75t-77 191zM674 246q0 117 76.5 189.5t193.5 72.5t193.5 -72.5t76.5 -189.5q0 -116 -77 -191t-193 -75t-193 75t-77 191zM1307 246q0 117 76.5 189.5t193.5 72.5 t193.5 -72.5t76.5 -189.5q0 -116 -77 -191t-193 -75t-193 75t-77 191z" />
<glyph unicode="&#x202f;" horiz-adv-x="414" />
<glyph unicode="&#x2039;" horiz-adv-x="774" d="M51 561l283 410h409l-272 -410l272 -409h-409z" />
<glyph unicode="&#x203a;" horiz-adv-x="774" d="M31 152l272 409l-272 410h409l283 -410l-283 -409h-409z" />
<glyph unicode="&#x205f;" horiz-adv-x="517" />
<glyph unicode="&#x20ac;" horiz-adv-x="1710" d="M61 461v229h185v27v26h-185v230h226q79 226 282 359.5t475 133.5q209 0 373.5 -73.5t270.5 -212.5l-306 -273q-133 168 -313 168q-157 0 -248 -102h350v-230h-438v-26v-27h438v-229h-350q92 -103 248 -103q180 0 313 168l306 -272q-107 -139 -271 -213t-373 -74 q-272 0 -475 134t-282 360h-226z" />
<glyph unicode="&#x2122;" horiz-adv-x="2185" d="M8 1200v234h858v-234h-266v-616h-328v616h-264zM952 584v850h269l292 -439l285 439h266l4 -850h-296l-5 362l-186 -289h-143l-189 277v-350h-297z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1116" d="M0 0v1116h1116v-1116h-1116z" />
<hkern u1="&#x2a;" u2="&#xee;" k="-164" />
<hkern u1="&#x2f;" u2="&#xf0;" k="82" />
<hkern u1="&#x2f;" u2="i" k="-41" />
<hkern u1="F" u2="&#xef;" k="-61" />
<hkern u1="F" u2="&#xee;" k="-102" />
<hkern u1="Q" u2="&#x7d;" k="-41" />
<hkern u1="Q" u2="_" k="-82" />
<hkern u1="Q" u2="]" k="-41" />
<hkern u1="Q" u2="&#x2f;" k="-123" />
<hkern u1="T" u2="&#xef;" k="-61" />
<hkern u1="T" u2="&#xee;" k="-123" />
<hkern u1="V" u2="&#xef;" k="-92" />
<hkern u1="V" u2="&#xee;" k="-113" />
<hkern u1="V" u2="&#xec;" k="-51" />
<hkern u1="V" u2="i" k="-10" />
<hkern u1="W" u2="&#xef;" k="-92" />
<hkern u1="W" u2="&#xee;" k="-113" />
<hkern u1="W" u2="&#xec;" k="-51" />
<hkern u1="W" u2="i" k="-10" />
<hkern u1="Y" u2="&#xef;" k="-113" />
<hkern u1="Y" u2="&#xee;" k="-113" />
<hkern u1="Y" u2="&#xec;" k="-92" />
<hkern u1="Y" u2="i" k="-10" />
<hkern u1="_" u2="y" k="-41" />
<hkern u1="f" u2="&#xef;" k="-123" />
<hkern u1="f" u2="&#xee;" k="-123" />
<hkern u1="f" u2="&#xec;" k="-123" />
<hkern u1="i" u2="\" k="-20" />
<hkern u1="j" u2="\" k="-20" />
<hkern u1="q" u2="j" k="-102" />
<hkern u1="&#xa3;" u2="&#xef;" k="-41" />
<hkern u1="&#xa3;" u2="&#xee;" k="-82" />
<hkern u1="&#xaa;" u2="&#xee;" k="-164" />
<hkern u1="&#xba;" u2="&#xee;" k="-164" />
<hkern u1="&#xbf;" u2="y" k="61" />
<hkern u1="&#xdd;" u2="&#xef;" k="-113" />
<hkern u1="&#xdd;" u2="&#xee;" k="-113" />
<hkern u1="&#xdd;" u2="&#xec;" k="-92" />
<hkern u1="&#xdd;" u2="i" k="-10" />
<hkern u1="&#xee;" u2="&#xba;" k="-164" />
<hkern u1="&#xee;" u2="&#xaa;" k="-164" />
<hkern u1="&#xee;" u2="&#x3f;" k="-82" />
<hkern u1="&#xee;" u2="&#x2a;" k="-164" />
<hkern u1="&#x178;" u2="&#xef;" k="-113" />
<hkern u1="&#x178;" u2="&#xee;" k="-113" />
<hkern u1="&#x178;" u2="&#xec;" k="-92" />
<hkern u1="&#x178;" u2="i" k="-10" />
<hkern u1="&#x2018;" u2="&#xec;" k="-61" />
<hkern u1="&#x201c;" u2="&#xec;" k="-61" />
<hkern g1="ampersand" 	g2="backslash" 	k="164" />
<hkern g1="ampersand" 	g2="bracketright,braceright" 	k="41" />
<hkern g1="ampersand" 	g2="colon,semicolon" 	k="20" />
<hkern g1="ampersand" 	g2="degree" 	k="123" />
<hkern g1="ampersand" 	g2="exclam" 	k="31" />
<hkern g1="ampersand" 	g2="exclamdown" 	k="20" />
<hkern g1="ampersand" 	g2="four" 	k="-20" />
<hkern g1="ampersand" 	g2="one" 	k="61" />
<hkern g1="ampersand" 	g2="paragraph" 	k="143" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="ampersand" 	g2="question" 	k="113" />
<hkern g1="ampersand" 	g2="questiondown" 	k="20" />
<hkern g1="ampersand" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="ampersand" 	g2="seven" 	k="20" />
<hkern g1="ampersand" 	g2="slash" 	k="-41" />
<hkern g1="ampersand" 	g2="three" 	k="20" />
<hkern g1="ampersand" 	g2="trademark" 	k="82" />
<hkern g1="ampersand" 	g2="two" 	k="41" />
<hkern g1="currency" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="currency" 	g2="two" 	k="10" />
<hkern g1="degree" 	g2="backslash" 	k="-123" />
<hkern g1="degree" 	g2="four" 	k="82" />
<hkern g1="degree" 	g2="one" 	k="-102" />
<hkern g1="degree" 	g2="percent" 	k="-61" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="143" />
<hkern g1="degree" 	g2="question" 	k="-41" />
<hkern g1="degree" 	g2="questiondown" 	k="102" />
<hkern g1="degree" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="degree" 	g2="seven" 	k="-61" />
<hkern g1="degree" 	g2="slash" 	k="102" />
<hkern g1="degree" 	g2="three" 	k="-41" />
<hkern g1="degree" 	g2="two" 	k="-61" />
<hkern g1="degree" 	g2="nine" 	k="-61" />
<hkern g1="percent" 	g2="backslash" 	k="61" />
<hkern g1="percent" 	g2="degree" 	k="41" />
<hkern g1="percent" 	g2="four" 	k="-61" />
<hkern g1="percent" 	g2="one" 	k="41" />
<hkern g1="percent" 	g2="percent" 	k="266" />
<hkern g1="percent" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="percent" 	g2="question" 	k="102" />
<hkern g1="percent" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="percent" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="percent" 	g2="seven" 	k="20" />
<hkern g1="percent" 	g2="three" 	k="-20" />
<hkern g1="percent" 	g2="two" 	k="-20" />
<hkern g1="percent" 	g2="parenright" 	k="41" />
<hkern g1="percent" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="percent" 	g2="eight" 	k="-41" />
<hkern g1="percent" 	g2="five" 	k="-41" />
<hkern g1="percent" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="section" 	g2="four" 	k="-20" />
<hkern g1="section" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="section" 	g2="slash" 	k="-20" />
<hkern g1="section" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="section" 	g2="eight" 	k="-20" />
<hkern g1="section" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="trademark" 	g2="backslash" 	k="-82" />
<hkern g1="trademark" 	g2="exclamdown" 	k="-20" />
<hkern g1="trademark" 	g2="seven" 	k="-41" />
<hkern g1="trademark" 	g2="slash" 	k="61" />
<hkern g1="trademark" 	g2="nine" 	k="-61" />
<hkern g1="trademark" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="yen" 	g2="backslash" 	k="-102" />
<hkern g1="yen" 	g2="bracketright,braceright" 	k="-82" />
<hkern g1="yen" 	g2="colon,semicolon" 	k="82" />
<hkern g1="yen" 	g2="exclam" 	k="-61" />
<hkern g1="yen" 	g2="exclamdown" 	k="41" />
<hkern g1="yen" 	g2="four" 	k="20" />
<hkern g1="yen" 	g2="one" 	k="-61" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="yen" 	g2="questiondown" 	k="82" />
<hkern g1="yen" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="yen" 	g2="seven" 	k="-41" />
<hkern g1="yen" 	g2="slash" 	k="61" />
<hkern g1="yen" 	g2="three" 	k="-20" />
<hkern g1="yen" 	g2="parenright" 	k="-82" />
<hkern g1="yen" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="yen" 	g2="underscore" 	k="20" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="yen" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="yen" 	g2="zero,six" 	k="31" />
<hkern g1="backslash" 	g2="ampersand" 	k="-20" />
<hkern g1="backslash" 	g2="backslash" 	k="164" />
<hkern g1="backslash" 	g2="bracketright,braceright" 	k="-102" />
<hkern g1="backslash" 	g2="colon,semicolon" 	k="-123" />
<hkern g1="backslash" 	g2="degree" 	k="123" />
<hkern g1="backslash" 	g2="exclamdown" 	k="-82" />
<hkern g1="backslash" 	g2="five" 	k="-20" />
<hkern g1="backslash" 	g2="one" 	k="41" />
<hkern g1="backslash" 	g2="paragraph" 	k="61" />
<hkern g1="backslash" 	g2="parenright" 	k="-61" />
<hkern g1="backslash" 	g2="percent" 	k="20" />
<hkern g1="backslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-164" />
<hkern g1="backslash" 	g2="question" 	k="123" />
<hkern g1="backslash" 	g2="questiondown" 	k="-61" />
<hkern g1="backslash" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="backslash" 	g2="section" 	k="-61" />
<hkern g1="backslash" 	g2="seven" 	k="102" />
<hkern g1="backslash" 	g2="three" 	k="-20" />
<hkern g1="backslash" 	g2="trademark" 	k="123" />
<hkern g1="backslash" 	g2="two" 	k="-20" />
<hkern g1="backslash" 	g2="underscore" 	k="-287" />
<hkern g1="backslash" 	g2="zero,six" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="backslash" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="exclamdown" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="paragraph" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="parenright" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="question" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="seven" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="three" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="trademark" 	k="-123" />
<hkern g1="bracketleft,braceleft" 	g2="two" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="underscore" 	k="-82" />
<hkern g1="bracketleft,braceleft" 	g2="exclam" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="slash" 	k="-102" />
<hkern g1="bracketleft,braceleft" 	g2="yen" 	k="-82" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="51" />
<hkern g1="colon,semicolon" 	g2="question" 	k="20" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="underscore" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="slash" 	k="-123" />
<hkern g1="colon,semicolon" 	g2="yen" 	k="82" />
<hkern g1="exclam" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="exclam" 	g2="one" 	k="-20" />
<hkern g1="exclam" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="exclam" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="exclam" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="exclam" 	g2="trademark" 	k="-41" />
<hkern g1="exclam" 	g2="yen" 	k="-61" />
<hkern g1="exclamdown" 	g2="backslash" 	k="113" />
<hkern g1="exclamdown" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="exclamdown" 	g2="one" 	k="41" />
<hkern g1="exclamdown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="exclamdown" 	g2="question" 	k="20" />
<hkern g1="exclamdown" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="exclamdown" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="exclamdown" 	g2="trademark" 	k="20" />
<hkern g1="exclamdown" 	g2="underscore" 	k="-61" />
<hkern g1="exclamdown" 	g2="slash" 	k="-82" />
<hkern g1="exclamdown" 	g2="yen" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="102" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="one" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="question" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="underscore" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="backslash" 	k="133" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="one" 	k="51" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="paragraph" 	k="164" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="percent" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="question" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="section" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="seven" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="three" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="two" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="yen" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="parenleft" 	g2="backslash" 	k="-102" />
<hkern g1="parenleft" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="parenleft" 	g2="one" 	k="-20" />
<hkern g1="parenleft" 	g2="trademark" 	k="-82" />
<hkern g1="parenleft" 	g2="underscore" 	k="-20" />
<hkern g1="parenleft" 	g2="four" 	k="41" />
<hkern g1="parenleft" 	g2="slash" 	k="-82" />
<hkern g1="parenleft" 	g2="yen" 	k="-82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="184" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="degree" 	k="143" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclamdown" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="paragraph" 	k="328" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="143" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="123" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="questiondown" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="51" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="113" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="two" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="underscore" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero,six" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclam" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="slash" 	k="-164" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="currency,Euro" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="-20" />
<hkern g1="question" 	g2="degree" 	k="-20" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="question" 	g2="question" 	k="10" />
<hkern g1="question" 	g2="questiondown" 	k="195" />
<hkern g1="question" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="question" 	g2="three" 	k="31" />
<hkern g1="question" 	g2="underscore" 	k="20" />
<hkern g1="question" 	g2="four" 	k="102" />
<hkern g1="question" 	g2="slash" 	k="61" />
<hkern g1="question" 	g2="yen" 	k="31" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="question" 	g2="eight" 	k="20" />
<hkern g1="question" 	g2="nine" 	k="-20" />
<hkern g1="questiondown" 	g2="ampersand" 	k="20" />
<hkern g1="questiondown" 	g2="backslash" 	k="154" />
<hkern g1="questiondown" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="questiondown" 	g2="colon,semicolon" 	k="20" />
<hkern g1="questiondown" 	g2="degree" 	k="143" />
<hkern g1="questiondown" 	g2="one" 	k="102" />
<hkern g1="questiondown" 	g2="paragraph" 	k="195" />
<hkern g1="questiondown" 	g2="percent" 	k="123" />
<hkern g1="questiondown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="questiondown" 	g2="question" 	k="287" />
<hkern g1="questiondown" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="questiondown" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="questiondown" 	g2="section" 	k="-20" />
<hkern g1="questiondown" 	g2="seven" 	k="102" />
<hkern g1="questiondown" 	g2="trademark" 	k="102" />
<hkern g1="questiondown" 	g2="underscore" 	k="-102" />
<hkern g1="questiondown" 	g2="zero,six" 	k="123" />
<hkern g1="questiondown" 	g2="exclam" 	k="20" />
<hkern g1="questiondown" 	g2="four" 	k="102" />
<hkern g1="questiondown" 	g2="slash" 	k="-82" />
<hkern g1="questiondown" 	g2="yen" 	k="102" />
<hkern g1="questiondown" 	g2="sterling" 	k="-41" />
<hkern g1="questiondown" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="82" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="questiondown" 	g2="eight" 	k="102" />
<hkern g1="quoteleft,quotedblleft" 	g2="backslash" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="paragraph" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="percent" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="51" />
<hkern g1="quoteleft,quotedblleft" 	g2="question" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="section" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclam" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="slash" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="nine" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="backslash" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="exclamdown" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="one" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="paragraph" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="percent" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="question" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="questiondown" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="section" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="trademark" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="exclam" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="123" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="yen" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="backslash" 	k="-123" />
<hkern g1="quotedbl,quotesingle" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="degree" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-82" />
<hkern g1="quotedbl,quotesingle" 	g2="paragraph" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="percent" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="quotedbl,quotesingle" 	g2="question" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="questiondown" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="quotedbl,quotesingle" 	k="-92" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-61" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="trademark" 	k="-61" />
<hkern g1="quotedbl,quotesingle" 	g2="two" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="exclam" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="yen" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="currency,Euro" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="nine" 	k="-61" />
<hkern g1="slash" 	g2="ampersand" 	k="61" />
<hkern g1="slash" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="31" />
<hkern g1="slash" 	g2="degree" 	k="-82" />
<hkern g1="slash" 	g2="exclamdown" 	k="133" />
<hkern g1="slash" 	g2="one" 	k="-61" />
<hkern g1="slash" 	g2="parenright" 	k="-82" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="184" />
<hkern g1="slash" 	g2="questiondown" 	k="154" />
<hkern g1="slash" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="slash" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="slash" 	g2="quotedbl,quotesingle" 	k="-123" />
<hkern g1="slash" 	g2="section" 	k="41" />
<hkern g1="slash" 	g2="trademark" 	k="-82" />
<hkern g1="slash" 	g2="underscore" 	k="225" />
<hkern g1="slash" 	g2="zero,six" 	k="41" />
<hkern g1="slash" 	g2="four" 	k="123" />
<hkern g1="slash" 	g2="slash" 	k="164" />
<hkern g1="slash" 	g2="yen" 	k="-82" />
<hkern g1="slash" 	g2="sterling" 	k="20" />
<hkern g1="slash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="154" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="slash" 	g2="eight" 	k="61" />
<hkern g1="underscore" 	g2="backslash" 	k="266" />
<hkern g1="underscore" 	g2="bracketright,braceright" 	k="-82" />
<hkern g1="underscore" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="underscore" 	g2="exclamdown" 	k="-61" />
<hkern g1="underscore" 	g2="five" 	k="-20" />
<hkern g1="underscore" 	g2="one" 	k="10" />
<hkern g1="underscore" 	g2="paragraph" 	k="61" />
<hkern g1="underscore" 	g2="parenright" 	k="-20" />
<hkern g1="underscore" 	g2="percent" 	k="41" />
<hkern g1="underscore" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="underscore" 	g2="question" 	k="51" />
<hkern g1="underscore" 	g2="section" 	k="-41" />
<hkern g1="underscore" 	g2="three" 	k="-41" />
<hkern g1="underscore" 	g2="trademark" 	k="102" />
<hkern g1="underscore" 	g2="two" 	k="-41" />
<hkern g1="underscore" 	g2="zero,six" 	k="41" />
<hkern g1="underscore" 	g2="four" 	k="41" />
<hkern g1="underscore" 	g2="slash" 	k="-225" />
<hkern g1="underscore" 	g2="yen" 	k="20" />
<hkern g1="underscore" 	g2="sterling" 	k="-20" />
<hkern g1="underscore" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="underscore" 	g2="nine" 	k="-20" />
<hkern g1="eight" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="10" />
<hkern g1="eight" 	g2="T" 	k="20" />
<hkern g1="eight" 	g2="V,W" 	k="41" />
<hkern g1="eight" 	g2="X" 	k="51" />
<hkern g1="eight" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="eight" 	g2="backslash" 	k="61" />
<hkern g1="eight" 	g2="j" 	k="16" />
<hkern g1="eight" 	g2="percent" 	k="20" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="eight" 	g2="question" 	k="20" />
<hkern g1="eight" 	g2="questiondown" 	k="41" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="eight" 	g2="section" 	k="-20" />
<hkern g1="eight" 	g2="v,w,y,yacute,ydieresis" 	k="10" />
<hkern g1="eight" 	g2="x" 	k="31" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="five" 	g2="T" 	k="20" />
<hkern g1="five" 	g2="V,W" 	k="20" />
<hkern g1="five" 	g2="X" 	k="31" />
<hkern g1="five" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="five" 	g2="backslash" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="20" />
<hkern g1="five" 	g2="v,w,y,yacute,ydieresis" 	k="31" />
<hkern g1="five" 	g2="x" 	k="31" />
<hkern g1="five" 	g2="J" 	k="20" />
<hkern g1="five" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="seven" 	k="20" />
<hkern g1="five" 	g2="three" 	k="10" />
<hkern g1="five" 	g2="two" 	k="10" />
<hkern g1="four" 	g2="T" 	k="113" />
<hkern g1="four" 	g2="V,W" 	k="123" />
<hkern g1="four" 	g2="X" 	k="31" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="184" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="percent" 	k="61" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="four" 	g2="question" 	k="123" />
<hkern g1="four" 	g2="questiondown" 	k="-20" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="four" 	g2="section" 	k="-61" />
<hkern g1="four" 	g2="v,w,y,yacute,ydieresis" 	k="61" />
<hkern g1="four" 	g2="x" 	k="41" />
<hkern g1="four" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="J" 	k="-20" />
<hkern g1="four" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="four" 	g2="seven" 	k="72" />
<hkern g1="four" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="four" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-51" />
<hkern g1="four" 	g2="slash" 	k="-41" />
<hkern g1="four" 	g2="ampersand" 	k="-41" />
<hkern g1="four" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="four" 	g2="currency,Euro" 	k="-20" />
<hkern g1="four" 	g2="degree" 	k="61" />
<hkern g1="four" 	g2="eight" 	k="-20" />
<hkern g1="four" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="four" 	g2="sterling" 	k="-20" />
<hkern g1="four" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-41" />
<hkern g1="four" 	g2="one" 	k="51" />
<hkern g1="four" 	g2="paragraph" 	k="61" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="four" 	g2="t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="four" 	g2="trademark" 	k="41" />
<hkern g1="four" 	g2="underscore" 	k="-41" />
<hkern g1="seven" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="113" />
<hkern g1="seven" 	g2="V,W" 	k="-10" />
<hkern g1="seven" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="seven" 	g2="backslash" 	k="-20" />
<hkern g1="seven" 	g2="j" 	k="47" />
<hkern g1="seven" 	g2="percent" 	k="-20" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="92" />
<hkern g1="seven" 	g2="question" 	k="-31" />
<hkern g1="seven" 	g2="questiondown" 	k="102" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="seven" 	g2="section" 	k="20" />
<hkern g1="seven" 	g2="v,w,y,yacute,ydieresis" 	k="-20" />
<hkern g1="seven" 	g2="yen" 	k="-41" />
<hkern g1="seven" 	g2="J" 	k="41" />
<hkern g1="seven" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="seven" 	g2="five" 	k="31" />
<hkern g1="seven" 	g2="three" 	k="10" />
<hkern g1="seven" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="seven" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="72" />
<hkern g1="seven" 	g2="slash" 	k="72" />
<hkern g1="seven" 	g2="ampersand" 	k="41" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="41" />
<hkern g1="seven" 	g2="currency,Euro" 	k="41" />
<hkern g1="seven" 	g2="degree" 	k="-31" />
<hkern g1="seven" 	g2="eight" 	k="31" />
<hkern g1="seven" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="102" />
<hkern g1="seven" 	g2="sterling" 	k="20" />
<hkern g1="seven" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="seven" 	g2="one" 	k="-20" />
<hkern g1="seven" 	g2="paragraph" 	k="-41" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-61" />
<hkern g1="seven" 	g2="trademark" 	k="-82" />
<hkern g1="seven" 	g2="underscore" 	k="61" />
<hkern g1="seven" 	g2="exclamdown" 	k="41" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="seven" 	g2="s" 	k="61" />
<hkern g1="seven" 	g2="z" 	k="31" />
<hkern g1="seven" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="seven" 	g2="four" 	k="133" />
<hkern g1="seven" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="31" />
<hkern g1="seven" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="seven" 	g2="zero,six" 	k="41" />
<hkern g1="six" 	g2="T" 	k="20" />
<hkern g1="six" 	g2="V,W" 	k="20" />
<hkern g1="six" 	g2="j" 	k="16" />
<hkern g1="six" 	g2="percent" 	k="41" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="six" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="six" 	g2="section" 	k="-41" />
<hkern g1="six" 	g2="v,w,y,yacute,ydieresis" 	k="41" />
<hkern g1="six" 	g2="x" 	k="31" />
<hkern g1="six" 	g2="slash" 	k="-20" />
<hkern g1="six" 	g2="ampersand" 	k="-20" />
<hkern g1="six" 	g2="currency,Euro" 	k="-20" />
<hkern g1="six" 	g2="degree" 	k="20" />
<hkern g1="six" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="six" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-10" />
<hkern g1="three" 	g2="T" 	k="20" />
<hkern g1="three" 	g2="V,W" 	k="20" />
<hkern g1="three" 	g2="X" 	k="20" />
<hkern g1="three" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="three" 	g2="j" 	k="16" />
<hkern g1="three" 	g2="percent" 	k="20" />
<hkern g1="three" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="three" 	g2="x" 	k="41" />
<hkern g1="three" 	g2="five" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="10" />
<hkern g1="three" 	g2="three" 	k="10" />
<hkern g1="three" 	g2="two" 	k="10" />
<hkern g1="three" 	g2="degree" 	k="31" />
<hkern g1="three" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="two" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-20" />
<hkern g1="two" 	g2="V,W" 	k="31" />
<hkern g1="two" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="two" 	g2="percent" 	k="-20" />
<hkern g1="two" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="two" 	g2="J" 	k="-41" />
<hkern g1="two" 	g2="slash" 	k="-20" />
<hkern g1="two" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="two" 	g2="underscore" 	k="-61" />
<hkern g1="zero,nine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="31" />
<hkern g1="zero,nine" 	g2="T" 	k="41" />
<hkern g1="zero,nine" 	g2="V,W" 	k="51" />
<hkern g1="zero,nine" 	g2="X" 	k="82" />
<hkern g1="zero,nine" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="zero,nine" 	g2="backslash" 	k="41" />
<hkern g1="zero,nine" 	g2="j" 	k="16" />
<hkern g1="zero,nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="zero,nine" 	g2="question" 	k="41" />
<hkern g1="zero,nine" 	g2="questiondown" 	k="61" />
<hkern g1="zero,nine" 	g2="x" 	k="20" />
<hkern g1="zero,nine" 	g2="yen" 	k="31" />
<hkern g1="zero,nine" 	g2="J" 	k="61" />
<hkern g1="zero,nine" 	g2="three" 	k="20" />
<hkern g1="zero,nine" 	g2="two" 	k="20" />
<hkern g1="zero,nine" 	g2="slash" 	k="41" />
<hkern g1="zero,nine" 	g2="one" 	k="20" />
<hkern g1="zero,nine" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="zero,nine" 	g2="underscore" 	k="41" />
<hkern g1="zero,nine" 	g2="Z" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="-31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V,W" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,ordfeminine,ordmasculine" 	k="205" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="degree" 	k="205" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="eight" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="exclamdown" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="four" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="nine" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="92" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="paragraph" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="questiondown" 	k="-41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="seven" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="slash" 	k="-61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t,uniFB01,uniFB02" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="three" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="164" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="underscore" 	k="-102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v,w,y,yacute,ydieresis" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="z" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="zero,six" 	k="31" />
<hkern g1="B" 	g2="T" 	k="20" />
<hkern g1="B" 	g2="V,W" 	k="10" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="B" 	g2="trademark" 	k="20" />
<hkern g1="B" 	g2="underscore" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="T" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="X" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="C,Ccedilla,Euro" 	g2="backslash" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="colon,semicolon" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="degree" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="eight" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="exclamdown" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="nine" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="one" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="C,Ccedilla,Euro" 	g2="s" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="underscore" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="x" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="z" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="zero,six" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="dollar,S" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="ampersand" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="V,W" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="X" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="zero,six" 	k="10" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="51" />
<hkern g1="F" 	g2="J" 	k="31" />
<hkern g1="F" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="F" 	g2="T" 	k="-10" />
<hkern g1="F" 	g2="X" 	k="20" />
<hkern g1="F" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="F" 	g2="backslash" 	k="-20" />
<hkern g1="F" 	g2="degree" 	k="-61" />
<hkern g1="F" 	g2="eight" 	k="20" />
<hkern g1="F" 	g2="exclamdown" 	k="20" />
<hkern g1="F" 	g2="four" 	k="72" />
<hkern g1="F" 	g2="nine" 	k="-20" />
<hkern g1="F" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="F" 	g2="one" 	k="-31" />
<hkern g1="F" 	g2="paragraph" 	k="-20" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="F" 	g2="question" 	k="-31" />
<hkern g1="F" 	g2="questiondown" 	k="82" />
<hkern g1="F" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="F" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="F" 	g2="quotedbl,quotesingle" 	k="-82" />
<hkern g1="F" 	g2="s" 	k="20" />
<hkern g1="F" 	g2="seven" 	k="-20" />
<hkern g1="F" 	g2="slash" 	k="72" />
<hkern g1="F" 	g2="trademark" 	k="-61" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="F" 	g2="underscore" 	k="41" />
<hkern g1="F" 	g2="v,w,y,yacute,ydieresis" 	k="-10" />
<hkern g1="F" 	g2="x" 	k="20" />
<hkern g1="F" 	g2="z" 	k="20" />
<hkern g1="F" 	g2="zero,six" 	k="20" />
<hkern g1="F" 	g2="ampersand" 	k="31" />
<hkern g1="F" 	g2="five" 	k="20" />
<hkern g1="F" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="F" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="F" 	g2="exclam" 	k="-41" />
<hkern g1="F" 	g2="j" 	k="31" />
<hkern g1="F" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="F" 	g2="two" 	k="-20" />
<hkern g1="sterling" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="92" />
<hkern g1="sterling" 	g2="J" 	k="41" />
<hkern g1="sterling" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="sterling" 	g2="X" 	k="20" />
<hkern g1="sterling" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="sterling" 	g2="backslash" 	k="-20" />
<hkern g1="sterling" 	g2="colon,semicolon" 	k="61" />
<hkern g1="sterling" 	g2="eight" 	k="61" />
<hkern g1="sterling" 	g2="exclamdown" 	k="51" />
<hkern g1="sterling" 	g2="four" 	k="31" />
<hkern g1="sterling" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="sterling" 	g2="nine" 	k="41" />
<hkern g1="sterling" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="sterling" 	g2="one" 	k="-20" />
<hkern g1="sterling" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="sterling" 	g2="questiondown" 	k="31" />
<hkern g1="sterling" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="sterling" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="sterling" 	g2="s" 	k="82" />
<hkern g1="sterling" 	g2="slash" 	k="-31" />
<hkern g1="sterling" 	g2="t,uniFB01,uniFB02" 	k="41" />
<hkern g1="sterling" 	g2="three" 	k="20" />
<hkern g1="sterling" 	g2="trademark" 	k="-41" />
<hkern g1="sterling" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="sterling" 	g2="underscore" 	k="-20" />
<hkern g1="sterling" 	g2="v,w,y,yacute,ydieresis" 	k="41" />
<hkern g1="sterling" 	g2="x" 	k="51" />
<hkern g1="sterling" 	g2="z" 	k="82" />
<hkern g1="sterling" 	g2="zero,six" 	k="61" />
<hkern g1="sterling" 	g2="dollar,S" 	k="20" />
<hkern g1="sterling" 	g2="ampersand" 	k="51" />
<hkern g1="sterling" 	g2="five" 	k="41" />
<hkern g1="sterling" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="sterling" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="sterling" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="sterling" 	g2="j" 	k="51" />
<hkern g1="sterling" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="41" />
<hkern g1="sterling" 	g2="two" 	k="20" />
<hkern g1="sterling" 	g2="section" 	k="20" />
<hkern g1="G" 	g2="backslash" 	k="20" />
<hkern g1="G" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="72" />
<hkern g1="K" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="K" 	g2="T" 	k="113" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="K" 	g2="V,W" 	k="82" />
<hkern g1="K" 	g2="X" 	k="92" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="113" />
<hkern g1="K" 	g2="degree" 	k="20" />
<hkern g1="K" 	g2="eight" 	k="51" />
<hkern g1="K" 	g2="four" 	k="82" />
<hkern g1="K" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="82" />
<hkern g1="K" 	g2="nine" 	k="20" />
<hkern g1="K" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="K" 	g2="one" 	k="-41" />
<hkern g1="K" 	g2="paragraph" 	k="41" />
<hkern g1="K" 	g2="question" 	k="82" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="K" 	g2="s" 	k="20" />
<hkern g1="K" 	g2="t,uniFB01,uniFB02" 	k="41" />
<hkern g1="K" 	g2="trademark" 	k="-41" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="K" 	g2="underscore" 	k="-123" />
<hkern g1="K" 	g2="v,w,y,yacute,ydieresis" 	k="82" />
<hkern g1="K" 	g2="x" 	k="102" />
<hkern g1="K" 	g2="z" 	k="61" />
<hkern g1="K" 	g2="zero,six" 	k="51" />
<hkern g1="K" 	g2="dollar,S" 	k="31" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="K" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="K" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="K" 	g2="two" 	k="-10" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-20" />
<hkern g1="L" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="L" 	g2="T" 	k="133" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="L" 	g2="V,W" 	k="92" />
<hkern g1="L" 	g2="X" 	k="20" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="L" 	g2="asterisk,ordfeminine,ordmasculine" 	k="143" />
<hkern g1="L" 	g2="backslash" 	k="143" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="L" 	g2="degree" 	k="133" />
<hkern g1="L" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="L" 	g2="nine" 	k="20" />
<hkern g1="L" 	g2="one" 	k="61" />
<hkern g1="L" 	g2="paragraph" 	k="184" />
<hkern g1="L" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="L" 	g2="question" 	k="164" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="102" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="102" />
<hkern g1="L" 	g2="seven" 	k="102" />
<hkern g1="L" 	g2="slash" 	k="-20" />
<hkern g1="L" 	g2="t,uniFB01,uniFB02" 	k="10" />
<hkern g1="L" 	g2="three" 	k="-20" />
<hkern g1="L" 	g2="trademark" 	k="164" />
<hkern g1="L" 	g2="underscore" 	k="-82" />
<hkern g1="L" 	g2="v,w,y,yacute,ydieresis" 	k="72" />
<hkern g1="L" 	g2="zero,six" 	k="20" />
<hkern g1="L" 	g2="ampersand" 	k="-20" />
<hkern g1="L" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V,W" 	k="51" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="51" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="degree" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="questiondown" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="j" 	k="-164" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="10" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="92" />
<hkern g1="P" 	g2="J" 	k="61" />
<hkern g1="P" 	g2="T" 	k="51" />
<hkern g1="P" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="P" 	g2="V,W" 	k="41" />
<hkern g1="P" 	g2="X" 	k="51" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="P" 	g2="backslash" 	k="41" />
<hkern g1="P" 	g2="degree" 	k="-41" />
<hkern g1="P" 	g2="eight" 	k="20" />
<hkern g1="P" 	g2="four" 	k="61" />
<hkern g1="P" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="P" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="51" />
<hkern g1="P" 	g2="questiondown" 	k="92" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="slash" 	k="82" />
<hkern g1="P" 	g2="t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="P" 	g2="three" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="82" />
<hkern g1="P" 	g2="x" 	k="61" />
<hkern g1="P" 	g2="ampersand" 	k="51" />
<hkern g1="P" 	g2="five" 	k="20" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="P" 	g2="j" 	k="20" />
<hkern g1="P" 	g2="two" 	k="10" />
<hkern g1="P" 	g2="Z" 	k="20" />
<hkern g1="P" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="20" />
<hkern g1="R" 	g2="J" 	k="20" />
<hkern g1="R" 	g2="T" 	k="20" />
<hkern g1="R" 	g2="X" 	k="41" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="degree" 	k="-31" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="10" />
<hkern g1="R" 	g2="question" 	k="10" />
<hkern g1="R" 	g2="questiondown" 	k="20" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="R" 	g2="t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="R" 	g2="underscore" 	k="-20" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="dollar,S" 	g2="T" 	k="20" />
<hkern g1="dollar,S" 	g2="X" 	k="20" />
<hkern g1="dollar,S" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="dollar,S" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="20" />
<hkern g1="dollar,S" 	g2="colon,semicolon" 	k="10" />
<hkern g1="dollar,S" 	g2="degree" 	k="31" />
<hkern g1="dollar,S" 	g2="exclamdown" 	k="10" />
<hkern g1="dollar,S" 	g2="four" 	k="-20" />
<hkern g1="dollar,S" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="dollar,S" 	g2="nine" 	k="20" />
<hkern g1="dollar,S" 	g2="one" 	k="20" />
<hkern g1="dollar,S" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="questiondown" 	k="20" />
<hkern g1="dollar,S" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="dollar,S" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="dollar,S" 	g2="seven" 	k="20" />
<hkern g1="dollar,S" 	g2="underscore" 	k="41" />
<hkern g1="dollar,S" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="dollar,S" 	g2="x" 	k="20" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="72" />
<hkern g1="T" 	g2="J" 	k="20" />
<hkern g1="T" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="T" 	k="-20" />
<hkern g1="T" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="T" 	g2="V,W" 	k="41" />
<hkern g1="T" 	g2="X" 	k="61" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="T" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="T" 	g2="backslash" 	k="-61" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="degree" 	k="-82" />
<hkern g1="T" 	g2="eight" 	k="20" />
<hkern g1="T" 	g2="exclamdown" 	k="41" />
<hkern g1="T" 	g2="four" 	k="164" />
<hkern g1="T" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="82" />
<hkern g1="T" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="one" 	k="-82" />
<hkern g1="T" 	g2="paragraph" 	k="-61" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="72" />
<hkern g1="T" 	g2="question" 	k="-61" />
<hkern g1="T" 	g2="questiondown" 	k="92" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-41" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="T" 	g2="s" 	k="72" />
<hkern g1="T" 	g2="seven" 	k="-41" />
<hkern g1="T" 	g2="slash" 	k="41" />
<hkern g1="T" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="T" 	g2="three" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-102" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="T" 	g2="v,w,y,yacute,ydieresis" 	k="61" />
<hkern g1="T" 	g2="x" 	k="61" />
<hkern g1="T" 	g2="z" 	k="20" />
<hkern g1="T" 	g2="zero,six" 	k="41" />
<hkern g1="T" 	g2="ampersand" 	k="41" />
<hkern g1="T" 	g2="five" 	k="20" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="T" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="T" 	g2="exclam" 	k="-41" />
<hkern g1="T" 	g2="j" 	k="-123" />
<hkern g1="T" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="T" 	g2="two" 	k="-61" />
<hkern g1="Thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="61" />
<hkern g1="Thorn" 	g2="J" 	k="82" />
<hkern g1="Thorn" 	g2="T" 	k="51" />
<hkern g1="Thorn" 	g2="V,W" 	k="20" />
<hkern g1="Thorn" 	g2="X" 	k="61" />
<hkern g1="Thorn" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="one" 	k="41" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="Thorn" 	g2="question" 	k="61" />
<hkern g1="Thorn" 	g2="questiondown" 	k="51" />
<hkern g1="Thorn" 	g2="slash" 	k="61" />
<hkern g1="Thorn" 	g2="t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="Thorn" 	g2="three" 	k="41" />
<hkern g1="Thorn" 	g2="trademark" 	k="20" />
<hkern g1="Thorn" 	g2="underscore" 	k="61" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="Thorn" 	g2="j" 	k="20" />
<hkern g1="Thorn" 	g2="two" 	k="51" />
<hkern g1="Thorn" 	g2="Z" 	k="31" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="31" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="T" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="underscore" 	k="20" />
<hkern g1="J,U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="j" 	k="10" />
<hkern g1="V,W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="82" />
<hkern g1="V,W" 	g2="J" 	k="31" />
<hkern g1="V,W" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="V,W" 	g2="T" 	k="41" />
<hkern g1="V,W" 	g2="X" 	k="61" />
<hkern g1="V,W" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="V,W" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="V,W" 	g2="backslash" 	k="-61" />
<hkern g1="V,W" 	g2="colon,semicolon" 	k="61" />
<hkern g1="V,W" 	g2="degree" 	k="-41" />
<hkern g1="V,W" 	g2="eight" 	k="41" />
<hkern g1="V,W" 	g2="exclamdown" 	k="61" />
<hkern g1="V,W" 	g2="four" 	k="164" />
<hkern g1="V,W" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="V,W" 	g2="nine" 	k="20" />
<hkern g1="V,W" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="V,W" 	g2="one" 	k="-61" />
<hkern g1="V,W" 	g2="paragraph" 	k="-20" />
<hkern g1="V,W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="V,W" 	g2="questiondown" 	k="133" />
<hkern g1="V,W" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="V,W" 	g2="s" 	k="133" />
<hkern g1="V,W" 	g2="seven" 	k="-20" />
<hkern g1="V,W" 	g2="slash" 	k="82" />
<hkern g1="V,W" 	g2="t,uniFB01,uniFB02" 	k="20" />
<hkern g1="V,W" 	g2="three" 	k="-20" />
<hkern g1="V,W" 	g2="trademark" 	k="-82" />
<hkern g1="V,W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="V,W" 	g2="underscore" 	k="20" />
<hkern g1="V,W" 	g2="v,w,y,yacute,ydieresis" 	k="41" />
<hkern g1="V,W" 	g2="x" 	k="61" />
<hkern g1="V,W" 	g2="z" 	k="72" />
<hkern g1="V,W" 	g2="zero,six" 	k="51" />
<hkern g1="V,W" 	g2="dollar,S" 	k="31" />
<hkern g1="V,W" 	g2="ampersand" 	k="41" />
<hkern g1="V,W" 	g2="five" 	k="31" />
<hkern g1="V,W" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="V,W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="113" />
<hkern g1="V,W" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="V,W" 	g2="exclam" 	k="-20" />
<hkern g1="V,W" 	g2="j" 	k="41" />
<hkern g1="V,W" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="72" />
<hkern g1="V,W" 	g2="Z" 	k="20" />
<hkern g1="V,W" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="V,W" 	g2="parenright" 	k="-41" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="82" />
<hkern g1="X" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="X" 	g2="T" 	k="61" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="X" 	g2="V,W" 	k="61" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="X" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="X" 	g2="colon,semicolon" 	k="20" />
<hkern g1="X" 	g2="degree" 	k="41" />
<hkern g1="X" 	g2="eight" 	k="51" />
<hkern g1="X" 	g2="exclamdown" 	k="20" />
<hkern g1="X" 	g2="four" 	k="123" />
<hkern g1="X" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="51" />
<hkern g1="X" 	g2="nine" 	k="41" />
<hkern g1="X" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="X" 	g2="one" 	k="-20" />
<hkern g1="X" 	g2="question" 	k="61" />
<hkern g1="X" 	g2="questiondown" 	k="-20" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="X" 	g2="s" 	k="41" />
<hkern g1="X" 	g2="seven" 	k="20" />
<hkern g1="X" 	g2="slash" 	k="-20" />
<hkern g1="X" 	g2="t,uniFB01,uniFB02" 	k="61" />
<hkern g1="X" 	g2="trademark" 	k="-61" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="X" 	g2="underscore" 	k="-82" />
<hkern g1="X" 	g2="v,w,y,yacute,ydieresis" 	k="82" />
<hkern g1="X" 	g2="x" 	k="102" />
<hkern g1="X" 	g2="z" 	k="41" />
<hkern g1="X" 	g2="zero,six" 	k="82" />
<hkern g1="X" 	g2="dollar,S" 	k="20" />
<hkern g1="X" 	g2="ampersand" 	k="41" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="41" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="X" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="X" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="X" 	g2="Z" 	k="20" />
<hkern g1="X" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="V,W" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="X" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="backslash" 	k="-61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="degree" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="eight" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="exclamdown" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="four" 	k="205" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="nine" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="one" 	k="-82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="paragraph" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="questiondown" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="154" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="seven" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t,uniFB01,uniFB02" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="three" 	k="-10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v,w,y,yacute,ydieresis" 	k="92" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="zero,six" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="dollar,S" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="ampersand" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="five" 	k="51" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="133" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="bracketright,braceright" 	k="-82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="exclam" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="-102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="72" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="two" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="section" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="parenright" 	k="-31" />
<hkern g1="Z" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="Z" 	g2="X" 	k="20" />
<hkern g1="Z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="Z" 	g2="exclamdown" 	k="-20" />
<hkern g1="Z" 	g2="one" 	k="-41" />
<hkern g1="Z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="Z" 	g2="question" 	k="-20" />
<hkern g1="Z" 	g2="trademark" 	k="-61" />
<hkern g1="Z" 	g2="underscore" 	k="-61" />
<hkern g1="Z" 	g2="zero,six" 	k="20" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-10" />
<hkern g1="Z" 	g2="bracketright,braceright" 	k="-41" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="82" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="V,W" 	k="41" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="51" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="123" />
<hkern g1="g,i,j,q,u,mu,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="question" 	k="-82" />
<hkern g1="z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="z" 	g2="backslash" 	k="102" />
<hkern g1="z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="z" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="z" 	g2="t,uniFB01,uniFB02" 	k="-10" />
<hkern g1="z" 	g2="underscore" 	k="-20" />
<hkern g1="z" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="z" 	g2="x" 	k="20" />
<hkern g1="ampersand" 	g2="J" 	k="-41" />
<hkern g1="ampersand" 	g2="T" 	k="41" />
<hkern g1="ampersand" 	g2="V,W" 	k="41" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="ampersand" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="ampersand" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="ampersand" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="ampersand" 	g2="v,w,y,yacute,ydieresis" 	k="41" />
<hkern g1="ampersand" 	g2="x" 	k="20" />
<hkern g1="degree" 	g2="J" 	k="20" />
<hkern g1="degree" 	g2="T" 	k="-82" />
<hkern g1="degree" 	g2="V,W" 	k="-41" />
<hkern g1="degree" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="degree" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="degree" 	g2="t,uniFB01,uniFB02" 	k="-41" />
<hkern g1="degree" 	g2="v,w,y,yacute,ydieresis" 	k="-61" />
<hkern g1="degree" 	g2="x" 	k="-20" />
<hkern g1="degree" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="205" />
<hkern g1="degree" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="degree" 	g2="dollar,S" 	k="-41" />
<hkern g1="degree" 	g2="X" 	k="41" />
<hkern g1="degree" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="degree" 	g2="s" 	k="20" />
<hkern g1="percent" 	g2="asterisk,ordfeminine,ordmasculine" 	k="102" />
<hkern g1="percent" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-61" />
<hkern g1="trademark" 	g2="J" 	k="41" />
<hkern g1="trademark" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-41" />
<hkern g1="trademark" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="trademark" 	g2="t,uniFB01,uniFB02" 	k="-82" />
<hkern g1="trademark" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="41" />
<hkern g1="trademark" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-41" />
<hkern g1="trademark" 	g2="s" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="205" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="J" 	k="31" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="dollar,S" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="T" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="V,W" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="X" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Z" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="ampersand" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="bracketright,braceright" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="degree" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="exclam" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="four" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="guillemotright,guilsinglright" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="nine" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="one" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="paragraph" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="percent" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="question" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="questiondown" 	k="143" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="seven" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="slash" 	k="256" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="t,uniFB01,uniFB02" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="three" 	k="10" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="trademark" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="two" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="underscore" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="v,w,y,yacute,ydieresis" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="x" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="z" 	k="-20" />
<hkern g1="backslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-61" />
<hkern g1="backslash" 	g2="J" 	k="-61" />
<hkern g1="backslash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="backslash" 	g2="T" 	k="41" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="backslash" 	g2="V,W" 	k="82" />
<hkern g1="backslash" 	g2="X" 	k="-41" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="backslash" 	g2="asterisk,ordfeminine,ordmasculine" 	k="256" />
<hkern g1="backslash" 	g2="j" 	k="-246" />
<hkern g1="backslash" 	g2="v,w,y,yacute,ydieresis" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="T" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="V,W" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="X" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="-82" />
<hkern g1="bracketleft,braceleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-205" />
<hkern g1="colon,semicolon" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-61" />
<hkern g1="colon,semicolon" 	g2="T" 	k="20" />
<hkern g1="colon,semicolon" 	g2="V,W" 	k="61" />
<hkern g1="colon,semicolon" 	g2="X" 	k="20" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="colon,semicolon" 	g2="t,uniFB01,uniFB02" 	k="-41" />
<hkern g1="colon,semicolon" 	g2="x" 	k="20" />
<hkern g1="exclam" 	g2="J" 	k="-41" />
<hkern g1="exclam" 	g2="T" 	k="-41" />
<hkern g1="exclam" 	g2="V,W" 	k="-20" />
<hkern g1="exclam" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="exclam" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="exclamdown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-20" />
<hkern g1="exclamdown" 	g2="J" 	k="-20" />
<hkern g1="exclamdown" 	g2="T" 	k="41" />
<hkern g1="exclamdown" 	g2="V,W" 	k="61" />
<hkern g1="exclamdown" 	g2="X" 	k="20" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="exclamdown" 	g2="j" 	k="-164" />
<hkern g1="exclamdown" 	g2="t,uniFB01,uniFB02" 	k="-31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="-61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V,W" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="J" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="T" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="V,W" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="X" 	k="51" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="v,w,y,yacute,ydieresis" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="x" 	k="61" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="dollar,S" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="z" 	k="20" />
<hkern g1="parenleft" 	g2="j" 	k="-246" />
<hkern g1="parenright" 	g2="T" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="J" 	k="-102" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="72" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V,W" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v,w,y,yacute,ydieresis" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="dollar,S" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-20" />
<hkern g1="question" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="102" />
<hkern g1="question" 	g2="J" 	k="72" />
<hkern g1="question" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="question" 	g2="V,W" 	k="41" />
<hkern g1="question" 	g2="X" 	k="51" />
<hkern g1="question" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="question" 	g2="v,w,y,yacute,ydieresis" 	k="-20" />
<hkern g1="question" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="question" 	g2="Z" 	k="20" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-82" />
<hkern g1="questiondown" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="123" />
<hkern g1="questiondown" 	g2="T" 	k="133" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="72" />
<hkern g1="questiondown" 	g2="V,W" 	k="154" />
<hkern g1="questiondown" 	g2="X" 	k="-41" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="questiondown" 	g2="asterisk,ordfeminine,ordmasculine" 	k="184" />
<hkern g1="questiondown" 	g2="j" 	k="-143" />
<hkern g1="questiondown" 	g2="v,w,y,yacute,ydieresis" 	k="123" />
<hkern g1="questiondown" 	g2="x" 	k="-41" />
<hkern g1="questiondown" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="questiondown" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="102" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="v,w,y,yacute,ydieresis" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="t,uniFB01,uniFB02" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="10" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="V,W" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="v,w,y,yacute,ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="82" />
<hkern g1="slash" 	g2="J" 	k="20" />
<hkern g1="slash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="T" 	k="-61" />
<hkern g1="slash" 	g2="V,W" 	k="-61" />
<hkern g1="slash" 	g2="Y,Yacute,Ydieresis" 	k="-61" />
<hkern g1="slash" 	g2="j" 	k="-20" />
<hkern g1="slash" 	g2="v,w,y,yacute,ydieresis" 	k="61" />
<hkern g1="slash" 	g2="t,uniFB01,uniFB02" 	k="41" />
<hkern g1="slash" 	g2="x" 	k="82" />
<hkern g1="slash" 	g2="dollar,S" 	k="41" />
<hkern g1="slash" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="slash" 	g2="z" 	k="123" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="123" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="123" />
<hkern g1="slash" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="123" />
<hkern g1="slash" 	g2="s" 	k="123" />
<hkern g1="underscore" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="-102" />
<hkern g1="underscore" 	g2="J" 	k="-143" />
<hkern g1="underscore" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="underscore" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="underscore" 	g2="V,W" 	k="20" />
<hkern g1="underscore" 	g2="X" 	k="-82" />
<hkern g1="underscore" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="underscore" 	g2="j" 	k="-246" />
<hkern g1="underscore" 	g2="v,w,y,yacute,ydieresis" 	k="41" />
<hkern g1="underscore" 	g2="t,uniFB01,uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="x" 	k="-61" />
<hkern g1="underscore" 	g2="dollar,S" 	k="-41" />
<hkern g1="underscore" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="underscore" 	g2="Z" 	k="-41" />
<hkern g1="underscore" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="underscore" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="underscore" 	g2="s" 	k="-20" />
<hkern g1="underscore" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="-61" />
<hkern g1="c,cent,ccedilla" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="T" 	k="102" />
<hkern g1="c,cent,ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="V,W" 	k="82" />
<hkern g1="c,cent,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="82" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="c,cent,ccedilla" 	g2="degree" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="c,cent,ccedilla" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="parenright" 	k="51" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="41" />
<hkern g1="c,cent,ccedilla" 	g2="quoteleft,quotedblleft" 	k="-51" />
<hkern g1="c,cent,ccedilla" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="c,cent,ccedilla" 	g2="t,uniFB01,uniFB02" 	k="-20" />
<hkern g1="c,cent,ccedilla" 	g2="three" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="20" />
<hkern g1="c,cent,ccedilla" 	g2="v,w,y,yacute,ydieresis" 	k="25" />
<hkern g1="c,cent,ccedilla" 	g2="x" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="184" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V,W" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="102" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="123" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="degree" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="one" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="82" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="three" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="82" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="nine" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="paragraph" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="questiondown" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="seven" 	k="51" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="two" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="underscore" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="10" />
<hkern g1="eth" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="eth" 	g2="backslash" 	k="20" />
<hkern g1="eth" 	g2="degree" 	k="-41" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="eth" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="eth" 	g2="trademark" 	k="-41" />
<hkern g1="eth" 	g2="underscore" 	k="41" />
<hkern g1="f" 	g2="ampersand" 	k="41" />
<hkern g1="f" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="f" 	g2="backslash" 	k="-123" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="f" 	g2="degree" 	k="-61" />
<hkern g1="f" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="f" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f" 	g2="one" 	k="-61" />
<hkern g1="f" 	g2="parenright" 	k="-102" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="f" 	g2="question" 	k="-41" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="f" 	g2="three" 	k="-20" />
<hkern g1="f" 	g2="trademark" 	k="-102" />
<hkern g1="f" 	g2="v,w,y,yacute,ydieresis" 	k="-20" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="f" 	g2="nine" 	k="-41" />
<hkern g1="f" 	g2="paragraph" 	k="-61" />
<hkern g1="f" 	g2="questiondown" 	k="41" />
<hkern g1="f" 	g2="seven" 	k="-61" />
<hkern g1="f" 	g2="two" 	k="-20" />
<hkern g1="f" 	g2="underscore" 	k="61" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-82" />
<hkern g1="f" 	g2="exclam" 	k="-20" />
<hkern g1="f" 	g2="four" 	k="41" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="f" 	g2="j" 	k="-102" />
<hkern g1="f" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="f" 	g2="percent" 	k="-20" />
<hkern g1="f" 	g2="slash" 	k="61" />
<hkern g1="k" 	g2="T" 	k="-20" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="k" 	g2="ampersand" 	k="41" />
<hkern g1="k" 	g2="backslash" 	k="82" />
<hkern g1="k" 	g2="eight" 	k="31" />
<hkern g1="k" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="k" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="51" />
<hkern g1="k" 	g2="one" 	k="-41" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="k" 	g2="question" 	k="-41" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-16" />
<hkern g1="k" 	g2="three" 	k="-20" />
<hkern g1="k" 	g2="v,w,y,yacute,ydieresis" 	k="72" />
<hkern g1="k" 	g2="x" 	k="41" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="k" 	g2="nine" 	k="-20" />
<hkern g1="k" 	g2="seven" 	k="-20" />
<hkern g1="k" 	g2="two" 	k="-61" />
<hkern g1="k" 	g2="underscore" 	k="-102" />
<hkern g1="k" 	g2="z" 	k="31" />
<hkern g1="k" 	g2="four" 	k="61" />
<hkern g1="k" 	g2="s" 	k="41" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="k" 	g2="zero,six" 	k="20" />
<hkern g1="d,l,uniFB02" 	g2="t,uniFB01,uniFB02" 	k="-143" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="backslash" 	k="123" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="degree" 	k="20" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="one" 	k="31" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="question" 	k="82" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="trademark" 	k="41" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="paragraph" 	k="20" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="seven" 	k="31" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="two" 	k="10" />
<hkern g1="a,h,m,n,agrave,aacute,acircumflex,atilde,adieresis,aring,ntilde" 	g2="percent" 	k="51" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="92" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V,W" 	k="82" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="123" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="degree" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="one" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="82" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="-10" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="three" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="trademark" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v,w,y,yacute,ydieresis" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="nine" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="paragraph" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="questiondown" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="seven" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="two" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="underscore" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="10" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="percent" 	k="41" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="61" />
<hkern g1="r" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="r" 	g2="V,W" 	k="41" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="r" 	g2="ampersand" 	k="41" />
<hkern g1="r" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="r" 	g2="backslash" 	k="41" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="r" 	g2="degree" 	k="-82" />
<hkern g1="r" 	g2="eight" 	k="-10" />
<hkern g1="r" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="r" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="r" 	g2="one" 	k="-41" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="r" 	g2="question" 	k="-41" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-82" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-82" />
<hkern g1="r" 	g2="t,uniFB01,uniFB02" 	k="-31" />
<hkern g1="r" 	g2="v,w,y,yacute,ydieresis" 	k="-31" />
<hkern g1="r" 	g2="X" 	k="72" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="r" 	g2="nine" 	k="-41" />
<hkern g1="r" 	g2="paragraph" 	k="-61" />
<hkern g1="r" 	g2="questiondown" 	k="61" />
<hkern g1="r" 	g2="seven" 	k="-51" />
<hkern g1="r" 	g2="two" 	k="-20" />
<hkern g1="r" 	g2="underscore" 	k="41" />
<hkern g1="r" 	g2="four" 	k="31" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="r" 	g2="slash" 	k="82" />
<hkern g1="r" 	g2="zero,six" 	k="-20" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="102" />
<hkern g1="r" 	g2="five" 	k="-10" />
<hkern g1="r" 	g2="section" 	k="-20" />
<hkern g1="s" 	g2="backslash" 	k="123" />
<hkern g1="s" 	g2="degree" 	k="20" />
<hkern g1="s" 	g2="question" 	k="20" />
<hkern g1="s" 	g2="three" 	k="20" />
<hkern g1="s" 	g2="trademark" 	k="20" />
<hkern g1="s" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="nine" 	k="10" />
<hkern g1="s" 	g2="paragraph" 	k="41" />
<hkern g1="s" 	g2="seven" 	k="41" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="t" 	g2="ampersand" 	k="10" />
<hkern g1="t" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="t" 	g2="backslash" 	k="123" />
<hkern g1="t" 	g2="degree" 	k="10" />
<hkern g1="t" 	g2="eight" 	k="20" />
<hkern g1="t" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="t" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="t" 	g2="one" 	k="20" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="question" 	k="20" />
<hkern g1="t" 	g2="t,uniFB01,uniFB02" 	k="20" />
<hkern g1="t" 	g2="trademark" 	k="41" />
<hkern g1="t" 	g2="v,w,y,yacute,ydieresis" 	k="20" />
<hkern g1="t" 	g2="paragraph" 	k="41" />
<hkern g1="t" 	g2="seven" 	k="20" />
<hkern g1="t" 	g2="underscore" 	k="-41" />
<hkern g1="t" 	g2="four" 	k="20" />
<hkern g1="t" 	g2="zero,six" 	k="20" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="T" 	k="-20" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="ampersand" 	k="61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="backslash" 	k="41" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="degree" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="one" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="question" 	k="-82" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="t,uniFB01,uniFB02" 	k="-31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="three" 	k="-20" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="v,w,y,yacute,ydieresis" 	k="41" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="x" 	k="61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="X" 	k="61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="nine" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="paragraph" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="questiondown" 	k="92" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="seven" 	k="-31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="two" 	k="-61" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="underscore" 	k="41" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="z" 	k="20" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="four" 	k="82" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="j" 	k="31" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="slash" 	k="82" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="s" 	k="20" />
<hkern g1="v,w,y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE" 	k="61" />
<hkern g1="x" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="x" 	g2="V,W" 	k="51" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="x" 	g2="ampersand" 	k="41" />
<hkern g1="x" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="x" 	g2="backslash" 	k="82" />
<hkern g1="x" 	g2="colon,semicolon" 	k="20" />
<hkern g1="x" 	g2="degree" 	k="-20" />
<hkern g1="x" 	g2="eight" 	k="20" />
<hkern g1="x" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="x" 	g2="c,d,e,g,o,q,cent,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="x" 	g2="one" 	k="-41" />
<hkern g1="x" 	g2="question" 	k="-61" />
<hkern g1="x" 	g2="v,w,y,yacute,ydieresis" 	k="61" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="x" 	g2="paragraph" 	k="-20" />
<hkern g1="x" 	g2="two" 	k="-41" />
<hkern g1="x" 	g2="underscore" 	k="-61" />
<hkern g1="x" 	g2="z" 	k="20" />
<hkern g1="x" 	g2="four" 	k="82" />
<hkern g1="x" 	g2="j" 	k="-102" />
<hkern g1="x" 	g2="slash" 	k="-20" />
<hkern g1="x" 	g2="s" 	k="20" />
<hkern g1="x" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="x" 	g2="zero,six" 	k="20" />
</font>
</defs></svg> 