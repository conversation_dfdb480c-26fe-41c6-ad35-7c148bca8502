<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="montserrat_alternatesmedium" horiz-adv-x="1396" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="550" />
<glyph unicode="&#xfb01;" horiz-adv-x="1429" d="M186 0v1182q0 161 94 255.5t265 94.5q139 0 221 -64l-59 -149q-67 51 -152 51q-176 0 -176 -190v-95h317v-161h-313v-924h-197zM1010 1421q0 54 38 91.5t95 37.5t95 -36t38 -89q0 -56 -37.5 -93.5t-95.5 -37.5q-57 0 -95 36.5t-38 90.5zM1044 0v1085h197v-1085h-197z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1333" d="M31 924v161h184v13q0 204 114.5 319t309.5 115q202 0 310 -113t108 -321v-967q0 -182 168 -182q55 0 96 18l12 -162q-74 -22 -141 -22q-158 0 -245 86.5t-87 243.5v1007q0 120 -58 183t-165 63t-166 -63t-59 -183v-35h311v-161h-311v-924h-197v924h-184z" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="550" />
<glyph unicode=" "  horiz-adv-x="550" />
<glyph unicode="&#x09;" horiz-adv-x="550" />
<glyph unicode="&#xa0;" horiz-adv-x="550" />
<glyph unicode="!" horiz-adv-x="548" d="M141 119q0 56 38.5 93.5t96.5 37.5q57 0 94.5 -37.5t37.5 -93.5q0 -55 -38 -93t-94 -38q-58 0 -96.5 38t-38.5 93zM160 1434h231l-39 -981h-155z" />
<glyph unicode="&#x22;" horiz-adv-x="800" d="M129 1434h172l-14 -553h-146zM500 1434h172l-15 -553h-145z" />
<glyph unicode="#" horiz-adv-x="1439" d="M55 369v149h301l52 397h-287v150h305l45 369h145l-45 -369h385l45 369h146l-45 -369h282v-150h-301l-49 -397h285v-149h-303l-45 -369h-146l45 369h-387l-45 -369h-145l45 369h-283zM502 518h387l49 397h-385z" />
<glyph unicode="$" horiz-adv-x="1271" d="M84 164l76 160q73 -67 186.5 -112t235.5 -52v475q-58 14 -97.5 25t-92 30.5t-88 39.5t-73.5 51t-60.5 66.5t-37 85t-14.5 107.5q0 161 118 274t345 132v233h131v-231q245 -10 415 -125l-67 -164q-167 106 -348 115v-477q62 -15 99.5 -25.5t93.5 -30.5t91 -39.5t75 -50.5 t62.5 -66.5t38 -85t15.5 -108.5q0 -77 -29 -145t-86.5 -123t-149.5 -91t-210 -46v-232h-131v232q-150 7 -284 55.5t-214 122.5zM319 1049q0 -46 19.5 -82t58 -61.5t81.5 -43t104 -33.5v443q-131 -17 -197 -77t-66 -146zM713 162q137 13 205.5 71.5t68.5 145.5q0 47 -20 83.5 t-61 62.5t-85 43t-108 34v-440z" />
<glyph unicode="%" horiz-adv-x="1726" d="M76 1055q0 175 92.5 283t241.5 108t240 -107t91 -284t-91 -284t-240 -107t-241.5 108t-92.5 283zM209 1055q0 -127 54.5 -201t146.5 -74q94 0 147 73t53 202t-53 201.5t-147 72.5q-93 0 -147 -73.5t-54 -200.5zM295 0l979 1434h158l-979 -1434h-158zM985 379 q0 175 91.5 283t240.5 108t241.5 -108t92.5 -283t-92.5 -283t-241.5 -108t-240.5 108t-91.5 283zM1116 379q0 -129 53.5 -202t147.5 -73q92 0 146.5 74t54.5 201t-54 200.5t-147 73.5q-94 0 -147.5 -72.5t-53.5 -201.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1404" d="M92 356q0 131 80.5 233t267.5 208q-95 95 -133.5 170t-38.5 157q0 145 105.5 233.5t281.5 88.5q164 0 260.5 -80t96.5 -219q0 -109 -70.5 -195t-234.5 -180l354 -352q63 119 88 260l158 -51q-37 -190 -125 -328l192 -192l-110 -132l-191 191q-190 -184 -479 -184 q-219 0 -360.5 104t-141.5 268zM285 377q0 -103 88.5 -166.5t232.5 -63.5q211 0 350 136l-407 405q-148 -83 -206 -153t-58 -158zM451 1128q0 -56 29.5 -106.5t115.5 -138.5q141 79 195.5 136.5t54.5 125.5q0 72 -50 115t-141 43q-95 0 -149.5 -48.5t-54.5 -126.5z" />
<glyph unicode="'" horiz-adv-x="430" d="M129 1434h172l-14 -553h-146z" />
<glyph unicode="(" horiz-adv-x="690" d="M195 561q0 282 63 528.5t180 430.5h187q-238 -417 -238 -959q0 -541 238 -958h-187q-118 187 -180.5 432t-62.5 526z" />
<glyph unicode=")" horiz-adv-x="692" d="M66 -397q239 419 239 958q0 540 -239 959h186q119 -184 182.5 -430.5t63.5 -528.5q0 -281 -63.5 -526t-182.5 -432h-186z" />
<glyph unicode="*" horiz-adv-x="819" d="M39 1001l246 138l-246 135l63 115l248 -146l-2 277h123l-2 -277l248 146l63 -115l-243 -135l243 -138l-63 -112l-248 145l2 -278h-123l2 278l-248 -145z" />
<glyph unicode="+" horiz-adv-x="1191" d="M137 635v162h373v368h172v-368h373v-162h-373v-367h-172v367h-373z" />
<glyph unicode="," horiz-adv-x="464" d="M98 129q0 61 39.5 100t98.5 39q58 0 95.5 -39.5t37.5 -99.5q0 -48 -35 -147l-96 -285h-129l75 305q-39 14 -62.5 48t-23.5 79z" />
<glyph unicode="-" horiz-adv-x="784" d="M117 477v170h551v-170h-551z" />
<glyph unicode="." horiz-adv-x="464" d="M94 129q0 60 40.5 99.5t98.5 39.5q57 0 96.5 -39.5t39.5 -99.5q0 -61 -40 -101t-96 -40q-58 0 -98.5 40t-40.5 101z" />
<glyph unicode="/" horiz-adv-x="720" d="M-57 -205l675 1929h179l-676 -1929h-178z" />
<glyph unicode="0" horiz-adv-x="1366" d="M98 717q0 172 44.5 311t122.5 231t185.5 141.5t233.5 49.5q125 0 232 -49.5t185.5 -141.5t122.5 -231t44 -311t-44 -311t-122.5 -231t-185.5 -141.5t-232 -49.5q-126 0 -233.5 49.5t-185.5 141.5t-122.5 231t-44.5 311zM303 717q0 -268 103.5 -409.5t277.5 -141.5 q173 0 276 141.5t103 409.5t-103 409.5t-276 141.5q-174 0 -277.5 -141.5t-103.5 -409.5z" />
<glyph unicode="1" horiz-adv-x="757" d="M16 1255v179h527v-1434h-203v1255h-324z" />
<glyph unicode="2" horiz-adv-x="1175" d="M29 1233q84 103 220.5 160t303.5 57q225 0 358 -107.5t133 -291.5q0 -115 -49 -217.5t-186 -233.5l-438 -422h735v-178h-1030v139l583 563q106 102 143.5 177t37.5 151q0 112 -78.5 175t-226.5 63q-237 0 -367 -156z" />
<glyph unicode="3" horiz-adv-x="1171" d="M10 158l94 161q74 -69 189 -111t244 -42q160 0 248.5 68t88.5 186t-87.5 184t-266.5 66h-114v143l360 442h-690v179h946v-140l-375 -463q210 -20 321 -129.5t111 -279.5q0 -191 -140 -314.5t-402 -123.5q-155 0 -296 46.5t-231 127.5z" />
<glyph unicode="4" horiz-adv-x="1370" d="M78 352v144l733 938h221l-702 -906h551v312h192v-312h273v-176h-273v-352h-199v352h-796z" />
<glyph unicode="5" horiz-adv-x="1175" d="M35 158l94 161q74 -69 188.5 -111t241.5 -42q161 0 250.5 69t89.5 187q0 63 -21.5 109.5t-70.5 82t-134.5 53t-207.5 17.5h-326l76 750h809v-179h-637l-39 -393h160q157 0 273 -31.5t186 -90t103.5 -135.5t33.5 -175q0 -94 -34 -174.5t-99.5 -141.5t-170.5 -95.5 t-239 -34.5q-153 0 -293.5 46.5t-232.5 127.5z" />
<glyph unicode="6" horiz-adv-x="1263" d="M98 698q0 180 49.5 323t139 236.5t214 143t275.5 49.5q221 0 357 -84l-78 -160q-103 68 -275 68q-223 0 -350 -139t-127 -402q0 -33 2 -51q61 91 169 139.5t243 48.5q216 0 353.5 -119t137.5 -315q0 -203 -145 -327.5t-363 -124.5q-289 0 -445.5 185.5t-156.5 528.5z M352 426q0 -116 90.5 -196t249.5 -80q141 0 229.5 76t88.5 202t-89 201.5t-239 75.5q-145 0 -237.5 -79.5t-92.5 -199.5z" />
<glyph unicode="7" horiz-adv-x="1224" d="M61 999v435h1082v-140l-580 -1294h-217l567 1255h-655v-256h-197z" />
<glyph unicode="8" horiz-adv-x="1318" d="M90 406q0 123 65 211.5t187 134.5q-201 94 -201 309q0 179 141.5 284t374.5 105q235 0 378 -105t143 -284q0 -214 -203 -309q124 -48 189 -136t65 -210q0 -195 -154.5 -308.5t-417.5 -113.5q-261 0 -414 113.5t-153 308.5zM295 412q0 -122 96 -192t266 -70t268.5 70 t98.5 192q0 119 -98.5 187.5t-268.5 68.5t-266 -68.5t-96 -187.5zM344 1053q0 -106 83 -168t230 -62q149 0 234.5 62t85.5 168q0 107 -87 169t-233 62q-145 0 -229 -62t-84 -169z" />
<glyph unicode="9" horiz-adv-x="1263" d="M57 997q0 203 144 328t362 125q289 0 445.5 -186t156.5 -529q0 -180 -49.5 -323t-139 -236.5t-214 -142.5t-275.5 -49q-220 0 -356 84l78 159q104 -67 276 -67q223 0 349.5 138.5t126.5 401.5v52q-62 -92 -170 -140.5t-244 -48.5q-216 0 -353 119t-137 315zM256 1006 q0 -127 88 -202t238 -75q146 0 238.5 79.5t92.5 199.5q0 116 -91 196t-251 80q-141 0 -228 -76t-87 -202z" />
<glyph unicode=":" horiz-adv-x="464" d="M94 129q0 60 40.5 99.5t98.5 39.5q57 0 96.5 -39.5t39.5 -99.5q0 -61 -40 -101t-96 -40q-58 0 -98.5 40t-40.5 101zM94 956q0 61 40.5 100.5t98.5 39.5q57 0 96.5 -40t39.5 -100q0 -61 -40 -101t-96 -40q-58 0 -98.5 40t-40.5 101z" />
<glyph unicode=";" horiz-adv-x="464" d="M94 956q0 61 40.5 100.5t98.5 39.5q57 0 96.5 -40t39.5 -100q0 -61 -40 -101t-96 -40q-58 0 -98.5 40t-40.5 101zM98 129q0 61 39.5 100t98.5 39q58 0 95.5 -39.5t37.5 -99.5q0 -48 -35 -147l-96 -285h-129l75 305q-39 14 -62.5 48t-23.5 79z" />
<glyph unicode="&#x3c;" horiz-adv-x="1191" d="M137 629v176l918 362v-170l-727 -280l727 -281v-170z" />
<glyph unicode="=" horiz-adv-x="1191" d="M137 393v162h918v-162h-918zM137 879v161h918v-161h-918z" />
<glyph unicode="&#x3e;" horiz-adv-x="1191" d="M137 266v170l729 281l-729 280v170l918 -362v-176z" />
<glyph unicode="?" horiz-adv-x="1173" d="M18 1221q178 229 531 229q222 0 353.5 -98t131.5 -267q0 -69 -20 -128t-52 -100.5t-70.5 -79t-77 -71.5t-70.5 -69.5t-52 -82.5t-20 -101h-205q0 65 20 121.5t51.5 97.5t70 78t77 70.5t70 67.5t51.5 76.5t20 90.5q0 100 -78 160.5t-214 60.5q-239 0 -367 -162zM438 119 q0 56 37.5 93.5t95.5 37.5q57 0 94 -37.5t37 -93.5q0 -55 -37.5 -93t-93.5 -38q-58 0 -95.5 38t-37.5 93z" />
<glyph unicode="@" horiz-adv-x="2117" d="M98 520q0 201 71 373t197 294t306.5 191.5t392.5 69.5q277 0 494.5 -113.5t338.5 -318.5t121 -467t-96 -411.5t-268 -149.5q-99 0 -163 50.5t-83 143.5q-125 -194 -389 -194q-218 0 -360 148.5t-142 377.5q0 228 141.5 375t360.5 147q120 0 216.5 -44.5t158.5 -129.5v166 h178v-737q0 -77 32.5 -112.5t84.5 -35.5q91 0 140.5 106t49.5 298q0 170 -59.5 313t-166.5 242.5t-258.5 155t-330.5 55.5q-181 0 -333.5 -59t-258.5 -163t-165.5 -251t-59.5 -320q0 -233 100.5 -413.5t285 -281.5t423.5 -101q222 0 395 90l45 -131q-92 -46 -210 -71.5 t-230 -25.5q-210 0 -389.5 70t-304 193t-195 296.5t-70.5 374.5zM700 514q0 -165 97 -266t252 -101q152 0 251 100t99 267t-98.5 266t-251.5 99q-155 0 -252 -99.5t-97 -265.5z" />
<glyph unicode="A" horiz-adv-x="1638" d="M203 0v805q0 312 165.5 478.5t448.5 166.5q286 0 452.5 -166.5t166.5 -478.5v-805h-205v395h-828v-395h-200zM403 573h828v250q0 220 -109.5 332.5t-304.5 112.5t-304.5 -112.5t-109.5 -332.5v-250z" />
<glyph unicode="B" horiz-adv-x="1550" d="M215 0v1434h643q246 0 380 -98t134 -273q0 -113 -52 -195t-140 -125q125 -35 196.5 -126t71.5 -230q0 -186 -140 -286.5t-409 -100.5h-684zM420 166h471q174 0 262 57.5t88 179.5q0 240 -350 240h-471v-477zM420 809h420q158 0 242.5 58t84.5 171t-84.5 171.5 t-242.5 58.5h-420v-459z" />
<glyph unicode="C" horiz-adv-x="1480" d="M98 717q0 157 58 293t158.5 232.5t241.5 152t302 55.5q167 0 307.5 -57t237.5 -166l-133 -129q-161 170 -404 170q-120 0 -224.5 -42t-179 -114.5t-117 -175t-42.5 -219.5t42.5 -219.5t117 -175t179 -114.5t224.5 -42q241 0 404 172l133 -129q-98 -110 -238.5 -167.5 t-308.5 -57.5q-214 0 -387.5 94t-272 262t-98.5 377z" />
<glyph unicode="D" horiz-adv-x="1691" d="M215 0v1434h604q228 0 404 -90t273 -253t97 -374t-97 -374t-273 -253t-404 -90h-604zM420 178h387q264 0 423 148t159 391q0 242 -159 390t-423 148h-387v-1077z" />
<glyph unicode="E" horiz-adv-x="1318" d="M104 393q0 123 70.5 212.5t183.5 127.5q-90 42 -141 125t-51 191q0 83 36 154.5t106 127t182.5 87.5t254.5 32q134 0 260 -29.5t218 -81.5l-60 -163q-183 98 -409 98q-187 0 -285 -66.5t-98 -173.5t76 -164t217 -57h368v-176h-377q-162 0 -254 -57t-92 -172 q0 -116 104 -182t312 -66q143 0 273 39.5t214 109.5l70 -159q-93 -78 -242 -122t-325 -44q-294 0 -452.5 112.5t-158.5 296.5z" />
<glyph unicode="F" horiz-adv-x="1241" d="M203 0v922q0 247 157 387.5t437 140.5q257 0 413 -109l-67 -170q-137 97 -340 97q-197 0 -296 -89.5t-99 -258.5v-164h655v-176h-655v-580h-205z" />
<glyph unicode="G" horiz-adv-x="1581" d="M98 711q0 212 99 381t274.5 263.5t394.5 94.5q171 0 312 -56.5t239 -164.5l-127 -127q-172 166 -416 166q-249 0 -410 -156.5t-161 -400.5q0 -129 45 -234.5t120.5 -173.5t171.5 -104.5t202 -36.5q215 0 366 133v430h197v-725h-180v96q-156 -112 -400 -112 q-115 0 -222.5 30t-199 91t-159.5 147t-107 204t-39 255z" />
<glyph unicode="H" horiz-adv-x="1662" d="M215 0v1434h205v-617h823v617h205v-1434h-205v639h-823v-639h-205z" />
<glyph unicode="I" horiz-adv-x="933" d="M88 0v178h277v1077h-277v179h758v-179h-277v-1077h277v-178h-758z" />
<glyph unicode="J" horiz-adv-x="1046" d="M-18 -14l114 139q119 -170 295 -170q248 0 248 291v1009h-516v179h721v-1178q0 -238 -114.5 -357.5t-336.5 -119.5q-127 0 -234.5 54t-176.5 153z" />
<glyph unicode="K" horiz-adv-x="1472" d="M215 0v1434h205v-801l778 801h234l-613 -646l651 -788h-239l-549 637l-262 -266v-371h-205z" />
<glyph unicode="L" horiz-adv-x="1216" d="M215 0v1434h205v-1256h776v-178h-981z" />
<glyph unicode="M" horiz-adv-x="2512" d="M215 0v1434h197v-209q64 107 179.5 166t268.5 59q159 0 275 -63t172 -177q67 115 191.5 177.5t283.5 62.5q248 0 387 -143t139 -416v-891h-203v885q0 189 -98 286t-260 97q-177 0 -281 -102t-104 -310v-856h-203v885q0 191 -93 287t-255 96q-179 0 -285 -102t-106 -310 v-856h-205z" />
<glyph unicode="N" horiz-adv-x="1634" d="M215 0v1434h197v-203q144 219 471 219q258 0 403.5 -155.5t145.5 -444.5v-850h-203v844q0 208 -102.5 316t-284.5 108q-197 0 -309.5 -115t-112.5 -340v-813h-205z" />
<glyph unicode="O" horiz-adv-x="1720" d="M98 717q0 155 58 291t158.5 233t243 153t304.5 56q215 0 389 -94.5t272.5 -262.5t98.5 -376t-98.5 -376t-272.5 -262.5t-389 -94.5q-162 0 -304.5 56t-243 153t-158.5 233t-58 291zM303 717q0 -238 159 -394.5t400 -156.5q238 0 396.5 157t158.5 394t-158.5 394 t-396.5 157q-241 0 -400 -156.5t-159 -394.5z" />
<glyph unicode="P" horiz-adv-x="1478" d="M215 0v1434h559q280 0 439 -133.5t159 -366.5t-159 -366.5t-439 -133.5h-354v-434h-205zM420 612h348q195 0 297 83.5t102 238.5t-102 238t-297 83h-348v-643z" />
<glyph unicode="Q" horiz-adv-x="1720" d="M98 717q0 155 58 291t158.5 233t243 153t304.5 56q215 0 389 -94.5t272.5 -262.5t98.5 -376q0 -144 -49 -271.5t-136 -222t-209 -156t-265 -77.5v-266h-205v266q-291 34 -475.5 236.5t-184.5 490.5zM303 717q0 -211 128.5 -361t332.5 -182v338h190v-340q204 31 333.5 182 t129.5 363q0 236 -158.5 393.5t-396.5 157.5q-119 0 -223 -42t-177.5 -115t-116 -175.5t-42.5 -218.5z" />
<glyph unicode="R" horiz-adv-x="1488" d="M215 0v1434h559q280 0 439 -133.5t159 -366.5q0 -165 -82 -282t-233 -171l340 -481h-223l-310 440q-58 -4 -90 -4h-354v-436h-205zM420 610h348q195 0 297 84.5t102 239.5t-102 238t-297 83h-348v-645z" />
<glyph unicode="S" horiz-adv-x="1271" d="M84 164l76 160q81 -74 209.5 -120t263.5 -46q178 0 266 61t88 160q0 71 -48 118.5t-124.5 73.5t-169 47t-185 49.5t-169 69.5t-124.5 118t-48 185q0 85 33.5 158t99 130t172.5 89.5t244 32.5q128 0 250 -33t210 -94l-67 -164q-183 117 -393 117q-174 0 -261.5 -63.5 t-87.5 -163.5q0 -56 29.5 -97t80 -66.5t115.5 -45.5t137.5 -37t144.5 -36t137 -49.5t115.5 -71.5t80 -106t29.5 -149t-34 -156.5t-100.5 -129t-174.5 -89t-246 -32.5q-162 0 -312 50t-237 130z" />
<glyph unicode="T" horiz-adv-x="1224" d="M-8 1286q273 164 618 164q350 0 623 -164l-72 -162q-210 121 -446 142v-1266h-203v1266q-239 -21 -449 -142z" />
<glyph unicode="U" horiz-adv-x="1626" d="M203 584v850h205v-844q0 -208 102 -316t281 -108q193 0 305 115t112 340v813h203v-1434h-197v203q-144 -219 -466 -219q-255 0 -400 156t-145 444z" />
<glyph unicode="V" horiz-adv-x="1458" d="M-2 1434h221l516 -1178l520 1178h205l-629 -1434h-202z" />
<glyph unicode="W" horiz-adv-x="2437" d="M203 571v863h205v-856q0 -412 350 -412q171 0 264.5 100.5t93.5 311.5v856h205v-856q0 -211 92.5 -311.5t263.5 -100.5q353 0 353 412v856h204v-863q0 -290 -144.5 -438.5t-412.5 -148.5q-158 0 -277 56t-183 155q-65 -99 -182.5 -155t-276.5 -56q-267 0 -411 148.5 t-144 438.5z" />
<glyph unicode="X" horiz-adv-x="1378" d="M27 0l538 735l-506 699h234l401 -551l398 551h223l-506 -693l543 -741h-236l-430 592l-424 -592h-235z" />
<glyph unicode="Y" horiz-adv-x="1611" d="M193 954v480h204v-471q0 -210 102.5 -318t284.5 -108q196 0 309 115t113 341v441h203v-1008q0 -312 -166 -477.5t-459 -165.5q-177 0 -321 54.5t-242 154.5l88 160q188 -185 475 -185q422 0 422 451v145q-144 -209 -444 -209q-269 0 -419 155.5t-150 444.5z" />
<glyph unicode="Z" horiz-adv-x="1366" d="M98 0v127l412 518h-289v170h424l348 440h-887v179h1160v-125l-394 -498h267v-170h-402l-364 -463h923v-178h-1198z" />
<glyph unicode="[" horiz-adv-x="681" d="M215 -397v1917h428v-162h-231v-1594h231v-161h-428z" />
<glyph unicode="\" horiz-adv-x="720" d="M-78 1724h178l676 -1929h-178z" />
<glyph unicode="]" horiz-adv-x="681" d="M39 -236h231v1594h-231v162h428v-1917h-428v161z" />
<glyph unicode="^" horiz-adv-x="1193" d="M154 291l362 852h164l360 -852h-157l-285 690l-287 -690h-157z" />
<glyph unicode="_" horiz-adv-x="1024" d="M0 0h1024v-127h-1024v127z" />
<glyph unicode="`" horiz-adv-x="1228" d="M240 1509h249l279 -266h-184z" />
<glyph unicode="a" d="M86 543q0 247 154.5 400t394.5 153q245 0 379 -174v163h196v-1085h-188v172q-65 -91 -165 -137.5t-222 -46.5q-240 0 -394.5 154t-154.5 401zM285 543q0 -171 103.5 -277t262.5 -106q158 0 261.5 106t103.5 277t-103.5 276t-261.5 105q-159 0 -262.5 -105t-103.5 -276z " />
<glyph unicode="b" d="M186 0v1520h197v-598q134 174 379 174q240 0 394.5 -153t154.5 -400t-154.5 -401t-394.5 -154q-122 0 -222 46.5t-165 137.5v-172h-189zM381 543q0 -171 103.5 -277t260.5 -106q159 0 263 106t104 277t-104 276t-263 105q-158 0 -261 -105t-103 -276z" />
<glyph unicode="c" horiz-adv-x="1169" d="M86 543q0 243 161 398t412 155q148 0 263.5 -59.5t179.5 -172.5l-150 -96q-103 156 -295 156q-162 0 -267 -105t-105 -276q0 -174 104.5 -278.5t267.5 -104.5q193 0 295 155l150 -94q-64 -113 -179.5 -173t-263.5 -60q-251 0 -412 156t-161 399z" />
<glyph unicode="d" d="M86 543q0 247 154.5 400t394.5 153q245 0 379 -174v598h196v-1520h-188v172q-65 -91 -165 -137.5t-222 -46.5q-240 0 -394.5 154t-154.5 401zM285 543q0 -171 103.5 -277t262.5 -106q158 0 261.5 106t103.5 277t-103.5 276t-261.5 105q-159 0 -262.5 -105t-103.5 -276z " />
<glyph unicode="e" horiz-adv-x="1253" d="M86 543q0 241 154 397t391 156q148 0 268 -65t191.5 -187t74.5 -281l-862 -168q41 -112 138.5 -173.5t234.5 -61.5q194 0 313 131l107 -127q-72 -86 -181.5 -131t-244.5 -45q-261 0 -422.5 155t-161.5 400zM276 532l695 132q-27 119 -118.5 192.5t-221.5 73.5 q-156 0 -255.5 -104t-99.5 -273v-21z" />
<glyph unicode="f" horiz-adv-x="694" d="M186 0v1182q0 161 94 255.5t265 94.5q139 0 221 -64l-59 -149q-67 51 -152 51q-176 0 -176 -190v-95h317v-161h-313v-924h-197z" />
<glyph unicode="g" horiz-adv-x="1413" d="M86 571q0 154 72.5 274t197.5 185.5t281 65.5q124 0 228.5 -45t172.5 -133v167h187v-938q0 -284 -141.5 -420.5t-421.5 -136.5q-153 0 -292.5 42t-226.5 120l95 152q76 -65 187 -103.5t230 -38.5q191 0 282 90t91 275v86q-69 -83 -170.5 -125.5t-220.5 -42.5 q-237 0 -394 147t-157 379zM285 571q0 -156 104.5 -255t269.5 -99q163 0 268 99t105 255q0 157 -104.5 255t-268.5 98q-165 0 -269.5 -98t-104.5 -255z" />
<glyph unicode="h" horiz-adv-x="1394" d="M186 0v1520h197v-588q62 79 160.5 121.5t222.5 42.5q206 0 328.5 -119.5t122.5 -351.5v-625h-197v602q0 158 -76 238t-217 80q-160 0 -252 -93.5t-92 -267.5v-559h-197z" />
<glyph unicode="i" horiz-adv-x="571" d="M152 1421q0 54 38 91.5t95 37.5t95 -36t38 -89q0 -56 -37.5 -93.5t-95.5 -37.5q-57 0 -95 36.5t-38 90.5zM186 0v1085h197v-1085h-197z" />
<glyph unicode="j" horiz-adv-x="581" d="M-188 -344l63 151q60 -51 160 -51q78 0 120 49t42 142v1138h196v-1134q0 -168 -90.5 -264.5t-255.5 -96.5q-154 0 -235 66zM162 1421q0 54 38 91.5t95 37.5t95 -36t38 -89q0 -56 -37.5 -93.5t-95.5 -37.5q-57 0 -95 36.5t-38 90.5z" />
<glyph unicode="k" horiz-adv-x="1261" d="M186 0v1520h197v-975l592 540h237l-456 -448l501 -637h-241l-408 506l-225 -209v-297h-197z" />
<glyph unicode="l" horiz-adv-x="657" d="M186 319v1201h197v-1184q0 -182 168 -182q50 0 96 20l10 -162q-67 -24 -141 -24q-158 0 -244 87t-86 244z" />
<glyph unicode="m" horiz-adv-x="2164" d="M186 0v1085h189v-161q58 83 155 127.5t220 44.5q127 0 225 -51t151 -150q62 94 172 147.5t246 53.5q205 0 325 -119.5t120 -351.5v-625h-197v602q0 158 -73 238t-208 80q-151 0 -238 -93.5t-87 -267.5v-559h-197v602q0 158 -72.5 238t-207.5 80q-151 0 -238.5 -93.5 t-87.5 -267.5v-559h-197z" />
<glyph unicode="n" horiz-adv-x="1394" d="M186 0v1085h189v-163q60 84 161 129t230 45q206 0 328.5 -119.5t122.5 -351.5v-625h-197v602q0 158 -76 238t-217 80q-160 0 -252 -93.5t-92 -267.5v-559h-197z" />
<glyph unicode="o" horiz-adv-x="1300" d="M86 543q0 242 160 397.5t405 155.5t404 -155.5t159 -397.5q0 -160 -72.5 -287t-201 -197.5t-289.5 -70.5q-245 0 -405 156.5t-160 398.5zM285 543q0 -171 103.5 -277t262.5 -106t262 106t103 277t-103 276t-262 105t-262.5 -105t-103.5 -276z" />
<glyph unicode="p" d="M186 -397v1482h189v-172q65 90 165.5 136.5t221.5 46.5q240 0 394.5 -153t154.5 -400q0 -248 -154.5 -401.5t-394.5 -153.5q-117 0 -214.5 44.5t-164.5 131.5v-561h-197zM381 543q0 -171 103.5 -277t260.5 -106q159 0 263 106t104 277q0 170 -104 275.5t-263 105.5 q-157 0 -260.5 -105.5t-103.5 -275.5z" />
<glyph unicode="q" d="M86 543q0 247 154.5 400t394.5 153q121 0 221.5 -46.5t165.5 -136.5v172h188v-1482h-196v561q-67 -87 -164.5 -131.5t-214.5 -44.5q-240 0 -394.5 153.5t-154.5 401.5zM285 543q0 -171 103.5 -277t262.5 -106q158 0 261.5 106t103.5 277q0 169 -104 275t-261 106 q-159 0 -262.5 -105.5t-103.5 -275.5z" />
<glyph unicode="r" horiz-adv-x="839" d="M186 0v1085h189v-182q106 193 395 193v-191q-16 2 -45 2q-160 0 -251 -95t-91 -271v-541h-197z" />
<glyph unicode="s" horiz-adv-x="1026" d="M49 115l82 155q70 -50 173.5 -81t207.5 -31q258 0 258 147q0 49 -38 79.5t-98.5 44.5t-133.5 25t-146 29t-133.5 48t-98.5 90.5t-38 148.5q0 147 122.5 236.5t330.5 89.5q107 0 216 -27t179 -72l-84 -155q-134 86 -313 86q-125 0 -190.5 -41.5t-65.5 -108.5q0 -52 38 -84 t98.5 -47.5t134 -27t147 -30t134 -47.5t98.5 -88t38 -145q0 -148 -126 -234.5t-343 -86.5q-132 0 -256 36t-193 91z" />
<glyph unicode="t" horiz-adv-x="808" d="M176 328v995h197v-238h311v-161h-311v-588q0 -88 43.5 -135t126.5 -47q90 0 153 51l62 -142q-87 -75 -238 -75q-165 0 -254.5 88t-89.5 252z" />
<glyph unicode="u" horiz-adv-x="1386" d="M176 461v624h197v-602q0 -158 76 -238.5t217 -80.5q156 0 245.5 94t89.5 266v561h197v-1085h-186v164q-60 -84 -156.5 -130t-212.5 -46q-217 0 -342 120t-125 353z" />
<glyph unicode="v" horiz-adv-x="1144" d="M-4 1085h205l372 -870l381 870h193l-475 -1085h-201z" />
<glyph unicode="w" horiz-adv-x="2017" d="M176 485v600h197v-581q0 -173 64 -256.5t202 -83.5q139 0 205.5 84t66.5 256v581h197v-581q0 -172 66.5 -256t205.5 -84q137 0 201 83.5t64 256.5v581h196v-600q0 -239 -118.5 -368t-344.5 -129q-265 0 -368 194q-105 -194 -371 -194q-227 0 -345 128.5t-118 368.5z" />
<glyph unicode="x" horiz-adv-x="1130" d="M29 0l428 555l-408 530h219l299 -391l297 391h215l-409 -530l432 -555h-223l-314 416l-317 -416h-219z" />
<glyph unicode="y" horiz-adv-x="1386" d="M172 -248l96 152q72 -65 173 -102.5t214 -37.5q179 0 262.5 88.5t83.5 274.5v80q-60 -78 -153.5 -120t-204.5 -42q-217 0 -342 119t-125 352v569h197v-546q0 -157 76 -237.5t217 -80.5q156 0 245.5 94t89.5 267v503h197v-938q0 -284 -133 -420.5t-399 -136.5 q-145 0 -277.5 42t-216.5 120z" />
<glyph unicode="z" horiz-adv-x="1087" d="M92 0v127l277 344h-197v156h324l237 297h-629v161h881v-129l-268 -331h203v-156h-328l-248 -307h657v-162h-909z" />
<glyph unicode="{" horiz-adv-x="718" d="M117 479v164h71q93 0 93 102v472q0 150 78 226.5t231 76.5h90v-162h-51q-76 0 -114 -41t-38 -119v-448q0 -85 -26.5 -127.5t-85.5 -61.5q59 -19 85.5 -61t26.5 -127v-449q0 -78 38 -119t114 -41h51v-161h-90q-153 0 -231 76.5t-78 226.5v471q0 102 -93 102h-71z" />
<glyph unicode="|" horiz-adv-x="612" d="M215 -397v1917h182v-1917h-182z" />
<glyph unicode="}" horiz-adv-x="718" d="M39 -236h49q156 0 156 160v449q0 85 25.5 127t84.5 61q-59 19 -84.5 61t-25.5 128v448q0 160 -156 160h-49v162h90q154 0 232.5 -77t78.5 -226v-472q0 -102 90 -102h70v-164h-70q-90 0 -90 -102v-471q0 -149 -78.5 -226t-232.5 -77h-90v161z" />
<glyph unicode="~" horiz-adv-x="1191" d="M119 557q4 166 76.5 251t193.5 85q55 0 105.5 -20.5t88.5 -49.5t72 -58.5t70.5 -50t71.5 -20.5q67 0 105.5 50.5t41.5 136.5h129q-4 -167 -76.5 -252.5t-193.5 -85.5q-55 0 -105 20.5t-88 49.5t-72.5 58t-71 49.5t-71.5 20.5q-66 0 -105 -49.5t-42 -134.5h-129z" />
<glyph unicode="&#xa1;" horiz-adv-x="548" d="M141 963q0 56 38.5 94.5t96.5 38.5q57 0 94.5 -38.5t37.5 -94.5q0 -54 -37.5 -91.5t-94.5 -37.5q-58 0 -96.5 37.5t-38.5 91.5zM160 -315l37 948h155l39 -948h-231z" />
<glyph unicode="&#xa2;" horiz-adv-x="1169" d="M86 543q0 221 136 372t356 177v239h131v-237q131 -9 233 -68t160 -162l-150 -96q-87 133 -243 152v-756q157 19 243 151l150 -94q-58 -103 -160.5 -162.5t-232.5 -68.5v-236h-131v238q-219 26 -355.5 177.5t-136.5 373.5zM285 543q0 -151 80.5 -251t212.5 -124v747 q-132 -25 -212.5 -124.5t-80.5 -247.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1323" d="M61 0v178h224v471h-224v135h224v134q0 245 159 388.5t445 143.5q241 0 403 -102l-67 -170q-131 90 -355 90q-188 0 -285.5 -90t-97.5 -258v-136h545v-135h-545v-471h775v-178h-1201z" />
<glyph unicode="&#xa4;" horiz-adv-x="1433" d="M63 117l211 211q-112 144 -112 325q0 178 108 324l-207 207l113 121l215 -213q145 104 326 104q176 0 325 -102l213 211l115 -121l-207 -205q111 -146 111 -326t-115 -327l211 -209l-115 -121l-217 215q-148 -98 -321 -98q-179 0 -322 100l-219 -217zM324 653 q0 -156 115.5 -267.5t277.5 -111.5q163 0 281 112t118 267q0 156 -118 268.5t-281 112.5t-278 -112t-115 -269z" />
<glyph unicode="&#xa5;" horiz-adv-x="1447" d="M-8 1434h217l518 -725l520 725h209l-567 -795h368v-117h-432v-166h432v-116h-432v-240h-202v240h-433v116h433v166h-433v117h371z" />
<glyph unicode="&#xa6;" horiz-adv-x="612" d="M215 319h182v-716h-182v716zM215 803v717h182v-717h-182z" />
<glyph unicode="&#xa7;" horiz-adv-x="1024" d="M53 -86l64 149q66 -54 165 -88t201 -34q118 0 184 44.5t66 125.5q0 43 -22.5 75t-60.5 51.5t-87.5 34t-104.5 27.5t-109.5 28t-104 39t-87.5 56.5t-60.5 84.5t-22.5 120q0 87 41 155t112 111q-108 74 -108 219q0 152 125.5 243t343.5 91q99 0 207.5 -28t171.5 -75 l-64 -149q-134 94 -330 94q-136 0 -207 -44t-71 -126q0 -42 22.5 -73t60.5 -50t87.5 -34t104 -28t108.5 -29t103.5 -40.5t87.5 -58t60.5 -87t22.5 -122.5q0 -82 -40.5 -148.5t-110.5 -109.5q108 -77 108 -223q0 -149 -119.5 -240.5t-314.5 -91.5q-120 0 -238.5 37t-183.5 94 zM246 637q0 -38 17 -68t41.5 -48.5t68 -35.5t78 -26t92 -23t90.5 -24q67 21 107 68.5t40 113.5q0 38 -17 67.5t-40.5 47.5t-67 35t-77 26t-91 23t-90.5 24q-68 -21 -109.5 -68t-41.5 -112z" />
<glyph unicode="&#xa8;" horiz-adv-x="1228" d="M319 1382q0 45 31 76t74 31t73.5 -31t30.5 -76t-30.5 -74.5t-73.5 -29.5q-44 0 -74.5 29.5t-30.5 74.5zM700 1382q0 45 31 76t74 31t73.5 -31t30.5 -76t-30.5 -74.5t-73.5 -29.5q-44 0 -74.5 29.5t-30.5 74.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1642" d="M98 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM195 717q0 -174 81 -318.5t224.5 -228.5t318.5 -84t319.5 84 t227 229.5t82.5 321.5t-80.5 319.5t-224 225.5t-320.5 82t-321.5 -84t-225.5 -228.5t-81 -318.5zM410 717q0 186 121.5 304t310.5 118q107 0 194 -44.5t138 -121.5l-117 -84q-74 110 -217 110q-119 0 -197 -77.5t-78 -204.5t78 -205t197 -78q143 0 217 111l117 -82 q-50 -78 -137.5 -123t-194.5 -45q-189 0 -310.5 118t-121.5 304z" />
<glyph unicode="&#xaa;" horiz-adv-x="827" d="M76 1061q0 87 67.5 137.5t210.5 50.5h199v19q0 79 -44 116t-134 37q-131 0 -225 -71l-54 92q53 41 133 64.5t160 23.5q305 0 305 -266v-387h-131v81q-29 -40 -88.5 -65t-140.5 -25q-124 0 -191 53.5t-67 139.5zM211 1067q0 -47 39.5 -73.5t111.5 -26.5q139 0 191 100v94 h-180q-162 0 -162 -94z" />
<glyph unicode="&#xab;" horiz-adv-x="1032" d="M94 543l295 393h180l-288 -393l288 -391h-180zM487 543l295 393h181l-289 -393l289 -391h-181z" />
<glyph unicode="&#xac;" horiz-adv-x="1191" d="M137 637v162h918v-527h-174v365h-744z" />
<glyph unicode="&#xad;" horiz-adv-x="784" d="M117 477v170h551v-170h-551z" />
<glyph unicode="&#xae;" horiz-adv-x="1642" d="M98 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM195 717q0 -174 81 -318.5t224.5 -228.5t318.5 -84t319.5 84 t227 229.5t82.5 321.5t-80.5 319.5t-224 225.5t-320.5 82t-321.5 -84t-225.5 -228.5t-81 -318.5zM530 307v819h328q153 0 242.5 -75t89.5 -203q0 -93 -45 -157.5t-127 -94.5l186 -289h-141l-170 262h-35h-188v-262h-140zM668 684h180q98 0 152.5 43t54.5 121t-54 120 t-153 42h-180v-326z" />
<glyph unicode="&#xaf;" horiz-adv-x="1228" d="M281 1315v123h667v-123h-667z" />
<glyph unicode="&#xb0;" horiz-adv-x="858" d="M92 1114q0 138 97.5 235t238.5 97t239.5 -97t98.5 -235q0 -139 -98.5 -236.5t-239.5 -97.5t-238.5 97t-97.5 237zM207 1114q0 -96 63.5 -159.5t157.5 -63.5t157.5 64t63.5 159t-63.5 159t-157.5 64t-157.5 -63.5t-63.5 -159.5z" />
<glyph unicode="&#xb1;" horiz-adv-x="1191" d="M137 0v162h918v-162h-918zM137 735v162h373v356h172v-356h373v-162h-373v-356h-172v356h-373z" />
<glyph unicode="&#xb2;" horiz-adv-x="880" d="M57 1393q109 137 344 137q161 0 249.5 -67.5t88.5 -174.5q0 -65 -32.5 -122.5t-124.5 -139.5l-271 -238h467v-118h-684v92l379 332q71 64 97 104t26 80q0 57 -51.5 93t-153.5 36q-153 0 -233 -92z" />
<glyph unicode="&#xb3;" horiz-adv-x="880" d="M55 764l58 108q51 -41 130 -65.5t169 -24.5q112 0 168.5 37t56.5 103q0 65 -54 101t-163 36h-96v96l229 246h-471v119h657v-93l-241 -260q136 -10 209 -76.5t73 -168.5q0 -113 -95.5 -188t-272.5 -75q-110 0 -208 29t-149 76z" />
<glyph unicode="&#xb4;" horiz-adv-x="1228" d="M461 1243l278 266h250l-344 -266h-184z" />
<glyph unicode="&#xb5;" d="M186 -397v1482h197v-602q0 -157 76.5 -238t216.5 -81q156 0 246 94t90 266v561h196v-1085h-186v168q-55 -92 -146.5 -136t-195.5 -44q-196 0 -297 125v-510h-197z" />
<glyph unicode="&#xb6;" horiz-adv-x="1327" d="M39 1153q0 168 124.5 267.5t330.5 99.5h618v-1725h-164v1571h-319v-1571h-164v991q-190 2 -308 101.5t-118 265.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="546" d="M135 563q0 61 40 100t99 39q58 0 97 -39t39 -100q0 -62 -39 -101.5t-97 -39.5q-59 0 -99 39.5t-40 101.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1228" d="M381 -414l43 101q60 -35 139 -35q115 0 115 82q0 37 -29 58.5t-88 21.5h-55l51 200h113l-31 -116q87 -9 132.5 -54.5t45.5 -115.5q0 -86 -69 -136.5t-185 -50.5q-108 0 -182 45z" />
<glyph unicode="&#xb9;" horiz-adv-x="880" d="M166 670v118h237v613h-221v119h367v-732h209v-118h-592z" />
<glyph unicode="&#xba;" horiz-adv-x="856" d="M66 1200q0 145 102 237.5t260 92.5t260.5 -92.5t102.5 -237.5t-102.5 -238.5t-260.5 -93.5t-260 93.5t-102 238.5zM203 1200q0 -94 62.5 -153.5t162.5 -59.5t162.5 59.5t62.5 153.5q0 95 -62.5 154t-162.5 59t-162.5 -59t-62.5 -154z" />
<glyph unicode="&#xbb;" horiz-adv-x="1032" d="M70 152l288 391l-288 393h180l295 -393l-295 -391h-180zM463 152l289 391l-289 393h180l295 -393l-295 -391h-180z" />
<glyph unicode="&#xbc;" horiz-adv-x="2117" d="M166 584v118h237v613h-221v119h367v-732h209v-118h-592zM492 0l978 1434h156l-979 -1434h-155zM1282 203v96l412 551h159l-397 -528h311v170h129v-170h166v-119h-166v-203h-141v203h-473z" />
<glyph unicode="&#xbd;" horiz-adv-x="2117" d="M166 584v118h237v613h-221v119h367v-732h209v-118h-592zM492 0l978 1434h156l-979 -1434h-155zM1294 723q109 137 344 137q161 0 249.5 -67.5t88.5 -174.5q0 -65 -32.5 -122.5t-124.5 -139.5l-271 -237h467v-119h-684v92l379 332q71 64 97 104t26 80q0 57 -51.5 93 t-153.5 36q-153 0 -233 -92z" />
<glyph unicode="&#xbe;" horiz-adv-x="2117" d="M55 678l58 108q51 -41 130 -65.5t169 -24.5q112 0 168.5 37t56.5 103q0 65 -54 101t-163 36h-96v96l229 246h-471v119h657v-93l-241 -260q136 -10 209 -76.5t73 -168.5q0 -113 -95.5 -188t-272.5 -75q-110 0 -208 29t-149 76zM492 0l978 1434h156l-979 -1434h-155z M1282 203v96l412 551h159l-397 -528h311v170h129v-170h166v-119h-166v-203h-141v203h-473z" />
<glyph unicode="&#xbf;" horiz-adv-x="1173" d="M141 18q0 68 20 125.5t51.5 98t70 77t77 70t70 68t52 79.5t20.5 97h205q0 -63 -20 -117.5t-51.5 -94.5t-70.5 -76t-77.5 -69.5t-70 -66.5t-51.5 -75t-20 -89q0 -87 80 -145t213 -58q239 0 367 162l149 -106q-179 -230 -528 -230q-221 0 -353.5 94.5t-132.5 255.5z M471 963q0 56 37.5 94.5t95.5 38.5q57 0 95 -38.5t38 -94.5q0 -54 -38 -91.5t-95 -37.5q-58 0 -95.5 37t-37.5 92z" />
<glyph unicode="&#xc0;" horiz-adv-x="1638" d="M203 0v805q0 312 165.5 478.5t448.5 166.5q286 0 452.5 -166.5t166.5 -478.5v-805h-205v395h-828v-395h-200zM403 573h828v250q0 220 -109.5 332.5t-304.5 112.5t-304.5 -112.5t-109.5 -332.5v-250zM442 1817h250l279 -267h-185z" />
<glyph unicode="&#xc1;" horiz-adv-x="1638" d="M203 0v805q0 312 165.5 478.5t448.5 166.5q286 0 452.5 -166.5t166.5 -478.5v-805h-205v395h-828v-395h-200zM403 573h828v250q0 220 -109.5 332.5t-304.5 112.5t-304.5 -112.5t-109.5 -332.5v-250zM664 1550l278 267h250l-344 -267h-184z" />
<glyph unicode="&#xc2;" horiz-adv-x="1638" d="M203 0v805q0 312 165.5 478.5t448.5 166.5q286 0 452.5 -166.5t166.5 -478.5v-805h-205v395h-828v-395h-200zM403 573h828v250q0 220 -109.5 332.5t-304.5 112.5t-304.5 -112.5t-109.5 -332.5v-250zM451 1550l272 267h188l273 -267h-178l-189 158l-188 -158h-178z" />
<glyph unicode="&#xc3;" horiz-adv-x="1638" d="M203 0v805q0 312 165.5 478.5t448.5 166.5q286 0 452.5 -166.5t166.5 -478.5v-805h-205v395h-828v-395h-200zM403 573h828v250q0 220 -109.5 332.5t-304.5 112.5t-304.5 -112.5t-109.5 -332.5v-250zM473 1567q3 114 57.5 182t143.5 68q44 0 87 -22t70 -48t58.5 -48 t56.5 -22q45 0 73.5 33t31.5 90h110q-3 -110 -57 -177t-143 -67q-44 0 -87 21.5t-70.5 47.5t-59 47.5t-56.5 21.5q-45 0 -73 -34t-31 -93h-111z" />
<glyph unicode="&#xc4;" horiz-adv-x="1638" d="M203 0v805q0 312 165.5 478.5t448.5 166.5q286 0 452.5 -166.5t166.5 -478.5v-805h-205v395h-828v-395h-200zM403 573h828v250q0 220 -109.5 332.5t-304.5 112.5t-304.5 -112.5t-109.5 -332.5v-250zM522 1690q0 45 31 75.5t74 30.5t73.5 -30.5t30.5 -75.5t-30.5 -75 t-73.5 -30t-74 30t-31 75zM903 1690q0 45 31 75.5t74 30.5t73.5 -30.5t30.5 -75.5t-30.5 -75t-73.5 -30t-74 30t-31 75z" />
<glyph unicode="&#xc5;" horiz-adv-x="1638" d="M203 0v805q0 312 165.5 478.5t448.5 166.5q286 0 452.5 -166.5t166.5 -478.5v-805h-205v395h-828v-395h-200zM403 573h828v250q0 220 -109.5 332.5t-304.5 112.5t-304.5 -112.5t-109.5 -332.5v-250zM590 1786q0 93 65 159t160 66q96 0 161.5 -66t65.5 -159t-65.5 -157 t-161.5 -64q-95 0 -160 64t-65 157zM682 1786q0 -58 37.5 -96.5t95.5 -38.5q59 0 97 38.5t38 96.5q0 59 -38.5 98t-96.5 39q-57 0 -95 -38.5t-38 -98.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2199" d="M184 0v805q0 301 156 465t444 164h1307v-174h-813v-449h723v-166h-723v-471h842v-174h-1043v416h-688v-416h-205zM389 588h688v661h-278q-202 0 -306 -113.5t-104 -334.5v-213z" />
<glyph unicode="&#xc7;" horiz-adv-x="1480" d="M98 717q0 157 58 293t158.5 232.5t241.5 152t302 55.5q167 0 307.5 -57t237.5 -166l-133 -129q-161 170 -404 170q-120 0 -224.5 -42t-179 -114.5t-117 -175t-42.5 -219.5t42.5 -219.5t117 -175t179 -114.5t224.5 -42q241 0 404 172l133 -129q-96 -107 -233 -164.5 t-300 -60.5l-22 -86q87 -9 132.5 -54.5t45.5 -115.5q0 -86 -69 -136.5t-185 -50.5q-108 0 -182 45l43 101q60 -35 139 -35q115 0 115 82q0 37 -29 58.5t-88 21.5h-55l45 176q-143 15 -265.5 76t-210 156t-137 223t-49.5 272z" />
<glyph unicode="&#xc8;" horiz-adv-x="1318" d="M104 393q0 123 70.5 212.5t183.5 127.5q-90 42 -141 125t-51 191q0 83 36 154.5t106 127t182.5 87.5t254.5 32q134 0 260 -29.5t218 -81.5l-60 -163q-183 98 -409 98q-187 0 -285 -66.5t-98 -173.5t76 -164t217 -57h368v-176h-377q-162 0 -254 -57t-92 -172 q0 -116 104 -182t312 -66q143 0 273 39.5t214 109.5l70 -159q-93 -78 -242 -122t-325 -44q-294 0 -452.5 112.5t-158.5 296.5zM342 1817h250l278 -267h-184z" />
<glyph unicode="&#xc9;" horiz-adv-x="1318" d="M104 393q0 123 70.5 212.5t183.5 127.5q-90 42 -141 125t-51 191q0 83 36 154.5t106 127t182.5 87.5t254.5 32q134 0 260 -29.5t218 -81.5l-60 -163q-183 98 -409 98q-187 0 -285 -66.5t-98 -173.5t76 -164t217 -57h368v-176h-377q-162 0 -254 -57t-92 -172 q0 -116 104 -182t312 -66q143 0 273 39.5t214 109.5l70 -159q-93 -78 -242 -122t-325 -44q-294 0 -452.5 112.5t-158.5 296.5zM563 1550l279 267h250l-344 -267h-185z" />
<glyph unicode="&#xca;" horiz-adv-x="1318" d="M104 393q0 123 70.5 212.5t183.5 127.5q-90 42 -141 125t-51 191q0 83 36 154.5t106 127t182.5 87.5t254.5 32q134 0 260 -29.5t218 -81.5l-60 -163q-183 98 -409 98q-187 0 -285 -66.5t-98 -173.5t76 -164t217 -57h368v-176h-377q-162 0 -254 -57t-92 -172 q0 -116 104 -182t312 -66q143 0 273 39.5t214 109.5l70 -159q-93 -78 -242 -122t-325 -44q-294 0 -452.5 112.5t-158.5 296.5zM350 1550l273 267h188l272 -267h-178l-188 158l-189 -158h-178z" />
<glyph unicode="&#xcb;" horiz-adv-x="1318" d="M104 393q0 123 70.5 212.5t183.5 127.5q-90 42 -141 125t-51 191q0 83 36 154.5t106 127t182.5 87.5t254.5 32q134 0 260 -29.5t218 -81.5l-60 -163q-183 98 -409 98q-187 0 -285 -66.5t-98 -173.5t76 -164t217 -57h368v-176h-377q-162 0 -254 -57t-92 -172 q0 -116 104 -182t312 -66q143 0 273 39.5t214 109.5l70 -159q-93 -78 -242 -122t-325 -44q-294 0 -452.5 112.5t-158.5 296.5zM422 1690q0 45 30.5 75.5t73.5 30.5t74 -30.5t31 -75.5t-31 -75t-74 -30t-73.5 30t-30.5 75zM803 1690q0 45 30.5 75.5t73.5 30.5t74 -30.5 t31 -75.5t-31 -75t-74 -30t-73.5 30t-30.5 75z" />
<glyph unicode="&#xcc;" horiz-adv-x="933" d="M88 0v178h277v1077h-277v179h758v-179h-277v-1077h277v-178h-758zM94 1817h250l279 -267h-185z" />
<glyph unicode="&#xcd;" horiz-adv-x="933" d="M88 0v178h277v1077h-277v179h758v-179h-277v-1077h277v-178h-758zM315 1550l279 267h250l-344 -267h-185z" />
<glyph unicode="&#xce;" horiz-adv-x="933" d="M88 0v178h277v1077h-277v179h758v-179h-277v-1077h277v-178h-758zM102 1550l273 267h188l273 -267h-179l-188 158l-188 -158h-179z" />
<glyph unicode="&#xcf;" horiz-adv-x="933" d="M88 0v178h277v1077h-277v179h758v-179h-277v-1077h277v-178h-758zM174 1690q0 45 31 75.5t74 30.5t73.5 -30.5t30.5 -75.5t-30.5 -75t-73.5 -30t-74 30t-31 75zM555 1690q0 45 30.5 75.5t73.5 30.5t74 -30.5t31 -75.5t-31 -75t-74 -30t-73.5 30t-30.5 75z" />
<glyph unicode="&#xd0;" horiz-adv-x="1708" d="M23 649v162h208v623h605q228 0 404 -90t273 -253t97 -374t-97 -374t-273 -253t-404 -90h-605v649h-208zM436 178h387q264 0 423 148t159 391q0 242 -159 390t-423 148h-387v-444h406v-162h-406v-471z" />
<glyph unicode="&#xd1;" horiz-adv-x="1634" d="M215 0v1434h197v-203q144 219 471 219q258 0 403.5 -155.5t145.5 -444.5v-850h-203v844q0 208 -102.5 316t-284.5 108q-197 0 -309.5 -115t-112.5 -340v-813h-205zM479 1567q3 114 57.5 182t143.5 68q44 0 87 -22t70 -48t58.5 -48t56.5 -22q45 0 73.5 33t31.5 90h110 q-3 -110 -57 -177t-143 -67q-44 0 -87 21.5t-70.5 47.5t-59 47.5t-56.5 21.5q-45 0 -73 -34t-31 -93h-111z" />
<glyph unicode="&#xd2;" horiz-adv-x="1720" d="M98 717q0 155 58 291t158.5 233t243 153t304.5 56q215 0 389 -94.5t272.5 -262.5t98.5 -376t-98.5 -376t-272.5 -262.5t-389 -94.5q-162 0 -304.5 56t-243 153t-158.5 233t-58 291zM303 717q0 -238 159 -394.5t400 -156.5q238 0 396.5 157t158.5 394t-158.5 394 t-396.5 157q-241 0 -400 -156.5t-159 -394.5zM487 1817h250l279 -267h-185z" />
<glyph unicode="&#xd3;" horiz-adv-x="1720" d="M98 717q0 155 58 291t158.5 233t243 153t304.5 56q215 0 389 -94.5t272.5 -262.5t98.5 -376t-98.5 -376t-272.5 -262.5t-389 -94.5q-162 0 -304.5 56t-243 153t-158.5 233t-58 291zM303 717q0 -238 159 -394.5t400 -156.5q238 0 396.5 157t158.5 394t-158.5 394 t-396.5 157q-241 0 -400 -156.5t-159 -394.5zM709 1550l278 267h250l-344 -267h-184z" />
<glyph unicode="&#xd4;" horiz-adv-x="1720" d="M98 717q0 155 58 291t158.5 233t243 153t304.5 56q215 0 389 -94.5t272.5 -262.5t98.5 -376t-98.5 -376t-272.5 -262.5t-389 -94.5q-162 0 -304.5 56t-243 153t-158.5 233t-58 291zM303 717q0 -238 159 -394.5t400 -156.5q238 0 396.5 157t158.5 394t-158.5 394 t-396.5 157q-241 0 -400 -156.5t-159 -394.5zM496 1550l272 267h188l273 -267h-178l-189 158l-188 -158h-178z" />
<glyph unicode="&#xd5;" horiz-adv-x="1720" d="M98 717q0 155 58 291t158.5 233t243 153t304.5 56q215 0 389 -94.5t272.5 -262.5t98.5 -376t-98.5 -376t-272.5 -262.5t-389 -94.5q-162 0 -304.5 56t-243 153t-158.5 233t-58 291zM303 717q0 -238 159 -394.5t400 -156.5q238 0 396.5 157t158.5 394t-158.5 394 t-396.5 157q-241 0 -400 -156.5t-159 -394.5zM518 1567q3 114 57.5 182t143.5 68q44 0 87 -22t70 -48t58.5 -48t56.5 -22q45 0 73.5 33t31.5 90h110q-3 -110 -57 -177t-143 -67q-44 0 -87 21.5t-70.5 47.5t-59 47.5t-56.5 21.5q-45 0 -73 -34t-31 -93h-111z" />
<glyph unicode="&#xd6;" horiz-adv-x="1720" d="M98 717q0 155 58 291t158.5 233t243 153t304.5 56q215 0 389 -94.5t272.5 -262.5t98.5 -376t-98.5 -376t-272.5 -262.5t-389 -94.5q-162 0 -304.5 56t-243 153t-158.5 233t-58 291zM303 717q0 -238 159 -394.5t400 -156.5q238 0 396.5 157t158.5 394t-158.5 394 t-396.5 157q-241 0 -400 -156.5t-159 -394.5zM567 1690q0 45 31 75.5t74 30.5t73.5 -30.5t30.5 -75.5t-30.5 -75t-73.5 -30t-74 30t-31 75zM948 1690q0 45 31 75.5t74 30.5t73.5 -30.5t30.5 -75.5t-30.5 -75t-73.5 -30t-74 30t-31 75z" />
<glyph unicode="&#xd7;" horiz-adv-x="1191" d="M219 451l264 266l-264 266l109 119l268 -271l268 271l111 -119l-264 -266l264 -266l-111 -119l-268 270l-268 -270z" />
<glyph unicode="&#xd8;" horiz-adv-x="1720" d="M98 717q0 155 58 291t158.5 233t243 153t304.5 56q206 0 381 -90l156 217h160l-207 -289q128 -100 199 -247.5t71 -323.5q0 -208 -98.5 -376t-272.5 -262.5t-389 -94.5q-206 0 -381 90l-155 -217h-160l205 286q-129 102 -201 250.5t-72 323.5zM303 717 q0 -127 47.5 -234.5t134.5 -181.5l645 903q-122 64 -268 64q-241 0 -400 -156.5t-159 -394.5zM592 229q123 -63 270 -63q238 0 396.5 157t158.5 394q0 126 -46.5 232t-131.5 181z" />
<glyph unicode="&#xd9;" horiz-adv-x="1626" d="M203 584v850h205v-844q0 -208 102 -316t281 -108q193 0 305 115t112 340v813h203v-1434h-197v203q-144 -219 -466 -219q-255 0 -400 156t-145 444zM428 1817h250l278 -267h-184z" />
<glyph unicode="&#xda;" horiz-adv-x="1626" d="M203 584v850h205v-844q0 -208 102 -316t281 -108q193 0 305 115t112 340v813h203v-1434h-197v203q-144 -219 -466 -219q-255 0 -400 156t-145 444zM649 1550l279 267h250l-344 -267h-185z" />
<glyph unicode="&#xdb;" horiz-adv-x="1626" d="M203 584v850h205v-844q0 -208 102 -316t281 -108q193 0 305 115t112 340v813h203v-1434h-197v203q-144 -219 -466 -219q-255 0 -400 156t-145 444zM436 1550l273 267h188l272 -267h-178l-188 158l-189 -158h-178z" />
<glyph unicode="&#xdc;" horiz-adv-x="1626" d="M203 584v850h205v-844q0 -208 102 -316t281 -108q193 0 305 115t112 340v813h203v-1434h-197v203q-144 -219 -466 -219q-255 0 -400 156t-145 444zM508 1690q0 45 30.5 75.5t73.5 30.5t74 -30.5t31 -75.5t-31 -75t-74 -30t-73.5 30t-30.5 75zM889 1690q0 45 30.5 75.5 t73.5 30.5t74 -30.5t31 -75.5t-31 -75t-74 -30t-73.5 30t-30.5 75z" />
<glyph unicode="&#xdd;" horiz-adv-x="1611" d="M193 954v480h204v-471q0 -210 102.5 -318t284.5 -108q196 0 309 115t113 341v441h203v-1008q0 -312 -166 -477.5t-459 -165.5q-177 0 -321 54.5t-242 154.5l88 160q188 -185 475 -185q422 0 422 451v145q-144 -209 -444 -209q-269 0 -419 155.5t-150 444.5zM647 1550 l279 267h250l-345 -267h-184z" />
<glyph unicode="&#xde;" horiz-adv-x="1478" d="M215 0v1434h205v-177h354q279 0 438.5 -133.5t159.5 -367.5q0 -233 -159 -366.5t-439 -133.5h-354v-256h-205zM420 432h348q195 0 297 84.5t102 239.5t-102 239t-297 84h-348v-647z" />
<glyph unicode="&#xdf;" horiz-adv-x="1384" d="M186 0v1012q0 126 38.5 226t108 163.5t162.5 97t205 33.5q217 0 346.5 -115t129.5 -291q0 -110 -51.5 -194t-135.5 -135q140 -37 223.5 -136t83.5 -247q0 -197 -137 -311.5t-356 -114.5q-148 0 -236 32l31 166q79 -26 197 -26q140 0 222.5 67.5t82.5 190.5t-89 190.5 t-239 67.5h-117v166q153 1 241.5 76t88.5 200q0 111 -75.5 176.5t-209.5 65.5q-150 0 -233.5 -85t-83.5 -247v-1028h-197z" />
<glyph unicode="&#xe0;" d="M86 543q0 247 154.5 400t394.5 153q245 0 379 -174v163h196v-1085h-188v172q-65 -91 -165 -137.5t-222 -46.5q-240 0 -394.5 154t-154.5 401zM285 543q0 -171 103.5 -277t262.5 -106q158 0 261.5 106t103.5 277t-103.5 276t-261.5 105q-159 0 -262.5 -105t-103.5 -276z M311 1509h250l279 -266h-185z" />
<glyph unicode="&#xe1;" d="M86 543q0 247 154.5 400t394.5 153q245 0 379 -174v163h196v-1085h-188v172q-65 -91 -165 -137.5t-222 -46.5q-240 0 -394.5 154t-154.5 401zM285 543q0 -171 103.5 -277t262.5 -106q158 0 261.5 106t103.5 277t-103.5 276t-261.5 105q-159 0 -262.5 -105t-103.5 -276z M532 1243l279 266h250l-344 -266h-185z" />
<glyph unicode="&#xe2;" d="M86 543q0 247 154.5 400t394.5 153q245 0 379 -174v163h196v-1085h-188v172q-65 -91 -165 -137.5t-222 -46.5q-240 0 -394.5 154t-154.5 401zM285 543q0 -171 103.5 -277t262.5 -106q158 0 261.5 106t103.5 277t-103.5 276t-261.5 105q-159 0 -262.5 -105t-103.5 -276z M319 1243l273 266h188l273 -266h-179l-188 158l-188 -158h-179z" />
<glyph unicode="&#xe3;" d="M86 543q0 247 154.5 400t394.5 153q245 0 379 -174v163h196v-1085h-188v172q-65 -91 -165 -137.5t-222 -46.5q-240 0 -394.5 154t-154.5 401zM285 543q0 -171 103.5 -277t262.5 -106q158 0 261.5 106t103.5 277t-103.5 276t-261.5 105q-159 0 -262.5 -105t-103.5 -276z M342 1260q3 114 57.5 181.5t143.5 67.5q36 0 70.5 -14.5t60.5 -35t49.5 -40.5t47 -34.5t44.5 -14.5q45 0 73.5 33t31.5 90h110q-3 -110 -57.5 -177t-143.5 -67q-44 0 -87 21.5t-70 47t-58.5 47t-56.5 21.5q-45 0 -73 -33.5t-31 -92.5h-111z" />
<glyph unicode="&#xe4;" d="M86 543q0 247 154.5 400t394.5 153q245 0 379 -174v163h196v-1085h-188v172q-65 -91 -165 -137.5t-222 -46.5q-240 0 -394.5 154t-154.5 401zM285 543q0 -171 103.5 -277t262.5 -106q158 0 261.5 106t103.5 277t-103.5 276t-261.5 105q-159 0 -262.5 -105t-103.5 -276z M391 1382q0 45 31 76t74 31t73.5 -31t30.5 -76t-30.5 -74.5t-73.5 -29.5q-44 0 -74.5 29.5t-30.5 74.5zM772 1382q0 45 31 76t74 31t73.5 -31t30.5 -76t-30.5 -74.5t-73.5 -29.5q-44 0 -74.5 29.5t-30.5 74.5z" />
<glyph unicode="&#xe5;" d="M86 543q0 247 154.5 400t394.5 153q245 0 379 -174v163h196v-1085h-188v172q-65 -91 -165 -137.5t-222 -46.5q-240 0 -394.5 154t-154.5 401zM285 543q0 -171 103.5 -277t262.5 -106q158 0 261.5 106t103.5 277t-103.5 276t-261.5 105q-159 0 -262.5 -105t-103.5 -276z M459 1438q0 93 65 159t160 66q96 0 161.5 -66t65.5 -159t-65.5 -157t-161.5 -64q-95 0 -160 64t-65 157zM551 1438q0 -58 37.5 -96.5t95.5 -38.5q59 0 97 38.5t38 96.5q0 59 -38.5 98t-96.5 39q-57 0 -95 -38.5t-38 -98.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="2025" d="M102 315q0 64 23.5 117.5t73 97.5t136.5 68.5t204 24.5h311v41q0 126 -73 194t-216 68q-98 0 -190.5 -31.5t-155.5 -85.5l-82 147q84 67 201.5 103.5t245.5 36.5q300 0 403 -193q73 90 184.5 141.5t241.5 51.5q225 0 374.5 -147.5t155.5 -389.5l-866 -168 q42 -106 142.5 -168.5t234.5 -62.5q184 0 309 131l109 -127q-150 -176 -422 -176q-151 0 -277.5 56.5t-207.5 168.5q-62 -118 -178.5 -171.5t-258.5 -53.5q-196 0 -309 89.5t-113 237.5zM297 319q0 -86 64.5 -133t179.5 -47q143 0 226 74.5t83 198.5v69h-303 q-250 0 -250 -162zM1047 532l694 134q-27 118 -116.5 191t-217.5 73q-158 0 -259 -102.5t-101 -262.5v-33z" />
<glyph unicode="&#xe7;" horiz-adv-x="1169" d="M86 543q0 243 161 398t412 155q148 0 263.5 -59.5t179.5 -172.5l-150 -96q-103 156 -295 156q-162 0 -267 -105t-105 -276q0 -174 104.5 -278.5t267.5 -104.5q193 0 295 155l150 -94q-64 -113 -178 -173t-260 -60l-25 -90q87 -9 132.5 -54.5t45.5 -115.5 q0 -86 -69 -136.5t-185 -50.5q-108 0 -182 45l43 101q60 -35 139 -35q115 0 115 82q0 37 -29 58.5t-88 21.5h-55l47 182q-209 34 -338 183t-129 364z" />
<glyph unicode="&#xe8;" horiz-adv-x="1253" d="M86 543q0 241 154 397t391 156q148 0 268 -65t191.5 -187t74.5 -281l-862 -168q41 -112 138.5 -173.5t234.5 -61.5q194 0 313 131l107 -127q-72 -86 -181.5 -131t-244.5 -45q-261 0 -422.5 155t-161.5 400zM248 1509h250l278 -266h-184zM276 532l695 132 q-27 119 -118.5 192.5t-221.5 73.5q-156 0 -255.5 -104t-99.5 -273v-21z" />
<glyph unicode="&#xe9;" horiz-adv-x="1253" d="M86 543q0 241 154 397t391 156q148 0 268 -65t191.5 -187t74.5 -281l-862 -168q41 -112 138.5 -173.5t234.5 -61.5q194 0 313 131l107 -127q-72 -86 -181.5 -131t-244.5 -45q-261 0 -422.5 155t-161.5 400zM276 532l695 132q-27 119 -118.5 192.5t-221.5 73.5 q-156 0 -255.5 -104t-99.5 -273v-21zM469 1243l279 266h249l-344 -266h-184z" />
<glyph unicode="&#xea;" horiz-adv-x="1253" d="M86 543q0 241 154 397t391 156q148 0 268 -65t191.5 -187t74.5 -281l-862 -168q41 -112 138.5 -173.5t234.5 -61.5q194 0 313 131l107 -127q-72 -86 -181.5 -131t-244.5 -45q-261 0 -422.5 155t-161.5 400zM256 1243l272 266h189l272 -266h-178l-188 158l-189 -158h-178z M276 532l695 132q-27 119 -118.5 192.5t-221.5 73.5q-156 0 -255.5 -104t-99.5 -273v-21z" />
<glyph unicode="&#xeb;" horiz-adv-x="1253" d="M86 543q0 241 154 397t391 156q148 0 268 -65t191.5 -187t74.5 -281l-862 -168q41 -112 138.5 -173.5t234.5 -61.5q194 0 313 131l107 -127q-72 -86 -181.5 -131t-244.5 -45q-261 0 -422.5 155t-161.5 400zM276 532l695 132q-27 119 -118.5 192.5t-221.5 73.5 q-156 0 -255.5 -104t-99.5 -273v-21zM328 1382q0 45 30.5 76t73.5 31t74 -31t31 -76t-30.5 -74.5t-74.5 -29.5q-43 0 -73.5 29.5t-30.5 74.5zM709 1382q0 45 30.5 76t73.5 31t74 -31t31 -76t-30.5 -74.5t-74.5 -29.5q-43 0 -73.5 29.5t-30.5 74.5z" />
<glyph unicode="&#xec;" horiz-adv-x="571" d="M-90 1509h250l278 -266h-184zM186 0v1085h197v-1085h-197z" />
<glyph unicode="&#xed;" horiz-adv-x="571" d="M131 1243l279 266h249l-344 -266h-184zM186 0v1085h197v-1085h-197z" />
<glyph unicode="&#xee;" horiz-adv-x="571" d="M-31 1243l224 266h184l223 -266h-168l-147 152l-148 -152h-168zM186 0v1085h197v-1085h-197z" />
<glyph unicode="&#xef;" horiz-adv-x="571" d="M37 1382q0 45 29 75t73 30q43 0 72 -30t29 -75q0 -44 -29 -73t-72 -29q-44 0 -73 29.5t-29 72.5zM186 0v1085h197v-1085h-197zM330 1382q0 44 28.5 74.5t71.5 30.5q44 0 73 -29.5t29 -75.5q0 -44 -29 -73t-73 -29q-43 0 -71.5 29.5t-28.5 72.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1310" d="M86 442q0 206 147 331.5t375 125.5q138 0 246.5 -57t169.5 -162q2 25 2 70q0 304 -164 452l-547 -219l-53 129l445 180q-92 29 -193 29q-161 0 -297 -47l-33 168q147 45 330 45q240 0 408 -109l182 74l53 -131l-115 -45q183 -205 183 -569q0 -336 -164 -529.5 t-459 -193.5q-146 0 -262.5 55t-185 160.5t-68.5 242.5zM285 442q0 -132 91.5 -212t239.5 -80q161 0 257 86t96 206q0 125 -96 208t-244 83q-160 0 -252 -79.5t-92 -211.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1394" d="M186 0v1085h189v-163q60 84 161 129t230 45q206 0 328.5 -119.5t122.5 -351.5v-625h-197v602q0 158 -76 238t-217 80q-160 0 -252 -93.5t-92 -267.5v-559h-197zM358 1260q3 114 57.5 181.5t143.5 67.5q36 0 70.5 -14.5t60.5 -35t49.5 -40.5t47 -34.5t44.5 -14.5 q45 0 73.5 33t31.5 90h111q-3 -110 -57.5 -177t-143.5 -67q-44 0 -87 21.5t-70.5 47t-59 47t-56.5 21.5q-45 0 -73 -33.5t-31 -92.5h-111z" />
<glyph unicode="&#xf2;" horiz-adv-x="1300" d="M86 543q0 242 160 397.5t405 155.5t404 -155.5t159 -397.5q0 -160 -72.5 -287t-201 -197.5t-289.5 -70.5q-245 0 -405 156.5t-160 398.5zM274 1509h250l279 -266h-185zM285 543q0 -171 103.5 -277t262.5 -106t262 106t103 277t-103 276t-262 105t-262.5 -105t-103.5 -276 z" />
<glyph unicode="&#xf3;" horiz-adv-x="1300" d="M86 543q0 242 160 397.5t405 155.5t404 -155.5t159 -397.5q0 -160 -72.5 -287t-201 -197.5t-289.5 -70.5q-245 0 -405 156.5t-160 398.5zM285 543q0 -171 103.5 -277t262.5 -106t262 106t103 277t-103 276t-262 105t-262.5 -105t-103.5 -276zM496 1243l278 266h250 l-344 -266h-184z" />
<glyph unicode="&#xf4;" horiz-adv-x="1300" d="M86 543q0 242 160 397.5t405 155.5t404 -155.5t159 -397.5q0 -160 -72.5 -287t-201 -197.5t-289.5 -70.5q-245 0 -405 156.5t-160 398.5zM283 1243l272 266h188l273 -266h-178l-189 158l-188 -158h-178zM285 543q0 -171 103.5 -277t262.5 -106t262 106t103 277t-103 276 t-262 105t-262.5 -105t-103.5 -276z" />
<glyph unicode="&#xf5;" horiz-adv-x="1300" d="M86 543q0 242 160 397.5t405 155.5t404 -155.5t159 -397.5q0 -160 -72.5 -287t-201 -197.5t-289.5 -70.5q-245 0 -405 156.5t-160 398.5zM285 543q0 -171 103.5 -277t262.5 -106t262 106t103 277t-103 276t-262 105t-262.5 -105t-103.5 -276zM305 1260q3 114 57.5 181.5 t143.5 67.5q36 0 70.5 -14.5t60.5 -35t49.5 -40.5t47 -34.5t44.5 -14.5q45 0 73.5 33t31.5 90h110q-3 -110 -57 -177t-143 -67q-44 0 -87 21.5t-70.5 47t-59 47t-56.5 21.5q-45 0 -73 -33.5t-31 -92.5h-111z" />
<glyph unicode="&#xf6;" horiz-adv-x="1300" d="M86 543q0 242 160 397.5t405 155.5t404 -155.5t159 -397.5q0 -160 -72.5 -287t-201 -197.5t-289.5 -70.5q-245 0 -405 156.5t-160 398.5zM285 543q0 -171 103.5 -277t262.5 -106t262 106t103 277t-103 276t-262 105t-262.5 -105t-103.5 -276zM354 1382q0 45 31 76t74 31 t73.5 -31t30.5 -76t-30.5 -74.5t-73.5 -29.5q-44 0 -74.5 29.5t-30.5 74.5zM735 1382q0 45 31 76t74 31t73.5 -31t30.5 -76t-30.5 -74.5t-73.5 -29.5q-44 0 -74.5 29.5t-30.5 74.5z" />
<glyph unicode="&#xf7;" horiz-adv-x="1191" d="M137 635v162h918v-162h-918zM479 315q0 52 33.5 86.5t83.5 34.5q51 0 85 -34.5t34 -86.5q0 -54 -34 -89.5t-85 -35.5q-50 0 -83.5 35.5t-33.5 89.5zM479 1120q0 54 33.5 88.5t83.5 34.5q51 0 85 -34.5t34 -88.5q0 -53 -34 -88t-85 -35q-50 0 -83.5 35.5t-33.5 87.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1300" d="M86 543q0 242 160 397.5t405 155.5q147 0 267 -60l122 189h121l-157 -240q100 -75 155 -189t55 -253q0 -160 -72.5 -287t-201 -197.5t-289.5 -70.5q-146 0 -264 59l-127 -192h-123l162 245q-101 74 -157 189t-56 254zM285 543q0 -182 116 -289l416 633q-73 37 -166 37 q-159 0 -262.5 -105t-103.5 -276zM485 197q73 -37 166 -37q159 0 262 106t103 277q0 181 -115 286z" />
<glyph unicode="&#xf9;" horiz-adv-x="1386" d="M176 461v624h197v-602q0 -158 76 -238.5t217 -80.5q156 0 245.5 94t89.5 266v561h197v-1085h-186v164q-60 -84 -156.5 -130t-212.5 -46q-217 0 -342 120t-125 353zM313 1509h250l279 -266h-185z" />
<glyph unicode="&#xfa;" horiz-adv-x="1386" d="M176 461v624h197v-602q0 -158 76 -238.5t217 -80.5q156 0 245.5 94t89.5 266v561h197v-1085h-186v164q-60 -84 -156.5 -130t-212.5 -46q-217 0 -342 120t-125 353zM535 1243l278 266h250l-344 -266h-184z" />
<glyph unicode="&#xfb;" horiz-adv-x="1386" d="M176 461v624h197v-602q0 -158 76 -238.5t217 -80.5q156 0 245.5 94t89.5 266v561h197v-1085h-186v164q-60 -84 -156.5 -130t-212.5 -46q-217 0 -342 120t-125 353zM322 1243l272 266h188l273 -266h-178l-189 158l-188 -158h-178z" />
<glyph unicode="&#xfc;" horiz-adv-x="1386" d="M176 461v624h197v-602q0 -158 76 -238.5t217 -80.5q156 0 245.5 94t89.5 266v561h197v-1085h-186v164q-60 -84 -156.5 -130t-212.5 -46q-217 0 -342 120t-125 353zM393 1382q0 45 31 76t74 31t73.5 -31t30.5 -76t-30.5 -74.5t-73.5 -29.5q-44 0 -74.5 29.5t-30.5 74.5z M774 1382q0 45 31 76t74 31t73.5 -31t30.5 -76t-30.5 -74.5t-73.5 -29.5q-44 0 -74.5 29.5t-30.5 74.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="1386" d="M172 -248l96 152q72 -65 173 -102.5t214 -37.5q179 0 262.5 88.5t83.5 274.5v80q-60 -78 -153.5 -120t-204.5 -42q-217 0 -342 119t-125 352v569h197v-546q0 -157 76 -237.5t217 -80.5q156 0 245.5 94t89.5 267v503h197v-938q0 -284 -133 -420.5t-399 -136.5 q-145 0 -277.5 42t-216.5 120zM535 1243l278 266h250l-344 -266h-184z" />
<glyph unicode="&#xfe;" d="M186 -397v1917h197v-596q132 172 379 172q240 0 394.5 -153t154.5 -400q0 -248 -154.5 -401.5t-394.5 -153.5q-117 0 -214.5 44.5t-164.5 131.5v-561h-197zM381 543q0 -171 103.5 -277t260.5 -106q159 0 263 106t104 277q0 170 -104 275.5t-263 105.5 q-157 0 -260.5 -105.5t-103.5 -275.5z" />
<glyph unicode="&#xff;" horiz-adv-x="1386" d="M172 -248l96 152q72 -65 173 -102.5t214 -37.5q179 0 262.5 88.5t83.5 274.5v80q-60 -78 -153.5 -120t-204.5 -42q-217 0 -342 119t-125 352v569h197v-546q0 -157 76 -237.5t217 -80.5q156 0 245.5 94t89.5 267v503h197v-938q0 -284 -133 -420.5t-399 -136.5 q-145 0 -277.5 42t-216.5 120zM393 1382q0 45 31 76t74 31t73.5 -31t30.5 -76t-30.5 -74.5t-73.5 -29.5q-44 0 -74.5 29.5t-30.5 74.5zM774 1382q0 45 31 76t74 31t73.5 -31t30.5 -76t-30.5 -74.5t-73.5 -29.5q-44 0 -74.5 29.5t-30.5 74.5z" />
<glyph unicode="&#x152;" horiz-adv-x="2306" d="M98 715q0 212 97 375.5t273 253.5t404 90h1289v-179h-809v-440h721v-174h-721v-463h837v-178h-1317q-345 0 -559.5 198.5t-214.5 516.5zM303 715q0 -240 158.5 -388.5t421.5 -148.5h266v1077h-266q-263 0 -421.5 -148t-158.5 -392z" />
<glyph unicode="&#x153;" horiz-adv-x="2215" d="M86 543q0 242 160 397.5t405 155.5q153 0 272 -64t189 -178q72 114 192.5 178t274.5 64q152 0 276 -65t198 -188.5t77 -283.5l-891 -168q42 -107 143.5 -169t239.5 -62q201 0 326 131l110 -127q-75 -86 -188 -131t-252 -45q-170 0 -300.5 64t-203.5 179 q-70 -114 -190 -178.5t-273 -64.5q-245 0 -405 156.5t-160 398.5zM285 543q0 -171 103.5 -277t262.5 -106t262 106t103 277t-103 276t-262 105t-262.5 -105t-103.5 -276zM1212 532l715 134q-27 118 -120.5 191t-227.5 73q-161 0 -264 -102.5t-103 -262.5v-33z" />
<glyph unicode="&#x178;" horiz-adv-x="1611" d="M193 954v480h204v-471q0 -210 102.5 -318t284.5 -108q196 0 309 115t113 341v441h203v-1008q0 -312 -166 -477.5t-459 -165.5q-177 0 -321 54.5t-242 154.5l88 160q188 -185 475 -185q422 0 422 451v145q-144 -209 -444 -209q-269 0 -419 155.5t-150 444.5zM506 1690 q0 45 30.5 75.5t73.5 30.5t74 -30.5t31 -75.5t-31 -75t-74 -30t-73.5 30t-30.5 75zM887 1690q0 45 30.5 75.5t73.5 30.5t74 -30.5t31 -75.5t-31 -75t-74 -30t-73.5 30t-30.5 75z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1228" d="M248 1243l272 266h189l272 -266h-178l-189 158l-188 -158h-178z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1228" d="M270 1260q3 114 57.5 181.5t143.5 67.5q36 0 70.5 -14.5t60.5 -35t49.5 -40.5t47 -34.5t44.5 -14.5q45 0 73.5 33t31.5 90h110q-3 -110 -57 -177t-143 -67q-44 0 -87 21.5t-70.5 47t-59 47t-56.5 21.5q-45 0 -73 -33.5t-31 -92.5h-111z" />
<glyph unicode="&#x2000;" horiz-adv-x="1005" />
<glyph unicode="&#x2001;" horiz-adv-x="2011" />
<glyph unicode="&#x2002;" horiz-adv-x="1005" />
<glyph unicode="&#x2003;" horiz-adv-x="2011" />
<glyph unicode="&#x2004;" horiz-adv-x="670" />
<glyph unicode="&#x2005;" horiz-adv-x="502" />
<glyph unicode="&#x2006;" horiz-adv-x="335" />
<glyph unicode="&#x2007;" horiz-adv-x="335" />
<glyph unicode="&#x2008;" horiz-adv-x="251" />
<glyph unicode="&#x2009;" horiz-adv-x="402" />
<glyph unicode="&#x200a;" horiz-adv-x="111" />
<glyph unicode="&#x2010;" horiz-adv-x="784" d="M117 477v170h551v-170h-551z" />
<glyph unicode="&#x2011;" horiz-adv-x="784" d="M117 477v170h551v-170h-551z" />
<glyph unicode="&#x2012;" horiz-adv-x="784" d="M117 477v170h551v-170h-551z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 494v137h1024v-137h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 494v137h2048v-137h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="464" d="M94 1087q0 49 37 146l96 287h129l-75 -306q40 -14 63 -47t23 -80q0 -61 -39 -100t-97 -39q-60 0 -98.5 39.5t-38.5 99.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="464" d="M98 1393q0 61 39.5 100t98.5 39q58 0 95.5 -39.5t37.5 -99.5q0 -53 -33 -146l-98 -286h-129l75 305q-39 14 -62.5 48t-23.5 79z" />
<glyph unicode="&#x201a;" horiz-adv-x="464" d="M98 129q0 61 39.5 100t98.5 39q58 0 95.5 -39.5t37.5 -99.5q0 -48 -35 -147l-96 -285h-129l75 305q-39 14 -62.5 48t-23.5 79z" />
<glyph unicode="&#x201c;" horiz-adv-x="851" d="M94 1087q0 49 37 146l96 287h129l-75 -306q40 -14 63 -47t23 -80q0 -61 -39 -100t-97 -39q-60 0 -98.5 39.5t-38.5 99.5zM481 1087q0 49 37 146l96 287h129l-75 -306q40 -14 63 -47t23 -80q0 -61 -39 -100t-97 -39q-60 0 -98.5 39.5t-38.5 99.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="851" d="M98 1393q0 61 39.5 100t98.5 39q58 0 95.5 -39.5t37.5 -99.5q0 -53 -33 -146l-98 -286h-129l75 305q-39 14 -62.5 48t-23.5 79zM485 1393q0 61 39.5 100t98.5 39q58 0 95.5 -39.5t37.5 -99.5q0 -53 -33 -146l-98 -286h-129l75 305q-39 14 -62.5 48t-23.5 79z" />
<glyph unicode="&#x201e;" horiz-adv-x="851" d="M98 129q0 61 39.5 100t98.5 39q58 0 95.5 -39.5t37.5 -99.5q0 -48 -35 -147l-96 -285h-129l75 305q-39 14 -62.5 48t-23.5 79zM485 129q0 61 39.5 100t98.5 39q58 0 95.5 -39.5t37.5 -99.5q0 -48 -35 -147l-96 -285h-129l75 305q-39 14 -62.5 48t-23.5 79z" />
<glyph unicode="&#x2022;" horiz-adv-x="643" d="M135 565q0 78 55.5 132.5t131.5 54.5q75 0 129.5 -54.5t54.5 -132.5q0 -81 -54.5 -134.5t-129.5 -53.5q-77 0 -132 53.5t-55 134.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1415" d="M94 129q0 60 40.5 99.5t98.5 39.5q57 0 96.5 -39.5t39.5 -99.5q0 -61 -40 -101t-96 -40q-58 0 -98.5 40t-40.5 101zM569 129q0 60 41 99.5t99 39.5q57 0 96 -39t39 -100t-39.5 -101t-95.5 -40q-58 0 -99 40t-41 101zM1044 129q0 60 41 99.5t99 39.5q56 0 95.5 -39 t39.5 -100t-39.5 -101t-95.5 -40q-58 0 -99 40t-41 101z" />
<glyph unicode="&#x202f;" horiz-adv-x="402" />
<glyph unicode="&#x2039;" horiz-adv-x="638" d="M94 543l295 393h180l-288 -393l288 -391h-180z" />
<glyph unicode="&#x203a;" horiz-adv-x="638" d="M70 152l288 391l-288 393h180l295 -393l-295 -391h-180z" />
<glyph unicode="&#x205f;" horiz-adv-x="502" />
<glyph unicode="&#x20ac;" horiz-adv-x="1652" d="M61 518v115h213q-4 54 -4 84t4 84h-213v114h234q65 241 265 388t468 147q169 0 310 -57t237 -166l-131 -129q-161 170 -406 170q-187 0 -327.5 -96.5t-198.5 -256.5h649v-114h-678q-6 -41 -6 -84t6 -84h678v-115h-649q58 -160 198.5 -256t327.5 -96q243 0 406 172 l131 -129q-96 -110 -237.5 -167.5t-309.5 -57.5q-269 0 -468.5 146.5t-264.5 387.5h-234z" />
<glyph unicode="&#x2122;" horiz-adv-x="2107" d="M8 1313v121h766v-121h-309v-729h-148v729h-309zM899 584v850h121l379 -582l370 582h121l2 -850h-141l-2 596l-319 -496h-68l-322 485v-585h-141z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1085" d="M0 0v1085h1085v-1085h-1085z" />
<hkern u1="&#x2a;" u2="&#xee;" k="-164" />
<hkern u1="&#x2f;" u2="&#xf0;" k="96" />
<hkern u1="&#x2f;" u2="i" k="-16" />
<hkern u1="F" u2="&#xef;" k="-16" />
<hkern u1="F" u2="&#xee;" k="-57" />
<hkern u1="T" u2="&#xef;" k="-57" />
<hkern u1="T" u2="&#xee;" k="-82" />
<hkern u1="V" u2="&#xef;" k="-39" />
<hkern u1="V" u2="&#xee;" k="-74" />
<hkern u1="V" u2="&#xec;" k="-37" />
<hkern u1="V" u2="i" k="29" />
<hkern u1="f" u2="&#xef;" k="-123" />
<hkern u1="f" u2="&#xee;" k="-98" />
<hkern u1="f" u2="&#xec;" k="-137" />
<hkern u1="i" u2="\" k="82" />
<hkern u1="j" u2="\" k="82" />
<hkern u1="q" u2="j" k="-102" />
<hkern u1="&#xa3;" u2="&#xef;" k="-16" />
<hkern u1="&#xa3;" u2="&#xee;" k="-57" />
<hkern u1="&#xaa;" u2="&#xee;" k="-164" />
<hkern u1="&#xba;" u2="&#xee;" k="-164" />
<hkern u1="&#xee;" u2="&#xba;" k="-164" />
<hkern u1="&#xee;" u2="&#xaa;" k="-164" />
<hkern u1="&#xee;" u2="&#x3f;" k="-82" />
<hkern u1="&#xee;" u2="&#x2a;" k="-164" />
<hkern u1="&#x2018;" u2="&#xec;" k="-61" />
<hkern u1="&#x201c;" u2="&#xec;" k="-61" />
<hkern g1="ampersand" 	g2="ampersand" 	k="14" />
<hkern g1="ampersand" 	g2="backslash" 	k="164" />
<hkern g1="ampersand" 	g2="bracketright,braceright" 	k="16" />
<hkern g1="ampersand" 	g2="colon,semicolon" 	k="14" />
<hkern g1="ampersand" 	g2="degree" 	k="84" />
<hkern g1="ampersand" 	g2="exclam" 	k="25" />
<hkern g1="ampersand" 	g2="exclamdown" 	k="20" />
<hkern g1="ampersand" 	g2="four" 	k="-6" />
<hkern g1="ampersand" 	g2="one" 	k="61" />
<hkern g1="ampersand" 	g2="paragraph" 	k="90" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-4" />
<hkern g1="ampersand" 	g2="question" 	k="152" />
<hkern g1="ampersand" 	g2="questiondown" 	k="6" />
<hkern g1="ampersand" 	g2="quoteleft,quotedblleft" 	k="66" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="53" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="100" />
<hkern g1="ampersand" 	g2="seven" 	k="35" />
<hkern g1="ampersand" 	g2="slash" 	k="-80" />
<hkern g1="ampersand" 	g2="three" 	k="6" />
<hkern g1="ampersand" 	g2="trademark" 	k="68" />
<hkern g1="ampersand" 	g2="two" 	k="16" />
<hkern g1="ampersand" 	g2="underscore" 	k="-78" />
<hkern g1="currency" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="currency" 	g2="questiondown" 	k="14" />
<hkern g1="currency" 	g2="seven" 	k="25" />
<hkern g1="currency" 	g2="two" 	k="16" />
<hkern g1="currency" 	g2="underscore" 	k="14" />
<hkern g1="currency" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-14" />
<hkern g1="degree" 	g2="backslash" 	k="-109" />
<hkern g1="degree" 	g2="four" 	k="82" />
<hkern g1="degree" 	g2="one" 	k="-88" />
<hkern g1="degree" 	g2="percent" 	k="-47" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="129" />
<hkern g1="degree" 	g2="question" 	k="-41" />
<hkern g1="degree" 	g2="questiondown" 	k="127" />
<hkern g1="degree" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="degree" 	g2="seven" 	k="-86" />
<hkern g1="degree" 	g2="slash" 	k="117" />
<hkern g1="degree" 	g2="three" 	k="-35" />
<hkern g1="degree" 	g2="two" 	k="-61" />
<hkern g1="degree" 	g2="five" 	k="-14" />
<hkern g1="degree" 	g2="nine" 	k="-76" />
<hkern g1="degree" 	g2="zero,six" 	k="-6" />
<hkern g1="percent" 	g2="backslash" 	k="76" />
<hkern g1="percent" 	g2="degree" 	k="55" />
<hkern g1="percent" 	g2="four" 	k="-68" />
<hkern g1="percent" 	g2="one" 	k="41" />
<hkern g1="percent" 	g2="percent" 	k="248" />
<hkern g1="percent" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="percent" 	g2="question" 	k="117" />
<hkern g1="percent" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="percent" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="percent" 	g2="quotedbl,quotesingle" 	k="55" />
<hkern g1="percent" 	g2="seven" 	k="35" />
<hkern g1="percent" 	g2="slash" 	k="-2" />
<hkern g1="percent" 	g2="three" 	k="-20" />
<hkern g1="percent" 	g2="two" 	k="-20" />
<hkern g1="percent" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="percent" 	g2="five" 	k="-41" />
<hkern g1="percent" 	g2="parenright" 	k="41" />
<hkern g1="percent" 	g2="eight" 	k="-41" />
<hkern g1="section" 	g2="four" 	k="-35" />
<hkern g1="section" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="section" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="section" 	g2="seven" 	k="-14" />
<hkern g1="section" 	g2="slash" 	k="-45" />
<hkern g1="section" 	g2="underscore" 	k="-14" />
<hkern g1="section" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="section" 	g2="eight" 	k="-20" />
<hkern g1="trademark" 	g2="backslash" 	k="-82" />
<hkern g1="trademark" 	g2="exclamdown" 	k="-6" />
<hkern g1="trademark" 	g2="questiondown" 	k="25" />
<hkern g1="trademark" 	g2="seven" 	k="-27" />
<hkern g1="trademark" 	g2="slash" 	k="86" />
<hkern g1="trademark" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="trademark" 	g2="nine" 	k="-61" />
<hkern g1="yen" 	g2="backslash" 	k="-102" />
<hkern g1="yen" 	g2="bracketright,braceright" 	k="-29" />
<hkern g1="yen" 	g2="colon,semicolon" 	k="82" />
<hkern g1="yen" 	g2="exclam" 	k="-23" />
<hkern g1="yen" 	g2="exclamdown" 	k="41" />
<hkern g1="yen" 	g2="four" 	k="20" />
<hkern g1="yen" 	g2="one" 	k="-61" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="yen" 	g2="questiondown" 	k="82" />
<hkern g1="yen" 	g2="quoteright,quotedblright" 	k="-16" />
<hkern g1="yen" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="yen" 	g2="seven" 	k="-55" />
<hkern g1="yen" 	g2="slash" 	k="61" />
<hkern g1="yen" 	g2="three" 	k="-20" />
<hkern g1="yen" 	g2="underscore" 	k="20" />
<hkern g1="yen" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="47" />
<hkern g1="yen" 	g2="zero,six" 	k="25" />
<hkern g1="yen" 	g2="parenright" 	k="-29" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="backslash" 	g2="ampersand" 	k="-35" />
<hkern g1="backslash" 	g2="backslash" 	k="139" />
<hkern g1="backslash" 	g2="bracketright,braceright" 	k="-78" />
<hkern g1="backslash" 	g2="colon,semicolon" 	k="-98" />
<hkern g1="backslash" 	g2="degree" 	k="123" />
<hkern g1="backslash" 	g2="exclamdown" 	k="-82" />
<hkern g1="backslash" 	g2="five" 	k="-6" />
<hkern g1="backslash" 	g2="four" 	k="14" />
<hkern g1="backslash" 	g2="guillemotright,guilsinglright" 	k="-18" />
<hkern g1="backslash" 	g2="one" 	k="41" />
<hkern g1="backslash" 	g2="paragraph" 	k="61" />
<hkern g1="backslash" 	g2="parenright" 	k="-61" />
<hkern g1="backslash" 	g2="percent" 	k="20" />
<hkern g1="backslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-203" />
<hkern g1="backslash" 	g2="question" 	k="123" />
<hkern g1="backslash" 	g2="questiondown" 	k="-61" />
<hkern g1="backslash" 	g2="quoteleft,quotedblleft" 	k="106" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="106" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="backslash" 	g2="section" 	k="-61" />
<hkern g1="backslash" 	g2="seven" 	k="102" />
<hkern g1="backslash" 	g2="slash" 	k="-25" />
<hkern g1="backslash" 	g2="three" 	k="-20" />
<hkern g1="backslash" 	g2="trademark" 	k="123" />
<hkern g1="backslash" 	g2="two" 	k="-20" />
<hkern g1="backslash" 	g2="underscore" 	k="-287" />
<hkern g1="backslash" 	g2="zero,six" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="backslash" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="exclamdown" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="27" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="-37" />
<hkern g1="bracketleft,braceleft" 	g2="paragraph" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="parenright" 	k="-16" />
<hkern g1="bracketleft,braceleft" 	g2="question" 	k="-16" />
<hkern g1="bracketleft,braceleft" 	g2="quotedbl,quotesingle" 	k="-16" />
<hkern g1="bracketleft,braceleft" 	g2="seven" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="slash" 	k="-78" />
<hkern g1="bracketleft,braceleft" 	g2="three" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="trademark" 	k="-98" />
<hkern g1="bracketleft,braceleft" 	g2="two" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="underscore" 	k="-57" />
<hkern g1="bracketleft,braceleft" 	g2="exclam" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="16" />
<hkern g1="bracketleft,braceleft" 	g2="yen" 	k="-29" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="37" />
<hkern g1="colon,semicolon" 	g2="question" 	k="20" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="-14" />
<hkern g1="colon,semicolon" 	g2="slash" 	k="-98" />
<hkern g1="colon,semicolon" 	g2="underscore" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="yen" 	k="82" />
<hkern g1="exclam" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="exclam" 	g2="one" 	k="-20" />
<hkern g1="exclam" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-6" />
<hkern g1="exclam" 	g2="quoteleft,quotedblleft" 	k="-6" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="-6" />
<hkern g1="exclam" 	g2="quotedbl,quotesingle" 	k="-6" />
<hkern g1="exclam" 	g2="seven" 	k="-14" />
<hkern g1="exclam" 	g2="trademark" 	k="-16" />
<hkern g1="exclam" 	g2="yen" 	k="-23" />
<hkern g1="exclamdown" 	g2="backslash" 	k="113" />
<hkern g1="exclamdown" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="exclamdown" 	g2="one" 	k="41" />
<hkern g1="exclamdown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-6" />
<hkern g1="exclamdown" 	g2="question" 	k="6" />
<hkern g1="exclamdown" 	g2="quoteleft,quotedblleft" 	k="-6" />
<hkern g1="exclamdown" 	g2="quoteright,quotedblright" 	k="-6" />
<hkern g1="exclamdown" 	g2="slash" 	k="-82" />
<hkern g1="exclamdown" 	g2="trademark" 	k="6" />
<hkern g1="exclamdown" 	g2="underscore" 	k="-23" />
<hkern g1="exclamdown" 	g2="yen" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="117" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="five" 	k="-14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="-14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="one" 	k="6" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-6" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="question" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="questiondown" 	k="-14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="slash" 	k="-18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="underscore" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="backslash" 	k="133" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="four" 	k="-14" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="one" 	k="37" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="paragraph" 	k="178" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="percent" 	k="76" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="question" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="section" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="seven" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="three" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="two" 	k="27" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="zero,six" 	k="-14" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-6" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="yen" 	k="47" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="currency,Euro" 	k="-14" />
<hkern g1="parenleft" 	g2="backslash" 	k="-78" />
<hkern g1="parenleft" 	g2="bracketright,braceright" 	k="-16" />
<hkern g1="parenleft" 	g2="four" 	k="41" />
<hkern g1="parenleft" 	g2="one" 	k="-20" />
<hkern g1="parenleft" 	g2="slash" 	k="-68" />
<hkern g1="parenleft" 	g2="trademark" 	k="-57" />
<hkern g1="parenleft" 	g2="underscore" 	k="-6" />
<hkern g1="parenleft" 	g2="yen" 	k="-29" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="199" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="degree" 	k="129" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclamdown" 	k="-6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="18" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="guillemotright,guilsinglright" 	k="-6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="27" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="paragraph" 	k="264" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="129" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="109" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="questiondown" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="37" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="37" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="113" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="27" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="slash" 	k="-203" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="two" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="underscore" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero,six" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclam" 	k="-6" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="currency,Euro" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="-20" />
<hkern g1="question" 	g2="ampersand" 	k="39" />
<hkern g1="question" 	g2="degree" 	k="-20" />
<hkern g1="question" 	g2="exclamdown" 	k="14" />
<hkern g1="question" 	g2="four" 	k="102" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="question" 	g2="question" 	k="55" />
<hkern g1="question" 	g2="questiondown" 	k="219" />
<hkern g1="question" 	g2="quoteleft,quotedblleft" 	k="-47" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="-47" />
<hkern g1="question" 	g2="slash" 	k="61" />
<hkern g1="question" 	g2="three" 	k="31" />
<hkern g1="question" 	g2="underscore" 	k="39" />
<hkern g1="question" 	g2="exclam" 	k="14" />
<hkern g1="question" 	g2="yen" 	k="31" />
<hkern g1="question" 	g2="eight" 	k="20" />
<hkern g1="question" 	g2="bracketleft" 	k="33" />
<hkern g1="question" 	g2="nine" 	k="-20" />
<hkern g1="questiondown" 	g2="ampersand" 	k="35" />
<hkern g1="questiondown" 	g2="backslash" 	k="193" />
<hkern g1="questiondown" 	g2="bracketright,braceright" 	k="-16" />
<hkern g1="questiondown" 	g2="colon,semicolon" 	k="6" />
<hkern g1="questiondown" 	g2="degree" 	k="119" />
<hkern g1="questiondown" 	g2="four" 	k="88" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="questiondown" 	g2="one" 	k="88" />
<hkern g1="questiondown" 	g2="paragraph" 	k="180" />
<hkern g1="questiondown" 	g2="percent" 	k="123" />
<hkern g1="questiondown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-6" />
<hkern g1="questiondown" 	g2="question" 	k="272" />
<hkern g1="questiondown" 	g2="questiondown" 	k="53" />
<hkern g1="questiondown" 	g2="quoteleft,quotedblleft" 	k="47" />
<hkern g1="questiondown" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle" 	k="70" />
<hkern g1="questiondown" 	g2="section" 	k="-6" />
<hkern g1="questiondown" 	g2="seven" 	k="88" />
<hkern g1="questiondown" 	g2="slash" 	k="-82" />
<hkern g1="questiondown" 	g2="trademark" 	k="78" />
<hkern g1="questiondown" 	g2="underscore" 	k="-102" />
<hkern g1="questiondown" 	g2="zero,six" 	k="98" />
<hkern g1="questiondown" 	g2="exclam" 	k="6" />
<hkern g1="questiondown" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="82" />
<hkern g1="questiondown" 	g2="yen" 	k="102" />
<hkern g1="questiondown" 	g2="sterling" 	k="-16" />
<hkern g1="questiondown" 	g2="currency,Euro" 	k="39" />
<hkern g1="questiondown" 	g2="eight" 	k="88" />
<hkern g1="quoteleft,quotedblleft" 	g2="ampersand" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="backslash" 	k="-106" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="-6" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="76" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-76" />
<hkern g1="quoteleft,quotedblleft" 	g2="paragraph" 	k="-76" />
<hkern g1="quoteleft,quotedblleft" 	g2="percent" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="37" />
<hkern g1="quoteleft,quotedblleft" 	g2="question" 	k="-76" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteleft,quotedblleft" 	k="-57" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteright,quotedblright" 	k="-57" />
<hkern g1="quoteleft,quotedblleft" 	g2="section" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-86" />
<hkern g1="quoteleft,quotedblleft" 	g2="slash" 	k="106" />
<hkern g1="quoteleft,quotedblleft" 	g2="three" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="trademark" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="two" 	k="-25" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclam" 	k="-6" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="nine" 	k="-66" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="backslash" 	k="-106" />
<hkern g1="quoteright,quotedblright" 	g2="exclamdown" 	k="-6" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="117" />
<hkern g1="quoteright,quotedblright" 	g2="one" 	k="-96" />
<hkern g1="quoteright,quotedblright" 	g2="paragraph" 	k="-76" />
<hkern g1="quoteright,quotedblright" 	g2="percent" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="37" />
<hkern g1="quoteright,quotedblright" 	g2="question" 	k="-76" />
<hkern g1="quoteright,quotedblright" 	g2="questiondown" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="quoteleft,quotedblleft" 	k="-57" />
<hkern g1="quoteright,quotedblright" 	g2="quoteright,quotedblright" 	k="-57" />
<hkern g1="quoteright,quotedblright" 	g2="section" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-55" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="106" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="-35" />
<hkern g1="quoteright,quotedblright" 	g2="trademark" 	k="-68" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="exclam" 	k="-6" />
<hkern g1="quoteright,quotedblright" 	g2="yen" 	k="-16" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="45" />
<hkern g1="quotedbl,quotesingle" 	g2="backslash" 	k="-123" />
<hkern g1="quotedbl,quotesingle" 	g2="bracketright,braceright" 	k="-16" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="degree" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="five" 	k="-14" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-96" />
<hkern g1="quotedbl,quotesingle" 	g2="paragraph" 	k="-94" />
<hkern g1="quotedbl,quotesingle" 	g2="percent" 	k="-55" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="quotedbl,quotesingle" 	g2="question" 	k="-66" />
<hkern g1="quotedbl,quotesingle" 	g2="questiondown" 	k="88" />
<hkern g1="quotedbl,quotesingle" 	g2="quotedbl,quotesingle" 	k="-92" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-100" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-35" />
<hkern g1="quotedbl,quotesingle" 	g2="trademark" 	k="-76" />
<hkern g1="quotedbl,quotesingle" 	g2="two" 	k="-45" />
<hkern g1="quotedbl,quotesingle" 	g2="underscore" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="zero,six" 	k="-14" />
<hkern g1="quotedbl,quotesingle" 	g2="exclam" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="yen" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="currency,Euro" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="nine" 	k="-86" />
<hkern g1="slash" 	g2="ampersand" 	k="76" />
<hkern g1="slash" 	g2="backslash" 	k="-53" />
<hkern g1="slash" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="31" />
<hkern g1="slash" 	g2="degree" 	k="-96" />
<hkern g1="slash" 	g2="exclamdown" 	k="133" />
<hkern g1="slash" 	g2="four" 	k="137" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="100" />
<hkern g1="slash" 	g2="one" 	k="-61" />
<hkern g1="slash" 	g2="parenright" 	k="-68" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="199" />
<hkern g1="slash" 	g2="questiondown" 	k="154" />
<hkern g1="slash" 	g2="quoteleft,quotedblleft" 	k="-100" />
<hkern g1="slash" 	g2="quoteright,quotedblright" 	k="-106" />
<hkern g1="slash" 	g2="quotedbl,quotesingle" 	k="-123" />
<hkern g1="slash" 	g2="section" 	k="41" />
<hkern g1="slash" 	g2="seven" 	k="-25" />
<hkern g1="slash" 	g2="slash" 	k="139" />
<hkern g1="slash" 	g2="trademark" 	k="-96" />
<hkern g1="slash" 	g2="underscore" 	k="250" />
<hkern g1="slash" 	g2="zero,six" 	k="41" />
<hkern g1="slash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="139" />
<hkern g1="slash" 	g2="yen" 	k="-82" />
<hkern g1="slash" 	g2="sterling" 	k="20" />
<hkern g1="slash" 	g2="eight" 	k="47" />
<hkern g1="underscore" 	g2="backslash" 	k="266" />
<hkern g1="underscore" 	g2="bracketright,braceright" 	k="-57" />
<hkern g1="underscore" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="underscore" 	g2="exclamdown" 	k="-23" />
<hkern g1="underscore" 	g2="five" 	k="-20" />
<hkern g1="underscore" 	g2="four" 	k="55" />
<hkern g1="underscore" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="underscore" 	g2="one" 	k="16" />
<hkern g1="underscore" 	g2="paragraph" 	k="145" />
<hkern g1="underscore" 	g2="parenright" 	k="-6" />
<hkern g1="underscore" 	g2="percent" 	k="55" />
<hkern g1="underscore" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="underscore" 	g2="question" 	k="27" />
<hkern g1="underscore" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="underscore" 	g2="section" 	k="-55" />
<hkern g1="underscore" 	g2="slash" 	k="-264" />
<hkern g1="underscore" 	g2="three" 	k="-55" />
<hkern g1="underscore" 	g2="trademark" 	k="102" />
<hkern g1="underscore" 	g2="two" 	k="-66" />
<hkern g1="underscore" 	g2="zero,six" 	k="41" />
<hkern g1="underscore" 	g2="yen" 	k="20" />
<hkern g1="underscore" 	g2="sterling" 	k="-35" />
<hkern g1="underscore" 	g2="currency,Euro" 	k="14" />
<hkern g1="underscore" 	g2="nine" 	k="-20" />
<hkern g1="eight" 	g2="AE" 	k="10" />
<hkern g1="eight" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="6" />
<hkern g1="eight" 	g2="J" 	k="76" />
<hkern g1="eight" 	g2="T" 	k="10" />
<hkern g1="eight" 	g2="V" 	k="35" />
<hkern g1="eight" 	g2="X" 	k="45" />
<hkern g1="eight" 	g2="Z" 	k="6" />
<hkern g1="eight" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-14" />
<hkern g1="eight" 	g2="backslash" 	k="61" />
<hkern g1="eight" 	g2="j" 	k="16" />
<hkern g1="eight" 	g2="percent" 	k="20" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="eight" 	g2="question" 	k="20" />
<hkern g1="eight" 	g2="questiondown" 	k="41" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="-6" />
<hkern g1="eight" 	g2="section" 	k="-20" />
<hkern g1="eight" 	g2="v" 	k="-8" />
<hkern g1="eight" 	g2="x" 	k="12" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="five" 	g2="J" 	k="88" />
<hkern g1="five" 	g2="V" 	k="14" />
<hkern g1="five" 	g2="X" 	k="25" />
<hkern g1="five" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="five" 	g2="backslash" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="20" />
<hkern g1="five" 	g2="v" 	k="16" />
<hkern g1="five" 	g2="x" 	k="16" />
<hkern g1="five" 	g2="degree" 	k="25" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="seven" 	k="27" />
<hkern g1="five" 	g2="three" 	k="10" />
<hkern g1="five" 	g2="two" 	k="10" />
<hkern g1="four" 	g2="J" 	k="14" />
<hkern g1="four" 	g2="T" 	k="106" />
<hkern g1="four" 	g2="V" 	k="109" />
<hkern g1="four" 	g2="X" 	k="16" />
<hkern g1="four" 	g2="Z" 	k="14" />
<hkern g1="four" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="percent" 	k="61" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="four" 	g2="question" 	k="109" />
<hkern g1="four" 	g2="questiondown" 	k="-20" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="47" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="four" 	g2="section" 	k="-43" />
<hkern g1="four" 	g2="v" 	k="37" />
<hkern g1="four" 	g2="x" 	k="27" />
<hkern g1="four" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="degree" 	k="61" />
<hkern g1="four" 	g2="five" 	k="6" />
<hkern g1="four" 	g2="seven" 	k="96" />
<hkern g1="four" 	g2="three" 	k="25" />
<hkern g1="four" 	g2="two" 	k="6" />
<hkern g1="four" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="four" 	g2="dollar,S" 	k="14" />
<hkern g1="four" 	g2="ae" 	k="-57" />
<hkern g1="four" 	g2="slash" 	k="-55" />
<hkern g1="four" 	g2="underscore" 	k="-41" />
<hkern g1="four" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="-41" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="four" 	g2="ampersand" 	k="-41" />
<hkern g1="four" 	g2="colon,semicolon" 	k="-14" />
<hkern g1="four" 	g2="currency,Euro" 	k="-20" />
<hkern g1="four" 	g2="eight" 	k="-20" />
<hkern g1="four" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="four" 	g2="sterling" 	k="-20" />
<hkern g1="four" 	g2="nine" 	k="14" />
<hkern g1="four" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-27" />
<hkern g1="four" 	g2="one" 	k="51" />
<hkern g1="four" 	g2="paragraph" 	k="37" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="four" 	g2="uniFB02" 	k="-4" />
<hkern g1="four" 	g2="trademark" 	k="41" />
<hkern g1="seven" 	g2="AE" 	k="131" />
<hkern g1="seven" 	g2="J" 	k="35" />
<hkern g1="seven" 	g2="T" 	k="-20" />
<hkern g1="seven" 	g2="V" 	k="-10" />
<hkern g1="seven" 	g2="X" 	k="6" />
<hkern g1="seven" 	g2="Z" 	k="14" />
<hkern g1="seven" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-55" />
<hkern g1="seven" 	g2="backslash" 	k="-20" />
<hkern g1="seven" 	g2="j" 	k="43" />
<hkern g1="seven" 	g2="percent" 	k="-6" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="106" />
<hkern g1="seven" 	g2="question" 	k="-31" />
<hkern g1="seven" 	g2="questiondown" 	k="127" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-61" />
<hkern g1="seven" 	g2="section" 	k="20" />
<hkern g1="seven" 	g2="v" 	k="4" />
<hkern g1="seven" 	g2="x" 	k="39" />
<hkern g1="seven" 	g2="yen" 	k="-27" />
<hkern g1="seven" 	g2="degree" 	k="-12" />
<hkern g1="seven" 	g2="five" 	k="37" />
<hkern g1="seven" 	g2="three" 	k="16" />
<hkern g1="seven" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="seven" 	g2="dollar,S" 	k="14" />
<hkern g1="seven" 	g2="ae" 	k="78" />
<hkern g1="seven" 	g2="slash" 	k="86" />
<hkern g1="seven" 	g2="underscore" 	k="61" />
<hkern g1="seven" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="6" />
<hkern g1="seven" 	g2="ampersand" 	k="66" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="41" />
<hkern g1="seven" 	g2="currency,Euro" 	k="41" />
<hkern g1="seven" 	g2="eight" 	k="37" />
<hkern g1="seven" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="102" />
<hkern g1="seven" 	g2="sterling" 	k="27" />
<hkern g1="seven" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="seven" 	g2="one" 	k="-20" />
<hkern g1="seven" 	g2="paragraph" 	k="-55" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="seven" 	g2="uniFB02" 	k="25" />
<hkern g1="seven" 	g2="trademark" 	k="-82" />
<hkern g1="seven" 	g2="exclamdown" 	k="55" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="35" />
<hkern g1="seven" 	g2="s" 	k="61" />
<hkern g1="seven" 	g2="z" 	k="55" />
<hkern g1="seven" 	g2="zero,six" 	k="41" />
<hkern g1="seven" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="seven" 	g2="bracketright,braceright" 	k="-2" />
<hkern g1="seven" 	g2="four" 	k="133" />
<hkern g1="seven" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="49" />
<hkern g1="seven" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="55" />
<hkern g1="six" 	g2="AE" 	k="-14" />
<hkern g1="six" 	g2="J" 	k="51" />
<hkern g1="six" 	g2="T" 	k="25" />
<hkern g1="six" 	g2="V" 	k="20" />
<hkern g1="six" 	g2="X" 	k="14" />
<hkern g1="six" 	g2="asterisk,ordfeminine,ordmasculine" 	k="25" />
<hkern g1="six" 	g2="j" 	k="16" />
<hkern g1="six" 	g2="percent" 	k="41" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="six" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="six" 	g2="section" 	k="-41" />
<hkern g1="six" 	g2="v" 	k="23" />
<hkern g1="six" 	g2="x" 	k="31" />
<hkern g1="six" 	g2="degree" 	k="39" />
<hkern g1="six" 	g2="seven" 	k="14" />
<hkern g1="six" 	g2="slash" 	k="-20" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="six" 	g2="ampersand" 	k="-20" />
<hkern g1="six" 	g2="currency,Euro" 	k="-20" />
<hkern g1="six" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="six" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-16" />
<hkern g1="three" 	g2="AE" 	k="-6" />
<hkern g1="three" 	g2="J" 	k="55" />
<hkern g1="three" 	g2="V" 	k="20" />
<hkern g1="three" 	g2="X" 	k="20" />
<hkern g1="three" 	g2="j" 	k="12" />
<hkern g1="three" 	g2="percent" 	k="20" />
<hkern g1="three" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="three" 	g2="v" 	k="14" />
<hkern g1="three" 	g2="x" 	k="27" />
<hkern g1="three" 	g2="degree" 	k="31" />
<hkern g1="three" 	g2="five" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="25" />
<hkern g1="three" 	g2="three" 	k="10" />
<hkern g1="three" 	g2="two" 	k="10" />
<hkern g1="two" 	g2="AE" 	k="-6" />
<hkern g1="two" 	g2="J" 	k="-6" />
<hkern g1="two" 	g2="V" 	k="25" />
<hkern g1="two" 	g2="backslash" 	k="14" />
<hkern g1="two" 	g2="percent" 	k="-20" />
<hkern g1="two" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="two" 	g2="v" 	k="-6" />
<hkern g1="two" 	g2="x" 	k="14" />
<hkern g1="two" 	g2="seven" 	k="6" />
<hkern g1="two" 	g2="slash" 	k="-20" />
<hkern g1="two" 	g2="underscore" 	k="-61" />
<hkern g1="two" 	g2="ampersand" 	k="14" />
<hkern g1="two" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="14" />
<hkern g1="two" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="two" 	g2="z" 	k="14" />
<hkern g1="two" 	g2="zero,six" 	k="6" />
<hkern g1="two" 	g2="four" 	k="39" />
<hkern g1="two" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="6" />
<hkern g1="zero,nine" 	g2="AE" 	k="16" />
<hkern g1="zero,nine" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="zero,nine" 	g2="J" 	k="102" />
<hkern g1="zero,nine" 	g2="T" 	k="31" />
<hkern g1="zero,nine" 	g2="V" 	k="51" />
<hkern g1="zero,nine" 	g2="X" 	k="68" />
<hkern g1="zero,nine" 	g2="Z" 	k="39" />
<hkern g1="zero,nine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-14" />
<hkern g1="zero,nine" 	g2="backslash" 	k="41" />
<hkern g1="zero,nine" 	g2="j" 	k="16" />
<hkern g1="zero,nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="zero,nine" 	g2="question" 	k="41" />
<hkern g1="zero,nine" 	g2="questiondown" 	k="61" />
<hkern g1="zero,nine" 	g2="quotedbl,quotesingle" 	k="-14" />
<hkern g1="zero,nine" 	g2="v" 	k="-14" />
<hkern g1="zero,nine" 	g2="x" 	k="20" />
<hkern g1="zero,nine" 	g2="yen" 	k="25" />
<hkern g1="zero,nine" 	g2="degree" 	k="-6" />
<hkern g1="zero,nine" 	g2="seven" 	k="14" />
<hkern g1="zero,nine" 	g2="three" 	k="20" />
<hkern g1="zero,nine" 	g2="two" 	k="6" />
<hkern g1="zero,nine" 	g2="slash" 	k="41" />
<hkern g1="zero,nine" 	g2="underscore" 	k="41" />
<hkern g1="zero,nine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-14" />
<hkern g1="zero,nine" 	g2="one" 	k="6" />
<hkern g1="zero,nine" 	g2="uniFB02" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,ordfeminine,ordmasculine" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="45" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="8" />
<hkern g1="B" 	g2="J" 	k="61" />
<hkern g1="B" 	g2="T" 	k="10" />
<hkern g1="B" 	g2="V" 	k="10" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-6" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="6" />
<hkern g1="B" 	g2="trademark" 	k="6" />
<hkern g1="B" 	g2="underscore" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="J" 	k="55" />
<hkern g1="C,Ccedilla,Euro" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="6" />
<hkern g1="C,Ccedilla,Euro" 	g2="V" 	k="14" />
<hkern g1="C,Ccedilla,Euro" 	g2="X" 	k="35" />
<hkern g1="C,Ccedilla,Euro" 	g2="Y,Yacute,Ydieresis" 	k="25" />
<hkern g1="C,Ccedilla,Euro" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="C,Ccedilla,Euro" 	g2="backslash" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="colon,semicolon" 	k="14" />
<hkern g1="C,Ccedilla,Euro" 	g2="degree" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="eight" 	k="27" />
<hkern g1="C,Ccedilla,Euro" 	g2="exclamdown" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="four" 	k="53" />
<hkern g1="C,Ccedilla,Euro" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="C,Ccedilla,Euro" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="6" />
<hkern g1="C,Ccedilla,Euro" 	g2="nine" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="C,Ccedilla,Euro" 	g2="one" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="paragraph" 	k="-14" />
<hkern g1="C,Ccedilla,Euro" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-27" />
<hkern g1="C,Ccedilla,Euro" 	g2="questiondown" 	k="25" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteleft,quotedblleft" 	k="-4" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="C,Ccedilla,Euro" 	g2="s" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="three" 	k="6" />
<hkern g1="C,Ccedilla,Euro" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="35" />
<hkern g1="C,Ccedilla,Euro" 	g2="underscore" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="x" 	k="35" />
<hkern g1="C,Ccedilla,Euro" 	g2="z" 	k="35" />
<hkern g1="C,Ccedilla,Euro" 	g2="zero,six" 	k="45" />
<hkern g1="C,Ccedilla,Euro" 	g2="AE" 	k="35" />
<hkern g1="C,Ccedilla,Euro" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="25" />
<hkern g1="C,Ccedilla,Euro" 	g2="dollar,S" 	k="6" />
<hkern g1="C,Ccedilla,Euro" 	g2="ae" 	k="14" />
<hkern g1="C,Ccedilla,Euro" 	g2="ampersand" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="section" 	k="14" />
<hkern g1="C,Ccedilla,Euro" 	g2="two" 	k="14" />
<hkern g1="AE,OE" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="AE,OE" 	g2="V" 	k="20" />
<hkern g1="AE,OE" 	g2="X" 	k="16" />
<hkern g1="AE,OE" 	g2="colon,semicolon" 	k="14" />
<hkern g1="AE,OE" 	g2="eight" 	k="14" />
<hkern g1="AE,OE" 	g2="exclamdown" 	k="14" />
<hkern g1="AE,OE" 	g2="four" 	k="63" />
<hkern g1="AE,OE" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="AE,OE" 	g2="nine" 	k="6" />
<hkern g1="AE,OE" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="AE,OE" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="AE,OE" 	g2="question" 	k="25" />
<hkern g1="AE,OE" 	g2="s" 	k="14" />
<hkern g1="AE,OE" 	g2="seven" 	k="14" />
<hkern g1="AE,OE" 	g2="uniFB02" 	k="25" />
<hkern g1="AE,OE" 	g2="three" 	k="25" />
<hkern g1="AE,OE" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="14" />
<hkern g1="AE,OE" 	g2="underscore" 	k="-25" />
<hkern g1="AE,OE" 	g2="v" 	k="18" />
<hkern g1="AE,OE" 	g2="x" 	k="25" />
<hkern g1="AE,OE" 	g2="z" 	k="18" />
<hkern g1="AE,OE" 	g2="zero,six" 	k="29" />
<hkern g1="AE,OE" 	g2="ae" 	k="14" />
<hkern g1="AE,OE" 	g2="five" 	k="14" />
<hkern g1="AE,OE" 	g2="section" 	k="14" />
<hkern g1="AE,OE" 	g2="two" 	k="14" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="J" 	k="35" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="T" 	k="6" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="X" 	k="14" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="eight" 	k="27" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="four" 	k="74" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="nine" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="question" 	k="25" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="questiondown" 	k="-6" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="quoteleft,quotedblleft" 	k="23" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="s" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="slash" 	k="-20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="uniFB02" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="three" 	k="-6" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="16" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="underscore" 	k="-82" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="v" 	k="27" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="x" 	k="27" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="zero,six" 	k="37" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="ae" 	k="6" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="parenright" 	k="18" />
<hkern g1="F,sterling" 	g2="J" 	k="72" />
<hkern g1="F,sterling" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="F,sterling" 	g2="X" 	k="20" />
<hkern g1="F,sterling" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-53" />
<hkern g1="F,sterling" 	g2="backslash" 	k="-35" />
<hkern g1="F,sterling" 	g2="colon,semicolon" 	k="61" />
<hkern g1="F,sterling" 	g2="eight" 	k="68" />
<hkern g1="F,sterling" 	g2="exclamdown" 	k="51" />
<hkern g1="F,sterling" 	g2="four" 	k="70" />
<hkern g1="F,sterling" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="F,sterling" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="F,sterling" 	g2="nine" 	k="41" />
<hkern g1="F,sterling" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="F,sterling" 	g2="one" 	k="-35" />
<hkern g1="F,sterling" 	g2="paragraph" 	k="-25" />
<hkern g1="F,sterling" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="6" />
<hkern g1="F,sterling" 	g2="questiondown" 	k="45" />
<hkern g1="F,sterling" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="F,sterling" 	g2="quoteright,quotedblright" 	k="-6" />
<hkern g1="F,sterling" 	g2="s" 	k="82" />
<hkern g1="F,sterling" 	g2="seven" 	k="-14" />
<hkern g1="F,sterling" 	g2="slash" 	k="-25" />
<hkern g1="F,sterling" 	g2="uniFB02" 	k="41" />
<hkern g1="F,sterling" 	g2="three" 	k="20" />
<hkern g1="F,sterling" 	g2="trademark" 	k="-66" />
<hkern g1="F,sterling" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="55" />
<hkern g1="F,sterling" 	g2="underscore" 	k="-35" />
<hkern g1="F,sterling" 	g2="v" 	k="41" />
<hkern g1="F,sterling" 	g2="x" 	k="51" />
<hkern g1="F,sterling" 	g2="z" 	k="82" />
<hkern g1="F,sterling" 	g2="zero,six" 	k="76" />
<hkern g1="F,sterling" 	g2="AE" 	k="92" />
<hkern g1="F,sterling" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="F,sterling" 	g2="dollar,S" 	k="20" />
<hkern g1="F,sterling" 	g2="ae" 	k="82" />
<hkern g1="F,sterling" 	g2="ampersand" 	k="76" />
<hkern g1="F,sterling" 	g2="five" 	k="27" />
<hkern g1="F,sterling" 	g2="section" 	k="20" />
<hkern g1="F,sterling" 	g2="two" 	k="20" />
<hkern g1="F,sterling" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="F,sterling" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="F,sterling" 	g2="j" 	k="51" />
<hkern g1="F,sterling" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="41" />
<hkern g1="G" 	g2="J" 	k="20" />
<hkern g1="G" 	g2="backslash" 	k="35" />
<hkern g1="G" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="H,U,paragraph,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="14" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="eight" 	k="6" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="four" 	k="20" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="nine" 	k="6" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="trademark" 	k="-41" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="zero,six" 	k="20" />
<hkern g1="J" 	g2="J" 	k="41" />
<hkern g1="J" 	g2="underscore" 	k="20" />
<hkern g1="K" 	g2="J" 	k="8" />
<hkern g1="K" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="K" 	g2="T" 	k="41" />
<hkern g1="K" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="K" 	g2="V" 	k="82" />
<hkern g1="K" 	g2="X" 	k="78" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="55" />
<hkern g1="K" 	g2="degree" 	k="20" />
<hkern g1="K" 	g2="eight" 	k="45" />
<hkern g1="K" 	g2="four" 	k="96" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="K" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="68" />
<hkern g1="K" 	g2="nine" 	k="20" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="K" 	g2="one" 	k="-41" />
<hkern g1="K" 	g2="paragraph" 	k="16" />
<hkern g1="K" 	g2="question" 	k="82" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="37" />
<hkern g1="K" 	g2="s" 	k="35" />
<hkern g1="K" 	g2="slash" 	k="-14" />
<hkern g1="K" 	g2="uniFB02" 	k="55" />
<hkern g1="K" 	g2="three" 	k="14" />
<hkern g1="K" 	g2="trademark" 	k="-41" />
<hkern g1="K" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="37" />
<hkern g1="K" 	g2="underscore" 	k="-137" />
<hkern g1="K" 	g2="v" 	k="76" />
<hkern g1="K" 	g2="x" 	k="84" />
<hkern g1="K" 	g2="z" 	k="47" />
<hkern g1="K" 	g2="zero,six" 	k="45" />
<hkern g1="K" 	g2="AE" 	k="57" />
<hkern g1="K" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="K" 	g2="dollar,S" 	k="31" />
<hkern g1="K" 	g2="ae" 	k="27" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="two" 	k="-4" />
<hkern g1="K" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="K" 	g2="bracketright,braceright" 	k="-16" />
<hkern g1="K" 	g2="j" 	k="6" />
<hkern g1="K" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="K" 	g2="bracketleft" 	k="53" />
<hkern g1="L" 	g2="J" 	k="-61" />
<hkern g1="L" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="L" 	g2="T" 	k="133" />
<hkern g1="L" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="L" 	g2="V" 	k="106" />
<hkern g1="L" 	g2="X" 	k="20" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="55" />
<hkern g1="L" 	g2="asterisk,ordfeminine,ordmasculine" 	k="143" />
<hkern g1="L" 	g2="backslash" 	k="143" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="L" 	g2="degree" 	k="133" />
<hkern g1="L" 	g2="eight" 	k="14" />
<hkern g1="L" 	g2="four" 	k="63" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-14" />
<hkern g1="L" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="18" />
<hkern g1="L" 	g2="nine" 	k="20" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="L" 	g2="one" 	k="76" />
<hkern g1="L" 	g2="paragraph" 	k="160" />
<hkern g1="L" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="L" 	g2="question" 	k="188" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="88" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="96" />
<hkern g1="L" 	g2="seven" 	k="102" />
<hkern g1="L" 	g2="slash" 	k="-45" />
<hkern g1="L" 	g2="uniFB02" 	k="10" />
<hkern g1="L" 	g2="three" 	k="-6" />
<hkern g1="L" 	g2="trademark" 	k="139" />
<hkern g1="L" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="14" />
<hkern g1="L" 	g2="underscore" 	k="-121" />
<hkern g1="L" 	g2="v" 	k="78" />
<hkern g1="L" 	g2="x" 	k="14" />
<hkern g1="L" 	g2="zero,six" 	k="45" />
<hkern g1="L" 	g2="AE" 	k="-6" />
<hkern g1="L" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="14" />
<hkern g1="L" 	g2="dollar,S" 	k="14" />
<hkern g1="L" 	g2="ae" 	k="-6" />
<hkern g1="L" 	g2="ampersand" 	k="-6" />
<hkern g1="M,N,Ntilde" 	g2="backslash" 	k="20" />
<hkern g1="M,N,Ntilde" 	g2="one" 	k="6" />
<hkern g1="M,N,Ntilde" 	g2="question" 	k="10" />
<hkern g1="M,N,Ntilde" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="M,N,Ntilde" 	g2="trademark" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="45" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="51" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="degree" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-14" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="questiondown" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="37" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="uniFB02" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="25" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="27" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="-25" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="j" 	k="-164" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="16" />
<hkern g1="P" 	g2="J" 	k="96" />
<hkern g1="P" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="16" />
<hkern g1="P" 	g2="V" 	k="35" />
<hkern g1="P" 	g2="X" 	k="51" />
<hkern g1="P" 	g2="backslash" 	k="41" />
<hkern g1="P" 	g2="degree" 	k="-41" />
<hkern g1="P" 	g2="eight" 	k="14" />
<hkern g1="P" 	g2="four" 	k="61" />
<hkern g1="P" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="6" />
<hkern g1="P" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="57" />
<hkern g1="P" 	g2="questiondown" 	k="106" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="slash" 	k="96" />
<hkern g1="P" 	g2="uniFB02" 	k="-10" />
<hkern g1="P" 	g2="three" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="68" />
<hkern g1="P" 	g2="v" 	k="14" />
<hkern g1="P" 	g2="x" 	k="68" />
<hkern g1="P" 	g2="AE" 	k="92" />
<hkern g1="P" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="P" 	g2="ae" 	k="31" />
<hkern g1="P" 	g2="ampersand" 	k="66" />
<hkern g1="P" 	g2="five" 	k="20" />
<hkern g1="P" 	g2="two" 	k="10" />
<hkern g1="P" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="49" />
<hkern g1="P" 	g2="parenright" 	k="18" />
<hkern g1="P" 	g2="bracketright,braceright" 	k="18" />
<hkern g1="P" 	g2="j" 	k="20" />
<hkern g1="P" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="39" />
<hkern g1="P" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="P" 	g2="Z" 	k="35" />
<hkern g1="P" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="20" />
<hkern g1="R" 	g2="J" 	k="61" />
<hkern g1="R" 	g2="X" 	k="41" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="degree" 	k="-31" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="4" />
<hkern g1="R" 	g2="question" 	k="10" />
<hkern g1="R" 	g2="questiondown" 	k="20" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="R" 	g2="uniFB02" 	k="-10" />
<hkern g1="R" 	g2="three" 	k="14" />
<hkern g1="R" 	g2="underscore" 	k="-35" />
<hkern g1="R" 	g2="ae" 	k="10" />
<hkern g1="dollar,S" 	g2="J" 	k="61" />
<hkern g1="dollar,S" 	g2="T" 	k="16" />
<hkern g1="dollar,S" 	g2="X" 	k="27" />
<hkern g1="dollar,S" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="20" />
<hkern g1="dollar,S" 	g2="colon,semicolon" 	k="10" />
<hkern g1="dollar,S" 	g2="degree" 	k="31" />
<hkern g1="dollar,S" 	g2="exclamdown" 	k="4" />
<hkern g1="dollar,S" 	g2="four" 	k="-20" />
<hkern g1="dollar,S" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-35" />
<hkern g1="dollar,S" 	g2="nine" 	k="20" />
<hkern g1="dollar,S" 	g2="one" 	k="20" />
<hkern g1="dollar,S" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="questiondown" 	k="20" />
<hkern g1="dollar,S" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="dollar,S" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="dollar,S" 	g2="seven" 	k="20" />
<hkern g1="dollar,S" 	g2="three" 	k="14" />
<hkern g1="dollar,S" 	g2="underscore" 	k="41" />
<hkern g1="dollar,S" 	g2="v" 	k="20" />
<hkern g1="dollar,S" 	g2="x" 	k="20" />
<hkern g1="T" 	g2="J" 	k="14" />
<hkern g1="T" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="V" 	k="-35" />
<hkern g1="T" 	g2="X" 	k="27" />
<hkern g1="T" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-82" />
<hkern g1="T" 	g2="backslash" 	k="-41" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="degree" 	k="-61" />
<hkern g1="T" 	g2="eight" 	k="10" />
<hkern g1="T" 	g2="exclamdown" 	k="41" />
<hkern g1="T" 	g2="four" 	k="164" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="T" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="68" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="one" 	k="-82" />
<hkern g1="T" 	g2="paragraph" 	k="-86" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="66" />
<hkern g1="T" 	g2="question" 	k="-61" />
<hkern g1="T" 	g2="questiondown" 	k="106" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-66" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-76" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-25" />
<hkern g1="T" 	g2="s" 	k="68" />
<hkern g1="T" 	g2="seven" 	k="-61" />
<hkern g1="T" 	g2="slash" 	k="41" />
<hkern g1="T" 	g2="three" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-102" />
<hkern g1="T" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="37" />
<hkern g1="T" 	g2="underscore" 	k="14" />
<hkern g1="T" 	g2="x" 	k="6" />
<hkern g1="T" 	g2="z" 	k="41" />
<hkern g1="T" 	g2="zero,six" 	k="31" />
<hkern g1="T" 	g2="AE" 	k="82" />
<hkern g1="T" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="T" 	g2="ae" 	k="61" />
<hkern g1="T" 	g2="ampersand" 	k="61" />
<hkern g1="T" 	g2="five" 	k="-14" />
<hkern g1="T" 	g2="two" 	k="-41" />
<hkern g1="T" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="T" 	g2="bracketright,braceright" 	k="-23" />
<hkern g1="T" 	g2="j" 	k="20" />
<hkern g1="T" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="T" 	g2="exclam" 	k="-41" />
<hkern g1="T" 	g2="parenleft" 	k="25" />
<hkern g1="Thorn" 	g2="J" 	k="137" />
<hkern g1="Thorn" 	g2="T" 	k="35" />
<hkern g1="Thorn" 	g2="V" 	k="20" />
<hkern g1="Thorn" 	g2="X" 	k="61" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="one" 	k="41" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="47" />
<hkern g1="Thorn" 	g2="question" 	k="61" />
<hkern g1="Thorn" 	g2="questiondown" 	k="51" />
<hkern g1="Thorn" 	g2="slash" 	k="61" />
<hkern g1="Thorn" 	g2="uniFB02" 	k="-10" />
<hkern g1="Thorn" 	g2="three" 	k="41" />
<hkern g1="Thorn" 	g2="trademark" 	k="6" />
<hkern g1="Thorn" 	g2="underscore" 	k="61" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="AE" 	k="61" />
<hkern g1="Thorn" 	g2="ae" 	k="20" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="two" 	k="51" />
<hkern g1="Thorn" 	g2="j" 	k="20" />
<hkern g1="Thorn" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="31" />
<hkern g1="Thorn" 	g2="Z" 	k="37" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="J" 	k="51" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="X" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="questiondown" 	k="14" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="slash" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="underscore" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="x" 	k="14" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="AE" 	k="31" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="j" 	k="10" />
<hkern g1="V" 	g2="J" 	k="51" />
<hkern g1="V" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="45" />
<hkern g1="V" 	g2="T" 	k="-35" />
<hkern g1="V" 	g2="X" 	k="47" />
<hkern g1="V" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-68" />
<hkern g1="V" 	g2="backslash" 	k="-61" />
<hkern g1="V" 	g2="colon,semicolon" 	k="47" />
<hkern g1="V" 	g2="degree" 	k="-55" />
<hkern g1="V" 	g2="eight" 	k="35" />
<hkern g1="V" 	g2="exclamdown" 	k="61" />
<hkern g1="V" 	g2="four" 	k="150" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="V" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="47" />
<hkern g1="V" 	g2="nine" 	k="14" />
<hkern g1="V" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="V" 	g2="one" 	k="-61" />
<hkern g1="V" 	g2="paragraph" 	k="-45" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="76" />
<hkern g1="V" 	g2="questiondown" 	k="147" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-39" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-66" />
<hkern g1="V" 	g2="s" 	k="119" />
<hkern g1="V" 	g2="seven" 	k="-20" />
<hkern g1="V" 	g2="slash" 	k="82" />
<hkern g1="V" 	g2="uniFB02" 	k="6" />
<hkern g1="V" 	g2="three" 	k="-6" />
<hkern g1="V" 	g2="trademark" 	k="-82" />
<hkern g1="V" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="66" />
<hkern g1="V" 	g2="underscore" 	k="20" />
<hkern g1="V" 	g2="v" 	k="35" />
<hkern g1="V" 	g2="x" 	k="68" />
<hkern g1="V" 	g2="z" 	k="57" />
<hkern g1="V" 	g2="zero,six" 	k="51" />
<hkern g1="V" 	g2="AE" 	k="76" />
<hkern g1="V" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="45" />
<hkern g1="V" 	g2="dollar,S" 	k="31" />
<hkern g1="V" 	g2="ae" 	k="113" />
<hkern g1="V" 	g2="ampersand" 	k="55" />
<hkern g1="V" 	g2="five" 	k="16" />
<hkern g1="V" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="V" 	g2="parenright" 	k="-16" />
<hkern g1="V" 	g2="bracketright,braceright" 	k="-47" />
<hkern g1="V" 	g2="j" 	k="41" />
<hkern g1="V" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="72" />
<hkern g1="V" 	g2="Z" 	k="20" />
<hkern g1="V" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="V" 	g2="exclam" 	k="-20" />
<hkern g1="X" 	g2="J" 	k="8" />
<hkern g1="X" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="X" 	g2="T" 	k="27" />
<hkern g1="X" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="X" 	g2="V" 	k="47" />
<hkern g1="X" 	g2="X" 	k="39" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="X" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="X" 	g2="colon,semicolon" 	k="14" />
<hkern g1="X" 	g2="degree" 	k="41" />
<hkern g1="X" 	g2="eight" 	k="45" />
<hkern g1="X" 	g2="exclamdown" 	k="20" />
<hkern g1="X" 	g2="four" 	k="109" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="X" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="37" />
<hkern g1="X" 	g2="nine" 	k="35" />
<hkern g1="X" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="X" 	g2="one" 	k="-20" />
<hkern g1="X" 	g2="question" 	k="61" />
<hkern g1="X" 	g2="questiondown" 	k="-6" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="2" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="-14" />
<hkern g1="X" 	g2="s" 	k="41" />
<hkern g1="X" 	g2="seven" 	k="20" />
<hkern g1="X" 	g2="slash" 	k="-35" />
<hkern g1="X" 	g2="uniFB02" 	k="47" />
<hkern g1="X" 	g2="trademark" 	k="-61" />
<hkern g1="X" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="55" />
<hkern g1="X" 	g2="underscore" 	k="-96" />
<hkern g1="X" 	g2="v" 	k="57" />
<hkern g1="X" 	g2="x" 	k="78" />
<hkern g1="X" 	g2="z" 	k="27" />
<hkern g1="X" 	g2="zero,six" 	k="68" />
<hkern g1="X" 	g2="AE" 	k="68" />
<hkern g1="X" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="31" />
<hkern g1="X" 	g2="dollar,S" 	k="27" />
<hkern g1="X" 	g2="ae" 	k="35" />
<hkern g1="X" 	g2="ampersand" 	k="41" />
<hkern g1="X" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="X" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="X" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="X" 	g2="Z" 	k="20" />
<hkern g1="X" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="47" />
<hkern g1="X" 	g2="parenleft" 	k="18" />
<hkern g1="Z" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="Z" 	g2="T" 	k="-20" />
<hkern g1="Z" 	g2="X" 	k="20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="Z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="Z" 	g2="eight" 	k="6" />
<hkern g1="Z" 	g2="exclamdown" 	k="-6" />
<hkern g1="Z" 	g2="four" 	k="63" />
<hkern g1="Z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="25" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="Z" 	g2="one" 	k="-41" />
<hkern g1="Z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="Z" 	g2="question" 	k="-6" />
<hkern g1="Z" 	g2="slash" 	k="-14" />
<hkern g1="Z" 	g2="uniFB02" 	k="14" />
<hkern g1="Z" 	g2="trademark" 	k="-61" />
<hkern g1="Z" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="14" />
<hkern g1="Z" 	g2="underscore" 	k="-61" />
<hkern g1="Z" 	g2="v" 	k="25" />
<hkern g1="Z" 	g2="zero,six" 	k="39" />
<hkern g1="Z" 	g2="ae" 	k="-4" />
<hkern g1="Z" 	g2="ampersand" 	k="25" />
<hkern g1="Z" 	g2="five" 	k="6" />
<hkern g1="Z" 	g2="bracketright,braceright" 	k="-16" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="16" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="84" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="137" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="one" 	k="14" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="question" 	k="-29" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="dollar,S" 	k="33" />
<hkern g1="z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="z" 	g2="backslash" 	k="127" />
<hkern g1="z" 	g2="four" 	k="25" />
<hkern g1="z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="27" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="z" 	g2="one" 	k="25" />
<hkern g1="z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-6" />
<hkern g1="z" 	g2="uniFB02" 	k="-4" />
<hkern g1="z" 	g2="underscore" 	k="-45" />
<hkern g1="z" 	g2="v" 	k="20" />
<hkern g1="z" 	g2="x" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="14" />
<hkern g1="z" 	g2="two" 	k="14" />
<hkern g1="ampersand" 	g2="AE" 	k="-25" />
<hkern g1="ampersand" 	g2="J" 	k="-12" />
<hkern g1="ampersand" 	g2="T" 	k="61" />
<hkern g1="ampersand" 	g2="V" 	k="41" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="25" />
<hkern g1="ampersand" 	g2="Z" 	k="-25" />
<hkern g1="ampersand" 	g2="ae" 	k="-20" />
<hkern g1="ampersand" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="ampersand" 	g2="uniFB02" 	k="-20" />
<hkern g1="ampersand" 	g2="v" 	k="27" />
<hkern g1="ampersand" 	g2="x" 	k="6" />
<hkern g1="currency" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-14" />
<hkern g1="degree" 	g2="AE" 	k="190" />
<hkern g1="degree" 	g2="J" 	k="20" />
<hkern g1="degree" 	g2="T" 	k="-61" />
<hkern g1="degree" 	g2="V" 	k="-55" />
<hkern g1="degree" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="degree" 	g2="uniFB02" 	k="-41" />
<hkern g1="degree" 	g2="v" 	k="-76" />
<hkern g1="degree" 	g2="x" 	k="-20" />
<hkern g1="degree" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="degree" 	g2="dollar,S" 	k="-41" />
<hkern g1="degree" 	g2="X" 	k="41" />
<hkern g1="degree" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="degree" 	g2="s" 	k="6" />
<hkern g1="percent" 	g2="ae" 	k="-25" />
<hkern g1="percent" 	g2="asterisk,ordfeminine,ordmasculine" 	k="102" />
<hkern g1="percent" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-47" />
<hkern g1="section" 	g2="ae" 	k="-14" />
<hkern g1="section" 	g2="uniFB02" 	k="-14" />
<hkern g1="section" 	g2="v" 	k="-14" />
<hkern g1="section" 	g2="x" 	k="-14" />
<hkern g1="section" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-14" />
<hkern g1="section" 	g2="s" 	k="-14" />
<hkern g1="section" 	g2="j" 	k="-14" />
<hkern g1="section" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="-14" />
<hkern g1="section" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-14" />
<hkern g1="section" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="-14" />
<hkern g1="section" 	g2="z" 	k="-14" />
<hkern g1="trademark" 	g2="AE" 	k="41" />
<hkern g1="trademark" 	g2="J" 	k="20" />
<hkern g1="trademark" 	g2="ae" 	k="-55" />
<hkern g1="trademark" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="trademark" 	g2="uniFB02" 	k="-68" />
<hkern g1="trademark" 	g2="v" 	k="-25" />
<hkern g1="trademark" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-55" />
<hkern g1="trademark" 	g2="s" 	k="-55" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="AE" 	k="190" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="dollar,S" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="T" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="V" 	k="-68" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="X" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Z" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="ampersand" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="currency,Euro" 	k="-14" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="degree" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="exclam" 	k="-14" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="four" 	k="27" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="guillemotright,guilsinglright" 	k="-27" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="nine" 	k="-66" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="one" 	k="-55" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="paragraph" 	k="-76" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="percent" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="68" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="question" 	k="-66" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="questiondown" 	k="168" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="seven" 	k="-74" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="slash" 	k="281" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="uniFB02" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="three" 	k="-4" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="trademark" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="two" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="underscore" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="v" 	k="-86" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="x" 	k="-27" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="z" 	k="-35" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="zero,six" 	k="-14" />
<hkern g1="backslash" 	g2="AE" 	k="-61" />
<hkern g1="backslash" 	g2="J" 	k="-102" />
<hkern g1="backslash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="backslash" 	g2="T" 	k="41" />
<hkern g1="backslash" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="backslash" 	g2="V" 	k="82" />
<hkern g1="backslash" 	g2="X" 	k="-41" />
<hkern g1="backslash" 	g2="asterisk,ordfeminine,ordmasculine" 	k="281" />
<hkern g1="backslash" 	g2="j" 	k="-246" />
<hkern g1="backslash" 	g2="v" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="J" 	k="-16" />
<hkern g1="bracketleft,braceleft" 	g2="T" 	k="-23" />
<hkern g1="bracketleft,braceleft" 	g2="V" 	k="-47" />
<hkern g1="bracketleft,braceleft" 	g2="X" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-205" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="33" />
<hkern g1="bracketleft,braceleft" 	g2="ae" 	k="18" />
<hkern g1="bracketright" 	g2="J" 	k="25" />
<hkern g1="bracketright" 	g2="T" 	k="33" />
<hkern g1="bracketright" 	g2="X" 	k="39" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-14" />
<hkern g1="colon,semicolon" 	g2="T" 	k="20" />
<hkern g1="colon,semicolon" 	g2="V" 	k="47" />
<hkern g1="colon,semicolon" 	g2="X" 	k="14" />
<hkern g1="colon,semicolon" 	g2="v" 	k="-14" />
<hkern g1="colon,semicolon" 	g2="uniFB02" 	k="-16" />
<hkern g1="colon,semicolon" 	g2="x" 	k="20" />
<hkern g1="exclam" 	g2="J" 	k="25" />
<hkern g1="exclam" 	g2="T" 	k="-41" />
<hkern g1="exclam" 	g2="V" 	k="-20" />
<hkern g1="exclam" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-14" />
<hkern g1="exclamdown" 	g2="AE" 	k="-20" />
<hkern g1="exclamdown" 	g2="J" 	k="-41" />
<hkern g1="exclamdown" 	g2="T" 	k="41" />
<hkern g1="exclamdown" 	g2="V" 	k="61" />
<hkern g1="exclamdown" 	g2="X" 	k="20" />
<hkern g1="exclamdown" 	g2="j" 	k="-139" />
<hkern g1="exclamdown" 	g2="uniFB02" 	k="-12" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="AE" 	k="-25" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="16" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="uniFB02" 	k="-6" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="AE" 	k="37" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="J" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-14" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="T" 	k="68" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="V" 	k="47" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="X" 	k="37" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="v" 	k="27" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="uniFB02" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="x" 	k="47" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="dollar,S" 	k="35" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="z" 	k="27" />
<hkern g1="parenleft" 	g2="J" 	k="18" />
<hkern g1="parenleft" 	g2="j" 	k="-182" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="AE" 	k="-68" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="J" 	k="-14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="66" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="76" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="68" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="68" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="ae" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uniFB02" 	k="14" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="dollar,S" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="25" />
<hkern g1="question" 	g2="AE" 	k="88" />
<hkern g1="question" 	g2="J" 	k="92" />
<hkern g1="question" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="question" 	g2="V" 	k="27" />
<hkern g1="question" 	g2="X" 	k="37" />
<hkern g1="question" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-25" />
<hkern g1="question" 	g2="v" 	k="-20" />
<hkern g1="question" 	g2="Z" 	k="20" />
<hkern g1="question" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="question" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="questiondown" 	g2="AE" 	k="-29" />
<hkern g1="questiondown" 	g2="J" 	k="-123" />
<hkern g1="questiondown" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="109" />
<hkern g1="questiondown" 	g2="T" 	k="119" />
<hkern g1="questiondown" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="72" />
<hkern g1="questiondown" 	g2="V" 	k="139" />
<hkern g1="questiondown" 	g2="X" 	k="-16" />
<hkern g1="questiondown" 	g2="asterisk,ordfeminine,ordmasculine" 	k="170" />
<hkern g1="questiondown" 	g2="j" 	k="-143" />
<hkern g1="questiondown" 	g2="v" 	k="98" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="questiondown" 	g2="ae" 	k="35" />
<hkern g1="questiondown" 	g2="uniFB02" 	k="25" />
<hkern g1="questiondown" 	g2="x" 	k="-16" />
<hkern g1="questiondown" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="questiondown" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="102" />
<hkern g1="questiondown" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="47" />
<hkern g1="questiondown" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="25" />
<hkern g1="questiondown" 	g2="s" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="68" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="16" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-66" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="2" />
<hkern g1="quoteleft,quotedblleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-68" />
<hkern g1="quoteleft,quotedblleft" 	g2="ae" 	k="12" />
<hkern g1="quoteleft,quotedblleft" 	g2="uniFB02" 	k="-37" />
<hkern g1="quoteleft,quotedblleft" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="68" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="6" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-76" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-66" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="-14" />
<hkern g1="quoteright,quotedblright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="v" 	k="-35" />
<hkern g1="quoteright,quotedblright" 	g2="ae" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="uniFB02" 	k="-6" />
<hkern g1="quoteright,quotedblright" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="55" />
<hkern g1="quoteright,quotedblright" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="88" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-25" />
<hkern g1="quotedbl,quotesingle" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="slash" 	g2="AE" 	k="82" />
<hkern g1="slash" 	g2="J" 	k="20" />
<hkern g1="slash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="T" 	k="-61" />
<hkern g1="slash" 	g2="V" 	k="-61" />
<hkern g1="slash" 	g2="j" 	k="-6" />
<hkern g1="slash" 	g2="v" 	k="61" />
<hkern g1="slash" 	g2="ae" 	k="137" />
<hkern g1="slash" 	g2="uniFB02" 	k="55" />
<hkern g1="slash" 	g2="x" 	k="121" />
<hkern g1="slash" 	g2="dollar,S" 	k="41" />
<hkern g1="slash" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="137" />
<hkern g1="slash" 	g2="z" 	k="137" />
<hkern g1="slash" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="137" />
<hkern g1="slash" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="slash" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="slash" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="137" />
<hkern g1="slash" 	g2="s" 	k="137" />
<hkern g1="underscore" 	g2="AE" 	k="-117" />
<hkern g1="underscore" 	g2="J" 	k="-178" />
<hkern g1="underscore" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="underscore" 	g2="T" 	k="14" />
<hkern g1="underscore" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="underscore" 	g2="V" 	k="20" />
<hkern g1="underscore" 	g2="X" 	k="-96" />
<hkern g1="underscore" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="underscore" 	g2="j" 	k="-221" />
<hkern g1="underscore" 	g2="v" 	k="27" />
<hkern g1="underscore" 	g2="Y,Yacute,Ydieresis" 	k="-6" />
<hkern g1="underscore" 	g2="ae" 	k="20" />
<hkern g1="underscore" 	g2="uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="x" 	k="-76" />
<hkern g1="underscore" 	g2="Z" 	k="-41" />
<hkern g1="underscore" 	g2="dollar,S" 	k="-41" />
<hkern g1="underscore" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="55" />
<hkern g1="underscore" 	g2="z" 	k="-53" />
<hkern g1="underscore" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="55" />
<hkern g1="underscore" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="underscore" 	g2="s" 	k="-20" />
<hkern g1="underscore" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="-61" />
<hkern g1="c,cent,ccedilla" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="27" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="45" />
<hkern g1="c,cent,ccedilla" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-27" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="96" />
<hkern g1="c,cent,ccedilla" 	g2="bracketright,braceright" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="bracketleft" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-14" />
<hkern g1="c,cent,ccedilla" 	g2="degree" 	k="-35" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="four" 	k="39" />
<hkern g1="c,cent,ccedilla" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="nine" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="47" />
<hkern g1="c,cent,ccedilla" 	g2="parenright" 	k="45" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="55" />
<hkern g1="c,cent,ccedilla" 	g2="questiondown" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="quoteleft,quotedblleft" 	k="-37" />
<hkern g1="c,cent,ccedilla" 	g2="quoteright,quotedblright" 	k="-47" />
<hkern g1="c,cent,ccedilla" 	g2="section" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="seven" 	k="25" />
<hkern g1="c,cent,ccedilla" 	g2="uniFB02" 	k="-14" />
<hkern g1="c,cent,ccedilla" 	g2="three" 	k="35" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="27" />
<hkern g1="c,cent,ccedilla" 	g2="two" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="v" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="x" 	k="35" />
<hkern g1="c,cent,ccedilla" 	g2="z" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="zero,six" 	k="14" />
<hkern g1="c,cent,ccedilla" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="c,cent,ccedilla" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="16" />
<hkern g1="c,cent,ccedilla" 	g2="V" 	k="29" />
<hkern g1="c,cent,ccedilla" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="137" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="degree" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="nine" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="one" 	k="55" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="96" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="questiondown" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="4" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="seven" 	k="57" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="three" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="two" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="76" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="ae" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="paragraph" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="percent" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="45" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="underscore" 	k="14" />
<hkern g1="eth" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="eth" 	g2="backslash" 	k="20" />
<hkern g1="eth" 	g2="degree" 	k="-41" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="37" />
<hkern g1="eth" 	g2="question" 	k="14" />
<hkern g1="eth" 	g2="questiondown" 	k="14" />
<hkern g1="eth" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="eth" 	g2="three" 	k="14" />
<hkern g1="eth" 	g2="trademark" 	k="-55" />
<hkern g1="eth" 	g2="zero,six" 	k="-14" />
<hkern g1="eth" 	g2="underscore" 	k="41" />
<hkern g1="f" 	g2="ampersand" 	k="27" />
<hkern g1="f" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-100" />
<hkern g1="f" 	g2="backslash" 	k="-129" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-96" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-45" />
<hkern g1="f" 	g2="degree" 	k="-76" />
<hkern g1="f" 	g2="eight" 	k="-25" />
<hkern g1="f" 	g2="four" 	k="55" />
<hkern g1="f" 	g2="nine" 	k="-59" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="f" 	g2="one" 	k="-100" />
<hkern g1="f" 	g2="parenright" 	k="-63" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="f" 	g2="question" 	k="-55" />
<hkern g1="f" 	g2="questiondown" 	k="27" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-98" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-145" />
<hkern g1="f" 	g2="seven" 	k="-86" />
<hkern g1="f" 	g2="three" 	k="-45" />
<hkern g1="f" 	g2="trademark" 	k="-156" />
<hkern g1="f" 	g2="two" 	k="-35" />
<hkern g1="f" 	g2="v" 	k="-20" />
<hkern g1="f" 	g2="zero,six" 	k="-25" />
<hkern g1="f" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="f" 	g2="ae" 	k="20" />
<hkern g1="f" 	g2="paragraph" 	k="-86" />
<hkern g1="f" 	g2="percent" 	k="-35" />
<hkern g1="f" 	g2="underscore" 	k="37" />
<hkern g1="f" 	g2="exclam" 	k="-6" />
<hkern g1="f" 	g2="five" 	k="-25" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="f" 	g2="j" 	k="-88" />
<hkern g1="f" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="f" 	g2="slash" 	k="61" />
<hkern g1="k" 	g2="ampersand" 	k="16" />
<hkern g1="k" 	g2="backslash" 	k="82" />
<hkern g1="k" 	g2="bracketleft" 	k="18" />
<hkern g1="k" 	g2="eight" 	k="16" />
<hkern g1="k" 	g2="four" 	k="61" />
<hkern g1="k" 	g2="nine" 	k="-20" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="k" 	g2="one" 	k="-16" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="k" 	g2="question" 	k="-16" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-6" />
<hkern g1="k" 	g2="seven" 	k="4" />
<hkern g1="k" 	g2="three" 	k="-20" />
<hkern g1="k" 	g2="two" 	k="-37" />
<hkern g1="k" 	g2="v" 	k="57" />
<hkern g1="k" 	g2="x" 	k="41" />
<hkern g1="k" 	g2="z" 	k="25" />
<hkern g1="k" 	g2="zero,six" 	k="20" />
<hkern g1="k" 	g2="V" 	k="18" />
<hkern g1="k" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="47" />
<hkern g1="k" 	g2="X" 	k="14" />
<hkern g1="k" 	g2="ae" 	k="20" />
<hkern g1="k" 	g2="underscore" 	k="-127" />
<hkern g1="k" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="14" />
<hkern g1="k" 	g2="s" 	k="35" />
<hkern g1="k" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="d,uniFB02" 	g2="uniFB02" 	k="-27" />
<hkern g1="l" 	g2="asterisk,ordfeminine,ordmasculine" 	k="57" />
<hkern g1="l" 	g2="backslash" 	k="88" />
<hkern g1="l" 	g2="colon,semicolon" 	k="-35" />
<hkern g1="l" 	g2="degree" 	k="41" />
<hkern g1="l" 	g2="eight" 	k="-14" />
<hkern g1="l" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="l" 	g2="one" 	k="41" />
<hkern g1="l" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-55" />
<hkern g1="l" 	g2="question" 	k="41" />
<hkern g1="l" 	g2="questiondown" 	k="-14" />
<hkern g1="l" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="l" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="l" 	g2="seven" 	k="31" />
<hkern g1="l" 	g2="uniFB02" 	k="10" />
<hkern g1="l" 	g2="three" 	k="-20" />
<hkern g1="l" 	g2="two" 	k="-35" />
<hkern g1="l" 	g2="v" 	k="51" />
<hkern g1="l" 	g2="x" 	k="-14" />
<hkern g1="l" 	g2="zero,six" 	k="-4" />
<hkern g1="l" 	g2="paragraph" 	k="16" />
<hkern g1="l" 	g2="underscore" 	k="-125" />
<hkern g1="l" 	g2="five" 	k="-6" />
<hkern g1="l" 	g2="slash" 	k="-53" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk,ordfeminine,ordmasculine" 	k="55" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="137" />
<hkern g1="h,m,n,ntilde" 	g2="degree" 	k="39" />
<hkern g1="h,m,n,ntilde" 	g2="nine" 	k="25" />
<hkern g1="h,m,n,ntilde" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-6" />
<hkern g1="h,m,n,ntilde" 	g2="one" 	k="45" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="96" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="h,m,n,ntilde" 	g2="seven" 	k="45" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="55" />
<hkern g1="h,m,n,ntilde" 	g2="two" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="paragraph" 	k="45" />
<hkern g1="h,m,n,ntilde" 	g2="percent" 	k="57" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk,ordfeminine,ordmasculine" 	k="35" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="137" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="degree" 	k="35" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="nine" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="one" 	k="55" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="96" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="questiondown" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="4" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="seven" 	k="55" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="three" 	k="47" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="trademark" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="two" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="37" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="16" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="39" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V" 	k="82" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="23" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="ae" 	k="10" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="paragraph" 	k="47" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="percent" 	k="55" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="underscore" 	k="55" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="J" 	k="66" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="113" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="AE" 	k="23" />
<hkern g1="r" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="r" 	g2="ampersand" 	k="66" />
<hkern g1="r" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="r" 	g2="backslash" 	k="41" />
<hkern g1="r" 	g2="bracketright,braceright" 	k="25" />
<hkern g1="r" 	g2="bracketleft" 	k="33" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="r" 	g2="degree" 	k="-63" />
<hkern g1="r" 	g2="eight" 	k="-4" />
<hkern g1="r" 	g2="four" 	k="55" />
<hkern g1="r" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="14" />
<hkern g1="r" 	g2="nine" 	k="-16" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="r" 	g2="one" 	k="12" />
<hkern g1="r" 	g2="parenright" 	k="18" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="r" 	g2="question" 	k="-16" />
<hkern g1="r" 	g2="questiondown" 	k="76" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-68" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-68" />
<hkern g1="r" 	g2="section" 	k="-20" />
<hkern g1="r" 	g2="seven" 	k="-45" />
<hkern g1="r" 	g2="uniFB02" 	k="-31" />
<hkern g1="r" 	g2="three" 	k="14" />
<hkern g1="r" 	g2="two" 	k="-6" />
<hkern g1="r" 	g2="v" 	k="-31" />
<hkern g1="r" 	g2="x" 	k="14" />
<hkern g1="r" 	g2="zero,six" 	k="-6" />
<hkern g1="r" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="r" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="39" />
<hkern g1="r" 	g2="V" 	k="55" />
<hkern g1="r" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="18" />
<hkern g1="r" 	g2="X" 	k="86" />
<hkern g1="r" 	g2="ae" 	k="16" />
<hkern g1="r" 	g2="paragraph" 	k="-61" />
<hkern g1="r" 	g2="underscore" 	k="27" />
<hkern g1="r" 	g2="five" 	k="-4" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="-6" />
<hkern g1="r" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="14" />
<hkern g1="r" 	g2="slash" 	k="82" />
<hkern g1="r" 	g2="s" 	k="6" />
<hkern g1="r" 	g2="AE" 	k="82" />
<hkern g1="r" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="25" />
<hkern g1="r" 	g2="dollar,S" 	k="33" />
<hkern g1="r" 	g2="parenleft" 	k="25" />
<hkern g1="s" 	g2="backslash" 	k="123" />
<hkern g1="s" 	g2="degree" 	k="27" />
<hkern g1="s" 	g2="nine" 	k="10" />
<hkern g1="s" 	g2="one" 	k="14" />
<hkern g1="s" 	g2="question" 	k="35" />
<hkern g1="s" 	g2="seven" 	k="55" />
<hkern g1="s" 	g2="three" 	k="20" />
<hkern g1="s" 	g2="trademark" 	k="35" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="s" 	g2="v" 	k="20" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="paragraph" 	k="47" />
<hkern g1="t" 	g2="ampersand" 	k="16" />
<hkern g1="t" 	g2="asterisk,ordfeminine,ordmasculine" 	k="35" />
<hkern g1="t" 	g2="backslash" 	k="137" />
<hkern g1="t" 	g2="degree" 	k="10" />
<hkern g1="t" 	g2="eight" 	k="14" />
<hkern g1="t" 	g2="four" 	k="59" />
<hkern g1="t" 	g2="nine" 	k="25" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="t" 	g2="one" 	k="39" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="question" 	k="35" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="t" 	g2="section" 	k="18" />
<hkern g1="t" 	g2="seven" 	k="45" />
<hkern g1="t" 	g2="uniFB02" 	k="27" />
<hkern g1="t" 	g2="trademark" 	k="55" />
<hkern g1="t" 	g2="two" 	k="14" />
<hkern g1="t" 	g2="v" 	k="20" />
<hkern g1="t" 	g2="zero,six" 	k="35" />
<hkern g1="t" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="t" 	g2="paragraph" 	k="41" />
<hkern g1="t" 	g2="underscore" 	k="-66" />
<hkern g1="t" 	g2="five" 	k="14" />
<hkern g1="v" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="12" />
<hkern g1="v" 	g2="ampersand" 	k="47" />
<hkern g1="v" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-86" />
<hkern g1="v" 	g2="backslash" 	k="41" />
<hkern g1="v" 	g2="colon,semicolon" 	k="-14" />
<hkern g1="v" 	g2="degree" 	k="-76" />
<hkern g1="v" 	g2="eight" 	k="-14" />
<hkern g1="v" 	g2="four" 	k="57" />
<hkern g1="v" 	g2="nine" 	k="-61" />
<hkern g1="v" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="v" 	g2="one" 	k="-23" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="68" />
<hkern g1="v" 	g2="question" 	k="-68" />
<hkern g1="v" 	g2="questiondown" 	k="68" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-68" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-35" />
<hkern g1="v" 	g2="seven" 	k="-31" />
<hkern g1="v" 	g2="uniFB02" 	k="-31" />
<hkern g1="v" 	g2="three" 	k="4" />
<hkern g1="v" 	g2="two" 	k="-23" />
<hkern g1="v" 	g2="v" 	k="16" />
<hkern g1="v" 	g2="x" 	k="61" />
<hkern g1="v" 	g2="z" 	k="20" />
<hkern g1="v" 	g2="zero,six" 	k="-14" />
<hkern g1="v" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="v" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="27" />
<hkern g1="v" 	g2="X" 	k="61" />
<hkern g1="v" 	g2="ae" 	k="35" />
<hkern g1="v" 	g2="paragraph" 	k="-61" />
<hkern g1="v" 	g2="underscore" 	k="27" />
<hkern g1="v" 	g2="five" 	k="-14" />
<hkern g1="v" 	g2="j" 	k="31" />
<hkern g1="v" 	g2="slash" 	k="63" />
<hkern g1="v" 	g2="s" 	k="14" />
<hkern g1="v" 	g2="AE" 	k="55" />
<hkern g1="v" 	g2="dollar,S" 	k="14" />
<hkern g1="w" 	g2="backslash" 	k="102" />
<hkern g1="w" 	g2="one" 	k="14" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="25" />
<hkern g1="w" 	g2="three" 	k="20" />
<hkern g1="w" 	g2="two" 	k="14" />
<hkern g1="w" 	g2="underscore" 	k="55" />
<hkern g1="x" 	g2="ampersand" 	k="41" />
<hkern g1="x" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-27" />
<hkern g1="x" 	g2="backslash" 	k="96" />
<hkern g1="x" 	g2="colon,semicolon" 	k="20" />
<hkern g1="x" 	g2="degree" 	k="-20" />
<hkern g1="x" 	g2="eight" 	k="6" />
<hkern g1="x" 	g2="four" 	k="68" />
<hkern g1="x" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="x" 	g2="one" 	k="-16" />
<hkern g1="x" 	g2="question" 	k="-23" />
<hkern g1="x" 	g2="seven" 	k="14" />
<hkern g1="x" 	g2="two" 	k="-2" />
<hkern g1="x" 	g2="v" 	k="61" />
<hkern g1="x" 	g2="z" 	k="20" />
<hkern g1="x" 	g2="zero,six" 	k="20" />
<hkern g1="x" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="x" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="25" />
<hkern g1="x" 	g2="V" 	k="45" />
<hkern g1="x" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="47" />
<hkern g1="x" 	g2="ae" 	k="20" />
<hkern g1="x" 	g2="paragraph" 	k="-6" />
<hkern g1="x" 	g2="underscore" 	k="-76" />
<hkern g1="x" 	g2="j" 	k="-12" />
<hkern g1="x" 	g2="slash" 	k="-20" />
<hkern g1="x" 	g2="s" 	k="20" />
<hkern g1="x" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="x" 	g2="dollar,S" 	k="18" />
</font>
</defs></svg> 