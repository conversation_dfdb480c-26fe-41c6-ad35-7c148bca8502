<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="montserrat_alternateslight" horiz-adv-x="1382" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="526" />
<glyph unicode="&#xfb01;" horiz-adv-x="1347" d="M217 0v1233q0 136 77.5 215.5t217.5 79.5q137 0 213 -70l-41 -78q-66 60 -166 60q-201 0 -201 -215v-156h342v-88h-340v-981h-102zM997 1411q0 34 24.5 59t59.5 25t59.5 -24t24.5 -58q0 -35 -24.5 -59.5t-59.5 -24.5t-59.5 24t-24.5 58zM1030 0v1069h103v-1069h-103z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1228" d="M41 981v88h197v72q0 190 92.5 288.5t257.5 98.5q168 0 258 -98t90 -289v-1053q0 -104 48 -158.5t142 -54.5q56 0 103 19l8 -87q-62 -20 -123 -20q-135 0 -207.5 76.5t-72.5 210.5v1075q0 147 -63.5 219t-182.5 72q-248 0 -248 -291v-80h340v-88h-340v-981h-102v981h-197z " />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="526" />
<glyph unicode=" "  horiz-adv-x="526" />
<glyph unicode="&#x09;" horiz-adv-x="526" />
<glyph unicode="&#xa0;" horiz-adv-x="526" />
<glyph unicode="!" horiz-adv-x="516" d="M174 76q0 35 24.5 59.5t59.5 24.5q37 0 61.5 -24.5t24.5 -59.5q0 -34 -25 -59t-61 -25q-35 0 -59.5 25t-24.5 59zM199 1434h118l-20 -1022h-78z" />
<glyph unicode="&#x22;" horiz-adv-x="731" d="M152 1434h94l-8 -529h-78zM483 1434h95l-9 -529h-77z" />
<glyph unicode="#" horiz-adv-x="1413" d="M66 406v81h305l57 459h-299v82h309l49 406h84l-51 -406h451l49 406h82l-49 -406h295v-82h-306l-57 -459h297v-81h-307l-49 -406h-82l49 406h-449l-51 -406h-82l49 406h-294zM455 487h448l58 459h-451z" />
<glyph unicode="$" horiz-adv-x="1247" d="M104 184l48 78q71 -74 189.5 -124t250.5 -56v594q-57 14 -95.5 25t-90 29.5t-85.5 38t-70.5 49t-58 63.5t-35.5 80.5t-14 101.5q0 153 114 259.5t335 119.5v237h76v-235q116 -3 226 -37t189 -92l-39 -84q-169 114 -376 121v-594q45 -11 70.5 -17.5t67.5 -18.5t67.5 -22 t61.5 -25.5t58.5 -31.5t49 -37t42.5 -45t30.5 -53t21 -64t6.5 -75q0 -156 -121 -262.5t-354 -116.5v-236h-76v236q-150 6 -282 59t-206 135zM248 1065q0 -53 19 -95.5t48 -71t77 -53t92.5 -39t107.5 -30.5v574q-170 -11 -257 -90t-87 -195zM668 82q184 9 277 86t93 194 q0 55 -21 99t-51.5 72.5t-84 53.5t-98.5 39t-115 31v-575z" />
<glyph unicode="%" horiz-adv-x="1673" d="M92 1055q0 174 88 280.5t228 106.5t228.5 -106.5t88.5 -280.5t-88.5 -280.5t-228.5 -106.5t-228 106.5t-88 280.5zM170 1055q0 -146 65.5 -232t172.5 -86q108 0 173.5 85t65.5 233t-65 232.5t-174 84.5q-107 0 -172.5 -85.5t-65.5 -231.5zM303 0l979 1434h88l-979 -1434 h-88zM948 379q0 174 89 280.5t229 106.5t227.5 -106.5t87.5 -280.5t-87.5 -280.5t-227.5 -106.5t-229 106.5t-89 280.5zM1026 379q0 -148 65.5 -233t174.5 -85q107 0 172 86t65 232t-65 231.5t-172 85.5q-109 0 -174.5 -85t-65.5 -232z" />
<glyph unicode="&#x26;" horiz-adv-x="1341" d="M106 348q0 125 84 228.5t287 222.5l-2 4q-106 112 -145 186.5t-39 159.5q0 133 92 213t248 80q145 0 230 -73t85 -200q0 -106 -72.5 -191t-255.5 -192l424 -448q70 124 103 291l84 -27q-41 -199 -123 -330l213 -225l-62 -67l-206 217q-177 -207 -478 -207 q-205 0 -336 99.5t-131 258.5zM207 356q0 -126 103 -203t268 -77q262 0 411 186l-452 477q-188 -111 -259 -195t-71 -188zM387 1149q0 -68 35.5 -131t136.5 -170q170 99 233.5 167t63.5 152q0 89 -59 142t-166 53q-114 0 -179 -58t-65 -155z" />
<glyph unicode="'" horiz-adv-x="397" d="M152 1434h94l-8 -529h-78z" />
<glyph unicode="(" horiz-adv-x="657" d="M229 561q0 283 60.5 530t173.5 429h98q-231 -393 -231 -959q0 -565 231 -958h-98q-234 387 -234 958z" />
<glyph unicode=")" horiz-adv-x="659" d="M98 -397q230 391 230 958q0 568 -230 959h97q114 -182 174.5 -428.5t60.5 -530.5q0 -575 -235 -958h-97z" />
<glyph unicode="*" horiz-adv-x="768" d="M49 1010l264 149l-264 148l37 65l262 -151v299h72l-2 -301l264 153l37 -65l-262 -148l262 -149l-37 -66l-264 154l2 -301h-72v299l-262 -152z" />
<glyph unicode="+" horiz-adv-x="1165" d="M147 672v88h390v381h92v-381h389v-88h-389v-379h-92v379h-390z" />
<glyph unicode="," horiz-adv-x="409" d="M119 80q0 37 24.5 61.5t61.5 24.5t61.5 -25.5t24.5 -60.5q0 -26 -19 -94l-79 -279h-74l67 289q-31 8 -49 30t-18 54z" />
<glyph unicode="-" horiz-adv-x="780" d="M127 504v92h526v-92h-526z" />
<glyph unicode="." horiz-adv-x="409" d="M119 80q0 36 25 61t61 25t61 -25t25 -61q0 -37 -25.5 -62.5t-60.5 -25.5t-60.5 25.5t-25.5 62.5z" />
<glyph unicode="/" horiz-adv-x="655" d="M-47 -205l676 1929h94l-676 -1929h-94z" />
<glyph unicode="0" horiz-adv-x="1347" d="M113 717q0 333 155.5 530t405.5 197t405.5 -197t155.5 -530t-155.5 -530t-405.5 -197t-405.5 197t-155.5 530zM217 717q0 -295 125.5 -463t331.5 -168t331 168t125 463t-125 463t-331 168t-331.5 -168t-125.5 -463z" />
<glyph unicode="1" horiz-adv-x="722" d="M18 1339v95h455v-1434h-104v1339h-351z" />
<glyph unicode="2" horiz-adv-x="1153" d="M45 1247q172 197 494 197q211 0 335.5 -102.5t124.5 -276.5q0 -110 -49.5 -211t-183.5 -233l-528 -527h831v-94h-985v74l606 602q119 119 161 204t42 175q0 137 -93.5 215t-269.5 78q-267 0 -411 -166z" />
<glyph unicode="3" horiz-adv-x="1142" d="M27 182l53 80q69 -78 189 -127t263 -49q191 0 293.5 84.5t102.5 232.5q0 150 -104.5 234t-313.5 84h-90v76l424 542h-764v95h899v-74l-430 -551q238 -6 361.5 -115.5t123.5 -290.5q0 -185 -131 -299t-371 -114q-157 0 -293 52.5t-212 139.5z" />
<glyph unicode="4" horiz-adv-x="1339" d="M88 397v76l778 961h117l-764 -945h688v351h101v-351h301v-92h-301v-397h-103v397h-817z" />
<glyph unicode="5" horiz-adv-x="1142" d="M53 182l53 80q67 -78 187.5 -127t261.5 -49q193 0 296 88.5t103 235.5q0 80 -26 138.5t-84.5 102.5t-159.5 66t-244 22h-290l75 695h760v-95h-672l-55 -505h205q596 0 596 -420q0 -188 -129.5 -306t-372.5 -118q-155 0 -291.5 52.5t-212.5 139.5z" />
<glyph unicode="6" horiz-adv-x="1232" d="M113 709q0 355 172.5 545t464.5 190q202 0 325 -74l-43 -84q-104 66 -282 66q-247 0 -390 -161t-143 -466q0 -93 12 -172q44 139 167.5 217t289.5 78q216 0 347.5 -116.5t131.5 -309.5q0 -194 -134.5 -313t-342.5 -119q-279 0 -427 189t-148 530zM274 416 q0 -64 27 -123.5t77 -107.5t129.5 -76.5t176.5 -28.5q168 0 272.5 93t104.5 247q0 155 -104.5 246.5t-284.5 91.5q-175 0 -286.5 -98t-111.5 -244z" />
<glyph unicode="7" horiz-adv-x="1189" d="M70 1065v369h1028v-74l-617 -1360h-114l610 1339h-805v-274h-102z" />
<glyph unicode="8" horiz-adv-x="1294" d="M104 393q0 134 79.5 226.5t226.5 130.5q-123 40 -188.5 122.5t-65.5 200.5q0 171 132.5 271t356.5 100t359 -100t135 -271q0 -118 -66.5 -200.5t-191.5 -122.5q148 -38 228.5 -131t80.5 -226q0 -187 -145.5 -295t-399.5 -108q-253 0 -397 107.5t-144 295.5zM209 393 q0 -146 115 -229.5t321 -83.5t323 84t117 229q0 144 -117 228t-323 84q-205 0 -320.5 -84t-115.5 -228zM260 1071q0 -130 101.5 -205t283.5 -75t285.5 75t103.5 203q0 132 -105 208.5t-284 76.5q-178 0 -281.5 -76t-103.5 -207z" />
<glyph unicode="9" horiz-adv-x="1232" d="M68 1012q0 194 134.5 313t342.5 119q279 0 427 -189t148 -530q0 -355 -172.5 -545t-464.5 -190q-204 0 -325 73l43 84q102 -65 282 -65q247 0 390 161t143 466q0 93 -12 172q-44 -139 -167.5 -217t-289.5 -78q-216 0 -347.5 116.5t-131.5 309.5zM172 1014 q0 -155 104.5 -246.5t284.5 -91.5q175 0 286 97.5t111 244.5q0 64 -27 123.5t-76.5 107.5t-129 76.5t-176.5 28.5q-168 0 -272.5 -93t-104.5 -247z" />
<glyph unicode=":" horiz-adv-x="409" d="M119 80q0 36 25 61t61 25t61 -25t25 -61q0 -37 -25.5 -62.5t-60.5 -25.5t-60.5 25.5t-25.5 62.5zM119 991q0 36 25 61t61 25t61 -25t25 -61q0 -37 -25.5 -62.5t-60.5 -25.5t-60.5 25.5t-25.5 62.5z" />
<glyph unicode=";" horiz-adv-x="409" d="M119 80q0 37 24.5 61.5t61.5 24.5t61.5 -25.5t24.5 -60.5q0 -26 -19 -94l-79 -279h-74l67 289q-31 8 -49 30t-18 54zM119 991q0 36 25 61t61 25t61 -25t25 -61q0 -37 -25.5 -62.5t-60.5 -25.5t-60.5 25.5t-25.5 62.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1165" d="M147 668v98l871 354v-94l-772 -309l772 -309v-95z" />
<glyph unicode="=" horiz-adv-x="1165" d="M147 442v86h871v-86h-871zM147 905v86h871v-86h-871z" />
<glyph unicode="&#x3e;" horiz-adv-x="1165" d="M147 313v95l773 309l-773 309v94l871 -354v-98z" />
<glyph unicode="?" horiz-adv-x="1150" d="M33 1241q172 203 493 203q210 0 334.5 -95.5t124.5 -254.5q0 -73 -21 -133.5t-55 -104t-75.5 -82t-82.5 -76t-75 -76.5t-55 -93t-21 -117h-106q0 71 21 131.5t55 103t75 81t82.5 75t75.5 74.5t55 90t21 112q0 122 -95 197.5t-264 75.5q-267 0 -411 -170zM463 76 q0 35 24.5 59.5t59.5 24.5q37 0 61.5 -24.5t24.5 -59.5q0 -34 -25 -59t-61 -25q-35 0 -59.5 25t-24.5 59z" />
<glyph unicode="@" horiz-adv-x="2115" d="M113 520q0 202 70 374.5t194.5 293t301 188.5t382.5 68q204 0 379 -66t299 -184t194 -287t70 -368q0 -258 -85.5 -402.5t-229.5 -144.5q-98 0 -156.5 58.5t-58.5 160.5v43q-62 -124 -176 -193t-261 -69q-220 0 -365 150t-145 382q0 231 145.5 381t364.5 150 q145 0 258.5 -67.5t175.5 -188.5v248h95v-809q0 -83 37 -122.5t98 -39.5q102 0 162.5 121.5t60.5 339.5q0 184 -63.5 339t-176.5 262.5t-273 167.5t-349 60q-190 0 -351 -62t-273.5 -172.5t-176 -268t-63.5 -343.5q0 -185 62.5 -343t174.5 -269t271.5 -173.5t347.5 -62.5 q99 0 203.5 22.5t187.5 67.5l26 -75q-87 -46 -200.5 -70.5t-216.5 -24.5q-205 0 -380.5 69t-298 190.5t-192 294t-69.5 374.5zM623 524q0 -197 120 -321.5t304 -124.5q185 0 305.5 124t120.5 322t-120.5 321.5t-305.5 123.5q-184 0 -304 -124t-120 -321z" />
<glyph unicode="A" horiz-adv-x="1642" d="M238 0v821q0 304 156 463.5t427 159.5q272 0 428 -159.5t156 -463.5v-821h-105v442h-958v-442h-104zM342 537h958v294q0 255 -126 386t-353 131t-353 -131t-126 -386v-294z" />
<glyph unicode="B" horiz-adv-x="1540" d="M250 0v1434h598q233 0 363 -95t130 -272q0 -124 -64 -208t-175 -120q153 -29 234 -120.5t81 -239.5q0 -183 -133.5 -281t-394.5 -98h-639zM354 90h535q424 0 424 297q0 295 -424 295h-535v-592zM354 772h490q188 0 289.5 73t101.5 214q0 140 -101.5 212t-289.5 72h-490 v-571z" />
<glyph unicode="C" horiz-adv-x="1466" d="M113 717q0 206 96.5 372.5t266.5 260.5t380 94q155 0 286.5 -50.5t223.5 -148.5l-66 -67q-173 170 -440 170q-136 0 -255 -48.5t-204.5 -132t-134.5 -200.5t-49 -250t49 -250t134.5 -200.5t204.5 -132t255 -48.5q265 0 440 172l66 -68q-93 -98 -224.5 -149t-285.5 -51 q-210 0 -380 94t-266.5 260.5t-96.5 372.5z" />
<glyph unicode="D" horiz-adv-x="1691" d="M250 0v1434h567q224 0 398 -91.5t269 -255t95 -370.5t-95 -370.5t-269 -255t-398 -91.5h-567zM354 94h455q302 0 484 173.5t182 449.5t-182 449t-484 173h-455v-1245z" />
<glyph unicode="E" horiz-adv-x="1282" d="M121 383q0 136 82.5 226t220.5 122q-115 35 -178.5 121.5t-63.5 206.5q0 80 34 149.5t99 122t168 83t232 30.5q130 0 250 -31t206 -88l-34 -84q-188 111 -418 111q-209 0 -319.5 -83t-110.5 -216t93 -206t259 -73h348v-94h-352q-189 0 -299.5 -73.5t-110.5 -217.5 q0 -142 116.5 -224.5t344.5 -82.5q157 0 289.5 45t214.5 125l45 -78q-86 -88 -231 -136t-322 -48q-271 0 -417 108.5t-146 284.5z" />
<glyph unicode="F" horiz-adv-x="1226" d="M238 0v961q0 225 138 354t400 129q260 0 418 -119l-43 -86q-143 109 -373 109q-219 0 -327.5 -101t-108.5 -291v-229h711v-94h-711v-633h-104z" />
<glyph unicode="G" horiz-adv-x="1583" d="M113 715q0 155 56.5 290.5t155.5 232t239 151.5t300 55q325 0 516 -197l-65 -69q-173 170 -449 170q-138 0 -258.5 -48.5t-206 -132t-135 -201t-49.5 -251.5q0 -146 52.5 -267t139.5 -199.5t198 -121.5t231 -43q122 0 235 43t201 129v455h100v-711h-94v143 q-178 -153 -449 -153q-140 0 -268 48.5t-229 138t-161 229t-60 309.5z" />
<glyph unicode="H" horiz-adv-x="1667" d="M250 0v1434h104v-660h959v660h104v-1434h-104v682h-959v-682h-104z" />
<glyph unicode="I" horiz-adv-x="890" d="M92 0v94h301v1245h-301v95h707v-95h-301v-1245h301v-94h-707z" />
<glyph unicode="J" horiz-adv-x="1003" d="M-2 -10l61 69q120 -180 318 -180q285 0 285 338v1122h-529v95h633v-1217q0 -432 -389 -432q-117 0 -217.5 54t-161.5 151z" />
<glyph unicode="K" horiz-adv-x="1439" d="M250 0v1434h104v-922l895 922h127l-631 -652l674 -782h-127l-616 707l-322 -330v-377h-104z" />
<glyph unicode="L" horiz-adv-x="1198" d="M250 0v1434h104v-1340h826v-94h-930z" />
<glyph unicode="M" horiz-adv-x="2508" d="M250 0v1434h102v-263q58 135 175 204t282 69q178 0 300 -78t171 -225q56 151 179.5 227t297.5 76q242 0 378 -139.5t136 -405.5v-899h-104v895q0 225 -112 339t-312 114q-205 0 -318.5 -120.5t-113.5 -367.5v-860h-105v895q0 226 -109.5 339.5t-308.5 113.5 q-206 0 -320 -120t-114 -368v-860h-104z" />
<glyph unicode="N" horiz-adv-x="1636" d="M250 0v1434h102v-277q65 144 192.5 215.5t305.5 71.5q258 0 403.5 -152t145.5 -434v-858h-105v854q0 243 -121 368.5t-342 125.5q-228 0 -352.5 -133t-124.5 -398v-817h-104z" />
<glyph unicode="O" horiz-adv-x="1716" d="M113 717q0 205 96.5 371.5t267 261t381.5 94.5q210 0 381 -94t268 -260.5t97 -372.5t-97 -372.5t-268 -260.5t-381 -94q-211 0 -381.5 94.5t-267 261t-96.5 371.5zM217 717q0 -134 49 -251t133.5 -200.5t203.5 -131.5t255 -48q135 0 253.5 48t203 131.5t133.5 200.5 t49 251t-49 251t-133.5 200.5t-203 131.5t-253.5 48q-136 0 -255 -48t-203.5 -131.5t-133.5 -200.5t-49 -251z" />
<glyph unicode="P" horiz-adv-x="1464" d="M250 0v1434h514q270 0 423.5 -128.5t153.5 -355.5q0 -226 -153.5 -353.5t-423.5 -127.5h-410v-469h-104zM354 563h410q229 0 350 100.5t121 286.5q0 187 -121 288t-350 101h-410v-776z" />
<glyph unicode="Q" horiz-adv-x="1716" d="M113 717q0 205 96.5 371.5t267 261t381.5 94.5q210 0 381 -94t268 -260.5t97 -372.5q0 -147 -52.5 -277.5t-144 -225.5t-220 -154t-276.5 -68v-268h-106v268q-198 12 -356 109t-247 258.5t-89 357.5zM217 717q0 -126 44.5 -238.5t122.5 -195t187.5 -134.5t235.5 -61v369 h100v-369q255 17 422.5 195t167.5 434q0 132 -49 249t-133.5 201t-203 132.5t-253.5 48.5t-254.5 -48.5t-204 -132.5t-133.5 -201t-49 -249z" />
<glyph unicode="R" horiz-adv-x="1474" d="M250 0v1434h514q270 0 423.5 -128.5t153.5 -355.5q0 -169 -87 -284.5t-248 -163.5l358 -502h-119l-342 479q-75 -10 -139 -10h-410v-469h-104zM354 561h410q229 0 350 101.5t121 287.5q0 187 -121 288t-350 101h-410v-778z" />
<glyph unicode="S" horiz-adv-x="1247" d="M104 184l48 78q76 -79 204 -129.5t269 -50.5q205 0 309 78t104 202q0 69 -30.5 120t-82.5 81.5t-119 53.5t-141.5 41t-148.5 37t-141 47.5t-119 68t-82.5 104t-30.5 148.5q0 78 30.5 145.5t90.5 121t159.5 84t227.5 30.5q118 0 233.5 -34.5t198.5 -94.5l-39 -84 q-176 121 -393 121q-199 0 -301 -80.5t-102 -206.5q0 -69 30.5 -120t82.5 -82t119 -54t141.5 -40.5t148.5 -36.5t141 -47.5t119 -67t82.5 -102t30.5 -146.5q0 -78 -31.5 -145t-93 -120t-162 -83.5t-229.5 -30.5q-159 0 -301.5 53.5t-221.5 140.5z" />
<glyph unicode="T" horiz-adv-x="1163" d="M2 1290q264 154 578 154q317 0 581 -154l-41 -84q-218 129 -485 142v-1348h-107v1348q-258 -9 -485 -142z" />
<glyph unicode="U" horiz-adv-x="1628" d="M238 575v859h104v-854q0 -243 120 -368.5t339 -125.5q225 0 349 133t124 397v818h104v-1434h-102v274q-66 -142 -192 -213t-300 -71q-256 0 -401 151.5t-145 433.5z" />
<glyph unicode="V" horiz-adv-x="1406" d="M12 1434h115l580 -1299l579 1299h109l-639 -1434h-105z" />
<glyph unicode="W" horiz-adv-x="2461" d="M238 557v877h104v-873q0 -475 418 -475q201 0 309.5 116.5t108.5 358.5v873h106v-873q0 -475 418 -475q200 0 309 116.5t109 358.5v873h104v-877q0 -280 -137 -423.5t-385 -143.5q-179 0 -299.5 76t-171.5 223q-51 -147 -172 -223t-299 -76q-248 0 -385 143.5t-137 423.5 z" />
<glyph unicode="X" horiz-adv-x="1314" d="M41 0l551 739l-512 695h123l456 -617l457 617h117l-512 -693l551 -741h-123l-494 664l-491 -664h-123z" />
<glyph unicode="Y" horiz-adv-x="1613" d="M227 950v484h105v-480q0 -243 121 -368t342 -125q228 0 352.5 132.5t124.5 397.5v443h104v-1026q0 -304 -153 -462.5t-428 -158.5q-173 0 -310 58t-229 167l53 82q179 -211 488 -211q230 0 352.5 131t122.5 392v237q-132 -278 -486 -278q-264 0 -411.5 151t-147.5 434z " />
<glyph unicode="Z" horiz-adv-x="1343" d="M106 0v70l476 614h-344v90h413l439 565h-979v95h1120v-68l-457 -594h322v-90h-391l-455 -588h1012v-94h-1156z" />
<glyph unicode="[" horiz-adv-x="626" d="M250 -397v1917h330v-88h-228v-1741h228v-88h-330z" />
<glyph unicode="\" horiz-adv-x="655" d="M-68 1724h95l675 -1929h-94z" />
<glyph unicode="]" horiz-adv-x="626" d="M47 -309h227v1741h-227v88h330v-1917h-330v88z" />
<glyph unicode="^" horiz-adv-x="1167" d="M184 295l355 844h92l354 -844h-90l-311 752l-312 -752h-88z" />
<glyph unicode="_" horiz-adv-x="1024" d="M0 0h1024v-74h-1024v74z" />
<glyph unicode="`" horiz-adv-x="1228" d="M299 1485h147l285 -242h-110z" />
<glyph unicode="a" d="M100 535q0 239 150.5 390.5t378.5 151.5q142 0 256.5 -67t179.5 -189v248h102v-1069h-100v252q-63 -124 -178 -192t-260 -68q-227 0 -378 153t-151 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5z" />
<glyph unicode="b" d="M217 0v1520h102v-695q64 120 179 186t256 66q228 0 378 -151.5t150 -390.5q0 -238 -150.5 -390.5t-377.5 -152.5q-145 0 -260.5 68.5t-178.5 191.5v-252h-98zM317 535q0 -199 122.5 -325t308.5 -126t309 126t123 325t-122.5 324.5t-309.5 125.5q-186 0 -308.5 -125.5 t-122.5 -324.5z" />
<glyph unicode="c" horiz-adv-x="1136" d="M100 535q0 238 152.5 390t388.5 152q130 0 235.5 -49.5t170.5 -146.5l-76 -56q-114 160 -330 160q-191 0 -313.5 -124.5t-122.5 -325.5t122.5 -326t313.5 -125q216 0 330 160l76 -56q-65 -96 -171 -146t-235 -50q-235 0 -388 153t-153 390z" />
<glyph unicode="d" d="M100 535q0 239 150.5 390.5t378.5 151.5q142 0 256.5 -67t179.5 -189v699h102v-1520h-100v252q-63 -124 -178 -192t-260 -68q-227 0 -378 153t-151 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5z" />
<glyph unicode="e" horiz-adv-x="1222" d="M100 535q0 237 146.5 389.5t374.5 152.5q212 0 351 -140.5t150 -356.5l-903 -177q38 -148 155 -233.5t281 -85.5q220 0 342 149l60 -67q-70 -85 -174.5 -129.5t-229.5 -44.5q-243 0 -398 152.5t-155 390.5zM201 543q0 -24 4 -64l815 160q-23 148 -131.5 248t-267.5 100 q-184 0 -302 -124.5t-118 -319.5z" />
<glyph unicode="f" horiz-adv-x="649" d="M217 0v1233q0 136 77.5 215.5t217.5 79.5q137 0 213 -70l-41 -78q-66 60 -166 60q-201 0 -201 -215v-156h342v-88h-340v-981h-102z" />
<glyph unicode="g" horiz-adv-x="1396" d="M100 559q0 227 152 372.5t381 145.5q148 0 266 -65.5t182 -184.5v242h99v-954q0 -266 -129 -393.5t-389 -127.5q-148 0 -278.5 44.5t-211.5 125.5l55 78q80 -74 192 -114.5t240 -40.5q214 0 316 101t102 314v181q-66 -117 -182.5 -180.5t-261.5 -63.5q-229 0 -381 146.5 t-152 373.5zM205 559q0 -188 123 -308t313 -120q191 0 314.5 120t123.5 308t-123.5 307t-314.5 119q-190 0 -313 -119t-123 -307z" />
<glyph unicode="h" horiz-adv-x="1378" d="M217 0v1520h102v-674q56 110 164 170.5t256 60.5q200 0 317.5 -116.5t117.5 -335.5v-625h-103v616q0 180 -90.5 274.5t-255.5 94.5q-188 0 -297 -112.5t-109 -303.5v-569h-102z" />
<glyph unicode="i" horiz-adv-x="534" d="M184 1411q0 34 24.5 59t59.5 25t59.5 -24t24.5 -58q0 -35 -24.5 -59.5t-59.5 -24.5t-59.5 24t-24.5 58zM217 0v1069h102v-1069h-102z" />
<glyph unicode="j" horiz-adv-x="544" d="M-180 -334l41 76q65 -59 174 -59q94 0 143 55.5t49 159.5v1171h103v-1175q0 -137 -76 -218.5t-213 -81.5q-146 0 -221 72zM195 1411q0 34 24.5 59t59.5 25t59 -24t24 -58q0 -35 -24 -59.5t-59 -24.5t-59.5 24t-24.5 58z" />
<glyph unicode="k" horiz-adv-x="1204" d="M217 0v1520h102v-1076l685 625h131l-461 -432l506 -637h-127l-455 571l-279 -254v-317h-102z" />
<glyph unicode="l" horiz-adv-x="610" d="M217 281v1239h102v-1227q0 -213 189 -213q52 0 102 20l8 -86q-58 -22 -122 -22q-135 0 -207 77t-72 212z" />
<glyph unicode="m" horiz-adv-x="2179" d="M217 0v1069h98v-229q53 113 159 175t251 62q146 0 247.5 -66t143.5 -192q54 119 167 188.5t265 69.5q198 0 312 -116t114 -336v-625h-102v616q0 179 -87.5 274t-246.5 95q-181 0 -286 -112.5t-105 -303.5v-569h-103v616q0 180 -87.5 274.5t-245.5 94.5 q-181 0 -286.5 -112.5t-105.5 -303.5v-569h-102z" />
<glyph unicode="n" horiz-adv-x="1378" d="M217 0v1069h98v-231q55 114 164 176.5t260 62.5q200 0 317.5 -116.5t117.5 -335.5v-625h-103v616q0 180 -90.5 274.5t-255.5 94.5q-188 0 -297 -112.5t-109 -303.5v-569h-102z" />
<glyph unicode="o" horiz-adv-x="1269" d="M100 535q0 237 152 389.5t383 152.5t382.5 -152.5t151.5 -389.5t-151.5 -390t-382.5 -153t-383 153t-152 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5z" />
<glyph unicode="p" d="M217 -397v1466h98v-252q63 123 178.5 191.5t260.5 68.5q227 0 377.5 -152.5t150.5 -389.5q0 -239 -150 -391t-378 -152q-141 0 -256 66t-179 186v-641h-102zM317 535q0 -199 122.5 -325t308.5 -126t309 126t123 325t-122.5 324.5t-309.5 125.5q-186 0 -308.5 -125.5 t-122.5 -324.5z" />
<glyph unicode="q" d="M100 535q0 237 150.5 389.5t378.5 152.5q145 0 260 -68t178 -192v252h100v-1466h-102v645q-65 -122 -179.5 -189t-256.5 -67q-228 0 -378.5 152t-150.5 391zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5z" />
<glyph unicode="r" horiz-adv-x="806" d="M217 0v1069h98v-233q50 117 154 179t254 62v-100q-4 0 -12.5 1t-12.5 1q-178 0 -278.5 -112t-100.5 -310v-557h-102z" />
<glyph unicode="s" horiz-adv-x="978" d="M59 129l47 82q65 -56 169 -92.5t217 -36.5q315 0 315 201q0 63 -39 103t-101.5 59.5t-138 32.5t-151.5 31t-138.5 46t-101.5 86t-39 143q0 128 105.5 210.5t300.5 82.5q102 0 202.5 -28.5t163.5 -75.5l-47 -82q-131 96 -321 96q-150 0 -225.5 -55t-75.5 -146 q0 -51 24 -88t65 -58t94.5 -36t112 -25.5t117 -22.5t112 -31.5t94.5 -48.5t65 -77.5t24 -113.5q0 -133 -111 -213t-311 -80q-129 0 -246 38.5t-182 98.5z" />
<glyph unicode="t" horiz-adv-x="782" d="M205 283v1020h102v-234h340v-88h-340v-688q0 -103 50.5 -158t148.5 -55q101 0 166 59l43 -71q-38 -37 -97 -56.5t-122 -19.5q-140 0 -215.5 77.5t-75.5 213.5z" />
<glyph unicode="u" horiz-adv-x="1370" d="M205 444v625h102v-616q0 -180 91.5 -274.5t260.5 -94.5q181 0 286.5 112.5t105.5 303.5v569h102v-1069h-98v229q-53 -113 -158 -175t-246 -62q-208 0 -327 116t-119 336z" />
<glyph unicode="v" horiz-adv-x="1081" d="M6 1069h109l426 -956l430 956h104l-483 -1069h-105z" />
<glyph unicode="w" horiz-adv-x="2088" d="M205 479v590h102v-584q0 -204 83.5 -302.5t258.5 -98.5q174 0 259 100t85 310v575h103v-575q0 -210 84.5 -310t259.5 -100q173 0 257.5 98.5t84.5 302.5v584h102v-590q0 -242 -112.5 -364.5t-331.5 -122.5q-153 0 -251.5 62t-144.5 196q-87 -258 -395 -258 q-220 0 -332 122t-112 365z" />
<glyph unicode="x" horiz-adv-x="1062" d="M47 0l424 551l-401 518h116l344 -444l344 444h115l-401 -518l430 -551h-121l-367 477l-366 -477h-117z" />
<glyph unicode="y" horiz-adv-x="1370" d="M205 492v577h102v-569q0 -180 91.5 -274.5t260.5 -94.5q181 0 286.5 112.5t105.5 303.5v522h102v-954q0 -266 -118.5 -393.5t-358.5 -127.5q-137 0 -259 45t-194 125l56 78q71 -74 173 -114.5t220 -40.5q195 0 287 100.5t92 314.5v164q-55 -107 -159 -167t-241 -60 q-208 0 -327 116.5t-119 336.5z" />
<glyph unicode="z" horiz-adv-x="1050" d="M100 0v70l332 428h-246v86h312l307 397h-694v88h827v-70l-324 -417h248v-84h-313l-318 -410h719v-88h-850z" />
<glyph unicode="{" horiz-adv-x="655" d="M127 518v88h72q92 0 92 105v563q0 246 252 246h65v-88h-59q-156 0 -156 -166v-553q0 -67 -20.5 -101.5t-63.5 -50.5q43 -16 63.5 -50t20.5 -101v-553q0 -166 156 -166h59v-88h-65q-252 0 -252 245v564q0 106 -92 106h-72z" />
<glyph unicode="|" horiz-adv-x="595" d="M250 -397v1917h96v-1917h-96z" />
<glyph unicode="}" horiz-adv-x="655" d="M47 -309h59q158 0 158 166v553q0 67 20 101.5t64 49.5q-44 15 -64 50t-20 102v553q0 166 -158 166h-59v88h66q254 0 254 -246v-563q0 -105 90 -105h71v-88h-71q-90 0 -90 -106v-564q0 -245 -254 -245h-66v88z" />
<glyph unicode="~" horiz-adv-x="1165" d="M133 578q4 137 70 212.5t172 75.5q54 0 102.5 -22t84.5 -54t70 -63.5t74 -53.5t82 -22q76 0 121.5 55.5t48.5 149.5h74q-6 -137 -71 -212t-170 -75q-54 0 -102.5 22t-84.5 53.5t-70.5 63.5t-74.5 54t-82 22q-76 0 -121.5 -55.5t-50.5 -150.5h-72z" />
<glyph unicode="&#xa1;" horiz-adv-x="516" d="M174 993q0 34 24.5 59t59.5 25q36 0 61 -25t25 -59q0 -35 -24.5 -59.5t-61.5 -24.5q-35 0 -59.5 24.5t-24.5 59.5zM199 -315l20 972h78l20 -972h-118z" />
<glyph unicode="&#xa2;" horiz-adv-x="1136" d="M100 535q0 225 137.5 374t354.5 166v240h74v-238q122 -5 220.5 -54.5t160.5 -141.5l-76 -56q-109 150 -305 160v-901q196 10 305 160l76 -56q-62 -91 -161 -141t-220 -55v-238h-74v240q-217 17 -354.5 167t-137.5 374zM205 535q0 -187 107.5 -309.5t279.5 -139.5v897 q-172 -17 -279.5 -139t-107.5 -309z" />
<glyph unicode="&#xa3;" horiz-adv-x="1288" d="M61 0v94h238v586h-238v74h238v207q0 222 139.5 352.5t401.5 130.5q254 0 413 -117l-43 -86q-144 107 -376 107q-216 0 -323.5 -101t-107.5 -291v-202h586v-74h-586v-586h826v-94h-1168z" />
<glyph unicode="&#xa4;" horiz-adv-x="1433" d="M74 72l217 217q-139 155 -139 360q0 208 139 363l-217 217l61 65l221 -219q156 127 361 127t362 -125l219 217l62 -65l-215 -215q143 -153 143 -365q0 -206 -143 -362l215 -215l-62 -66l-221 219q-159 -123 -360 -123q-202 0 -359 125l-223 -221zM240 649 q0 -189 140.5 -324t336.5 -135q199 0 341 135t142 324q0 192 -142 328.5t-341 136.5q-129 0 -239 -62.5t-174 -169.5t-64 -233z" />
<glyph unicode="&#xa5;" horiz-adv-x="1402" d="M10 1434h113l579 -766l580 766h111l-594 -793h422v-70h-467v-213h467v-69h-467v-289h-105v289h-469v69h469v213h-469v70h424z" />
<glyph unicode="&#xa6;" horiz-adv-x="595" d="M250 319h96v-716h-96v716zM250 803v717h96v-717h-96z" />
<glyph unicode="&#xa7;" horiz-adv-x="987" d="M61 -74l39 80q62 -58 163 -95.5t206 -37.5q142 0 220.5 55.5t78.5 161.5q0 67 -37.5 112.5t-98 69t-133 41.5t-145 39.5t-133 53t-98 91t-37.5 144.5q0 92 49 160.5t131 105.5q-133 73 -133 228q0 145 112 226t312 81q99 0 200 -29t163 -78l-39 -78q-131 99 -328 99 q-159 0 -242.5 -55t-83.5 -162q0 -52 23.5 -91t63 -62.5t90.5 -41.5t107.5 -32t113 -29.5t107.5 -39t90.5 -55t63 -83.5t23.5 -119q0 -89 -49.5 -158t-132.5 -106q135 -76 135 -232q0 -140 -107.5 -221.5t-289.5 -81.5q-115 0 -228.5 39.5t-175.5 99.5zM178 645 q0 -50 20.5 -88.5t50.5 -61.5t79 -43t91.5 -31t104.5 -27t101 -29q86 21 139 78t53 143q0 49 -20.5 87.5t-50 61.5t-79 42.5t-91 30t-103.5 26.5t-100 28q-88 -21 -141.5 -77t-53.5 -140z" />
<glyph unicode="&#xa8;" horiz-adv-x="1228" d="M365 1372q0 32 21 53t52 21q30 0 52 -21t22 -53q0 -30 -22 -52t-52 -22q-31 0 -52 21.5t-21 52.5zM717 1372q0 32 22 53t52 21q31 0 52 -21t21 -53q0 -31 -21 -52.5t-52 -21.5q-30 0 -52 22t-22 52z" />
<glyph unicode="&#xa9;" horiz-adv-x="1669" d="M113 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM174 717q0 -137 51 -260t138 -211.5t210 -140.5t261 -52 q183 0 335.5 88.5t240 242t87.5 337.5t-85.5 335t-236.5 237.5t-337 86.5t-339 -88t-239 -240t-86 -335zM428 717q0 188 119.5 308t304.5 120q99 0 182 -39t135 -111l-63 -51q-45 60 -111.5 91.5t-144.5 31.5q-146 0 -241 -97.5t-95 -252.5t95 -252.5t241 -97.5 q165 0 256 122l63 -49q-52 -73 -135 -112t-182 -39q-185 0 -304.5 120t-119.5 308z" />
<glyph unicode="&#xaa;" horiz-adv-x="815" d="M90 1063q0 80 62.5 128t197.5 48h230v47q0 174 -197 174q-143 0 -238 -78l-34 54q51 41 125.5 65.5t150.5 24.5q134 0 204.5 -61t70.5 -181v-399h-76v110q-68 -116 -248 -116q-118 0 -183 50t-65 134zM168 1065q0 -60 48.5 -93.5t137.5 -33.5q165 0 226 131v117h-222 q-190 0 -190 -121z" />
<glyph unicode="&#xab;" horiz-adv-x="931" d="M109 535l299 385h100l-295 -385l295 -383h-100zM451 535l299 385h100l-295 -385l295 -383h-100z" />
<glyph unicode="&#xac;" horiz-adv-x="1165" d="M147 674v86h871v-455h-94v369h-777z" />
<glyph unicode="&#xad;" horiz-adv-x="780" d="M127 504v92h526v-92h-526z" />
<glyph unicode="&#xae;" horiz-adv-x="1669" d="M113 717q0 149 56 282.5t151.5 230.5t230.5 153.5t287 56.5t286 -55.5t229.5 -151t150.5 -228.5t55 -284t-56.5 -285.5t-152.5 -231.5t-231 -153.5t-285 -56.5t-284 56.5t-230 153.5t-151.5 230.5t-55.5 282.5zM174 717q0 -137 51 -260t138 -211.5t210 -140.5t261 -52 q183 0 335.5 88.5t240 242t87.5 337.5t-85.5 335t-236.5 237.5t-337 86.5t-339 -88t-239 -240t-86 -335zM559 297v840h315q148 0 235 -75t87 -200q0 -96 -50.5 -163t-141.5 -95l198 -307h-86l-188 291q-16 -2 -54 -2h-235v-289h-80zM637 655h231q119 0 184.5 54.5 t65.5 152.5q0 97 -66 151t-184 54h-231v-412z" />
<glyph unicode="&#xaf;" horiz-adv-x="1228" d="M313 1327v74h602v-74h-602z" />
<glyph unicode="&#xb0;" horiz-adv-x="860" d="M111 1124q0 133 92.5 225.5t226.5 92.5q132 0 225 -92.5t93 -225.5q0 -132 -93 -224.5t-225 -92.5q-133 0 -226 92.5t-93 224.5zM180 1124q0 -106 72 -179t178 -73q105 0 177.5 73.5t72.5 178.5q0 106 -72 179t-178 73t-178 -73t-72 -179z" />
<glyph unicode="&#xb1;" horiz-adv-x="1165" d="M147 0v88h871v-88h-871zM147 711v88h390v377h92v-377h389v-88h-389v-375h-92v375h-390z" />
<glyph unicode="&#xb2;" horiz-adv-x="880" d="M72 1405q106 121 323 121q158 0 241 -64t83 -164q0 -64 -33.5 -121.5t-126.5 -140.5l-332 -297h537v-69h-662v53l398 354q80 72 109.5 120t29.5 97q0 71 -62.5 116.5t-185.5 45.5q-172 0 -262 -96z" />
<glyph unicode="&#xb3;" horiz-adv-x="880" d="M72 774l37 62q44 -46 125.5 -74.5t179.5 -28.5q129 0 195.5 47t66.5 131t-65.5 131.5t-194.5 47.5h-78v55l274 305h-524v70h631v-54l-283 -313q153 -1 236.5 -67t83.5 -175q0 -110 -89 -178.5t-253 -68.5q-110 0 -203 30t-139 80z" />
<glyph unicode="&#xb4;" horiz-adv-x="1228" d="M498 1243l284 242h148l-322 -242h-110z" />
<glyph unicode="&#xb5;" d="M217 -397v1466h102v-616q0 -180 92 -274.5t261 -94.5q181 0 286 112.5t105 303.5v569h102v-1069h-98v231q-53 -118 -155.5 -178.5t-237.5 -60.5q-255 0 -355 180v-569h-102z" />
<glyph unicode="&#xb6;" horiz-adv-x="1267" d="M47 1176q0 159 115.5 251.5t310.5 92.5h545v-1725h-90v1641h-377v-1641h-88v1034q-188 2 -302 96t-114 251z" />
<glyph unicode="&#xb7;" horiz-adv-x="491" d="M160 549q0 38 25 63t61 25t61 -25t25 -63q0 -37 -25.5 -62.5t-60.5 -25.5t-60.5 25.5t-25.5 62.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="1228" d="M412 -393l28 57q55 -43 133 -43q62 0 99 28.5t37 80.5q0 47 -35.5 75.5t-100.5 28.5h-41l48 178h67l-33 -123q81 -6 127.5 -50t46.5 -113q0 -77 -60 -123.5t-155 -46.5q-96 0 -161 51z" />
<glyph unicode="&#xb9;" horiz-adv-x="880" d="M176 670v69h260v711h-243v70h323v-781h232v-69h-572z" />
<glyph unicode="&#xba;" horiz-adv-x="841" d="M76 1202q0 141 97.5 232.5t246.5 91.5q151 0 248.5 -91.5t97.5 -232.5t-97.5 -232t-248.5 -91q-149 0 -246.5 91t-97.5 232zM154 1202q0 -113 75 -183.5t191 -70.5q118 0 193 70.5t75 183.5t-75 184.5t-193 71.5q-116 0 -191 -71.5t-75 -184.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="931" d="M84 152l295 383l-295 385h98l299 -385l-299 -383h-98zM426 152l295 383l-295 385h98l299 -385l-299 -383h-98z" />
<glyph unicode="&#xbc;" horiz-adv-x="2099" d="M176 584v69h260v711h-243v70h323v-781h232v-69h-572zM516 0l979 1434h88l-979 -1434h-88zM1282 229v56l430 565h90l-422 -553h387v199h74v-199h193v-68h-193v-229h-78v229h-481z" />
<glyph unicode="&#xbd;" horiz-adv-x="2099" d="M176 584v69h260v711h-243v70h323v-781h232v-69h-572zM516 0l979 1434h88l-979 -1434h-88zM1290 735q106 121 324 121q158 0 240.5 -63.5t82.5 -163.5q0 -65 -33 -122t-126 -140l-332 -297h536v-70h-661v53l397 355q81 73 110.5 120.5t29.5 96.5q0 71 -62.5 116t-185.5 45 q-172 0 -262 -96z" />
<glyph unicode="&#xbe;" horiz-adv-x="2099" d="M72 688l37 62q44 -46 125.5 -74.5t179.5 -28.5q129 0 195.5 47t66.5 131t-65.5 131.5t-194.5 47.5h-78v55l274 305h-524v70h631v-54l-283 -313q153 -1 236.5 -67t83.5 -175q0 -110 -89 -178.5t-253 -68.5q-110 0 -203 30t-139 80zM516 0l979 1434h88l-979 -1434h-88z M1282 229v56l430 565h90l-422 -553h387v199h74v-199h193v-68h-193v-229h-78v229h-481z" />
<glyph unicode="&#xbf;" horiz-adv-x="1150" d="M166 10q0 69 21 126.5t55 98.5t75 77.5t82 72t75 72.5t55.5 88.5t21.5 111.5h104q0 -68 -21 -125t-55 -97.5t-74.5 -77.5t-81.5 -71.5t-75 -71.5t-55 -86.5t-21 -107.5q0 -109 95.5 -181t261.5 -72q269 0 413 170l76 -60q-172 -203 -493 -203q-209 0 -334 93.5 t-125 242.5zM518 993q0 34 24.5 59t59.5 25q36 0 61 -25t25 -59q0 -35 -24.5 -59.5t-61.5 -24.5q-35 0 -59.5 24.5t-24.5 59.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1642" d="M238 0v821q0 304 156 463.5t427 159.5q272 0 428 -159.5t156 -463.5v-821h-105v442h-958v-442h-104zM342 537h958v294q0 255 -126 386t-353 131t-353 -131t-126 -386v-294zM506 1792h147l285 -242h-111z" />
<glyph unicode="&#xc1;" horiz-adv-x="1642" d="M238 0v821q0 304 156 463.5t427 159.5q272 0 428 -159.5t156 -463.5v-821h-105v442h-958v-442h-104zM342 537h958v294q0 255 -126 386t-353 131t-353 -131t-126 -386v-294zM705 1550l284 242h148l-322 -242h-110z" />
<glyph unicode="&#xc2;" horiz-adv-x="1642" d="M238 0v821q0 304 156 463.5t427 159.5q272 0 428 -159.5t156 -463.5v-821h-105v442h-958v-442h-104zM342 537h958v294q0 255 -126 386t-353 131t-353 -131t-126 -386v-294zM506 1550l258 242h115l258 -242h-103l-213 179l-213 -179h-102z" />
<glyph unicode="&#xc3;" horiz-adv-x="1642" d="M238 0v821q0 304 156 463.5t427 159.5q272 0 428 -159.5t156 -463.5v-821h-105v442h-958v-442h-104zM342 537h958v294q0 255 -126 386t-353 131t-353 -131t-126 -386v-294zM516 1575q3 93 49 149t121 56q41 0 80.5 -22t65.5 -49t58 -49t60 -22q47 0 76.5 35t32.5 94h67 q-3 -90 -49.5 -145t-120.5 -55q-41 0 -80.5 22t-65.5 48.5t-58 48.5t-60 22q-47 0 -76 -35.5t-32 -97.5h-68z" />
<glyph unicode="&#xc4;" horiz-adv-x="1642" d="M238 0v821q0 304 156 463.5t427 159.5q272 0 428 -159.5t156 -463.5v-821h-105v442h-958v-442h-104zM342 537h958v294q0 255 -126 386t-353 131t-353 -131t-126 -386v-294zM571 1679q0 32 21.5 53t52.5 21q30 0 52 -21t22 -53q0 -30 -22 -51.5t-52 -21.5q-31 0 -52.5 21 t-21.5 52zM924 1679q0 32 21.5 53t51.5 21q31 0 52.5 -21t21.5 -53q0 -31 -21.5 -52t-52.5 -21q-30 0 -51.5 21.5t-21.5 51.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1642" d="M238 0v821q0 304 156 463.5t427 159.5q272 0 428 -159.5t156 -463.5v-821h-105v442h-958v-442h-104zM342 537h958v294q0 255 -126 386t-353 131t-353 -131t-126 -386v-294zM616 1784q0 85 58 145t145 60t146 -60.5t59 -144.5q0 -83 -58.5 -141t-146.5 -58q-87 0 -145 58 t-58 141zM678 1784q0 -61 39.5 -101.5t101.5 -40.5t103 41.5t41 100.5q0 60 -41.5 102.5t-102.5 42.5t-101 -41.5t-40 -103.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="2181" d="M215 0v819q0 293 147.5 454t417.5 161h1286v-93h-854v-569h764v-88h-764v-592h885v-92h-989v463h-786v-463h-107zM322 555h786v782h-322q-226 0 -345 -134t-119 -386v-262z" />
<glyph unicode="&#xc7;" horiz-adv-x="1466" d="M113 717q0 206 96.5 372.5t266.5 260.5t380 94q155 0 286.5 -50.5t223.5 -148.5l-66 -67q-173 170 -440 170q-136 0 -255 -48.5t-204.5 -132t-134.5 -200.5t-49 -250t49 -250t134.5 -200.5t204.5 -132t255 -48.5q265 0 440 172l66 -68q-93 -99 -223.5 -149.5 t-284.5 -50.5l-27 -101q81 -6 128 -50t47 -113q0 -77 -60 -123.5t-155 -46.5q-97 0 -162 51l28 57q55 -43 134 -43q62 0 98.5 28.5t36.5 80.5q0 47 -35.5 75.5t-99.5 28.5h-41l41 158q-195 15 -349.5 112t-241.5 257t-87 356z" />
<glyph unicode="&#xc8;" horiz-adv-x="1282" d="M121 383q0 136 82.5 226t220.5 122q-115 35 -178.5 121.5t-63.5 206.5q0 80 34 149.5t99 122t168 83t232 30.5q130 0 250 -31t206 -88l-34 -84q-188 111 -418 111q-209 0 -319.5 -83t-110.5 -216t93 -206t259 -73h348v-94h-352q-189 0 -299.5 -73.5t-110.5 -217.5 q0 -142 116.5 -224.5t344.5 -82.5q157 0 289.5 45t214.5 125l45 -78q-86 -88 -231 -136t-322 -48q-271 0 -417 108.5t-146 284.5zM385 1792h147l285 -242h-110z" />
<glyph unicode="&#xc9;" horiz-adv-x="1282" d="M121 383q0 136 82.5 226t220.5 122q-115 35 -178.5 121.5t-63.5 206.5q0 80 34 149.5t99 122t168 83t232 30.5q130 0 250 -31t206 -88l-34 -84q-188 111 -418 111q-209 0 -319.5 -83t-110.5 -216t93 -206t259 -73h348v-94h-352q-189 0 -299.5 -73.5t-110.5 -217.5 q0 -142 116.5 -224.5t344.5 -82.5q157 0 289.5 45t214.5 125l45 -78q-86 -88 -231 -136t-322 -48q-271 0 -417 108.5t-146 284.5zM584 1550l284 242h148l-322 -242h-110z" />
<glyph unicode="&#xca;" horiz-adv-x="1282" d="M121 383q0 136 82.5 226t220.5 122q-115 35 -178.5 121.5t-63.5 206.5q0 80 34 149.5t99 122t168 83t232 30.5q130 0 250 -31t206 -88l-34 -84q-188 111 -418 111q-209 0 -319.5 -83t-110.5 -216t93 -206t259 -73h348v-94h-352q-189 0 -299.5 -73.5t-110.5 -217.5 q0 -142 116.5 -224.5t344.5 -82.5q157 0 289.5 45t214.5 125l45 -78q-86 -88 -231 -136t-322 -48q-271 0 -417 108.5t-146 284.5zM385 1550l258 242h115l258 -242h-103l-213 179l-213 -179h-102z" />
<glyph unicode="&#xcb;" horiz-adv-x="1282" d="M121 383q0 136 82.5 226t220.5 122q-115 35 -178.5 121.5t-63.5 206.5q0 80 34 149.5t99 122t168 83t232 30.5q130 0 250 -31t206 -88l-34 -84q-188 111 -418 111q-209 0 -319.5 -83t-110.5 -216t93 -206t259 -73h348v-94h-352q-189 0 -299.5 -73.5t-110.5 -217.5 q0 -142 116.5 -224.5t344.5 -82.5q157 0 289.5 45t214.5 125l45 -78q-86 -88 -231 -136t-322 -48q-271 0 -417 108.5t-146 284.5zM451 1679q0 32 21 53t52 21q30 0 52 -21t22 -53q0 -30 -22 -51.5t-52 -21.5q-31 0 -52 21t-21 52zM803 1679q0 32 22 53t52 21q31 0 52 -21 t21 -53q0 -31 -21 -52t-52 -21q-30 0 -52 21.5t-22 51.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="890" d="M92 0v94h301v1245h-301v95h707v-95h-301v-1245h301v-94h-707zM131 1792h148l284 -242h-110z" />
<glyph unicode="&#xcd;" horiz-adv-x="890" d="M92 0v94h301v1245h-301v95h707v-95h-301v-1245h301v-94h-707zM330 1550l284 242h148l-322 -242h-110z" />
<glyph unicode="&#xce;" horiz-adv-x="890" d="M92 0v94h301v1245h-301v95h707v-95h-301v-1245h301v-94h-707zM131 1550l258 242h115l258 -242h-103l-213 179l-213 -179h-102z" />
<glyph unicode="&#xcf;" horiz-adv-x="890" d="M92 0v94h301v1245h-301v95h707v-95h-301v-1245h301v-94h-707zM197 1679q0 32 21 53t52 21q30 0 52 -21t22 -53q0 -30 -22 -51.5t-52 -21.5q-31 0 -52 21t-21 52zM549 1679q0 32 22 53t52 21q31 0 52 -21t21 -53q0 -31 -21 -52t-52 -21q-30 0 -52 21.5t-22 51.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1697" d="M33 684v88h223v662h567q224 0 398 -91.5t269 -255t95 -370.5t-95 -370.5t-269 -255t-398 -91.5h-567v684h-223zM360 94h455q302 0 484 173.5t182 449.5t-182 449t-484 173h-455v-567h443v-88h-443v-590z" />
<glyph unicode="&#xd1;" horiz-adv-x="1636" d="M250 0v1434h102v-277q65 144 192.5 215.5t305.5 71.5q258 0 403.5 -152t145.5 -434v-858h-105v854q0 243 -121 368.5t-342 125.5q-228 0 -352.5 -133t-124.5 -398v-817h-104zM520 1575q3 93 49 149t121 56q41 0 80.5 -22t65.5 -49t58 -49t60 -22q47 0 76.5 35t32.5 94h67 q-3 -90 -49 -145t-120 -55q-41 0 -81 22t-65.5 48.5t-58 48.5t-60.5 22q-47 0 -76 -35.5t-32 -97.5h-68z" />
<glyph unicode="&#xd2;" horiz-adv-x="1716" d="M113 717q0 205 96.5 371.5t267 261t381.5 94.5q210 0 381 -94t268 -260.5t97 -372.5t-97 -372.5t-268 -260.5t-381 -94q-211 0 -381.5 94.5t-267 261t-96.5 371.5zM217 717q0 -134 49 -251t133.5 -200.5t203.5 -131.5t255 -48q135 0 253.5 48t203 131.5t133.5 200.5 t49 251t-49 251t-133.5 200.5t-203 131.5t-253.5 48q-136 0 -255 -48t-203.5 -131.5t-133.5 -200.5t-49 -251zM543 1792h147l285 -242h-111z" />
<glyph unicode="&#xd3;" horiz-adv-x="1716" d="M113 717q0 205 96.5 371.5t267 261t381.5 94.5q210 0 381 -94t268 -260.5t97 -372.5t-97 -372.5t-268 -260.5t-381 -94q-211 0 -381.5 94.5t-267 261t-96.5 371.5zM217 717q0 -134 49 -251t133.5 -200.5t203.5 -131.5t255 -48q135 0 253.5 48t203 131.5t133.5 200.5 t49 251t-49 251t-133.5 200.5t-203 131.5t-253.5 48q-136 0 -255 -48t-203.5 -131.5t-133.5 -200.5t-49 -251zM741 1550l285 242h148l-322 -242h-111z" />
<glyph unicode="&#xd4;" horiz-adv-x="1716" d="M113 717q0 205 96.5 371.5t267 261t381.5 94.5q210 0 381 -94t268 -260.5t97 -372.5t-97 -372.5t-268 -260.5t-381 -94q-211 0 -381.5 94.5t-267 261t-96.5 371.5zM217 717q0 -134 49 -251t133.5 -200.5t203.5 -131.5t255 -48q135 0 253.5 48t203 131.5t133.5 200.5 t49 251t-49 251t-133.5 200.5t-203 131.5t-253.5 48q-136 0 -255 -48t-203.5 -131.5t-133.5 -200.5t-49 -251zM543 1550l258 242h114l259 -242h-103l-213 179l-213 -179h-102z" />
<glyph unicode="&#xd5;" horiz-adv-x="1716" d="M113 717q0 205 96.5 371.5t267 261t381.5 94.5q210 0 381 -94t268 -260.5t97 -372.5t-97 -372.5t-268 -260.5t-381 -94q-211 0 -381.5 94.5t-267 261t-96.5 371.5zM217 717q0 -134 49 -251t133.5 -200.5t203.5 -131.5t255 -48q135 0 253.5 48t203 131.5t133.5 200.5 t49 251t-49 251t-133.5 200.5t-203 131.5t-253.5 48q-136 0 -255 -48t-203.5 -131.5t-133.5 -200.5t-49 -251zM553 1575q3 93 49 149t121 56q41 0 80.5 -22t65.5 -49t58 -49t60 -22q47 0 76.5 35t32.5 94h67q-3 -90 -49.5 -145t-120.5 -55q-41 0 -80.5 22t-65.5 48.5 t-58 48.5t-60 22q-47 0 -76 -35.5t-32 -97.5h-68z" />
<glyph unicode="&#xd6;" horiz-adv-x="1716" d="M113 717q0 205 96.5 371.5t267 261t381.5 94.5q210 0 381 -94t268 -260.5t97 -372.5t-97 -372.5t-268 -260.5t-381 -94q-211 0 -381.5 94.5t-267 261t-96.5 371.5zM217 717q0 -134 49 -251t133.5 -200.5t203.5 -131.5t255 -48q135 0 253.5 48t203 131.5t133.5 200.5 t49 251t-49 251t-133.5 200.5t-203 131.5t-253.5 48q-136 0 -255 -48t-203.5 -131.5t-133.5 -200.5t-49 -251zM608 1679q0 32 21.5 53t52.5 21q30 0 52 -21t22 -53q0 -30 -22 -51.5t-52 -21.5q-31 0 -52.5 21t-21.5 52zM961 1679q0 32 21.5 53t51.5 21q31 0 52.5 -21 t21.5 -53q0 -31 -21.5 -52t-52.5 -21q-30 0 -51.5 21.5t-21.5 51.5z" />
<glyph unicode="&#xd7;" horiz-adv-x="1165" d="M246 440l276 277l-276 276l59 64l279 -279l276 279l60 -64l-277 -276l277 -277l-60 -63l-276 278l-279 -278z" />
<glyph unicode="&#xd8;" horiz-adv-x="1716" d="M113 717q0 205 96.5 371.5t267 261t381.5 94.5q225 0 404 -109l176 242h92l-205 -283q131 -100 205 -249.5t74 -327.5q0 -206 -97 -372.5t-268 -260.5t-381 -94q-224 0 -403 106l-174 -239h-93l205 282q-132 98 -206 248.5t-74 329.5zM217 717q0 -152 62.5 -281 t173.5 -217l751 1034q-153 95 -346 95q-136 0 -255 -48t-203.5 -131.5t-133.5 -200.5t-49 -251zM514 178q153 -92 344 -92q135 0 253.5 48t203 131.5t133.5 200.5t49 251q0 152 -61 279.5t-170 215.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1628" d="M238 575v859h104v-854q0 -243 120 -368.5t339 -125.5q225 0 349 133t124 397v818h104v-1434h-102v274q-66 -142 -192 -213t-300 -71q-256 0 -401 151.5t-145 433.5zM489 1792h148l285 -242h-111z" />
<glyph unicode="&#xda;" horiz-adv-x="1628" d="M238 575v859h104v-854q0 -243 120 -368.5t339 -125.5q225 0 349 133t124 397v818h104v-1434h-102v274q-66 -142 -192 -213t-300 -71q-256 0 -401 151.5t-145 433.5zM688 1550l285 242h147l-321 -242h-111z" />
<glyph unicode="&#xdb;" horiz-adv-x="1628" d="M238 575v859h104v-854q0 -243 120 -368.5t339 -125.5q225 0 349 133t124 397v818h104v-1434h-102v274q-66 -142 -192 -213t-300 -71q-256 0 -401 151.5t-145 433.5zM489 1550l259 242h114l258 -242h-102l-213 179l-213 -179h-103z" />
<glyph unicode="&#xdc;" horiz-adv-x="1628" d="M238 575v859h104v-854q0 -243 120 -368.5t339 -125.5q225 0 349 133t124 397v818h104v-1434h-102v274q-66 -142 -192 -213t-300 -71q-256 0 -401 151.5t-145 433.5zM555 1679q0 32 21.5 53t52.5 21q30 0 51.5 -21t21.5 -53q0 -30 -21.5 -51.5t-51.5 -21.5q-31 0 -52.5 21 t-21.5 52zM907 1679q0 32 22 53t52 21q31 0 52.5 -21t21.5 -53q0 -31 -21.5 -52t-52.5 -21q-30 0 -52 21.5t-22 51.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1613" d="M227 950v484h105v-480q0 -243 121 -368t342 -125q228 0 352.5 132.5t124.5 397.5v443h104v-1026q0 -304 -153 -462.5t-428 -158.5q-173 0 -310 58t-229 167l53 82q179 -211 488 -211q230 0 352.5 131t122.5 392v237q-132 -278 -486 -278q-264 0 -411.5 151t-147.5 434z M686 1550l285 242h147l-321 -242h-111z" />
<glyph unicode="&#xde;" horiz-adv-x="1464" d="M250 0v1434h104v-195h410q270 0 423.5 -127.5t153.5 -353.5q0 -228 -153.5 -357t-423.5 -129h-410v-272h-104zM354 367h410q229 0 350 101.5t121 289.5q0 186 -121 287.5t-350 101.5h-410v-780z" />
<glyph unicode="&#xdf;" horiz-adv-x="1353" d="M217 0v1028q0 241 129.5 370.5t345.5 129.5q202 0 322.5 -106.5t120.5 -272.5q0 -125 -65 -218t-169 -136q162 -28 257 -130.5t95 -261.5q0 -188 -135 -299.5t-342 -111.5q-127 0 -225 31l20 90q88 -31 201 -31q168 0 273.5 86.5t105.5 237.5t-106.5 236t-280.5 85h-119 v88q182 1 285.5 90t103.5 240q0 132 -91 212.5t-251 80.5q-173 0 -273 -103.5t-100 -300.5v-1034h-102z" />
<glyph unicode="&#xe0;" d="M100 535q0 239 150.5 390.5t378.5 151.5q142 0 256.5 -67t179.5 -189v248h102v-1069h-100v252q-63 -124 -178 -192t-260 -68q-227 0 -378 153t-151 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM348 1485h148 l284 -242h-110z" />
<glyph unicode="&#xe1;" d="M100 535q0 239 150.5 390.5t378.5 151.5q142 0 256.5 -67t179.5 -189v248h102v-1069h-100v252q-63 -124 -178 -192t-260 -68q-227 0 -378 153t-151 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM547 1243 l284 242h148l-322 -242h-110z" />
<glyph unicode="&#xe2;" d="M100 535q0 239 150.5 390.5t378.5 151.5q142 0 256.5 -67t179.5 -189v248h102v-1069h-100v252q-63 -124 -178 -192t-260 -68q-227 0 -378 153t-151 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM348 1243 l258 242h115l258 -242h-102l-213 178l-213 -178h-103z" />
<glyph unicode="&#xe3;" d="M100 535q0 239 150.5 390.5t378.5 151.5q142 0 256.5 -67t179.5 -189v248h102v-1069h-100v252q-63 -124 -178 -192t-260 -68q-227 0 -378 153t-151 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM358 1268 q3 93 49 149t121 56q34 0 66 -14.5t56.5 -35.5t47 -42t47.5 -35.5t48 -14.5q47 0 76 35t32 94h68q-3 -90 -49.5 -145t-120.5 -55q-41 0 -80.5 22t-65.5 48.5t-58 48.5t-60 22q-47 0 -76.5 -36t-32.5 -97h-68z" />
<glyph unicode="&#xe4;" d="M100 535q0 239 150.5 390.5t378.5 151.5q142 0 256.5 -67t179.5 -189v248h102v-1069h-100v252q-63 -124 -178 -192t-260 -68q-227 0 -378 153t-151 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM414 1372 q0 32 21 53t52 21q30 0 52 -21t22 -53q0 -30 -22 -52t-52 -22q-31 0 -52 21.5t-21 52.5zM766 1372q0 32 22 53t52 21q31 0 52 -21t21 -53q0 -31 -21 -52.5t-52 -21.5q-30 0 -52 22t-22 52z" />
<glyph unicode="&#xe5;" d="M100 535q0 239 150.5 390.5t378.5 151.5q142 0 256.5 -67t179.5 -189v248h102v-1069h-100v252q-63 -124 -178 -192t-260 -68q-227 0 -378 153t-151 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM459 1419 q0 85 58 145t145 60t145.5 -60.5t58.5 -144.5q0 -83 -58 -140.5t-146 -57.5q-87 0 -145 57.5t-58 140.5zM520 1419q0 -61 40 -101t102 -40t102.5 41t40.5 100q0 61 -41 103.5t-102 42.5t-101.5 -42t-40.5 -104z" />
<glyph unicode="&#xe6;" horiz-adv-x="2017" d="M121 301q0 60 21 111t66 93t125 65.5t187 23.5h367v88q0 150 -81 227.5t-237 77.5q-107 0 -203.5 -36.5t-164.5 -98.5l-51 74q80 71 191.5 111t233.5 40q322 0 390 -254q67 119 186 186.5t268 67.5q210 0 348.5 -140.5t147.5 -356.5l-909 -179q40 -145 160.5 -231 t285.5 -86q208 0 336 149l61 -67q-148 -174 -399 -174q-160 0 -292.5 71.5t-205.5 208.5q-47 -144 -164.5 -212t-273.5 -68q-184 0 -288.5 84.5t-104.5 224.5zM223 303q0 -106 79 -166.5t218 -60.5q168 0 267.5 89.5t99.5 248.5v100h-365q-157 0 -228 -57t-71 -154zM989 530 q0 -35 2 -51l821 160q-24 149 -130.5 248.5t-264.5 99.5q-187 0 -307.5 -123.5t-120.5 -314.5v-19z" />
<glyph unicode="&#xe7;" horiz-adv-x="1136" d="M100 535q0 238 152.5 390t388.5 152q130 0 235.5 -49.5t170.5 -146.5l-76 -56q-114 160 -330 160q-191 0 -313.5 -124.5t-122.5 -325.5t122.5 -326t313.5 -125q216 0 330 160l76 -56q-65 -96 -171 -146t-235 -50h-10l-27 -103q81 -6 127.5 -50t46.5 -113 q0 -77 -60 -123.5t-155 -46.5q-97 0 -162 51l29 57q55 -43 133 -43q62 0 98.5 28.5t36.5 80.5q0 47 -35.5 75.5t-99.5 28.5h-41l43 162q-207 26 -336 174t-129 365z" />
<glyph unicode="&#xe8;" horiz-adv-x="1222" d="M100 535q0 237 146.5 389.5t374.5 152.5q212 0 351 -140.5t150 -356.5l-903 -177q38 -148 155 -233.5t281 -85.5q220 0 342 149l60 -67q-70 -85 -174.5 -129.5t-229.5 -44.5q-243 0 -398 152.5t-155 390.5zM201 543q0 -24 4 -64l815 160q-23 148 -131.5 248t-267.5 100 q-184 0 -302 -124.5t-118 -319.5zM291 1485h147l285 -242h-111z" />
<glyph unicode="&#xe9;" horiz-adv-x="1222" d="M100 535q0 237 146.5 389.5t374.5 152.5q212 0 351 -140.5t150 -356.5l-903 -177q38 -148 155 -233.5t281 -85.5q220 0 342 149l60 -67q-70 -85 -174.5 -129.5t-229.5 -44.5q-243 0 -398 152.5t-155 390.5zM201 543q0 -24 4 -64l815 160q-23 148 -131.5 248t-267.5 100 q-184 0 -302 -124.5t-118 -319.5zM489 1243l285 242h148l-322 -242h-111z" />
<glyph unicode="&#xea;" horiz-adv-x="1222" d="M100 535q0 237 146.5 389.5t374.5 152.5q212 0 351 -140.5t150 -356.5l-903 -177q38 -148 155 -233.5t281 -85.5q220 0 342 149l60 -67q-70 -85 -174.5 -129.5t-229.5 -44.5q-243 0 -398 152.5t-155 390.5zM201 543q0 -24 4 -64l815 160q-23 148 -131.5 248t-267.5 100 q-184 0 -302 -124.5t-118 -319.5zM291 1243l258 242h115l258 -242h-103l-213 178l-213 -178h-102z" />
<glyph unicode="&#xeb;" horiz-adv-x="1222" d="M100 535q0 237 146.5 389.5t374.5 152.5q212 0 351 -140.5t150 -356.5l-903 -177q38 -148 155 -233.5t281 -85.5q220 0 342 149l60 -67q-70 -85 -174.5 -129.5t-229.5 -44.5q-243 0 -398 152.5t-155 390.5zM201 543q0 -24 4 -64l815 160q-23 148 -131.5 248t-267.5 100 q-184 0 -302 -124.5t-118 -319.5zM356 1372q0 32 21.5 53t52.5 21q30 0 52 -21t22 -53q0 -30 -22 -52t-52 -22q-31 0 -52.5 21.5t-21.5 52.5zM709 1372q0 32 21.5 53t51.5 21q31 0 52.5 -21t21.5 -53q0 -31 -21.5 -52.5t-52.5 -21.5q-30 0 -51.5 22t-21.5 52z" />
<glyph unicode="&#xec;" horiz-adv-x="534" d="M-47 1485h147l285 -242h-111zM217 0v1069h102v-1069h-102z" />
<glyph unicode="&#xed;" horiz-adv-x="534" d="M152 1243l284 242h148l-322 -242h-110zM217 0v1069h102v-1069h-102z" />
<glyph unicode="&#xee;" horiz-adv-x="534" d="M4 1243l209 242h111l208 -242h-100l-164 176l-164 -176h-100zM217 0v1069h102v-1069h-102z" />
<glyph unicode="&#xef;" horiz-adv-x="534" d="M70 1372q0 31 20.5 52.5t50.5 21.5q31 0 51.5 -21.5t20.5 -52.5t-20.5 -51.5t-51.5 -20.5q-30 0 -50.5 21t-20.5 51zM217 0v1069h102v-1069h-102zM324 1372q0 31 20.5 52.5t50.5 21.5q31 0 51.5 -21.5t20.5 -52.5t-20.5 -51.5t-51.5 -20.5q-30 0 -50.5 21t-20.5 51z" />
<glyph unicode="&#xf0;" horiz-adv-x="1284" d="M100 440q0 202 139.5 325.5t364.5 123.5q171 0 296 -85.5t167 -238.5q12 73 12 168q0 187 -57.5 323t-163.5 214l-551 -236l-31 74l496 213q-107 49 -250 49q-159 0 -303 -45l-20 90q153 43 323 43q208 0 359 -92l180 78l31 -74l-136 -57q226 -200 226 -600 q0 -336 -153 -529.5t-437 -193.5q-216 0 -354 124t-138 326zM205 440q0 -161 108 -260.5t285 -99.5q98 0 179.5 31t133 82t79.5 115t28 132q0 152 -114.5 255.5t-291.5 103.5q-189 0 -298 -98.5t-109 -260.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1378" d="M217 0v1069h98v-231q55 114 164 176.5t260 62.5q200 0 317.5 -116.5t117.5 -335.5v-625h-103v616q0 180 -90.5 274.5t-255.5 94.5q-188 0 -297 -112.5t-109 -303.5v-569h-102zM387 1268q3 93 49 149t121 56q41 0 80.5 -22t65.5 -49t58 -49t60 -22q47 0 76.5 35t32.5 94 h67q-3 -90 -49.5 -145t-120.5 -55q-41 0 -80.5 22t-65.5 48.5t-58 48.5t-60 22q-47 0 -76 -35.5t-32 -97.5h-68z" />
<glyph unicode="&#xf2;" horiz-adv-x="1269" d="M100 535q0 237 152 389.5t383 152.5t382.5 -152.5t151.5 -389.5t-151.5 -390t-382.5 -153t-383 153t-152 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM319 1485h148l285 -242h-111z" />
<glyph unicode="&#xf3;" horiz-adv-x="1269" d="M100 535q0 237 152 389.5t383 152.5t382.5 -152.5t151.5 -389.5t-151.5 -390t-382.5 -153t-383 153t-152 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM518 1243l285 242h147l-321 -242h-111z" />
<glyph unicode="&#xf4;" horiz-adv-x="1269" d="M100 535q0 237 152 389.5t383 152.5t382.5 -152.5t151.5 -389.5t-151.5 -390t-382.5 -153t-383 153t-152 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM319 1243l259 242h114l258 -242h-102l-213 178 l-213 -178h-103z" />
<glyph unicode="&#xf5;" horiz-adv-x="1269" d="M100 535q0 237 152 389.5t383 152.5t382.5 -152.5t151.5 -389.5t-151.5 -390t-382.5 -153t-383 153t-152 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM330 1268q3 93 49 149t121 56q41 0 80.5 -22t65.5 -49 t58 -49t60 -22q47 0 76 35t32 94h68q-3 -90 -49.5 -145t-120.5 -55q-41 0 -80.5 22t-65.5 48.5t-58 48.5t-60 22q-47 0 -76.5 -36t-32.5 -97h-67z" />
<glyph unicode="&#xf6;" horiz-adv-x="1269" d="M100 535q0 237 152 389.5t383 152.5t382.5 -152.5t151.5 -389.5t-151.5 -390t-382.5 -153t-383 153t-152 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM385 1372q0 32 21.5 53t52.5 21q30 0 51.5 -21 t21.5 -53q0 -30 -22 -52t-51 -22q-31 0 -52.5 21.5t-21.5 52.5zM737 1372q0 32 22 53t52 21q31 0 52.5 -21t21.5 -53q0 -31 -21.5 -52.5t-52.5 -21.5q-30 0 -52 22t-22 52z" />
<glyph unicode="&#xf7;" horiz-adv-x="1165" d="M147 672v88h871v-88h-871zM504 336q0 34 23 57t55 23q34 0 57 -23t23 -57q0 -35 -23 -58.5t-57 -23.5q-33 0 -55.5 23.5t-22.5 58.5zM504 1100q0 34 23 57t55 23q34 0 57 -23t23 -57t-23 -58t-57 -24q-32 0 -55 24t-23 58z" />
<glyph unicode="&#xf8;" horiz-adv-x="1269" d="M100 535q0 237 152 389.5t383 152.5q152 0 278 -71l138 204h77l-161 -241q95 -73 148.5 -185.5t53.5 -248.5q0 -237 -151.5 -390t-382.5 -153q-156 0 -277 71l-139 -208h-78l164 243q-97 73 -151 186.5t-54 250.5zM205 535q0 -112 40.5 -203.5t112.5 -151.5l502 746 q-100 59 -225 59q-186 0 -308 -125.5t-122 -324.5zM412 143q100 -59 223 -59q186 0 308 126t122 325q0 226 -154 354z" />
<glyph unicode="&#xf9;" horiz-adv-x="1370" d="M205 444v625h102v-616q0 -180 91.5 -274.5t260.5 -94.5q181 0 286.5 112.5t105.5 303.5v569h102v-1069h-98v229q-53 -113 -158 -175t-246 -62q-208 0 -327 116t-119 336zM365 1485h147l285 -242h-111z" />
<glyph unicode="&#xfa;" horiz-adv-x="1370" d="M205 444v625h102v-616q0 -180 91.5 -274.5t260.5 -94.5q181 0 286.5 112.5t105.5 303.5v569h102v-1069h-98v229q-53 -113 -158 -175t-246 -62q-208 0 -327 116t-119 336zM563 1243l285 242h147l-321 -242h-111z" />
<glyph unicode="&#xfb;" horiz-adv-x="1370" d="M205 444v625h102v-616q0 -180 91.5 -274.5t260.5 -94.5q181 0 286.5 112.5t105.5 303.5v569h102v-1069h-98v229q-53 -113 -158 -175t-246 -62q-208 0 -327 116t-119 336zM365 1243l258 242h114l258 -242h-102l-213 178l-213 -178h-102z" />
<glyph unicode="&#xfc;" horiz-adv-x="1370" d="M205 444v625h102v-616q0 -180 91.5 -274.5t260.5 -94.5q181 0 286.5 112.5t105.5 303.5v569h102v-1069h-98v229q-53 -113 -158 -175t-246 -62q-208 0 -327 116t-119 336zM430 1372q0 32 21.5 53t52.5 21q30 0 52 -21t22 -53q0 -30 -22 -52t-52 -22q-31 0 -52.5 21.5 t-21.5 52.5zM782 1372q0 32 22 53t52 21q31 0 52.5 -21t21.5 -53q0 -31 -21.5 -52.5t-52.5 -21.5q-30 0 -52 22t-22 52z" />
<glyph unicode="&#xfd;" horiz-adv-x="1370" d="M205 492v577h102v-569q0 -180 91.5 -274.5t260.5 -94.5q181 0 286.5 112.5t105.5 303.5v522h102v-954q0 -266 -118.5 -393.5t-358.5 -127.5q-137 0 -259 45t-194 125l56 78q71 -74 173 -114.5t220 -40.5q195 0 287 100.5t92 314.5v164q-55 -107 -159 -167t-241 -60 q-208 0 -327 116.5t-119 336.5zM563 1243l285 242h147l-321 -242h-111z" />
<glyph unicode="&#xfe;" d="M217 -397v1917h102v-695q64 120 179 186t256 66q227 0 377.5 -152.5t150.5 -389.5q0 -239 -150 -391t-378 -152q-141 0 -256 66t-179 186v-641h-102zM317 535q0 -199 122.5 -325t308.5 -126t309 126t123 325t-122.5 324.5t-309.5 125.5q-186 0 -308.5 -125.5 t-122.5 -324.5z" />
<glyph unicode="&#xff;" horiz-adv-x="1370" d="M205 492v577h102v-569q0 -180 91.5 -274.5t260.5 -94.5q181 0 286.5 112.5t105.5 303.5v522h102v-954q0 -266 -118.5 -393.5t-358.5 -127.5q-137 0 -259 45t-194 125l56 78q71 -74 173 -114.5t220 -40.5q195 0 287 100.5t92 314.5v164q-55 -107 -159 -167t-241 -60 q-208 0 -327 116.5t-119 336.5zM430 1372q0 32 21.5 53t52.5 21q30 0 52 -21t22 -53q0 -30 -22 -52t-52 -22q-31 0 -52.5 21.5t-21.5 52.5zM782 1372q0 32 22 53t52 21q31 0 52.5 -21t21.5 -53q0 -31 -21.5 -52.5t-52.5 -21.5q-30 0 -52 22t-22 52z" />
<glyph unicode="&#x152;" horiz-adv-x="2287" d="M113 715q0 208 94.5 371.5t268.5 255.5t398 92h1254v-95h-854v-565h762v-92h-762v-588h883v-94h-1283q-168 0 -310.5 53t-241 147.5t-154 227t-55.5 287.5zM217 715q0 -135 47.5 -250t133.5 -196.5t210 -128t271 -46.5h290v1245h-290q-297 0 -479.5 -174t-182.5 -450z " />
<glyph unicode="&#x153;" horiz-adv-x="2222" d="M100 535q0 237 152 389.5t383 152.5q167 0 294.5 -84.5t186.5 -228.5q59 145 188 229t300 84q218 0 362.5 -140.5t155.5 -356.5l-938 -179q41 -145 163 -231t289 -86q229 0 357 149l61 -67q-73 -85 -182 -129.5t-238 -44.5q-184 0 -320 84t-198 231q-57 -146 -185 -230.5 t-296 -84.5q-231 0 -383 153t-152 390zM205 535q0 -199 122 -325t308 -126t308 126t122 325t-122 324.5t-308 125.5t-308 -125.5t-122 -324.5zM1167 530q0 -35 2 -51l848 160q-26 149 -137.5 248.5t-275.5 99.5q-192 0 -314.5 -123.5t-122.5 -314.5v-19z" />
<glyph unicode="&#x178;" horiz-adv-x="1613" d="M227 950v484h105v-480q0 -243 121 -368t342 -125q228 0 352.5 132.5t124.5 397.5v443h104v-1026q0 -304 -153 -462.5t-428 -158.5q-173 0 -310 58t-229 167l53 82q179 -211 488 -211q230 0 352.5 131t122.5 392v237q-132 -278 -486 -278q-264 0 -411.5 151t-147.5 434z M553 1679q0 32 21.5 53t52.5 21q30 0 51.5 -21t21.5 -53q0 -30 -21.5 -51.5t-51.5 -21.5q-31 0 -52.5 21t-21.5 52zM905 1679q0 32 22 53t52 21q31 0 52.5 -21t21.5 -53q0 -31 -21.5 -52t-52.5 -21q-30 0 -52 21.5t-22 51.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1228" d="M299 1243l258 242h115l258 -242h-103l-213 178l-213 -178h-102z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1228" d="M309 1268q3 93 49 149t121 56q41 0 80.5 -22t65.5 -49t58 -49t60 -22q47 0 76.5 35t32.5 94h68q-3 -90 -49.5 -145t-120.5 -55q-41 0 -81 22t-65.5 48.5t-58 48.5t-60.5 22q-47 0 -76 -35.5t-32 -97.5h-68z" />
<glyph unicode="&#x2000;" horiz-adv-x="994" />
<glyph unicode="&#x2001;" horiz-adv-x="1989" />
<glyph unicode="&#x2002;" horiz-adv-x="994" />
<glyph unicode="&#x2003;" horiz-adv-x="1989" />
<glyph unicode="&#x2004;" horiz-adv-x="663" />
<glyph unicode="&#x2005;" horiz-adv-x="497" />
<glyph unicode="&#x2006;" horiz-adv-x="331" />
<glyph unicode="&#x2007;" horiz-adv-x="331" />
<glyph unicode="&#x2008;" horiz-adv-x="248" />
<glyph unicode="&#x2009;" horiz-adv-x="397" />
<glyph unicode="&#x200a;" horiz-adv-x="110" />
<glyph unicode="&#x2010;" horiz-adv-x="780" d="M127 504v92h526v-92h-526z" />
<glyph unicode="&#x2011;" horiz-adv-x="780" d="M127 504v92h526v-92h-526z" />
<glyph unicode="&#x2012;" horiz-adv-x="780" d="M127 504v92h526v-92h-526z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 512v76h1024v-76h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 512v76h2048v-76h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="409" d="M119 1147q0 36 18 94l80 279h72l-66 -289q29 -8 47.5 -30.5t18.5 -53.5q0 -37 -24.5 -61.5t-59.5 -24.5q-38 0 -62 25t-24 61z" />
<glyph unicode="&#x2019;" horiz-adv-x="409" d="M119 1069l67 289q-31 8 -49 30t-18 54q0 37 24.5 61.5t61.5 24.5t61.5 -25.5t24.5 -60.5q0 -26 -19 -94l-79 -279h-74z" />
<glyph unicode="&#x201a;" horiz-adv-x="409" d="M119 80q0 37 24.5 61.5t61.5 24.5t61.5 -25.5t24.5 -60.5q0 -26 -19 -94l-79 -279h-74l67 289q-31 8 -49 30t-18 54z" />
<glyph unicode="&#x201c;" horiz-adv-x="722" d="M119 1147q0 36 18 94l80 279h72l-66 -289q29 -8 47.5 -30.5t18.5 -53.5q0 -37 -24.5 -61.5t-59.5 -24.5q-38 0 -62 25t-24 61zM432 1147q0 33 19 94l79 279h72l-65 -289q29 -8 47 -30.5t18 -53.5q0 -37 -24.5 -61.5t-59.5 -24.5q-38 0 -62 25t-24 61z" />
<glyph unicode="&#x201d;" horiz-adv-x="722" d="M119 1069l67 289q-31 8 -49 30t-18 54q0 37 24.5 61.5t61.5 24.5t61.5 -25.5t24.5 -60.5q0 -26 -19 -94l-79 -279h-74zM432 1069l68 289q-31 8 -49.5 30t-18.5 54q0 37 24.5 61.5t61.5 24.5t61.5 -25.5t24.5 -60.5q0 -30 -18 -94l-80 -279h-74z" />
<glyph unicode="&#x201e;" horiz-adv-x="722" d="M119 80q0 37 24.5 61.5t61.5 24.5t61.5 -25.5t24.5 -60.5q0 -26 -19 -94l-79 -279h-74l67 289q-31 8 -49 30t-18 54zM432 80q0 37 24.5 61.5t61.5 24.5t61.5 -25.5t24.5 -60.5q0 -30 -18 -94l-80 -279h-74l68 289q-31 8 -49.5 30t-18.5 54z" />
<glyph unicode="&#x2022;" horiz-adv-x="573" d="M160 557q0 54 37 90.5t90 36.5t90 -36.5t37 -90.5q0 -55 -37 -92t-90 -37t-90 37t-37 92z" />
<glyph unicode="&#x2026;" horiz-adv-x="1247" d="M119 80q0 36 25 61t61 25t61 -25t25 -61q0 -37 -25.5 -62.5t-60.5 -25.5t-60.5 25.5t-25.5 62.5zM539 80q0 36 25 61t61 25t61 -25t25 -61q0 -37 -25.5 -62.5t-60.5 -25.5t-60.5 25.5t-25.5 62.5zM958 80q0 36 25 61t61 25t61 -25t25 -61q0 -37 -25.5 -62.5t-60.5 -25.5 t-60.5 25.5t-25.5 62.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="397" />
<glyph unicode="&#x2039;" horiz-adv-x="589" d="M109 535l299 385h100l-295 -385l295 -383h-100z" />
<glyph unicode="&#x203a;" horiz-adv-x="589" d="M84 152l295 383l-295 385h98l299 -385l-299 -383h-98z" />
<glyph unicode="&#x205f;" horiz-adv-x="497" />
<glyph unicode="&#x20ac;" horiz-adv-x="1634" d="M61 541v69h226q-6 53 -6 107q0 53 6 106h-226v70h240q57 246 255 398.5t468 152.5q155 0 286.5 -50.5t225.5 -148.5l-68 -67q-173 170 -440 170q-225 0 -393.5 -126t-224.5 -329h747v-70h-762q-8 -47 -8 -106q0 -60 8 -107h762v-69h-747q56 -203 224.5 -329t393.5 -126 q265 0 440 172l68 -68q-94 -99 -226 -149.5t-286 -50.5q-270 0 -468 152.5t-255 398.5h-240z" />
<glyph unicode="&#x2122;" horiz-adv-x="2078" d="M8 1364v70h737v-70h-327v-780h-82v780h-328zM881 584v850h67l408 -641l403 641h70v-850h-80v702l-375 -592h-39l-374 582v-692h-80z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1064" d="M0 0v1065h1065v-1065h-1065z" />
<hkern u1="&#x2a;" u2="&#xee;" k="-164" />
<hkern u1="&#x2f;" u2="&#xf0;" k="100" />
<hkern u1="&#x2f;" u2="i" k="-6" />
<hkern u1="F" u2="&#xef;" k="-6" />
<hkern u1="F" u2="&#xee;" k="-47" />
<hkern u1="T" u2="&#xef;" k="-47" />
<hkern u1="T" u2="&#xee;" k="-82" />
<hkern u1="V" u2="&#xef;" k="-23" />
<hkern u1="V" u2="&#xee;" k="-59" />
<hkern u1="V" u2="&#xec;" k="-33" />
<hkern u1="V" u2="i" k="43" />
<hkern u1="f" u2="&#xef;" k="-123" />
<hkern u1="f" u2="&#xee;" k="-88" />
<hkern u1="f" u2="&#xec;" k="-141" />
<hkern u1="i" u2="\" k="119" />
<hkern u1="j" u2="\" k="119" />
<hkern u1="q" u2="j" k="-102" />
<hkern u1="&#xa3;" u2="&#xef;" k="-6" />
<hkern u1="&#xa3;" u2="&#xee;" k="-47" />
<hkern u1="&#xaa;" u2="&#xee;" k="-164" />
<hkern u1="&#xba;" u2="&#xee;" k="-164" />
<hkern u1="&#xee;" u2="&#xba;" k="-164" />
<hkern u1="&#xee;" u2="&#xaa;" k="-164" />
<hkern u1="&#xee;" u2="&#x3f;" k="-82" />
<hkern u1="&#xee;" u2="&#x2a;" k="-164" />
<hkern u1="&#x2018;" u2="&#xec;" k="-61" />
<hkern u1="&#x201c;" u2="&#xec;" k="-61" />
<hkern g1="ampersand" 	g2="ampersand" 	k="18" />
<hkern g1="ampersand" 	g2="backslash" 	k="164" />
<hkern g1="ampersand" 	g2="bracketright,braceright" 	k="6" />
<hkern g1="ampersand" 	g2="colon,semicolon" 	k="12" />
<hkern g1="ampersand" 	g2="degree" 	k="70" />
<hkern g1="ampersand" 	g2="exclam" 	k="23" />
<hkern g1="ampersand" 	g2="exclamdown" 	k="20" />
<hkern g1="ampersand" 	g2="four" 	k="-2" />
<hkern g1="ampersand" 	g2="one" 	k="61" />
<hkern g1="ampersand" 	g2="paragraph" 	k="74" />
<hkern g1="ampersand" 	g2="percent" 	k="82" />
<hkern g1="ampersand" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-14" />
<hkern g1="ampersand" 	g2="question" 	k="166" />
<hkern g1="ampersand" 	g2="questiondown" 	k="2" />
<hkern g1="ampersand" 	g2="quoteleft,quotedblleft" 	k="76" />
<hkern g1="ampersand" 	g2="quoteright,quotedblright" 	k="70" />
<hkern g1="ampersand" 	g2="quotedbl,quotesingle" 	k="115" />
<hkern g1="ampersand" 	g2="seven" 	k="39" />
<hkern g1="ampersand" 	g2="slash" 	k="-94" />
<hkern g1="ampersand" 	g2="three" 	k="2" />
<hkern g1="ampersand" 	g2="trademark" 	k="63" />
<hkern g1="ampersand" 	g2="two" 	k="6" />
<hkern g1="ampersand" 	g2="underscore" 	k="-104" />
<hkern g1="currency" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="currency" 	g2="questiondown" 	k="18" />
<hkern g1="currency" 	g2="seven" 	k="35" />
<hkern g1="currency" 	g2="two" 	k="18" />
<hkern g1="currency" 	g2="underscore" 	k="18" />
<hkern g1="currency" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-18" />
<hkern g1="degree" 	g2="backslash" 	k="-104" />
<hkern g1="degree" 	g2="four" 	k="82" />
<hkern g1="degree" 	g2="one" 	k="-84" />
<hkern g1="degree" 	g2="percent" 	k="-43" />
<hkern g1="degree" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="125" />
<hkern g1="degree" 	g2="question" 	k="-41" />
<hkern g1="degree" 	g2="questiondown" 	k="137" />
<hkern g1="degree" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="degree" 	g2="seven" 	k="-96" />
<hkern g1="degree" 	g2="slash" 	k="121" />
<hkern g1="degree" 	g2="three" 	k="-33" />
<hkern g1="degree" 	g2="two" 	k="-61" />
<hkern g1="degree" 	g2="five" 	k="-18" />
<hkern g1="degree" 	g2="nine" 	k="-80" />
<hkern g1="degree" 	g2="zero,six" 	k="-8" />
<hkern g1="percent" 	g2="backslash" 	k="80" />
<hkern g1="percent" 	g2="degree" 	k="59" />
<hkern g1="percent" 	g2="four" 	k="-70" />
<hkern g1="percent" 	g2="one" 	k="41" />
<hkern g1="percent" 	g2="percent" 	k="240" />
<hkern g1="percent" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="percent" 	g2="question" 	k="121" />
<hkern g1="percent" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="percent" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="percent" 	g2="quotedbl,quotesingle" 	k="59" />
<hkern g1="percent" 	g2="seven" 	k="39" />
<hkern g1="percent" 	g2="slash" 	k="-2" />
<hkern g1="percent" 	g2="three" 	k="-20" />
<hkern g1="percent" 	g2="two" 	k="-20" />
<hkern g1="percent" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="percent" 	g2="five" 	k="-41" />
<hkern g1="percent" 	g2="parenright" 	k="41" />
<hkern g1="percent" 	g2="eight" 	k="-41" />
<hkern g1="section" 	g2="four" 	k="-39" />
<hkern g1="section" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="section" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="section" 	g2="seven" 	k="-18" />
<hkern g1="section" 	g2="slash" 	k="-55" />
<hkern g1="section" 	g2="underscore" 	k="-18" />
<hkern g1="section" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-41" />
<hkern g1="section" 	g2="eight" 	k="-20" />
<hkern g1="trademark" 	g2="backslash" 	k="-82" />
<hkern g1="trademark" 	g2="exclamdown" 	k="-2" />
<hkern g1="trademark" 	g2="questiondown" 	k="35" />
<hkern g1="trademark" 	g2="seven" 	k="-23" />
<hkern g1="trademark" 	g2="slash" 	k="96" />
<hkern g1="trademark" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="trademark" 	g2="nine" 	k="-61" />
<hkern g1="yen" 	g2="backslash" 	k="-102" />
<hkern g1="yen" 	g2="bracketright,braceright" 	k="-12" />
<hkern g1="yen" 	g2="colon,semicolon" 	k="82" />
<hkern g1="yen" 	g2="exclam" 	k="-8" />
<hkern g1="yen" 	g2="exclamdown" 	k="41" />
<hkern g1="yen" 	g2="four" 	k="20" />
<hkern g1="yen" 	g2="one" 	k="-61" />
<hkern g1="yen" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="yen" 	g2="questiondown" 	k="82" />
<hkern g1="yen" 	g2="quoteright,quotedblright" 	k="-6" />
<hkern g1="yen" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="yen" 	g2="seven" 	k="-59" />
<hkern g1="yen" 	g2="slash" 	k="61" />
<hkern g1="yen" 	g2="three" 	k="-20" />
<hkern g1="yen" 	g2="underscore" 	k="20" />
<hkern g1="yen" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="43" />
<hkern g1="yen" 	g2="zero,six" 	k="23" />
<hkern g1="yen" 	g2="parenright" 	k="-12" />
<hkern g1="yen" 	g2="eight" 	k="20" />
<hkern g1="backslash" 	g2="ampersand" 	k="-39" />
<hkern g1="backslash" 	g2="backslash" 	k="129" />
<hkern g1="backslash" 	g2="bracketright,braceright" 	k="-68" />
<hkern g1="backslash" 	g2="colon,semicolon" 	k="-88" />
<hkern g1="backslash" 	g2="degree" 	k="123" />
<hkern g1="backslash" 	g2="exclamdown" 	k="-82" />
<hkern g1="backslash" 	g2="five" 	k="-2" />
<hkern g1="backslash" 	g2="four" 	k="18" />
<hkern g1="backslash" 	g2="guillemotright,guilsinglright" 	k="-27" />
<hkern g1="backslash" 	g2="one" 	k="41" />
<hkern g1="backslash" 	g2="paragraph" 	k="61" />
<hkern g1="backslash" 	g2="parenright" 	k="-61" />
<hkern g1="backslash" 	g2="percent" 	k="20" />
<hkern g1="backslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-217" />
<hkern g1="backslash" 	g2="question" 	k="123" />
<hkern g1="backslash" 	g2="questiondown" 	k="-61" />
<hkern g1="backslash" 	g2="quoteleft,quotedblleft" 	k="117" />
<hkern g1="backslash" 	g2="quoteright,quotedblright" 	k="117" />
<hkern g1="backslash" 	g2="quotedbl,quotesingle" 	k="123" />
<hkern g1="backslash" 	g2="section" 	k="-61" />
<hkern g1="backslash" 	g2="seven" 	k="102" />
<hkern g1="backslash" 	g2="slash" 	k="-35" />
<hkern g1="backslash" 	g2="three" 	k="-20" />
<hkern g1="backslash" 	g2="trademark" 	k="123" />
<hkern g1="backslash" 	g2="two" 	k="-20" />
<hkern g1="backslash" 	g2="underscore" 	k="-287" />
<hkern g1="backslash" 	g2="zero,six" 	k="41" />
<hkern g1="bracketleft,braceleft" 	g2="backslash" 	k="-61" />
<hkern g1="bracketleft,braceleft" 	g2="exclamdown" 	k="-2" />
<hkern g1="bracketleft,braceleft" 	g2="four" 	k="23" />
<hkern g1="bracketleft,braceleft" 	g2="one" 	k="-27" />
<hkern g1="bracketleft,braceleft" 	g2="paragraph" 	k="-2" />
<hkern g1="bracketleft,braceleft" 	g2="parenright" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="question" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="quotedbl,quotesingle" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="seven" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="slash" 	k="-68" />
<hkern g1="bracketleft,braceleft" 	g2="three" 	k="-2" />
<hkern g1="bracketleft,braceleft" 	g2="trademark" 	k="-88" />
<hkern g1="bracketleft,braceleft" 	g2="two" 	k="-2" />
<hkern g1="bracketleft,braceleft" 	g2="underscore" 	k="-47" />
<hkern g1="bracketleft,braceleft" 	g2="exclam" 	k="-2" />
<hkern g1="bracketleft,braceleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="23" />
<hkern g1="bracketleft,braceleft" 	g2="yen" 	k="-12" />
<hkern g1="colon,semicolon" 	g2="backslash" 	k="33" />
<hkern g1="colon,semicolon" 	g2="question" 	k="20" />
<hkern g1="colon,semicolon" 	g2="quotedbl,quotesingle" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="seven" 	k="-18" />
<hkern g1="colon,semicolon" 	g2="slash" 	k="-88" />
<hkern g1="colon,semicolon" 	g2="underscore" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="yen" 	k="82" />
<hkern g1="exclam" 	g2="bracketright,braceright" 	k="-2" />
<hkern g1="exclam" 	g2="one" 	k="-20" />
<hkern g1="exclam" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-2" />
<hkern g1="exclam" 	g2="quoteleft,quotedblleft" 	k="-2" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="-2" />
<hkern g1="exclam" 	g2="quotedbl,quotesingle" 	k="-2" />
<hkern g1="exclam" 	g2="seven" 	k="-18" />
<hkern g1="exclam" 	g2="trademark" 	k="-6" />
<hkern g1="exclam" 	g2="yen" 	k="-8" />
<hkern g1="exclamdown" 	g2="backslash" 	k="113" />
<hkern g1="exclamdown" 	g2="bracketright,braceright" 	k="-2" />
<hkern g1="exclamdown" 	g2="one" 	k="41" />
<hkern g1="exclamdown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-2" />
<hkern g1="exclamdown" 	g2="question" 	k="2" />
<hkern g1="exclamdown" 	g2="quoteleft,quotedblleft" 	k="-2" />
<hkern g1="exclamdown" 	g2="quoteright,quotedblright" 	k="-2" />
<hkern g1="exclamdown" 	g2="slash" 	k="-82" />
<hkern g1="exclamdown" 	g2="trademark" 	k="2" />
<hkern g1="exclamdown" 	g2="underscore" 	k="-8" />
<hkern g1="exclamdown" 	g2="yen" 	k="41" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="backslash" 	k="121" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="five" 	k="-18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="four" 	k="-18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="one" 	k="2" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-2" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="question" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="questiondown" 	k="-18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="slash" 	k="-27" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="underscore" 	k="-20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="backslash" 	k="133" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="four" 	k="-18" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="one" 	k="33" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="paragraph" 	k="182" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="percent" 	k="80" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="12" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="question" 	k="82" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="section" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="seven" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="three" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="trademark" 	k="41" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="two" 	k="23" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="zero,six" 	k="-18" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-2" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="yen" 	k="43" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="currency,Euro" 	k="-18" />
<hkern g1="parenleft" 	g2="backslash" 	k="-68" />
<hkern g1="parenleft" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="parenleft" 	g2="four" 	k="41" />
<hkern g1="parenleft" 	g2="one" 	k="-20" />
<hkern g1="parenleft" 	g2="slash" 	k="-63" />
<hkern g1="parenleft" 	g2="trademark" 	k="-47" />
<hkern g1="parenleft" 	g2="underscore" 	k="-2" />
<hkern g1="parenleft" 	g2="yen" 	k="-12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="backslash" 	k="203" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="degree" 	k="125" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclamdown" 	k="-2" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="four" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="guillemotright,guilsinglright" 	k="-2" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="one" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="paragraph" 	k="240" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="percent" 	k="125" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="question" 	k="104" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="questiondown" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="113" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="seven" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="slash" 	k="-217" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="three" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="trademark" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="two" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="underscore" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="zero,six" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="exclam" 	k="-2" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="yen" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="currency,Euro" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="eight" 	k="-20" />
<hkern g1="question" 	g2="ampersand" 	k="53" />
<hkern g1="question" 	g2="degree" 	k="-20" />
<hkern g1="question" 	g2="exclamdown" 	k="18" />
<hkern g1="question" 	g2="four" 	k="102" />
<hkern g1="question" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="question" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="102" />
<hkern g1="question" 	g2="question" 	k="72" />
<hkern g1="question" 	g2="questiondown" 	k="229" />
<hkern g1="question" 	g2="quoteleft,quotedblleft" 	k="-43" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="-43" />
<hkern g1="question" 	g2="slash" 	k="61" />
<hkern g1="question" 	g2="three" 	k="31" />
<hkern g1="question" 	g2="underscore" 	k="47" />
<hkern g1="question" 	g2="exclam" 	k="18" />
<hkern g1="question" 	g2="yen" 	k="31" />
<hkern g1="question" 	g2="eight" 	k="20" />
<hkern g1="question" 	g2="bracketleft" 	k="43" />
<hkern g1="question" 	g2="nine" 	k="-20" />
<hkern g1="questiondown" 	g2="ampersand" 	k="39" />
<hkern g1="questiondown" 	g2="backslash" 	k="207" />
<hkern g1="questiondown" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="questiondown" 	g2="colon,semicolon" 	k="2" />
<hkern g1="questiondown" 	g2="degree" 	k="109" />
<hkern g1="questiondown" 	g2="four" 	k="84" />
<hkern g1="questiondown" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="questiondown" 	g2="one" 	k="84" />
<hkern g1="questiondown" 	g2="paragraph" 	k="176" />
<hkern g1="questiondown" 	g2="percent" 	k="123" />
<hkern g1="questiondown" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-2" />
<hkern g1="questiondown" 	g2="question" 	k="268" />
<hkern g1="questiondown" 	g2="questiondown" 	k="70" />
<hkern g1="questiondown" 	g2="quoteleft,quotedblleft" 	k="43" />
<hkern g1="questiondown" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="questiondown" 	g2="quotedbl,quotesingle" 	k="53" />
<hkern g1="questiondown" 	g2="section" 	k="-2" />
<hkern g1="questiondown" 	g2="seven" 	k="84" />
<hkern g1="questiondown" 	g2="slash" 	k="-82" />
<hkern g1="questiondown" 	g2="trademark" 	k="68" />
<hkern g1="questiondown" 	g2="underscore" 	k="-102" />
<hkern g1="questiondown" 	g2="zero,six" 	k="88" />
<hkern g1="questiondown" 	g2="exclam" 	k="2" />
<hkern g1="questiondown" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="82" />
<hkern g1="questiondown" 	g2="yen" 	k="102" />
<hkern g1="questiondown" 	g2="sterling" 	k="-6" />
<hkern g1="questiondown" 	g2="currency,Euro" 	k="53" />
<hkern g1="questiondown" 	g2="eight" 	k="84" />
<hkern g1="quoteleft,quotedblleft" 	g2="ampersand" 	k="35" />
<hkern g1="quoteleft,quotedblleft" 	g2="backslash" 	k="-117" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="-2" />
<hkern g1="quoteleft,quotedblleft" 	g2="four" 	k="80" />
<hkern g1="quoteleft,quotedblleft" 	g2="one" 	k="-80" />
<hkern g1="quoteleft,quotedblleft" 	g2="paragraph" 	k="-80" />
<hkern g1="quoteleft,quotedblleft" 	g2="percent" 	k="-82" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="question" 	k="-80" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteleft,quotedblleft" 	k="-47" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteright,quotedblright" 	k="-47" />
<hkern g1="quoteleft,quotedblleft" 	g2="section" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="seven" 	k="-96" />
<hkern g1="quoteleft,quotedblleft" 	g2="slash" 	k="117" />
<hkern g1="quoteleft,quotedblleft" 	g2="three" 	k="-53" />
<hkern g1="quoteleft,quotedblleft" 	g2="trademark" 	k="-53" />
<hkern g1="quoteleft,quotedblleft" 	g2="two" 	k="-35" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclam" 	k="-2" />
<hkern g1="quoteleft,quotedblleft" 	g2="eight" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="nine" 	k="-76" />
<hkern g1="quoteright,quotedblright" 	g2="ampersand" 	k="35" />
<hkern g1="quoteright,quotedblright" 	g2="backslash" 	k="-117" />
<hkern g1="quoteright,quotedblright" 	g2="exclamdown" 	k="-2" />
<hkern g1="quoteright,quotedblright" 	g2="four" 	k="115" />
<hkern g1="quoteright,quotedblright" 	g2="one" 	k="-100" />
<hkern g1="quoteright,quotedblright" 	g2="paragraph" 	k="-80" />
<hkern g1="quoteright,quotedblright" 	g2="percent" 	k="-82" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="33" />
<hkern g1="quoteright,quotedblright" 	g2="question" 	k="-80" />
<hkern g1="quoteright,quotedblright" 	g2="questiondown" 	k="61" />
<hkern g1="quoteright,quotedblright" 	g2="quoteleft,quotedblleft" 	k="-47" />
<hkern g1="quoteright,quotedblright" 	g2="quoteright,quotedblright" 	k="-47" />
<hkern g1="quoteright,quotedblright" 	g2="section" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="seven" 	k="-59" />
<hkern g1="quoteright,quotedblright" 	g2="slash" 	k="117" />
<hkern g1="quoteright,quotedblright" 	g2="three" 	k="-39" />
<hkern g1="quoteright,quotedblright" 	g2="trademark" 	k="-63" />
<hkern g1="quoteright,quotedblright" 	g2="two" 	k="-41" />
<hkern g1="quoteright,quotedblright" 	g2="exclam" 	k="-2" />
<hkern g1="quoteright,quotedblright" 	g2="yen" 	k="-6" />
<hkern g1="quoteright,quotedblright" 	g2="nine" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="ampersand" 	k="55" />
<hkern g1="quotedbl,quotesingle" 	g2="backslash" 	k="-123" />
<hkern g1="quotedbl,quotesingle" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="quotedbl,quotesingle" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="quotedbl,quotesingle" 	g2="degree" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="five" 	k="-18" />
<hkern g1="quotedbl,quotesingle" 	g2="four" 	k="82" />
<hkern g1="quotedbl,quotesingle" 	g2="one" 	k="-100" />
<hkern g1="quotedbl,quotesingle" 	g2="paragraph" 	k="-111" />
<hkern g1="quotedbl,quotesingle" 	g2="percent" 	k="-59" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="113" />
<hkern g1="quotedbl,quotesingle" 	g2="question" 	k="-76" />
<hkern g1="quotedbl,quotesingle" 	g2="questiondown" 	k="109" />
<hkern g1="quotedbl,quotesingle" 	g2="quotedbl,quotesingle" 	k="-92" />
<hkern g1="quotedbl,quotesingle" 	g2="seven" 	k="-115" />
<hkern g1="quotedbl,quotesingle" 	g2="slash" 	k="123" />
<hkern g1="quotedbl,quotesingle" 	g2="three" 	k="-39" />
<hkern g1="quotedbl,quotesingle" 	g2="trademark" 	k="-80" />
<hkern g1="quotedbl,quotesingle" 	g2="two" 	k="-55" />
<hkern g1="quotedbl,quotesingle" 	g2="underscore" 	k="18" />
<hkern g1="quotedbl,quotesingle" 	g2="zero,six" 	k="-18" />
<hkern g1="quotedbl,quotesingle" 	g2="exclam" 	k="-2" />
<hkern g1="quotedbl,quotesingle" 	g2="yen" 	k="-41" />
<hkern g1="quotedbl,quotesingle" 	g2="currency,Euro" 	k="41" />
<hkern g1="quotedbl,quotesingle" 	g2="eight" 	k="-8" />
<hkern g1="quotedbl,quotesingle" 	g2="nine" 	k="-96" />
<hkern g1="slash" 	g2="ampersand" 	k="80" />
<hkern g1="slash" 	g2="backslash" 	k="-70" />
<hkern g1="slash" 	g2="bracketright,braceright" 	k="-61" />
<hkern g1="slash" 	g2="colon,semicolon" 	k="31" />
<hkern g1="slash" 	g2="degree" 	k="-100" />
<hkern g1="slash" 	g2="exclamdown" 	k="133" />
<hkern g1="slash" 	g2="four" 	k="141" />
<hkern g1="slash" 	g2="guillemotright,guilsinglright" 	k="115" />
<hkern g1="slash" 	g2="one" 	k="-61" />
<hkern g1="slash" 	g2="parenright" 	k="-63" />
<hkern g1="slash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="203" />
<hkern g1="slash" 	g2="questiondown" 	k="154" />
<hkern g1="slash" 	g2="quoteleft,quotedblleft" 	k="-115" />
<hkern g1="slash" 	g2="quoteright,quotedblright" 	k="-117" />
<hkern g1="slash" 	g2="quotedbl,quotesingle" 	k="-123" />
<hkern g1="slash" 	g2="section" 	k="41" />
<hkern g1="slash" 	g2="seven" 	k="-35" />
<hkern g1="slash" 	g2="slash" 	k="129" />
<hkern g1="slash" 	g2="trademark" 	k="-100" />
<hkern g1="slash" 	g2="underscore" 	k="260" />
<hkern g1="slash" 	g2="zero,six" 	k="41" />
<hkern g1="slash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="135" />
<hkern g1="slash" 	g2="yen" 	k="-82" />
<hkern g1="slash" 	g2="sterling" 	k="20" />
<hkern g1="slash" 	g2="eight" 	k="43" />
<hkern g1="underscore" 	g2="backslash" 	k="266" />
<hkern g1="underscore" 	g2="bracketright,braceright" 	k="-47" />
<hkern g1="underscore" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="underscore" 	g2="exclamdown" 	k="-8" />
<hkern g1="underscore" 	g2="five" 	k="-20" />
<hkern g1="underscore" 	g2="four" 	k="59" />
<hkern g1="underscore" 	g2="guillemotright,guilsinglright" 	k="-20" />
<hkern g1="underscore" 	g2="one" 	k="18" />
<hkern g1="underscore" 	g2="paragraph" 	k="176" />
<hkern g1="underscore" 	g2="parenright" 	k="-2" />
<hkern g1="underscore" 	g2="percent" 	k="59" />
<hkern g1="underscore" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="underscore" 	g2="question" 	k="16" />
<hkern g1="underscore" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="underscore" 	g2="section" 	k="-59" />
<hkern g1="underscore" 	g2="slash" 	k="-279" />
<hkern g1="underscore" 	g2="three" 	k="-59" />
<hkern g1="underscore" 	g2="trademark" 	k="102" />
<hkern g1="underscore" 	g2="two" 	k="-76" />
<hkern g1="underscore" 	g2="zero,six" 	k="41" />
<hkern g1="underscore" 	g2="yen" 	k="20" />
<hkern g1="underscore" 	g2="sterling" 	k="-39" />
<hkern g1="underscore" 	g2="currency,Euro" 	k="18" />
<hkern g1="underscore" 	g2="nine" 	k="-20" />
<hkern g1="eight" 	g2="AE" 	k="10" />
<hkern g1="eight" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="8" />
<hkern g1="eight" 	g2="J" 	k="74" />
<hkern g1="eight" 	g2="T" 	k="10" />
<hkern g1="eight" 	g2="V" 	k="33" />
<hkern g1="eight" 	g2="X" 	k="43" />
<hkern g1="eight" 	g2="Z" 	k="8" />
<hkern g1="eight" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-18" />
<hkern g1="eight" 	g2="backslash" 	k="61" />
<hkern g1="eight" 	g2="j" 	k="16" />
<hkern g1="eight" 	g2="percent" 	k="20" />
<hkern g1="eight" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="eight" 	g2="question" 	k="20" />
<hkern g1="eight" 	g2="questiondown" 	k="41" />
<hkern g1="eight" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="eight" 	g2="quotedbl,quotesingle" 	k="-8" />
<hkern g1="eight" 	g2="section" 	k="-20" />
<hkern g1="eight" 	g2="v" 	k="-16" />
<hkern g1="eight" 	g2="x" 	k="4" />
<hkern g1="eight" 	g2="yen" 	k="20" />
<hkern g1="five" 	g2="J" 	k="84" />
<hkern g1="five" 	g2="V" 	k="12" />
<hkern g1="five" 	g2="X" 	k="23" />
<hkern g1="five" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="five" 	g2="backslash" 	k="20" />
<hkern g1="five" 	g2="percent" 	k="20" />
<hkern g1="five" 	g2="v" 	k="12" />
<hkern g1="five" 	g2="x" 	k="12" />
<hkern g1="five" 	g2="degree" 	k="35" />
<hkern g1="five" 	g2="five" 	k="10" />
<hkern g1="five" 	g2="seven" 	k="29" />
<hkern g1="five" 	g2="three" 	k="10" />
<hkern g1="five" 	g2="two" 	k="10" />
<hkern g1="four" 	g2="J" 	k="18" />
<hkern g1="four" 	g2="T" 	k="104" />
<hkern g1="four" 	g2="V" 	k="104" />
<hkern g1="four" 	g2="X" 	k="12" />
<hkern g1="four" 	g2="Z" 	k="18" />
<hkern g1="four" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="four" 	g2="backslash" 	k="102" />
<hkern g1="four" 	g2="percent" 	k="61" />
<hkern g1="four" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="four" 	g2="question" 	k="104" />
<hkern g1="four" 	g2="questiondown" 	k="-20" />
<hkern g1="four" 	g2="quoteleft,quotedblleft" 	k="43" />
<hkern g1="four" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="four" 	g2="section" 	k="-35" />
<hkern g1="four" 	g2="v" 	k="27" />
<hkern g1="four" 	g2="x" 	k="23" />
<hkern g1="four" 	g2="yen" 	k="20" />
<hkern g1="four" 	g2="degree" 	k="61" />
<hkern g1="four" 	g2="five" 	k="8" />
<hkern g1="four" 	g2="seven" 	k="106" />
<hkern g1="four" 	g2="three" 	k="35" />
<hkern g1="four" 	g2="two" 	k="8" />
<hkern g1="four" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="four" 	g2="dollar,S" 	k="18" />
<hkern g1="four" 	g2="ae" 	k="-59" />
<hkern g1="four" 	g2="slash" 	k="-59" />
<hkern g1="four" 	g2="underscore" 	k="-41" />
<hkern g1="four" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="-41" />
<hkern g1="four" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="four" 	g2="ampersand" 	k="-41" />
<hkern g1="four" 	g2="colon,semicolon" 	k="-12" />
<hkern g1="four" 	g2="currency,Euro" 	k="-20" />
<hkern g1="four" 	g2="eight" 	k="-20" />
<hkern g1="four" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="four" 	g2="sterling" 	k="-20" />
<hkern g1="four" 	g2="nine" 	k="18" />
<hkern g1="four" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-23" />
<hkern g1="four" 	g2="one" 	k="51" />
<hkern g1="four" 	g2="paragraph" 	k="27" />
<hkern g1="four" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="four" 	g2="uniFB02" 	k="-2" />
<hkern g1="four" 	g2="trademark" 	k="41" />
<hkern g1="seven" 	g2="AE" 	k="139" />
<hkern g1="seven" 	g2="J" 	k="39" />
<hkern g1="seven" 	g2="T" 	k="-20" />
<hkern g1="seven" 	g2="V" 	k="-10" />
<hkern g1="seven" 	g2="X" 	k="8" />
<hkern g1="seven" 	g2="Z" 	k="18" />
<hkern g1="seven" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-59" />
<hkern g1="seven" 	g2="backslash" 	k="-20" />
<hkern g1="seven" 	g2="j" 	k="41" />
<hkern g1="seven" 	g2="percent" 	k="-2" />
<hkern g1="seven" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="111" />
<hkern g1="seven" 	g2="question" 	k="-31" />
<hkern g1="seven" 	g2="questiondown" 	k="137" />
<hkern g1="seven" 	g2="quoteleft,quotedblleft" 	k="-20" />
<hkern g1="seven" 	g2="quotedbl,quotesingle" 	k="-61" />
<hkern g1="seven" 	g2="section" 	k="20" />
<hkern g1="seven" 	g2="v" 	k="14" />
<hkern g1="seven" 	g2="x" 	k="53" />
<hkern g1="seven" 	g2="yen" 	k="-23" />
<hkern g1="seven" 	g2="degree" 	k="-4" />
<hkern g1="seven" 	g2="five" 	k="39" />
<hkern g1="seven" 	g2="three" 	k="18" />
<hkern g1="seven" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="39" />
<hkern g1="seven" 	g2="dollar,S" 	k="18" />
<hkern g1="seven" 	g2="ae" 	k="80" />
<hkern g1="seven" 	g2="slash" 	k="90" />
<hkern g1="seven" 	g2="underscore" 	k="61" />
<hkern g1="seven" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="8" />
<hkern g1="seven" 	g2="ampersand" 	k="76" />
<hkern g1="seven" 	g2="colon,semicolon" 	k="41" />
<hkern g1="seven" 	g2="currency,Euro" 	k="41" />
<hkern g1="seven" 	g2="eight" 	k="39" />
<hkern g1="seven" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="102" />
<hkern g1="seven" 	g2="sterling" 	k="29" />
<hkern g1="seven" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="seven" 	g2="one" 	k="-20" />
<hkern g1="seven" 	g2="paragraph" 	k="-59" />
<hkern g1="seven" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="seven" 	g2="uniFB02" 	k="35" />
<hkern g1="seven" 	g2="trademark" 	k="-82" />
<hkern g1="seven" 	g2="exclamdown" 	k="59" />
<hkern g1="seven" 	g2="guillemotright,guilsinglright" 	k="39" />
<hkern g1="seven" 	g2="s" 	k="61" />
<hkern g1="seven" 	g2="z" 	k="66" />
<hkern g1="seven" 	g2="zero,six" 	k="41" />
<hkern g1="seven" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="39" />
<hkern g1="seven" 	g2="bracketright,braceright" 	k="12" />
<hkern g1="seven" 	g2="four" 	k="133" />
<hkern g1="seven" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="57" />
<hkern g1="seven" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="59" />
<hkern g1="six" 	g2="AE" 	k="-18" />
<hkern g1="six" 	g2="J" 	k="51" />
<hkern g1="six" 	g2="T" 	k="35" />
<hkern g1="six" 	g2="V" 	k="20" />
<hkern g1="six" 	g2="X" 	k="18" />
<hkern g1="six" 	g2="asterisk,ordfeminine,ordmasculine" 	k="35" />
<hkern g1="six" 	g2="j" 	k="16" />
<hkern g1="six" 	g2="percent" 	k="41" />
<hkern g1="six" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="six" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="six" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="six" 	g2="section" 	k="-41" />
<hkern g1="six" 	g2="v" 	k="14" />
<hkern g1="six" 	g2="x" 	k="31" />
<hkern g1="six" 	g2="degree" 	k="47" />
<hkern g1="six" 	g2="seven" 	k="18" />
<hkern g1="six" 	g2="slash" 	k="-20" />
<hkern g1="six" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="six" 	g2="ampersand" 	k="-20" />
<hkern g1="six" 	g2="currency,Euro" 	k="-20" />
<hkern g1="six" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-10" />
<hkern g1="six" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-18" />
<hkern g1="three" 	g2="AE" 	k="-8" />
<hkern g1="three" 	g2="J" 	k="53" />
<hkern g1="three" 	g2="V" 	k="20" />
<hkern g1="three" 	g2="X" 	k="20" />
<hkern g1="three" 	g2="j" 	k="10" />
<hkern g1="three" 	g2="percent" 	k="20" />
<hkern g1="three" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="three" 	g2="v" 	k="12" />
<hkern g1="three" 	g2="x" 	k="23" />
<hkern g1="three" 	g2="degree" 	k="31" />
<hkern g1="three" 	g2="five" 	k="20" />
<hkern g1="three" 	g2="seven" 	k="29" />
<hkern g1="three" 	g2="three" 	k="10" />
<hkern g1="three" 	g2="two" 	k="10" />
<hkern g1="two" 	g2="AE" 	k="-2" />
<hkern g1="two" 	g2="J" 	k="-2" />
<hkern g1="two" 	g2="V" 	k="23" />
<hkern g1="two" 	g2="backslash" 	k="18" />
<hkern g1="two" 	g2="percent" 	k="-20" />
<hkern g1="two" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="two" 	g2="v" 	k="-8" />
<hkern g1="two" 	g2="x" 	k="18" />
<hkern g1="two" 	g2="seven" 	k="8" />
<hkern g1="two" 	g2="slash" 	k="-20" />
<hkern g1="two" 	g2="underscore" 	k="-61" />
<hkern g1="two" 	g2="ampersand" 	k="18" />
<hkern g1="two" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="18" />
<hkern g1="two" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="two" 	g2="z" 	k="18" />
<hkern g1="two" 	g2="zero,six" 	k="8" />
<hkern g1="two" 	g2="four" 	k="53" />
<hkern g1="two" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="8" />
<hkern g1="zero,nine" 	g2="AE" 	k="12" />
<hkern g1="zero,nine" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="zero,nine" 	g2="J" 	k="102" />
<hkern g1="zero,nine" 	g2="T" 	k="31" />
<hkern g1="zero,nine" 	g2="V" 	k="51" />
<hkern g1="zero,nine" 	g2="X" 	k="63" />
<hkern g1="zero,nine" 	g2="Z" 	k="47" />
<hkern g1="zero,nine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-18" />
<hkern g1="zero,nine" 	g2="backslash" 	k="41" />
<hkern g1="zero,nine" 	g2="j" 	k="16" />
<hkern g1="zero,nine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="23" />
<hkern g1="zero,nine" 	g2="question" 	k="41" />
<hkern g1="zero,nine" 	g2="questiondown" 	k="61" />
<hkern g1="zero,nine" 	g2="quotedbl,quotesingle" 	k="-18" />
<hkern g1="zero,nine" 	g2="v" 	k="-18" />
<hkern g1="zero,nine" 	g2="x" 	k="20" />
<hkern g1="zero,nine" 	g2="yen" 	k="23" />
<hkern g1="zero,nine" 	g2="degree" 	k="-8" />
<hkern g1="zero,nine" 	g2="seven" 	k="18" />
<hkern g1="zero,nine" 	g2="three" 	k="20" />
<hkern g1="zero,nine" 	g2="two" 	k="2" />
<hkern g1="zero,nine" 	g2="slash" 	k="41" />
<hkern g1="zero,nine" 	g2="underscore" 	k="41" />
<hkern g1="zero,nine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-18" />
<hkern g1="zero,nine" 	g2="one" 	k="2" />
<hkern g1="zero,nine" 	g2="uniFB02" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="2" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="49" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk,ordfeminine,ordmasculine" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="one" 	k="18" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="35" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="4" />
<hkern g1="B" 	g2="J" 	k="61" />
<hkern g1="B" 	g2="T" 	k="10" />
<hkern g1="B" 	g2="V" 	k="10" />
<hkern g1="B" 	g2="X" 	k="20" />
<hkern g1="B" 	g2="backslash" 	k="41" />
<hkern g1="B" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-2" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="2" />
<hkern g1="B" 	g2="trademark" 	k="2" />
<hkern g1="B" 	g2="underscore" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="J" 	k="59" />
<hkern g1="C,Ccedilla,Euro" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="C,Ccedilla,Euro" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="2" />
<hkern g1="C,Ccedilla,Euro" 	g2="V" 	k="18" />
<hkern g1="C,Ccedilla,Euro" 	g2="X" 	k="39" />
<hkern g1="C,Ccedilla,Euro" 	g2="Y,Yacute,Ydieresis" 	k="35" />
<hkern g1="C,Ccedilla,Euro" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="C,Ccedilla,Euro" 	g2="backslash" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="colon,semicolon" 	k="10" />
<hkern g1="C,Ccedilla,Euro" 	g2="degree" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="eight" 	k="29" />
<hkern g1="C,Ccedilla,Euro" 	g2="exclamdown" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="four" 	k="70" />
<hkern g1="C,Ccedilla,Euro" 	g2="guillemotright,guilsinglright" 	k="2" />
<hkern g1="C,Ccedilla,Euro" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="2" />
<hkern g1="C,Ccedilla,Euro" 	g2="nine" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="C,Ccedilla,Euro" 	g2="one" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="paragraph" 	k="-18" />
<hkern g1="C,Ccedilla,Euro" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-29" />
<hkern g1="C,Ccedilla,Euro" 	g2="questiondown" 	k="35" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteleft,quotedblleft" 	k="-8" />
<hkern g1="C,Ccedilla,Euro" 	g2="quoteright,quotedblright" 	k="-37" />
<hkern g1="C,Ccedilla,Euro" 	g2="s" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="three" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="39" />
<hkern g1="C,Ccedilla,Euro" 	g2="underscore" 	k="-20" />
<hkern g1="C,Ccedilla,Euro" 	g2="x" 	k="39" />
<hkern g1="C,Ccedilla,Euro" 	g2="z" 	k="39" />
<hkern g1="C,Ccedilla,Euro" 	g2="zero,six" 	k="49" />
<hkern g1="C,Ccedilla,Euro" 	g2="AE" 	k="39" />
<hkern g1="C,Ccedilla,Euro" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="29" />
<hkern g1="C,Ccedilla,Euro" 	g2="dollar,S" 	k="8" />
<hkern g1="C,Ccedilla,Euro" 	g2="ae" 	k="18" />
<hkern g1="C,Ccedilla,Euro" 	g2="ampersand" 	k="41" />
<hkern g1="C,Ccedilla,Euro" 	g2="five" 	k="20" />
<hkern g1="C,Ccedilla,Euro" 	g2="section" 	k="18" />
<hkern g1="C,Ccedilla,Euro" 	g2="two" 	k="18" />
<hkern g1="AE,OE" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="AE,OE" 	g2="V" 	k="20" />
<hkern g1="AE,OE" 	g2="X" 	k="18" />
<hkern g1="AE,OE" 	g2="colon,semicolon" 	k="18" />
<hkern g1="AE,OE" 	g2="eight" 	k="18" />
<hkern g1="AE,OE" 	g2="exclamdown" 	k="18" />
<hkern g1="AE,OE" 	g2="four" 	k="88" />
<hkern g1="AE,OE" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="41" />
<hkern g1="AE,OE" 	g2="nine" 	k="8" />
<hkern g1="AE,OE" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="AE,OE" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="AE,OE" 	g2="question" 	k="35" />
<hkern g1="AE,OE" 	g2="s" 	k="18" />
<hkern g1="AE,OE" 	g2="seven" 	k="18" />
<hkern g1="AE,OE" 	g2="uniFB02" 	k="35" />
<hkern g1="AE,OE" 	g2="three" 	k="35" />
<hkern g1="AE,OE" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="18" />
<hkern g1="AE,OE" 	g2="underscore" 	k="-35" />
<hkern g1="AE,OE" 	g2="v" 	k="27" />
<hkern g1="AE,OE" 	g2="x" 	k="35" />
<hkern g1="AE,OE" 	g2="z" 	k="27" />
<hkern g1="AE,OE" 	g2="zero,six" 	k="37" />
<hkern g1="AE,OE" 	g2="ae" 	k="18" />
<hkern g1="AE,OE" 	g2="five" 	k="18" />
<hkern g1="AE,OE" 	g2="section" 	k="18" />
<hkern g1="AE,OE" 	g2="two" 	k="18" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="J" 	k="39" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="T" 	k="2" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="X" 	k="18" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="eight" 	k="29" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="four" 	k="90" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="nine" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="question" 	k="35" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="questiondown" 	k="-2" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="s" 	k="20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="slash" 	k="-20" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="uniFB02" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="three" 	k="-2" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="18" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="underscore" 	k="-82" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="v" 	k="29" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="x" 	k="29" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="zero,six" 	k="39" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="ae" 	k="8" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="parenright" 	k="27" />
<hkern g1="F,sterling" 	g2="J" 	k="72" />
<hkern g1="F,sterling" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="F,sterling" 	g2="X" 	k="20" />
<hkern g1="F,sterling" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-70" />
<hkern g1="F,sterling" 	g2="backslash" 	k="-39" />
<hkern g1="F,sterling" 	g2="colon,semicolon" 	k="61" />
<hkern g1="F,sterling" 	g2="eight" 	k="70" />
<hkern g1="F,sterling" 	g2="exclamdown" 	k="51" />
<hkern g1="F,sterling" 	g2="four" 	k="84" />
<hkern g1="F,sterling" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="F,sterling" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="61" />
<hkern g1="F,sterling" 	g2="nine" 	k="41" />
<hkern g1="F,sterling" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="F,sterling" 	g2="one" 	k="-39" />
<hkern g1="F,sterling" 	g2="paragraph" 	k="-35" />
<hkern g1="F,sterling" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="2" />
<hkern g1="F,sterling" 	g2="questiondown" 	k="49" />
<hkern g1="F,sterling" 	g2="quoteleft,quotedblleft" 	k="2" />
<hkern g1="F,sterling" 	g2="quoteright,quotedblright" 	k="-2" />
<hkern g1="F,sterling" 	g2="s" 	k="82" />
<hkern g1="F,sterling" 	g2="seven" 	k="-18" />
<hkern g1="F,sterling" 	g2="slash" 	k="-23" />
<hkern g1="F,sterling" 	g2="uniFB02" 	k="41" />
<hkern g1="F,sterling" 	g2="three" 	k="20" />
<hkern g1="F,sterling" 	g2="trademark" 	k="-76" />
<hkern g1="F,sterling" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="59" />
<hkern g1="F,sterling" 	g2="underscore" 	k="-39" />
<hkern g1="F,sterling" 	g2="v" 	k="41" />
<hkern g1="F,sterling" 	g2="x" 	k="51" />
<hkern g1="F,sterling" 	g2="z" 	k="82" />
<hkern g1="F,sterling" 	g2="zero,six" 	k="80" />
<hkern g1="F,sterling" 	g2="AE" 	k="92" />
<hkern g1="F,sterling" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="F,sterling" 	g2="dollar,S" 	k="20" />
<hkern g1="F,sterling" 	g2="ae" 	k="82" />
<hkern g1="F,sterling" 	g2="ampersand" 	k="86" />
<hkern g1="F,sterling" 	g2="five" 	k="23" />
<hkern g1="F,sterling" 	g2="section" 	k="20" />
<hkern g1="F,sterling" 	g2="two" 	k="20" />
<hkern g1="F,sterling" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="51" />
<hkern g1="F,sterling" 	g2="bracketright,braceright" 	k="-2" />
<hkern g1="F,sterling" 	g2="j" 	k="51" />
<hkern g1="F,sterling" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="41" />
<hkern g1="G" 	g2="J" 	k="20" />
<hkern g1="G" 	g2="backslash" 	k="39" />
<hkern g1="G" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="G" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="H,U,paragraph,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="18" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="eight" 	k="8" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="four" 	k="20" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="nine" 	k="8" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="trademark" 	k="-41" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="zero,six" 	k="20" />
<hkern g1="J" 	g2="J" 	k="41" />
<hkern g1="J" 	g2="underscore" 	k="20" />
<hkern g1="K" 	g2="J" 	k="-8" />
<hkern g1="K" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="K" 	g2="T" 	k="41" />
<hkern g1="K" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="K" 	g2="V" 	k="82" />
<hkern g1="K" 	g2="X" 	k="74" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="59" />
<hkern g1="K" 	g2="degree" 	k="20" />
<hkern g1="K" 	g2="eight" 	k="43" />
<hkern g1="K" 	g2="four" 	k="100" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="2" />
<hkern g1="K" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="63" />
<hkern g1="K" 	g2="nine" 	k="20" />
<hkern g1="K" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="K" 	g2="one" 	k="-41" />
<hkern g1="K" 	g2="paragraph" 	k="6" />
<hkern g1="K" 	g2="question" 	k="82" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="K" 	g2="s" 	k="39" />
<hkern g1="K" 	g2="slash" 	k="-18" />
<hkern g1="K" 	g2="uniFB02" 	k="59" />
<hkern g1="K" 	g2="three" 	k="18" />
<hkern g1="K" 	g2="trademark" 	k="-41" />
<hkern g1="K" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="39" />
<hkern g1="K" 	g2="underscore" 	k="-141" />
<hkern g1="K" 	g2="v" 	k="74" />
<hkern g1="K" 	g2="x" 	k="76" />
<hkern g1="K" 	g2="z" 	k="43" />
<hkern g1="K" 	g2="zero,six" 	k="43" />
<hkern g1="K" 	g2="AE" 	k="53" />
<hkern g1="K" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="K" 	g2="dollar,S" 	k="31" />
<hkern g1="K" 	g2="ae" 	k="29" />
<hkern g1="K" 	g2="ampersand" 	k="41" />
<hkern g1="K" 	g2="two" 	k="-2" />
<hkern g1="K" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="K" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="K" 	g2="j" 	k="8" />
<hkern g1="K" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="K" 	g2="bracketleft" 	k="70" />
<hkern g1="L" 	g2="J" 	k="-61" />
<hkern g1="L" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="L" 	g2="T" 	k="133" />
<hkern g1="L" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="29" />
<hkern g1="L" 	g2="V" 	k="111" />
<hkern g1="L" 	g2="X" 	k="20" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="59" />
<hkern g1="L" 	g2="asterisk,ordfeminine,ordmasculine" 	k="143" />
<hkern g1="L" 	g2="backslash" 	k="143" />
<hkern g1="L" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="L" 	g2="degree" 	k="133" />
<hkern g1="L" 	g2="eight" 	k="18" />
<hkern g1="L" 	g2="four" 	k="88" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="-18" />
<hkern g1="L" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="33" />
<hkern g1="L" 	g2="nine" 	k="20" />
<hkern g1="L" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="L" 	g2="one" 	k="80" />
<hkern g1="L" 	g2="paragraph" 	k="150" />
<hkern g1="L" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-61" />
<hkern g1="L" 	g2="question" 	k="199" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="84" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="94" />
<hkern g1="L" 	g2="seven" 	k="102" />
<hkern g1="L" 	g2="slash" 	k="-55" />
<hkern g1="L" 	g2="uniFB02" 	k="10" />
<hkern g1="L" 	g2="three" 	k="-2" />
<hkern g1="L" 	g2="trademark" 	k="129" />
<hkern g1="L" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="18" />
<hkern g1="L" 	g2="underscore" 	k="-135" />
<hkern g1="L" 	g2="v" 	k="80" />
<hkern g1="L" 	g2="x" 	k="18" />
<hkern g1="L" 	g2="zero,six" 	k="55" />
<hkern g1="L" 	g2="AE" 	k="-2" />
<hkern g1="L" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="18" />
<hkern g1="L" 	g2="dollar,S" 	k="18" />
<hkern g1="L" 	g2="ae" 	k="-2" />
<hkern g1="L" 	g2="ampersand" 	k="-2" />
<hkern g1="M,N,Ntilde" 	g2="backslash" 	k="20" />
<hkern g1="M,N,Ntilde" 	g2="one" 	k="8" />
<hkern g1="M,N,Ntilde" 	g2="question" 	k="10" />
<hkern g1="M,N,Ntilde" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="M,N,Ntilde" 	g2="trademark" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="43" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="51" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="backslash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="degree" 	k="-20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-18" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="one" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="question" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="questiondown" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="quotedbl,quotesingle" 	k="39" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="41" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="uniFB02" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="three" 	k="23" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="-10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="underscore" 	k="61" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="23" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="two" 	k="20" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="parenright" 	k="-35" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="j" 	k="-164" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="10" />
<hkern g1="at,D,O,Q,copyright,registered,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="18" />
<hkern g1="P" 	g2="J" 	k="100" />
<hkern g1="P" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="6" />
<hkern g1="P" 	g2="V" 	k="33" />
<hkern g1="P" 	g2="X" 	k="51" />
<hkern g1="P" 	g2="backslash" 	k="41" />
<hkern g1="P" 	g2="degree" 	k="-41" />
<hkern g1="P" 	g2="eight" 	k="12" />
<hkern g1="P" 	g2="four" 	k="61" />
<hkern g1="P" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="2" />
<hkern g1="P" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="59" />
<hkern g1="P" 	g2="questiondown" 	k="111" />
<hkern g1="P" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="P" 	g2="slash" 	k="100" />
<hkern g1="P" 	g2="uniFB02" 	k="-10" />
<hkern g1="P" 	g2="three" 	k="31" />
<hkern g1="P" 	g2="underscore" 	k="63" />
<hkern g1="P" 	g2="v" 	k="18" />
<hkern g1="P" 	g2="x" 	k="70" />
<hkern g1="P" 	g2="AE" 	k="92" />
<hkern g1="P" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="10" />
<hkern g1="P" 	g2="ae" 	k="31" />
<hkern g1="P" 	g2="ampersand" 	k="70" />
<hkern g1="P" 	g2="five" 	k="20" />
<hkern g1="P" 	g2="two" 	k="10" />
<hkern g1="P" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="57" />
<hkern g1="P" 	g2="parenright" 	k="27" />
<hkern g1="P" 	g2="bracketright,braceright" 	k="27" />
<hkern g1="P" 	g2="j" 	k="20" />
<hkern g1="P" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="53" />
<hkern g1="P" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="20" />
<hkern g1="P" 	g2="Z" 	k="39" />
<hkern g1="P" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="20" />
<hkern g1="R" 	g2="J" 	k="61" />
<hkern g1="R" 	g2="X" 	k="41" />
<hkern g1="R" 	g2="backslash" 	k="41" />
<hkern g1="R" 	g2="degree" 	k="-31" />
<hkern g1="R" 	g2="four" 	k="41" />
<hkern g1="R" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="2" />
<hkern g1="R" 	g2="question" 	k="10" />
<hkern g1="R" 	g2="questiondown" 	k="20" />
<hkern g1="R" 	g2="quoteleft,quotedblleft" 	k="-10" />
<hkern g1="R" 	g2="uniFB02" 	k="-10" />
<hkern g1="R" 	g2="three" 	k="18" />
<hkern g1="R" 	g2="underscore" 	k="-39" />
<hkern g1="R" 	g2="ae" 	k="10" />
<hkern g1="dollar,S" 	g2="J" 	k="61" />
<hkern g1="dollar,S" 	g2="T" 	k="18" />
<hkern g1="dollar,S" 	g2="X" 	k="29" />
<hkern g1="dollar,S" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="dollar,S" 	g2="backslash" 	k="20" />
<hkern g1="dollar,S" 	g2="colon,semicolon" 	k="10" />
<hkern g1="dollar,S" 	g2="degree" 	k="31" />
<hkern g1="dollar,S" 	g2="exclamdown" 	k="2" />
<hkern g1="dollar,S" 	g2="four" 	k="-20" />
<hkern g1="dollar,S" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-39" />
<hkern g1="dollar,S" 	g2="nine" 	k="20" />
<hkern g1="dollar,S" 	g2="one" 	k="20" />
<hkern g1="dollar,S" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="dollar,S" 	g2="question" 	k="41" />
<hkern g1="dollar,S" 	g2="questiondown" 	k="20" />
<hkern g1="dollar,S" 	g2="quoteleft,quotedblleft" 	k="51" />
<hkern g1="dollar,S" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="dollar,S" 	g2="quotedbl,quotesingle" 	k="61" />
<hkern g1="dollar,S" 	g2="seven" 	k="20" />
<hkern g1="dollar,S" 	g2="three" 	k="18" />
<hkern g1="dollar,S" 	g2="underscore" 	k="41" />
<hkern g1="dollar,S" 	g2="v" 	k="20" />
<hkern g1="dollar,S" 	g2="x" 	k="20" />
<hkern g1="T" 	g2="J" 	k="18" />
<hkern g1="T" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="V" 	k="-39" />
<hkern g1="T" 	g2="X" 	k="23" />
<hkern g1="T" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-82" />
<hkern g1="T" 	g2="backslash" 	k="-41" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="degree" 	k="-61" />
<hkern g1="T" 	g2="eight" 	k="10" />
<hkern g1="T" 	g2="exclamdown" 	k="41" />
<hkern g1="T" 	g2="four" 	k="164" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="T" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="63" />
<hkern g1="T" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="T" 	g2="one" 	k="-82" />
<hkern g1="T" 	g2="paragraph" 	k="-96" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="63" />
<hkern g1="T" 	g2="question" 	k="-61" />
<hkern g1="T" 	g2="questiondown" 	k="111" />
<hkern g1="T" 	g2="quoteleft,quotedblleft" 	k="-76" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-80" />
<hkern g1="T" 	g2="quotedbl,quotesingle" 	k="-35" />
<hkern g1="T" 	g2="s" 	k="63" />
<hkern g1="T" 	g2="seven" 	k="-61" />
<hkern g1="T" 	g2="slash" 	k="41" />
<hkern g1="T" 	g2="three" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-102" />
<hkern g1="T" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="39" />
<hkern g1="T" 	g2="underscore" 	k="18" />
<hkern g1="T" 	g2="x" 	k="8" />
<hkern g1="T" 	g2="z" 	k="41" />
<hkern g1="T" 	g2="zero,six" 	k="31" />
<hkern g1="T" 	g2="AE" 	k="82" />
<hkern g1="T" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="20" />
<hkern g1="T" 	g2="ae" 	k="61" />
<hkern g1="T" 	g2="ampersand" 	k="61" />
<hkern g1="T" 	g2="five" 	k="-18" />
<hkern g1="T" 	g2="two" 	k="-41" />
<hkern g1="T" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="T" 	g2="bracketright,braceright" 	k="-8" />
<hkern g1="T" 	g2="j" 	k="20" />
<hkern g1="T" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="T" 	g2="exclam" 	k="-41" />
<hkern g1="T" 	g2="parenleft" 	k="35" />
<hkern g1="Thorn" 	g2="J" 	k="141" />
<hkern g1="Thorn" 	g2="T" 	k="39" />
<hkern g1="Thorn" 	g2="V" 	k="20" />
<hkern g1="Thorn" 	g2="X" 	k="61" />
<hkern g1="Thorn" 	g2="backslash" 	k="82" />
<hkern g1="Thorn" 	g2="one" 	k="41" />
<hkern g1="Thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="49" />
<hkern g1="Thorn" 	g2="question" 	k="61" />
<hkern g1="Thorn" 	g2="questiondown" 	k="51" />
<hkern g1="Thorn" 	g2="slash" 	k="61" />
<hkern g1="Thorn" 	g2="uniFB02" 	k="-10" />
<hkern g1="Thorn" 	g2="three" 	k="41" />
<hkern g1="Thorn" 	g2="trademark" 	k="2" />
<hkern g1="Thorn" 	g2="underscore" 	k="61" />
<hkern g1="Thorn" 	g2="x" 	k="20" />
<hkern g1="Thorn" 	g2="AE" 	k="61" />
<hkern g1="Thorn" 	g2="ae" 	k="20" />
<hkern g1="Thorn" 	g2="five" 	k="20" />
<hkern g1="Thorn" 	g2="two" 	k="51" />
<hkern g1="Thorn" 	g2="j" 	k="20" />
<hkern g1="Thorn" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="31" />
<hkern g1="Thorn" 	g2="Z" 	k="39" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="J" 	k="51" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="X" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="18" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="questiondown" 	k="18" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="slash" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="underscore" 	k="20" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="x" 	k="18" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="AE" 	k="31" />
<hkern g1="W,Y,Yacute,Ydieresis" 	g2="j" 	k="10" />
<hkern g1="V" 	g2="J" 	k="51" />
<hkern g1="V" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="V" 	g2="T" 	k="-39" />
<hkern g1="V" 	g2="X" 	k="43" />
<hkern g1="V" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-70" />
<hkern g1="V" 	g2="backslash" 	k="-61" />
<hkern g1="V" 	g2="colon,semicolon" 	k="43" />
<hkern g1="V" 	g2="degree" 	k="-59" />
<hkern g1="V" 	g2="eight" 	k="33" />
<hkern g1="V" 	g2="exclamdown" 	k="61" />
<hkern g1="V" 	g2="four" 	k="145" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="23" />
<hkern g1="V" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="43" />
<hkern g1="V" 	g2="nine" 	k="12" />
<hkern g1="V" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="113" />
<hkern g1="V" 	g2="one" 	k="-61" />
<hkern g1="V" 	g2="paragraph" 	k="-55" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="V" 	g2="questiondown" 	k="152" />
<hkern g1="V" 	g2="quoteleft,quotedblleft" 	k="-53" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-76" />
<hkern g1="V" 	g2="s" 	k="115" />
<hkern g1="V" 	g2="seven" 	k="-20" />
<hkern g1="V" 	g2="slash" 	k="82" />
<hkern g1="V" 	g2="uniFB02" 	k="2" />
<hkern g1="V" 	g2="three" 	k="-2" />
<hkern g1="V" 	g2="trademark" 	k="-82" />
<hkern g1="V" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="63" />
<hkern g1="V" 	g2="underscore" 	k="20" />
<hkern g1="V" 	g2="v" 	k="33" />
<hkern g1="V" 	g2="x" 	k="70" />
<hkern g1="V" 	g2="z" 	k="53" />
<hkern g1="V" 	g2="zero,six" 	k="51" />
<hkern g1="V" 	g2="AE" 	k="74" />
<hkern g1="V" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="43" />
<hkern g1="V" 	g2="dollar,S" 	k="31" />
<hkern g1="V" 	g2="ae" 	k="113" />
<hkern g1="V" 	g2="ampersand" 	k="59" />
<hkern g1="V" 	g2="five" 	k="12" />
<hkern g1="V" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="41" />
<hkern g1="V" 	g2="parenright" 	k="-6" />
<hkern g1="V" 	g2="bracketright,braceright" 	k="-43" />
<hkern g1="V" 	g2="j" 	k="41" />
<hkern g1="V" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="72" />
<hkern g1="V" 	g2="Z" 	k="20" />
<hkern g1="V" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="31" />
<hkern g1="V" 	g2="exclam" 	k="-20" />
<hkern g1="X" 	g2="J" 	k="-8" />
<hkern g1="X" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="X" 	g2="T" 	k="23" />
<hkern g1="X" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="X" 	g2="V" 	k="43" />
<hkern g1="X" 	g2="X" 	k="53" />
<hkern g1="X" 	g2="Y,Yacute,Ydieresis" 	k="39" />
<hkern g1="X" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="X" 	g2="colon,semicolon" 	k="12" />
<hkern g1="X" 	g2="degree" 	k="41" />
<hkern g1="X" 	g2="eight" 	k="43" />
<hkern g1="X" 	g2="exclamdown" 	k="20" />
<hkern g1="X" 	g2="four" 	k="104" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="X" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="33" />
<hkern g1="X" 	g2="nine" 	k="33" />
<hkern g1="X" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="X" 	g2="one" 	k="-20" />
<hkern g1="X" 	g2="question" 	k="61" />
<hkern g1="X" 	g2="questiondown" 	k="-2" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="-12" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="-18" />
<hkern g1="X" 	g2="s" 	k="41" />
<hkern g1="X" 	g2="seven" 	k="20" />
<hkern g1="X" 	g2="slash" 	k="-39" />
<hkern g1="X" 	g2="uniFB02" 	k="43" />
<hkern g1="X" 	g2="trademark" 	k="-61" />
<hkern g1="X" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="53" />
<hkern g1="X" 	g2="underscore" 	k="-100" />
<hkern g1="X" 	g2="v" 	k="47" />
<hkern g1="X" 	g2="x" 	k="68" />
<hkern g1="X" 	g2="z" 	k="23" />
<hkern g1="X" 	g2="zero,six" 	k="63" />
<hkern g1="X" 	g2="AE" 	k="63" />
<hkern g1="X" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="31" />
<hkern g1="X" 	g2="dollar,S" 	k="29" />
<hkern g1="X" 	g2="ae" 	k="33" />
<hkern g1="X" 	g2="ampersand" 	k="41" />
<hkern g1="X" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="X" 	g2="bracketright,braceright" 	k="-2" />
<hkern g1="X" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="20" />
<hkern g1="X" 	g2="Z" 	k="20" />
<hkern g1="X" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="49" />
<hkern g1="X" 	g2="parenleft" 	k="27" />
<hkern g1="Z" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="Z" 	g2="T" 	k="-20" />
<hkern g1="Z" 	g2="X" 	k="20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="Z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="Z" 	g2="colon,semicolon" 	k="-2" />
<hkern g1="Z" 	g2="eight" 	k="8" />
<hkern g1="Z" 	g2="exclamdown" 	k="-2" />
<hkern g1="Z" 	g2="four" 	k="88" />
<hkern g1="Z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="35" />
<hkern g1="Z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="Z" 	g2="one" 	k="-41" />
<hkern g1="Z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="Z" 	g2="question" 	k="-2" />
<hkern g1="Z" 	g2="slash" 	k="-18" />
<hkern g1="Z" 	g2="uniFB02" 	k="18" />
<hkern g1="Z" 	g2="trademark" 	k="-61" />
<hkern g1="Z" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="18" />
<hkern g1="Z" 	g2="underscore" 	k="-61" />
<hkern g1="Z" 	g2="v" 	k="35" />
<hkern g1="Z" 	g2="zero,six" 	k="47" />
<hkern g1="Z" 	g2="ae" 	k="-2" />
<hkern g1="Z" 	g2="ampersand" 	k="35" />
<hkern g1="Z" 	g2="five" 	k="8" />
<hkern g1="Z" 	g2="bracketright,braceright" 	k="-6" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="35" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="6" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="X" 	k="94" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="backslash" 	k="141" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="one" 	k="18" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="question" 	k="-12" />
<hkern g1="a,g,i,j,q,u,y,mu,agrave,aacute,acircumflex,atilde,adieresis,aring,igrave,iacute,icircumflex,idieresis,ugrave,uacute,ucircumflex,udieresis" 	g2="dollar,S" 	k="43" />
<hkern g1="z" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="z" 	g2="backslash" 	k="137" />
<hkern g1="z" 	g2="four" 	k="35" />
<hkern g1="z" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="29" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="z" 	g2="one" 	k="35" />
<hkern g1="z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-2" />
<hkern g1="z" 	g2="uniFB02" 	k="-2" />
<hkern g1="z" 	g2="underscore" 	k="-55" />
<hkern g1="z" 	g2="v" 	k="20" />
<hkern g1="z" 	g2="x" 	k="20" />
<hkern g1="z" 	g2="ampersand" 	k="18" />
<hkern g1="z" 	g2="two" 	k="18" />
<hkern g1="ampersand" 	g2="AE" 	k="-35" />
<hkern g1="ampersand" 	g2="J" 	k="-29" />
<hkern g1="ampersand" 	g2="T" 	k="61" />
<hkern g1="ampersand" 	g2="V" 	k="41" />
<hkern g1="ampersand" 	g2="Y,Yacute,Ydieresis" 	k="35" />
<hkern g1="ampersand" 	g2="Z" 	k="-35" />
<hkern g1="ampersand" 	g2="ae" 	k="-20" />
<hkern g1="ampersand" 	g2="asterisk,ordfeminine,ordmasculine" 	k="61" />
<hkern g1="ampersand" 	g2="uniFB02" 	k="-20" />
<hkern g1="ampersand" 	g2="v" 	k="23" />
<hkern g1="ampersand" 	g2="x" 	k="2" />
<hkern g1="currency" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-18" />
<hkern g1="degree" 	g2="AE" 	k="186" />
<hkern g1="degree" 	g2="J" 	k="20" />
<hkern g1="degree" 	g2="T" 	k="-61" />
<hkern g1="degree" 	g2="V" 	k="-59" />
<hkern g1="degree" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="degree" 	g2="uniFB02" 	k="-41" />
<hkern g1="degree" 	g2="v" 	k="-80" />
<hkern g1="degree" 	g2="x" 	k="-20" />
<hkern g1="degree" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-20" />
<hkern g1="degree" 	g2="dollar,S" 	k="-41" />
<hkern g1="degree" 	g2="X" 	k="41" />
<hkern g1="degree" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="degree" 	g2="s" 	k="2" />
<hkern g1="percent" 	g2="ae" 	k="-35" />
<hkern g1="percent" 	g2="asterisk,ordfeminine,ordmasculine" 	k="102" />
<hkern g1="percent" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-43" />
<hkern g1="section" 	g2="ae" 	k="-18" />
<hkern g1="section" 	g2="uniFB02" 	k="-18" />
<hkern g1="section" 	g2="v" 	k="-18" />
<hkern g1="section" 	g2="x" 	k="-18" />
<hkern g1="section" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-18" />
<hkern g1="section" 	g2="s" 	k="-18" />
<hkern g1="section" 	g2="j" 	k="-18" />
<hkern g1="section" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="-18" />
<hkern g1="section" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-18" />
<hkern g1="section" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="-18" />
<hkern g1="section" 	g2="z" 	k="-18" />
<hkern g1="trademark" 	g2="AE" 	k="41" />
<hkern g1="trademark" 	g2="J" 	k="20" />
<hkern g1="trademark" 	g2="ae" 	k="-59" />
<hkern g1="trademark" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="trademark" 	g2="uniFB02" 	k="-63" />
<hkern g1="trademark" 	g2="v" 	k="-35" />
<hkern g1="trademark" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-59" />
<hkern g1="trademark" 	g2="s" 	k="-59" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="AE" 	k="186" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="dollar,S" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="T" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="V" 	k="-70" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="X" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="Z" 	k="-20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="ampersand" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="bracketright,braceright" 	k="-2" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="currency,Euro" 	k="-18" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="degree" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="exclam" 	k="-12" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="four" 	k="29" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="guillemotright,guilsinglright" 	k="-23" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="nine" 	k="-76" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="one" 	k="-59" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="paragraph" 	k="-80" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="percent" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="63" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="question" 	k="-76" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="questiondown" 	k="178" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteleft,quotedblleft" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="quotedbl,quotesingle" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="seven" 	k="-90" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="slash" 	k="291" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="uniFB02" 	k="-61" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="three" 	k="-8" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="trademark" 	k="-82" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="two" 	k="-41" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="underscore" 	k="20" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="v" 	k="-96" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="x" 	k="-23" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="z" 	k="-39" />
<hkern g1="asterisk,ordfeminine,ordmasculine" 	g2="zero,six" 	k="-18" />
<hkern g1="backslash" 	g2="AE" 	k="-61" />
<hkern g1="backslash" 	g2="J" 	k="-102" />
<hkern g1="backslash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="backslash" 	g2="T" 	k="41" />
<hkern g1="backslash" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="backslash" 	g2="V" 	k="82" />
<hkern g1="backslash" 	g2="X" 	k="-41" />
<hkern g1="backslash" 	g2="asterisk,ordfeminine,ordmasculine" 	k="291" />
<hkern g1="backslash" 	g2="j" 	k="-246" />
<hkern g1="backslash" 	g2="v" 	k="-41" />
<hkern g1="bracketleft,braceleft" 	g2="J" 	k="-6" />
<hkern g1="bracketleft,braceleft" 	g2="T" 	k="-8" />
<hkern g1="bracketleft,braceleft" 	g2="V" 	k="-43" />
<hkern g1="bracketleft,braceleft" 	g2="X" 	k="-2" />
<hkern g1="bracketleft,braceleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-2" />
<hkern g1="bracketleft,braceleft" 	g2="j" 	k="-205" />
<hkern g1="bracketleft,braceleft" 	g2="Y,Yacute,Ydieresis" 	k="43" />
<hkern g1="bracketleft,braceleft" 	g2="ae" 	k="27" />
<hkern g1="bracketright" 	g2="J" 	k="35" />
<hkern g1="bracketright" 	g2="T" 	k="43" />
<hkern g1="bracketright" 	g2="X" 	k="53" />
<hkern g1="colon,semicolon" 	g2="AE" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="J" 	k="-18" />
<hkern g1="colon,semicolon" 	g2="T" 	k="20" />
<hkern g1="colon,semicolon" 	g2="V" 	k="43" />
<hkern g1="colon,semicolon" 	g2="X" 	k="12" />
<hkern g1="colon,semicolon" 	g2="v" 	k="-18" />
<hkern g1="colon,semicolon" 	g2="uniFB02" 	k="-6" />
<hkern g1="colon,semicolon" 	g2="x" 	k="20" />
<hkern g1="exclam" 	g2="J" 	k="35" />
<hkern g1="exclam" 	g2="T" 	k="-41" />
<hkern g1="exclam" 	g2="V" 	k="-20" />
<hkern g1="exclam" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-12" />
<hkern g1="exclamdown" 	g2="AE" 	k="-20" />
<hkern g1="exclamdown" 	g2="J" 	k="-41" />
<hkern g1="exclamdown" 	g2="T" 	k="41" />
<hkern g1="exclamdown" 	g2="V" 	k="61" />
<hkern g1="exclamdown" 	g2="X" 	k="20" />
<hkern g1="exclamdown" 	g2="j" 	k="-129" />
<hkern g1="exclamdown" 	g2="uniFB02" 	k="-4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="AE" 	k="-35" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="23" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="23" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="6" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-23" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="uniFB02" 	k="-2" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="AE" 	k="27" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="J" 	k="72" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="-18" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="T" 	k="63" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="V" 	k="43" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="X" 	k="33" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="v" 	k="23" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="18" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="uniFB02" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="x" 	k="43" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="dollar,S" 	k="39" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-20" />
<hkern g1="plus,hyphen,asciitilde,uni00AD,periodcentered,guillemotright,divide,bullet,guilsinglright" 	g2="z" 	k="29" />
<hkern g1="parenleft" 	g2="J" 	k="27" />
<hkern g1="parenleft" 	g2="j" 	k="-158" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="AE" 	k="-70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="J" 	k="-18" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="63" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="74" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk,ordfeminine,ordmasculine" 	k="63" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="ae" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="uniFB02" 	k="18" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-39" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="dollar,S" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="35" />
<hkern g1="question" 	g2="AE" 	k="84" />
<hkern g1="question" 	g2="J" 	k="92" />
<hkern g1="question" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="2" />
<hkern g1="question" 	g2="V" 	k="23" />
<hkern g1="question" 	g2="X" 	k="33" />
<hkern g1="question" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-35" />
<hkern g1="question" 	g2="v" 	k="-20" />
<hkern g1="question" 	g2="Z" 	k="20" />
<hkern g1="question" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="question" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="questiondown" 	g2="AE" 	k="-12" />
<hkern g1="questiondown" 	g2="J" 	k="-123" />
<hkern g1="questiondown" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="104" />
<hkern g1="questiondown" 	g2="T" 	k="115" />
<hkern g1="questiondown" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="72" />
<hkern g1="questiondown" 	g2="V" 	k="135" />
<hkern g1="questiondown" 	g2="X" 	k="-6" />
<hkern g1="questiondown" 	g2="asterisk,ordfeminine,ordmasculine" 	k="166" />
<hkern g1="questiondown" 	g2="j" 	k="-143" />
<hkern g1="questiondown" 	g2="v" 	k="88" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="questiondown" 	g2="ae" 	k="39" />
<hkern g1="questiondown" 	g2="uniFB02" 	k="35" />
<hkern g1="questiondown" 	g2="x" 	k="-6" />
<hkern g1="questiondown" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="63" />
<hkern g1="questiondown" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="102" />
<hkern g1="questiondown" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="43" />
<hkern g1="questiondown" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="35" />
<hkern g1="questiondown" 	g2="s" 	k="18" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="63" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="6" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-76" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-53" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="-12" />
<hkern g1="quoteleft,quotedblleft" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="v" 	k="-70" />
<hkern g1="quoteleft,quotedblleft" 	g2="ae" 	k="4" />
<hkern g1="quoteleft,quotedblleft" 	g2="uniFB02" 	k="-27" />
<hkern g1="quoteleft,quotedblleft" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="quoteleft,quotedblleft" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="63" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="2" />
<hkern g1="quoteright,quotedblright" 	g2="T" 	k="-80" />
<hkern g1="quoteright,quotedblright" 	g2="V" 	k="-76" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="-18" />
<hkern g1="quoteright,quotedblright" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="quoteright,quotedblright" 	g2="v" 	k="-39" />
<hkern g1="quoteright,quotedblright" 	g2="ae" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="uniFB02" 	k="-2" />
<hkern g1="quoteright,quotedblright" 	g2="dollar,S" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="59" />
<hkern g1="quoteright,quotedblright" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="AE" 	k="90" />
<hkern g1="quotedbl,quotesingle" 	g2="T" 	k="-35" />
<hkern g1="quotedbl,quotesingle" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-41" />
<hkern g1="slash" 	g2="AE" 	k="82" />
<hkern g1="slash" 	g2="J" 	k="20" />
<hkern g1="slash" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="slash" 	g2="T" 	k="-61" />
<hkern g1="slash" 	g2="V" 	k="-61" />
<hkern g1="slash" 	g2="j" 	k="-2" />
<hkern g1="slash" 	g2="v" 	k="61" />
<hkern g1="slash" 	g2="ae" 	k="141" />
<hkern g1="slash" 	g2="uniFB02" 	k="59" />
<hkern g1="slash" 	g2="x" 	k="135" />
<hkern g1="slash" 	g2="dollar,S" 	k="41" />
<hkern g1="slash" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="141" />
<hkern g1="slash" 	g2="z" 	k="141" />
<hkern g1="slash" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="141" />
<hkern g1="slash" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="slash" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="slash" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="141" />
<hkern g1="slash" 	g2="s" 	k="141" />
<hkern g1="underscore" 	g2="AE" 	k="-121" />
<hkern g1="underscore" 	g2="J" 	k="-182" />
<hkern g1="underscore" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="underscore" 	g2="T" 	k="18" />
<hkern g1="underscore" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="underscore" 	g2="V" 	k="20" />
<hkern g1="underscore" 	g2="X" 	k="-100" />
<hkern g1="underscore" 	g2="asterisk,ordfeminine,ordmasculine" 	k="20" />
<hkern g1="underscore" 	g2="j" 	k="-211" />
<hkern g1="underscore" 	g2="v" 	k="23" />
<hkern g1="underscore" 	g2="Y,Yacute,Ydieresis" 	k="-2" />
<hkern g1="underscore" 	g2="ae" 	k="20" />
<hkern g1="underscore" 	g2="uniFB02" 	k="41" />
<hkern g1="underscore" 	g2="x" 	k="-80" />
<hkern g1="underscore" 	g2="Z" 	k="-41" />
<hkern g1="underscore" 	g2="dollar,S" 	k="-41" />
<hkern g1="underscore" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="59" />
<hkern g1="underscore" 	g2="z" 	k="-70" />
<hkern g1="underscore" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="59" />
<hkern g1="underscore" 	g2="E,Egrave,Eacute,Ecircumflex,Edieresis" 	k="41" />
<hkern g1="underscore" 	g2="s" 	k="-20" />
<hkern g1="underscore" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="-61" />
<hkern g1="c,cent,ccedilla" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="23" />
<hkern g1="c,cent,ccedilla" 	g2="ampersand" 	k="55" />
<hkern g1="c,cent,ccedilla" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-23" />
<hkern g1="c,cent,ccedilla" 	g2="backslash" 	k="100" />
<hkern g1="c,cent,ccedilla" 	g2="bracketright,braceright" 	k="27" />
<hkern g1="c,cent,ccedilla" 	g2="bracketleft" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="colon,semicolon" 	k="-12" />
<hkern g1="c,cent,ccedilla" 	g2="degree" 	k="-33" />
<hkern g1="c,cent,ccedilla" 	g2="eight" 	k="10" />
<hkern g1="c,cent,ccedilla" 	g2="four" 	k="53" />
<hkern g1="c,cent,ccedilla" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="nine" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="one" 	k="49" />
<hkern g1="c,cent,ccedilla" 	g2="parenright" 	k="43" />
<hkern g1="c,cent,ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="c,cent,ccedilla" 	g2="question" 	k="59" />
<hkern g1="c,cent,ccedilla" 	g2="questiondown" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="quoteleft,quotedblleft" 	k="-33" />
<hkern g1="c,cent,ccedilla" 	g2="quoteright,quotedblright" 	k="-43" />
<hkern g1="c,cent,ccedilla" 	g2="section" 	k="27" />
<hkern g1="c,cent,ccedilla" 	g2="seven" 	k="35" />
<hkern g1="c,cent,ccedilla" 	g2="uniFB02" 	k="-12" />
<hkern g1="c,cent,ccedilla" 	g2="three" 	k="39" />
<hkern g1="c,cent,ccedilla" 	g2="trademark" 	k="29" />
<hkern g1="c,cent,ccedilla" 	g2="two" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="v" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="x" 	k="39" />
<hkern g1="c,cent,ccedilla" 	g2="z" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="zero,six" 	k="18" />
<hkern g1="c,cent,ccedilla" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="2" />
<hkern g1="c,cent,ccedilla" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="6" />
<hkern g1="c,cent,ccedilla" 	g2="V" 	k="12" />
<hkern g1="c,cent,ccedilla" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk,ordfeminine,ordmasculine" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="141" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="degree" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="nine" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="one" 	k="59" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="100" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="questiondown" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="seven" 	k="59" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="three" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="61" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="two" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="X" 	k="74" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="ae" 	k="31" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="paragraph" 	k="41" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="percent" 	k="18" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="55" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="underscore" 	k="12" />
<hkern g1="eth" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-20" />
<hkern g1="eth" 	g2="backslash" 	k="20" />
<hkern g1="eth" 	g2="degree" 	k="-41" />
<hkern g1="eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="39" />
<hkern g1="eth" 	g2="question" 	k="18" />
<hkern g1="eth" 	g2="questiondown" 	k="18" />
<hkern g1="eth" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="eth" 	g2="three" 	k="18" />
<hkern g1="eth" 	g2="trademark" 	k="-59" />
<hkern g1="eth" 	g2="zero,six" 	k="-18" />
<hkern g1="eth" 	g2="underscore" 	k="41" />
<hkern g1="f" 	g2="ampersand" 	k="23" />
<hkern g1="f" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-115" />
<hkern g1="f" 	g2="backslash" 	k="-131" />
<hkern g1="f" 	g2="bracketright,braceright" 	k="-100" />
<hkern g1="f" 	g2="colon,semicolon" 	k="-55" />
<hkern g1="f" 	g2="degree" 	k="-80" />
<hkern g1="f" 	g2="eight" 	k="-35" />
<hkern g1="f" 	g2="four" 	k="59" />
<hkern g1="f" 	g2="nine" 	k="-68" />
<hkern g1="f" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="f" 	g2="one" 	k="-115" />
<hkern g1="f" 	g2="parenright" 	k="-49" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="f" 	g2="question" 	k="-59" />
<hkern g1="f" 	g2="questiondown" 	k="23" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-125" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-170" />
<hkern g1="f" 	g2="seven" 	k="-96" />
<hkern g1="f" 	g2="three" 	k="-55" />
<hkern g1="f" 	g2="trademark" 	k="-172" />
<hkern g1="f" 	g2="two" 	k="-39" />
<hkern g1="f" 	g2="v" 	k="-20" />
<hkern g1="f" 	g2="zero,six" 	k="-35" />
<hkern g1="f" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="f" 	g2="ae" 	k="20" />
<hkern g1="f" 	g2="paragraph" 	k="-96" />
<hkern g1="f" 	g2="percent" 	k="-39" />
<hkern g1="f" 	g2="underscore" 	k="27" />
<hkern g1="f" 	g2="exclam" 	k="-2" />
<hkern g1="f" 	g2="five" 	k="-35" />
<hkern g1="f" 	g2="guillemotright,guilsinglright" 	k="2" />
<hkern g1="f" 	g2="j" 	k="-84" />
<hkern g1="f" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="-164" />
<hkern g1="f" 	g2="slash" 	k="61" />
<hkern g1="k" 	g2="ampersand" 	k="6" />
<hkern g1="k" 	g2="backslash" 	k="82" />
<hkern g1="k" 	g2="bracketleft" 	k="27" />
<hkern g1="k" 	g2="eight" 	k="12" />
<hkern g1="k" 	g2="four" 	k="61" />
<hkern g1="k" 	g2="nine" 	k="-20" />
<hkern g1="k" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="43" />
<hkern g1="k" 	g2="one" 	k="-6" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="k" 	g2="question" 	k="-6" />
<hkern g1="k" 	g2="quoteleft,quotedblleft" 	k="-2" />
<hkern g1="k" 	g2="seven" 	k="14" />
<hkern g1="k" 	g2="three" 	k="-20" />
<hkern g1="k" 	g2="two" 	k="-27" />
<hkern g1="k" 	g2="v" 	k="53" />
<hkern g1="k" 	g2="x" 	k="41" />
<hkern g1="k" 	g2="z" 	k="23" />
<hkern g1="k" 	g2="zero,six" 	k="20" />
<hkern g1="k" 	g2="V" 	k="27" />
<hkern g1="k" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="43" />
<hkern g1="k" 	g2="X" 	k="18" />
<hkern g1="k" 	g2="ae" 	k="20" />
<hkern g1="k" 	g2="underscore" 	k="-137" />
<hkern g1="k" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="18" />
<hkern g1="k" 	g2="s" 	k="33" />
<hkern g1="k" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="d,uniFB02" 	g2="uniFB02" 	k="14" />
<hkern g1="l" 	g2="asterisk,ordfeminine,ordmasculine" 	k="47" />
<hkern g1="l" 	g2="backslash" 	k="84" />
<hkern g1="l" 	g2="colon,semicolon" 	k="-39" />
<hkern g1="l" 	g2="degree" 	k="41" />
<hkern g1="l" 	g2="eight" 	k="-18" />
<hkern g1="l" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="l" 	g2="one" 	k="41" />
<hkern g1="l" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-59" />
<hkern g1="l" 	g2="question" 	k="41" />
<hkern g1="l" 	g2="questiondown" 	k="-18" />
<hkern g1="l" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="l" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="l" 	g2="seven" 	k="31" />
<hkern g1="l" 	g2="uniFB02" 	k="10" />
<hkern g1="l" 	g2="three" 	k="-20" />
<hkern g1="l" 	g2="two" 	k="-39" />
<hkern g1="l" 	g2="v" 	k="51" />
<hkern g1="l" 	g2="x" 	k="-18" />
<hkern g1="l" 	g2="zero,six" 	k="-14" />
<hkern g1="l" 	g2="paragraph" 	k="6" />
<hkern g1="l" 	g2="underscore" 	k="-150" />
<hkern g1="l" 	g2="five" 	k="-8" />
<hkern g1="l" 	g2="slash" 	k="-70" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk,ordfeminine,ordmasculine" 	k="59" />
<hkern g1="h,m,n,ntilde" 	g2="backslash" 	k="141" />
<hkern g1="h,m,n,ntilde" 	g2="degree" 	k="47" />
<hkern g1="h,m,n,ntilde" 	g2="nine" 	k="35" />
<hkern g1="h,m,n,ntilde" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-2" />
<hkern g1="h,m,n,ntilde" 	g2="one" 	k="49" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="100" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="h,m,n,ntilde" 	g2="seven" 	k="49" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="59" />
<hkern g1="h,m,n,ntilde" 	g2="two" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="v" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="paragraph" 	k="55" />
<hkern g1="h,m,n,ntilde" 	g2="percent" 	k="59" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk,ordfeminine,ordmasculine" 	k="39" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="141" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="degree" 	k="33" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="nine" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="one" 	k="59" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="100" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="questiondown" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="seven" 	k="59" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="three" 	k="49" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="trademark" 	k="61" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="two" 	k="20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="39" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="18" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="53" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V" 	k="82" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="-20" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="8" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="ae" 	k="4" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="paragraph" 	k="49" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="percent" 	k="59" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="underscore" 	k="59" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="J" 	k="70" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="113" />
<hkern g1="b,o,p,germandbls,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="AE" 	k="8" />
<hkern g1="r" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="35" />
<hkern g1="r" 	g2="ampersand" 	k="76" />
<hkern g1="r" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-61" />
<hkern g1="r" 	g2="backslash" 	k="41" />
<hkern g1="r" 	g2="bracketright,braceright" 	k="35" />
<hkern g1="r" 	g2="bracketleft" 	k="43" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-2" />
<hkern g1="r" 	g2="degree" 	k="-55" />
<hkern g1="r" 	g2="eight" 	k="-2" />
<hkern g1="r" 	g2="four" 	k="66" />
<hkern g1="r" 	g2="b,f,h,k,l,t,germandbls,thorn" 	k="18" />
<hkern g1="r" 	g2="nine" 	k="-6" />
<hkern g1="r" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="r" 	g2="one" 	k="29" />
<hkern g1="r" 	g2="parenright" 	k="27" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="r" 	g2="question" 	k="-6" />
<hkern g1="r" 	g2="questiondown" 	k="80" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-63" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-63" />
<hkern g1="r" 	g2="section" 	k="-20" />
<hkern g1="r" 	g2="seven" 	k="-43" />
<hkern g1="r" 	g2="uniFB02" 	k="-31" />
<hkern g1="r" 	g2="three" 	k="18" />
<hkern g1="r" 	g2="two" 	k="-2" />
<hkern g1="r" 	g2="v" 	k="-31" />
<hkern g1="r" 	g2="x" 	k="18" />
<hkern g1="r" 	g2="zero,six" 	k="-2" />
<hkern g1="r" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="r" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="53" />
<hkern g1="r" 	g2="V" 	k="59" />
<hkern g1="r" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="27" />
<hkern g1="r" 	g2="X" 	k="90" />
<hkern g1="r" 	g2="ae" 	k="18" />
<hkern g1="r" 	g2="paragraph" 	k="-61" />
<hkern g1="r" 	g2="underscore" 	k="23" />
<hkern g1="r" 	g2="five" 	k="-2" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="-2" />
<hkern g1="r" 	g2="i,m,n,p,r,mu,igrave,iacute,icircumflex,idieresis,ntilde" 	k="18" />
<hkern g1="r" 	g2="slash" 	k="82" />
<hkern g1="r" 	g2="s" 	k="8" />
<hkern g1="r" 	g2="AE" 	k="74" />
<hkern g1="r" 	g2="B,D,H,K,L,M,N,P,R,Eth,Ntilde,Thorn" 	k="35" />
<hkern g1="r" 	g2="dollar,S" 	k="43" />
<hkern g1="r" 	g2="parenleft" 	k="35" />
<hkern g1="s" 	g2="backslash" 	k="123" />
<hkern g1="s" 	g2="degree" 	k="29" />
<hkern g1="s" 	g2="nine" 	k="10" />
<hkern g1="s" 	g2="one" 	k="18" />
<hkern g1="s" 	g2="question" 	k="39" />
<hkern g1="s" 	g2="seven" 	k="59" />
<hkern g1="s" 	g2="three" 	k="20" />
<hkern g1="s" 	g2="trademark" 	k="39" />
<hkern g1="s" 	g2="two" 	k="20" />
<hkern g1="s" 	g2="v" 	k="20" />
<hkern g1="s" 	g2="x" 	k="20" />
<hkern g1="s" 	g2="paragraph" 	k="49" />
<hkern g1="t" 	g2="ampersand" 	k="18" />
<hkern g1="t" 	g2="asterisk,ordfeminine,ordmasculine" 	k="39" />
<hkern g1="t" 	g2="backslash" 	k="141" />
<hkern g1="t" 	g2="degree" 	k="10" />
<hkern g1="t" 	g2="eight" 	k="12" />
<hkern g1="t" 	g2="four" 	k="74" />
<hkern g1="t" 	g2="nine" 	k="35" />
<hkern g1="t" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="t" 	g2="one" 	k="47" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-20" />
<hkern g1="t" 	g2="question" 	k="39" />
<hkern g1="t" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="t" 	g2="section" 	k="27" />
<hkern g1="t" 	g2="seven" 	k="55" />
<hkern g1="t" 	g2="uniFB02" 	k="29" />
<hkern g1="t" 	g2="trademark" 	k="59" />
<hkern g1="t" 	g2="two" 	k="18" />
<hkern g1="t" 	g2="v" 	k="20" />
<hkern g1="t" 	g2="zero,six" 	k="39" />
<hkern g1="t" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="20" />
<hkern g1="t" 	g2="paragraph" 	k="41" />
<hkern g1="t" 	g2="underscore" 	k="-76" />
<hkern g1="t" 	g2="five" 	k="18" />
<hkern g1="v" 	g2="A,F,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="v" 	g2="ampersand" 	k="43" />
<hkern g1="v" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-96" />
<hkern g1="v" 	g2="backslash" 	k="41" />
<hkern g1="v" 	g2="colon,semicolon" 	k="-18" />
<hkern g1="v" 	g2="degree" 	k="-80" />
<hkern g1="v" 	g2="eight" 	k="-18" />
<hkern g1="v" 	g2="four" 	k="47" />
<hkern g1="v" 	g2="nine" 	k="-61" />
<hkern g1="v" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="v" 	g2="one" 	k="-8" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="70" />
<hkern g1="v" 	g2="question" 	k="-63" />
<hkern g1="v" 	g2="questiondown" 	k="57" />
<hkern g1="v" 	g2="quoteleft,quotedblleft" 	k="-70" />
<hkern g1="v" 	g2="quoteright,quotedblright" 	k="-39" />
<hkern g1="v" 	g2="seven" 	k="-31" />
<hkern g1="v" 	g2="uniFB02" 	k="-31" />
<hkern g1="v" 	g2="three" 	k="14" />
<hkern g1="v" 	g2="two" 	k="-8" />
<hkern g1="v" 	g2="v" 	k="6" />
<hkern g1="v" 	g2="x" 	k="61" />
<hkern g1="v" 	g2="z" 	k="20" />
<hkern g1="v" 	g2="zero,six" 	k="-18" />
<hkern g1="v" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="18" />
<hkern g1="v" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="23" />
<hkern g1="v" 	g2="X" 	k="61" />
<hkern g1="v" 	g2="ae" 	k="33" />
<hkern g1="v" 	g2="paragraph" 	k="-61" />
<hkern g1="v" 	g2="underscore" 	k="23" />
<hkern g1="v" 	g2="five" 	k="-18" />
<hkern g1="v" 	g2="j" 	k="31" />
<hkern g1="v" 	g2="slash" 	k="55" />
<hkern g1="v" 	g2="s" 	k="12" />
<hkern g1="v" 	g2="AE" 	k="53" />
<hkern g1="v" 	g2="dollar,S" 	k="18" />
<hkern g1="w" 	g2="backslash" 	k="102" />
<hkern g1="w" 	g2="one" 	k="18" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="w" 	g2="three" 	k="20" />
<hkern g1="w" 	g2="two" 	k="18" />
<hkern g1="w" 	g2="underscore" 	k="59" />
<hkern g1="x" 	g2="ampersand" 	k="41" />
<hkern g1="x" 	g2="asterisk,ordfeminine,ordmasculine" 	k="-23" />
<hkern g1="x" 	g2="backslash" 	k="100" />
<hkern g1="x" 	g2="colon,semicolon" 	k="20" />
<hkern g1="x" 	g2="degree" 	k="-20" />
<hkern g1="x" 	g2="eight" 	k="2" />
<hkern g1="x" 	g2="four" 	k="63" />
<hkern g1="x" 	g2="a,c,d,e,g,o,q,cent,agrave,aacute,acircumflex,atilde,adieresis,aring,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="x" 	g2="one" 	k="-6" />
<hkern g1="x" 	g2="question" 	k="-8" />
<hkern g1="x" 	g2="seven" 	k="18" />
<hkern g1="x" 	g2="two" 	k="12" />
<hkern g1="x" 	g2="v" 	k="61" />
<hkern g1="x" 	g2="z" 	k="20" />
<hkern g1="x" 	g2="zero,six" 	k="20" />
<hkern g1="x" 	g2="at,C,G,O,Q,copyright,registered,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="x" 	g2="U,W,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="35" />
<hkern g1="x" 	g2="V" 	k="43" />
<hkern g1="x" 	g2="plus,hyphen,asciitilde,guillemotleft,logicalnot,uni00AD,periodcentered,divide,bullet,guilsinglleft" 	k="43" />
<hkern g1="x" 	g2="ae" 	k="20" />
<hkern g1="x" 	g2="paragraph" 	k="-2" />
<hkern g1="x" 	g2="underscore" 	k="-80" />
<hkern g1="x" 	g2="j" 	k="20" />
<hkern g1="x" 	g2="slash" 	k="-20" />
<hkern g1="x" 	g2="s" 	k="20" />
<hkern g1="x" 	g2="u,w,y,ugrave,uacute,ucircumflex,udieresis,yacute,ydieresis" 	k="20" />
<hkern g1="x" 	g2="dollar,S" 	k="27" />
</font>
</defs></svg> 