<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid" width="214" height="214" style="shape-rendering: auto; display: block; background: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink"><g><g transform="translate(85,50)">
<g transform="rotate(0)">
<circle fill-opacity="1" fill="#8996a0" r="6" cy="0" cx="0">
  <animateTransform repeatCount="indefinite" dur="1s" keyTimes="0;1" values="1.5 1.5;1 1" begin="-0.875s" type="scale" attributeName="transform"></animateTransform>
  <animate begin="-0.875s" values="1;0" repeatCount="indefinite" dur="1s" keyTimes="0;1" attributeName="fill-opacity"></animate>
</circle>
</g>
</g><g transform="translate(74.74873734152916,74.74873734152916)">
<g transform="rotate(45)">
<circle fill-opacity="0.875" fill="#8996a0" r="6" cy="0" cx="0">
  <animateTransform repeatCount="indefinite" dur="1s" keyTimes="0;1" values="1.5 1.5;1 1" begin="-0.75s" type="scale" attributeName="transform"></animateTransform>
  <animate begin="-0.75s" values="1;0" repeatCount="indefinite" dur="1s" keyTimes="0;1" attributeName="fill-opacity"></animate>
</circle>
</g>
</g><g transform="translate(50,85)">
<g transform="rotate(90)">
<circle fill-opacity="0.75" fill="#8996a0" r="6" cy="0" cx="0">
  <animateTransform repeatCount="indefinite" dur="1s" keyTimes="0;1" values="1.5 1.5;1 1" begin="-0.625s" type="scale" attributeName="transform"></animateTransform>
  <animate begin="-0.625s" values="1;0" repeatCount="indefinite" dur="1s" keyTimes="0;1" attributeName="fill-opacity"></animate>
</circle>
</g>
</g><g transform="translate(25.25126265847084,74.74873734152916)">
<g transform="rotate(135)">
<circle fill-opacity="0.625" fill="#8996a0" r="6" cy="0" cx="0">
  <animateTransform repeatCount="indefinite" dur="1s" keyTimes="0;1" values="1.5 1.5;1 1" begin="-0.5s" type="scale" attributeName="transform"></animateTransform>
  <animate begin="-0.5s" values="1;0" repeatCount="indefinite" dur="1s" keyTimes="0;1" attributeName="fill-opacity"></animate>
</circle>
</g>
</g><g transform="translate(15,50.00000000000001)">
<g transform="rotate(180)">
<circle fill-opacity="0.5" fill="#8996a0" r="6" cy="0" cx="0">
  <animateTransform repeatCount="indefinite" dur="1s" keyTimes="0;1" values="1.5 1.5;1 1" begin="-0.375s" type="scale" attributeName="transform"></animateTransform>
  <animate begin="-0.375s" values="1;0" repeatCount="indefinite" dur="1s" keyTimes="0;1" attributeName="fill-opacity"></animate>
</circle>
</g>
</g><g transform="translate(25.251262658470832,25.25126265847084)">
<g transform="rotate(225)">
<circle fill-opacity="0.375" fill="#8996a0" r="6" cy="0" cx="0">
  <animateTransform repeatCount="indefinite" dur="1s" keyTimes="0;1" values="1.5 1.5;1 1" begin="-0.25s" type="scale" attributeName="transform"></animateTransform>
  <animate begin="-0.25s" values="1;0" repeatCount="indefinite" dur="1s" keyTimes="0;1" attributeName="fill-opacity"></animate>
</circle>
</g>
</g><g transform="translate(49.99999999999999,15)">
<g transform="rotate(270)">
<circle fill-opacity="0.25" fill="#8996a0" r="6" cy="0" cx="0">
  <animateTransform repeatCount="indefinite" dur="1s" keyTimes="0;1" values="1.5 1.5;1 1" begin="-0.125s" type="scale" attributeName="transform"></animateTransform>
  <animate begin="-0.125s" values="1;0" repeatCount="indefinite" dur="1s" keyTimes="0;1" attributeName="fill-opacity"></animate>
</circle>
</g>
</g><g transform="translate(74.74873734152916,25.251262658470832)">
<g transform="rotate(315)">
<circle fill-opacity="0.125" fill="#8996a0" r="6" cy="0" cx="0">
  <animateTransform repeatCount="indefinite" dur="1s" keyTimes="0;1" values="1.5 1.5;1 1" begin="0s" type="scale" attributeName="transform"></animateTransform>
  <animate begin="0s" values="1;0" repeatCount="indefinite" dur="1s" keyTimes="0;1" attributeName="fill-opacity"></animate>
</circle>
</g>
</g><g></g></g><!-- [ldio] generated by https://loading.io --></svg>